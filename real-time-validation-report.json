{"timestamp": "2025-06-09T20:02:06.637Z", "summary": {"totalFiles": 33, "filesWithTranslations": 5, "translationCoverage": "15.15", "totalTranslationKeys": 223, "totalHardcodedStrings": 464, "totalIssues": 252, "criticalIssues": 223, "systemHealth": "NEEDS_ATTENTION"}, "translationUsage": [{"file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "hookUsage": [], "usedKeys": ["messages.testComplete", "messages.testCompleteDesc", "messages.testFailed", "messages.testFailedDesc", "title", "subtitle", "features.quick.title", "features.quick.description", "features.professional.title", "features.professional.description", "features.personalized.title", "features.personalized.description", "features.practical.title", "features.practical.description", "instructions.title", "instructions.item1", "instructions.item2", "instructions.item3", "instructions.item4", "navigation.startTest", "result.title", "result.subtitle", "result.match", "result.constitutionFeatures", "result.commonSymptoms", "result.menstrualFeatures", "recommendations.acupoints.title", "recommendations.acupoints.primaryAcupoints", "recommendations.acupoints.location", "recommendations.acupoints.function", "recommendations.acupoints.method", "recommendations.acupoints.guidelines", "recommendations.acupoints.technique", "recommendations.acupoints.frequency", "recommendations.acupoints.duration", "recommendations.dietary.title", "recommendations.dietary.beneficialFoods", "recommendations.dietary.foodsToAvoid", "recommendations.dietary.dietaryPrinciples", "recommendations.lifestyle.title", "recommendations.lifestyle.description", "recommendations.lifestyle.reminder", "recommendations.lifestyle.reminderText", "recommendations.menstrualPain.title", "recommendations.menstrualPain.acupointTherapy", "recommendations.menstrualPain.lifestyleAdjustments", "emergencyKit.title", "navigation.retakeTest", "painScale.levels.none", "painScale.levels.mild", "painScale.levels.moderate", "painScale.levels.severe", "painScale.levels.extreme", "painScale.title", "painScale.reference", "painScale.descriptions.0-2", "painScale.descriptions.3-4", "painScale.descriptions.5-7", "painScale.descriptions.8-10", "navigation.previous", "navigation.completeTest", "navigation.next", "testComplete", "testCompleteDesc", "testFailed", "testFailedDesc"], "hardcodedStrings": [");\n  const [selectedAnswers, setSelectedAnswers] = useState<Record<string, string>>({});\n\n  const {\n    currentSession,\n    currentQuestion,\n    currentQuestionIndex,\n    totalQuestions,\n    progress,\n    isComplete,\n    result,\n    isLoading,\n    error,\n    startTest,\n    answerQuestion,\n    goToPreviousQuestion,\n    goToNextQuestion,\n    completeTest,\n    resetTest\n  } = useConstitutionTest();\n\n  const {\n    notifications,\n    removeNotification,\n    addSuccessNotification,\n    addErrorNotification\n  } = useNotifications();\n\n  const handleStartTest = () => {\n    startTest(locale);\n    setSelectedAnswers({});\n  };\n\n  // 处理单选答案\n  const handleAnswerSelect = (questionId: string, value: string | number) => {\n    const stringValue = String(value);\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: stringValue\n    }));\n\n    const answer: ConstitutionAnswer = {\n      questionId,\n      selectedValues: [stringValue],\n      timestamp: new Date().toISOString()\n    };\n\n    answerQuestion(answer);\n  };\n\n  // 处理多选答案\n  const handleMultipleAnswerSelect = (questionId: string, value: string) => {\n    const currentValues = Array.isArray(selectedAnswers[questionId])\n      ? selectedAnswers[questionId] as string[]\n      : selectedAnswers[questionId]\n        ? [selectedAnswers[questionId] as string]\n        : [];\n\n    let newValues: string[];\n\n    // 处理", ");\n\n    if (isNoneOption) {\n      // 如果选择", "];\n    } else {\n      // 如果选择其他选项，先移除", ");\n      newValues = filteredValues.includes(value)\n        ? filteredValues.filter(v => v !== value)\n        : [...filteredValues, value];\n    }\n\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: newValues\n    }));\n\n    const answer: ConstitutionAnswer = {\n      questionId,\n      selectedValues: newValues,\n      timestamp: new Date().toISOString()\n    };\n\n    answerQuestion(answer);\n  };\n\n  const handleNext = () => {\n    if (currentQuestionIndex >= totalQuestions - 1) {\n      // 完成测试\n      const testResult = completeTest();\n      if (testResult) {\n        addSuccessNotification(\n          t(", ")\n        );\n      }\n    } else {\n      goToNextQuestion();\n    }\n  };\n\n  const handlePrevious = () => {\n    goToPreviousQuestion();\n  };\n\n  const getCurrentAnswer = () => {\n    return currentQuestion ? selectedAnswers[currentQuestion.id] : undefined;\n  };\n\n  const canProceed = () => {\n    if (!currentQuestion) return false;\n\n    const answer = getCurrentAnswer();\n\n    // 对于多选题，检查是否有选择（可以为空数组，因为有些多选题不是必填的）\n    if (currentQuestion.type === ", ") {\n      return true; // 多选题允许不选择任何选项\n    }\n\n    // 对于单选题和滑块题，必须有选择\n    return answer !== undefined && answer !== null && answer !== ", ";\n  };\n\n  // 检查是否有痛经症状\n  const hasMenstrualPainSymptoms = (answers: ConstitutionAnswer[]): boolean => {\n    return answers.some(answer =>\n      answer.questionId === ", ")\n    );\n  };\n\n  // 获取痛经穴位建议\n  const getMenstrualPainAcupoints = (constitutionType: ConstitutionType, locale: string): any[] => {\n    const localeData = (menstrualPainAcupoints as any)[locale] || menstrualPainAcupoints.zh;\n    return localeData[constitutionType] || [];\n  };\n\n  // 获取痛经生活方式建议\n  const getMenstrualPainLifestyleTips = (constitutionType: ConstitutionType, locale: string): any[] => {\n    const localeData = (menstrualPainLifestyleTips as any)[locale] || menstrualPainLifestyleTips.zh;\n    return localeData[constitutionType] || [];\n  };\n\n  // 如果没有开始测试，显示介绍页面\n  if (!currentSession) {\n    return (\n      <div className=", ">\n        <NotificationContainer \n          notifications={notifications}\n          onRemove={removeNotification}\n        />\n        \n        {/* 介绍页面 - 紫色主题 */}\n        <div className=", ")}\n          </p>\n        </div>\n\n        {/* 测试特点 - 紫色主题卡片 */}\n        <div className=", ")}\n            </p>\n          </div>\n        </div>\n\n        {/* 测试说明 - 紫色主题 */}\n        <div className=", ")}\n            </li>\n          </ul>\n        </div>\n\n        {/* 开始按钮 - 紫色渐变 */}\n        <div className=", ")}</span>\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // 如果测试完成，显示结果\n  if (result) {\n    const typeInfo = constitutionTypeInfo[locale]?.[result.primaryType] || constitutionTypeInfo.zh[result.primaryType];\n    \n    return (\n      <div className=", ">\n        <NotificationContainer \n          notifications={notifications}\n          onRemove={removeNotification}\n        />\n        \n        {/* 结果标题 */}\n        <div className=", ")}\n          </p>\n        </div>\n\n        {/* 体质类型结果 */}\n        <div className=", ">{result.confidence}%</span>\n            </div>\n          </div>\n\n          {/* 体质特征 */}\n          <div className=", ">• {feature}</li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* 个性化建议 */}\n        <div className=", ">\n          {/* 穴位建议 */}\n          <div className=", ")}</strong>\n                {result.recommendations.acupoints.duration}\n              </p>\n            </div>\n          </div>\n\n          {/* 饮食建议 */}\n          <div className=", ">• {principle}</li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 场景化生活建议 */}\n        <div className=", ")}\n            </p>\n          </div>\n        </div>\n\n        {/* 痛经专项建议 */}\n        {hasMenstrualPainSymptoms(currentSession?.answers || []) && (\n          <div className=", ">\n              {/* 基于体质的痛经建议 */}\n              <div className=", ">{point.description}</p>\n                  </div>\n                ))}\n              </div>\n\n              {/* 生活方式建议 */}\n              <div className=", "></span>\n                      {tip}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* 个性化应急包推荐 */}\n        {hasMenstrualPainSymptoms(currentSession?.answers || []) && (\n          <div className=", "根据您的体质特点，为您推荐专属的应急包物品清单。提前准备，让经期更从容。", "必需", "推荐", "可选", "📦 打包建议：", " 优先携带", "物品，根据外出时间和场景选择", "和", "物品。建议准备一个专用的小包，方便随时取用。", "}\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* 相关文章推荐 */}\n        <div className=", "为您推荐的健康文章", "阅读全文", " />\n                </a>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* 社交沟通模板 */}\n        {hasMenstrualPainSymptoms(currentSession?.answers || []) && (\n          <div className=", "沟通模板助手", "经期不适时，与身边的人沟通很重要。这些模板可以帮助你更好地表达需求和寻求理解。", "亲密", "随意", "正式", "\n                        </p>\n\n                        <button\n                          onClick={() => {\n                            navigator.clipboard.writeText(template.content);\n                            // 可以添加复制成功的提示\n                          }}\n                          className=", "复制文本", "💡 使用提示：", " 这些模板仅供参考，请根据你的实际情况和关系亲密度进行调整。真诚的沟通是建立理解的关键。", "}\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* 重新测试按钮 */}\n        <div className=", ")}\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // 显示问题\n  return (\n    <div className=", ">\n      <NotificationContainer \n        notifications={notifications}\n        onRemove={removeNotification}\n      />\n      \n      {isLoading && <LoadingSpinner />}\n      \n      {/* 进度条 - 紫色主题 */}\n      <div className=", "第 ${currentQuestionIndex + 1} 题，共 ${totalQuestions} 题", "完成", ">\n          {/* 问题标题 - 紫色主题 */}\n          <div className=", ">\n                {currentQuestion.description}\n              </p>\n            )}\n          </div>\n\n          {/* 选项 */}\n          <div className=", " ? (\n              // 滑块类型问题\n              <div className=", ")}</span>\n                  </div>\n                </div>\n\n                {/* 当前选择的值显示 - 增强紫色主题 */}\n                <div className=", ">\n                        ({currentQuestion.options.find(opt => opt.value == (selectedAnswers[currentQuestion.id] || 0))?.label})\n                      </span>\n                    </span>\n                  </div>\n                </div>\n\n                {/* 疼痛程度说明 - 紫色主题 */}\n                <div className=", " ? (\n              // 多选问题 - 紫色主题\n              <div className=", ">{option.label}</span>\n                      </div>\n                    </label>\n                  );\n                })}\n              </div>\n            ) : (\n              // 普通单选问题 - 紫色主题\n              <div className=", ">{option.label}</span>\n                    </div>\n                  </label>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* 导航按钮 - 紫色主题 */}\n          <div className="], "hasTranslationHook": false, "translationKeyCount": 66, "timestamp": "2025-06-09T20:02:06.625Z"}, {"file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "hookUsage": ["useTranslations"], "usedKeys": ["messages.saveSuccess", "form.save", "messages.saveError", "messages.validationError", "messages.updateSuccess", "messages.updateError", "messages.confirmDelete", "messages.deleteSuccess", "entries.delete", "messages.deleteError", "navigation.overview", "navigation.addEntry", "navigation.viewEntries", "navigation.statistics", "navigation.export", "statistics.overview", "entries.noEntries", "entries.noEntriesDescription", "entries.add<PERSON><PERSON><PERSON>", "statistics.totalEntries", "statistics.averagePain", "statistics.trendDirection", "statistics.${statistics.trendDirection}", "form.editTitle", "form.title", "entries.title", "entries.painIntensity", "entries.duration", "entries.minutes", "entries.edit", "statistics.title", "statistics.inDevelopment", "export.title", "export.inDevelopment", "messages.close", "saveSuccess", "saveError", "validationError", "updateSuccess", "updateError", "confirmDelete", "deleteSuccess", "deleteError", "close"], "hardcodedStrings": [], "hasTranslationHook": true, "translationKeyCount": 44, "timestamp": "2025-06-09T20:02:06.627Z"}, {"file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "hookUsage": [], "usedKeys": ["results.validationMessage", "results.assessments.severe_symptoms", "results.assessments.severe_late", "results.assessments.severe_early", "results.assessments.moderate_late", "results.assessments.normal", "title", "subtitle", "questions.intensity.title", "questions.intensity.options.mild", "questions.intensity.options.moderate", "questions.intensity.options.severe", "questions.onset.title", "questions.onset.options.recent", "questions.onset.options.later", "questions.symptoms.title", "questions.symptoms.options.fever", "questions.symptoms.options.vomiting", "questions.symptoms.options.dizziness", "questions.symptoms.options.bleeding", "questions.symptoms.options.nonMenstrual", "actions.assess", "results.title", "results.consultAdvice", "actions.reset", "actions.moreInfo"], "hardcodedStrings": [");\n  const [severeSymptoms, setSevereSymptoms] = useState<string[]>([]);\n  const [result, setResult] = useState<AssessmentResult | null>(null);\n  const [showResult, setShowResult] = useState(false);\n\n  // 使用统一翻译Hook\n  const { t } = useInteractiveToolTranslations(", ";\n\n    // 检查是否有严重症状\n    if (severeSymptoms.length > 0) {\n      advice = t(", ";\n    }\n    // 根据痛经强度和开始时间评估\n    else if (intensity === ", ">\n          {/* 痛经强度 */}\n          <div className=", ">{option.label}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* 痛经开始时间 */}\n          <div className=", ">{option.label}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* 严重症状 */}\n          <div>\n            <h3 className="], "hasTranslationHook": false, "translationKeyCount": 26, "timestamp": "2025-06-09T20:02:06.627Z"}, {"file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "hookUsage": ["useTranslations", "useSafeTranslations"], "usedKeys": ["messages.assessmentComplete", "messages.assessmentCompleteDesc", "messages.assessmentFailed", "messages.assessmentFailedDesc", "result.severity", "severity.${result.severity}", "severity.${result.type}", "result.summary", "result.recommendations", "priority.${recommendation.priority}", "result.timeframe", "result.actionSteps", "result.retakeAssessment", "messages.resultsSaved", "messages.resultsSavedDesc", "result.saveResults", "navigation.previous", "navigation.skip", "navigation.finish", "navigation.next", "title", "subtitle", "start.title", "start.description", "start.feature1", "start.feature2", "start.feature3", "start.feature4", "start.startButton", "start.disclaimer", "result.title", "result.yourScore", "progress.questionOf", "assessmentComplete", "assessmentCompleteDesc", "assessmentFailed", "assessmentFailedDesc", "resultsSaved", "resultsSavedDesc"], "hardcodedStrings": [");\n  const [selectedAnswers, setSelectedAnswers] = useState<Record<string, any>>({});\n\n  const {\n    currentSession,\n    currentQuestion,\n    currentQuestionIndex,\n    totalQuestions,\n    progress,\n    isComplete,\n    result,\n    isLoading,\n    error,\n    startAssessment,\n    answerQuestion,\n    goToPreviousQuestion,\n    goToNextQuestion,\n    completeAssessment,\n    resetAssessment\n  } = useSymptomAssessment();\n\n  // 监听result变化\n  useEffect(() => {\n    console.log(", ", {\n      currentQuestionIndex,\n      totalQuestions,\n      isLastQuestion: currentQuestionIndex >= totalQuestions - 1\n    });\n\n    if (currentQuestionIndex >= totalQuestions - 1) {\n      // 已经是最后一题，完成评估\n      console.log(", ")\n        );\n        // 强制重新渲染以显示结果\n        setTimeout(() => {\n          console.log(", "症状评估工具", "专业的症状分析工具，帮助您了解自己的健康状况", "开始评估", "这个评估工具将帮助您了解症状的严重程度并提供个性化建议", ">\n              {(() => {\n                try {\n                  // 直接使用翻译键而不是数组\n                  const features = [\n                    t(", "12个专业问题", "个性化建议", "科学评估", "即时结果", "开始评估", "此工具仅供参考，不能替代专业医疗建议", "评估结果", "您的得分", ">\n        {/* Progress Bar - 移动端优化 */}\n        <div className=", " }}\n            />\n          </div>\n        </div>\n\n        {/* Question - 移动端优化 */}\n        {currentQuestion && (\n          <div className=", ">\n                {currentQuestion.description}\n              </p>\n            )}\n\n            {/* Question Input - 移动端优化 */}\n            <div className=", "无痛", "轻微", "中等", "严重", "极重", "}</span>\n                    </div>\n                  </div>\n\n                  {/* 当前选择的值显示 - 与中医体质测试保持一致的样式 */}\n                  <div className=", "疼痛程度：", "无痛", "轻微", "中等", "严重", "极重", ";\n                            }\n                          })()})\n                        </span>\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* 疼痛程度说明 - 与中医体质测试保持一致的样式 */}\n                  <div className=", "疼痛程度参考", "无痛或极轻微不适", "轻微疼痛，不影响日常活动", "中等疼痛，影响部分活动", "严重疼痛，严重影响生活", "}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Navigation - 移动端优化 */}\n        <div className="], "hasTranslationHook": true, "translationKeyCount": 39, "timestamp": "2025-06-09T20:02:06.628Z"}, {"file": "app/[locale]/interactive-tools/shared/hooks/useAppTranslations.ts", "hookUsage": ["useTranslations"], "usedKeys": ["${toolName}.${key}", "${optionsKey}.${option}"], "hardcodedStrings": ["\n  };\n};\n\n/**\n * 专门用于交互工具的翻译Hook\n */\nexport const useInteractiveToolTranslations = (toolName?: string) => {\n  const namespace = toolName ? ", "\n  };\n};\n\n/**\n * 获取多语言选项的工具函数\n */\nexport const useTranslatedOptions = (namespace: string, optionsKey: string) => {\n  const { t } = useAppTranslations(namespace);\n  \n  return (options: string[]) => {\n    return options.map(option => ({\n      value: option,\n      label: t("], "hasTranslationHook": true, "translationKeyCount": 2, "timestamp": "2025-06-09T20:02:06.628Z"}, {"file": "app/[locale]/interactive-tools/shared/hooks/useConstitutionTest.ts", "hookUsage": [], "usedKeys": [], "hardcodedStrings": ["测试未完成，请回答所有问题", "计算结果时出错，请重试"], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.629Z"}, {"file": "app/[locale]/interactive-tools/shared/hooks/useNotifications.ts", "hookUsage": [], "usedKeys": [], "hardcodedStrings": [], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.629Z"}, {"file": "app/[locale]/interactive-tools/shared/hooks/usePainTracker.ts", "hookUsage": [], "usedKeys": ["a", "T"], "hardcodedStrings": [");\n      }\n    }\n  }, [entries, storageKey, isLoading]);\n\n  // Recalculate statistics when entries change\n  useEffect(() => {\n    setStatistics(calculateStatistics(entries));\n  }, [entries]);\n\n  const addEntry = useCallback(async (data: PainEntryFormData): Promise<{ success: boolean; errors?: ValidationError[] }> => {\n    try {\n      setError(null);\n      \n      // Validate the entry\n      const validationErrors = validatePainEntry(data);\n      if (validationErrors.length > 0) {\n        return { success: false, errors: validationErrors };\n      }\n\n      // Check for duplicate dates - 允许用户选择是否覆盖\n      const existingEntry = entries.find(entry => entry.date === data.date);\n      if (existingEntry) {\n        // 如果用户明确表示要覆盖，则删除旧记录\n        if ((data as any).overwrite === true) {\n          setEntries(prev => prev.filter(entry => entry.date !== data.date));\n        } else {\n          return {\n            success: false,\n            errors: [{\n              field: "], "hasTranslationHook": false, "translationKeyCount": 2, "timestamp": "2025-06-09T20:02:06.629Z"}, {"file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "hookUsage": [], "usedKeys": ["recommendations.emergencyMedical.title", "recommendations.emergencyMedical.description", "recommendations.emergencyMedical.timeframe", "recommendations.emergencyMedical.actionSteps", "recommendations.painManagement.title", "recommendations.painManagement.description", "recommendations.painManagement.timeframe", "recommendations.painManagement.actionSteps", "recommendations.lifestyleChanges.title", "recommendations.lifestyleChanges.description", "recommendations.lifestyleChanges.timeframe", "recommendations.lifestyleChanges.actionSteps", "recommendations.selfcarePractices.title", "recommendations.selfcarePractices.description", "recommendations.selfcarePractices.timeframe", "recommendations.selfcarePractices.actionSteps", "resultMessages.emergency", "resultMessages.emergencySummary", "resultMessages.severe", "resultMessages.severeSummary", "resultMessages.moderate", "resultMessages.moderate<PERSON><PERSON><PERSON><PERSON>", "resultMessages.mild", "resultMessages.mildSummary", "result.nextSteps.trackSymptoms", "result.nextSteps.tryRecommendations", "result.nextSteps.consultDoctor", "messages.assessmentFailed", "assessmentFailed"], "hardcodedStrings": ["建议立即就医", "您的症状可能需要专业医疗评估和治疗", "立即", "联系您的妇科医生", "如果疼痛剧烈，考虑急诊就医", "记录详细的症状日志", "建议立即就医", "您的症状可能需要专业医疗评估和治疗", "立即", "联系您的妇科医生", "如果疼痛剧烈，考虑急诊就医", "记录详细的症状日志", "疼痛管理策略", "多种方法可以帮助缓解经期疼痛", "立即可用", "使用热敷垫或热水袋", "尝试轻度运动如散步", "考虑非处方止痛药（按说明使用）", "疼痛管理策略", "多种方法可以帮助缓解经期疼痛", "立即可用", "使用热敷垫或热水袋", "尝试轻度运动如散步", "考虑非处方止痛药（按说明使用）", "生活方式调整", "长期的生活方式改变可以显著改善症状", "2-3个月见效", "保持规律的运动习惯", "确保充足的睡眠", "学习压力管理技巧", "保持均衡饮食", "生活方式调整", "长期的生活方式改变可以显著改善症状", "2-3个月见效", "保持规律的运动习惯", "确保充足的睡眠", "学习压力管理技巧", "保持均衡饮食", "自我护理实践", "日常的自我护理可以帮助您更好地管理症状", "持续进行", "练习深呼吸和冥想", "使用疼痛追踪器记录症状", "建立支持网络", "学习放松技巧", "自我护理实践", "日常的自我护理可以帮助您更好地管理症状", "持续进行", "练习深呼吸和冥想", "使用疼痛追踪器记录症状", "建立支持网络", "学习放松技巧", "您的症状较为严重，建议尽快咨询医疗专业人士。", "评估显示您可能需要专业医疗关注。", "您的症状比较严重，建议采取综合管理策略。", "您的症状需要积极的管理和可能的医疗干预。", "您有中等程度的症状，可以通过多种方法进行管理。", "您的症状是可以管理的，建议采用多种缓解策略。", "您的症状相对较轻，通过简单的自我护理就能很好地管理。", "您的症状较轻，可以通过生活方式调整来改善。", "使用疼痛追踪器记录症状", "尝试推荐的缓解方法", "如果症状持续或恶化，请咨询医生", "使用疼痛追踪器记录症状", "尝试推荐的缓解方法", "如果症状持续或恶化，请咨询医生", "评估完成时出现错误，请重试。"], "hasTranslationHook": false, "translationKeyCount": 29, "timestamp": "2025-06-09T20:02:06.630Z"}, {"file": "components/ArticleInteractions.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": [" \n}: ArticleInteractionsProps) {\n  const [likes, setLikes] = useState(0);\n  const [isLiked, setIsLiked] = useState(false);\n  const [isBookmarked, setIsBookmarked] = useState(false);\n  const [views, setViews] = useState(0);\n  const [showShareMenu, setShowShareMenu] = useState(false);\n  const [copySuccess, setCopySuccess] = useState(false);\n\n  // 初始化数据\n  useEffect(() => {\n    // 从 localStorage 获取数据\n    const storedLikes = localStorage.getItem(", ");\n    if (storedViews) {\n      setViews(parseInt(storedViews));\n    } else {\n      // 首次访问，增加浏览量\n      const newViews = Math.floor(Math.random() * 50) + 20; // 模拟初始浏览量\n      setViews(newViews);\n      localStorage.setItem(", ", newViews.toString());\n    }\n\n    // 记录本次访问\n    const currentViews = parseInt(localStorage.getItem(", ", newViews.toString());\n  }, [articleId]);\n\n  // 点赞功能\n  const handleLike = () => {\n    const newLikedState = !isLiked;\n    const newLikes = newLikedState ? likes + 1 : likes - 1;\n    \n    setIsLiked(newLikedState);\n    setLikes(newLikes);\n    \n    localStorage.setItem(", ", newLikes.toString());\n  };\n\n  // 收藏功能\n  const handleBookmark = () => {\n    const newBookmarkedState = !isBookmarked;\n    setIsBookmarked(newBookmarkedState);\n    \n    localStorage.setItem(", ", newBookmarkedState.toString());\n    \n    // 管理收藏列表\n    const bookmarks = JSON.parse(localStorage.getItem(", ", JSON.stringify(bookmarks));\n  };\n\n  // 复制链接\n  const handleCopyLink = async () => {\n    try {\n      await navigator.clipboard.writeText(window.location.href);\n      setCopySuccess(true);\n      setTimeout(() => setCopySuccess(false), 2000);\n    } catch (err) {\n      console.error(", ", err);\n    }\n  };\n\n  // 分享到社交媒体\n  const handleShare = (platform: string) => {\n    const url = encodeURIComponent(window.location.href);\n    const title = encodeURIComponent(articleTitle);\n    \n    const shareUrls = {\n      facebook: ", "点赞文章", "已点赞", "收藏", "已收藏", "分享", "阅读量", "点赞", "复制链接", "已复制", "分享到 Facebook", "分享到 Twitter", "分享到 LinkedIn", "分享到 WhatsApp", "分享到 Telegram", "}>\n      {/* 统计信息 */}\n      <div className=", " />\n            {likes} {text.likes}\n          </span>\n        </div>\n      </div>\n\n      {/* 操作按钮 */}\n      <div className=", ">\n          {/* 点赞按钮 */}\n          <button\n            onClick={handleLike}\n            className={", ">\n              {isLiked ? text.liked : text.like}\n            </span>\n          </button>\n\n          {/* 收藏按钮 */}\n          <button\n            onClick={handleBookmark}\n            className={", ">\n              {isBookmarked ? text.bookmarked : text.bookmark}\n            </span>\n          </button>\n        </div>\n\n        {/* 分享按钮 */}\n        <div className=", ">{text.share}</span>\n          </button>\n\n          {/* 分享菜单 */}\n          {showShareMenu && (\n            <div className=", "\n              >\n                ✈️ {text.shareToTelegram}\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 点击外部关闭分享菜单 */}\n      {showShareMenu && (\n        <div\n          className="], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.630Z"}, {"file": "components/BreathingExercise.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": ["吸气", "屏息", "呼气", "4-7-8 深呼吸练习", "通过调节神经系统自然缓解疼痛", "练习方法：", "吸气 4秒", "屏息 7秒", "呼气 8秒", "正在进行：${getCurrentPhase().name}", "🫁 开始引导练习", "✅ 一轮练习完成！", "再次练习", "停止练习", "科学效果：", "疼痛感知", "肌肉紧张", "放松感受", "💡 建议：找一个舒适的坐位或躺位，放松全身肌肉。初学者建议进行3-4个循环。"], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.630Z"}, {"file": "components/DownloadButton.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": ["查看文档"], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.630Z"}, {"file": "components/EmbeddedPainAssessment.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": ["请先选择痛经强度", "您的痛经程度较轻，可以尝试热敷、轻度运动等自然缓解方法。", "您的痛经程度中等，建议结合多种缓解方法，如有需要可考虑非处方止痛药。", "您的痛经程度较重，建议咨询医生获得专业评估和治疗建议。", "💡 痛经快速自测", "1分钟了解您的痛经程度，获得初步建议", "您的痛经强度如何？", "轻微（可以忍受，不影响日常活动）", "中度（影响部分活动，但能坚持）", "重度（完全影响日常活动，需要休息）", "获取建议", "详细评估", "评估结果", "重新测试", "完整评估", "⚠️ 此工具仅供参考，不能替代专业医疗建议"], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.631Z"}, {"file": "components/Footer.tsx", "hookUsage": ["useTranslations"], "usedKeys": ["disclaimer", "privacy", "terms", "contact_email", "copyright"], "hardcodedStrings": ["医疗免责声明", "文章PDF下载中心", "平时调理"], "hasTranslationHook": true, "translationKeyCount": 5, "timestamp": "2025-06-09T20:02:06.631Z"}, {"file": "components/Header.tsx", "hookUsage": ["useTranslations"], "usedKeys": ["home", "scenarioSolutions"], "hardcodedStrings": ["互动解决方案", "文章PDF下载中心", "平时调理", "痛经健康指南", ">\n        {/* 📱 移动端优化头部高度 */}\n        <div className=", ">\n          {/* 📱 移动端优化Logo */}\n          <div className=", ">\n                periodhub.health\n              </span>\n            </Link>\n          </div>\n\n          {/* 📱 移动端优化桌面导航 */}\n          <nav className=", "}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* 📱 移动端优化右侧控件 */}\n          <div className=", ">\n            <LanguageSwitcher />\n\n            {/* 📱 移动端优化菜单按钮 */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=", " />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* 📱 移动端优化导航菜单 */}\n        {isMenuOpen && (\n          <div className=", "中文", ">\n      {/* 📱 移动端优化语言切换按钮 */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=", "} />\n      </button>\n\n      {/* 📱 移动端优化下拉菜单 */}\n      {isOpen && (\n        <div className="], "hasTranslationHook": true, "translationKeyCount": 2, "timestamp": "2025-06-09T20:02:06.631Z"}, {"file": "components/ImagePlaceholder.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": [], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.632Z"}, {"file": "components/NSAIDContent.tsx", "hookUsage": [], "usedKeys": ["|", "\\n", "style"], "hardcodedStrings": [");\n\n      if (calculateButton) {\n        // 强制设置按钮样式\n        const btn = calculateButton as HTMLButtonElement;\n        btn.style.setProperty(", "场景1：开场 - 表现痛经的不适感", "很多女性每个月都会经历痛经，那种痉挛、疼痛的感觉让人非常不适。", "场景2：解释痛经原因 - 前列腺素", "月经期间，子宫内膜会释放一种叫做", "的物质。前列腺素会引起子宫肌肉剧烈收缩，导致疼痛。", "场景3：引出NSAIDs", "而非甾体抗炎药，简称NSAID，是缓解痛经的常用药物。它们能从源头减少前列腺素的产生。", "场景4：药物服用", "当您服下NSAID药片后，它会进入消化系统。", "场景5：吸收进入血液", "然后通过消化道被吸收到血液里，随着血液流向全身。", "场景6：分布到作用部位", "药物分子随着血液循环，最终抵达引起疼痛的部位——比如您的子宫周围。", "场景7：作用机制 - 抑制COX酶", "在这里，NSAID药物找到了产生前列腺素的关键", "——环氧合酶，并抑制了它的活性。", "场景8：减少前列腺素", "环氧合酶的工作被打断，前列腺素的合成量就大大降低了。", "场景9：疼痛缓解", "随着前列腺素减少，子宫收缩变得温和，疼痛感明显减轻。", "场景10：药物代谢", "完成任务后，NSAID药物会被肝脏代谢，最终通过肾脏排出体外。", "场景11：总结", "这就是NSAID缓解痛经的完整过程：从服用到吸收，从作用到代谢，科学而有效。", "场景 ${scene.id} / ${scenes.length}", "抱歉，视频加载失败。请检查您的网络连接或稍后再试。", "视频加载错误", "没有可播放的场景", "请检查数据配置。", "场景 0 / 0", ", {\n            id: video.id,\n            className: video.className,\n            src: video.src,\n            parentElement: video.parentElement\n          });\n        });\n      }\n\n\n\n    }, 100); // Small delay to ensure DOM is ready\n\n    return () => {\n      clearTimeout(timer);\n      // styleInterval会在组件卸载时自动清理\n    };\n  }, []);\n\n  return (\n    <>\n      <style jsx>{"], "hasTranslationHook": false, "translationKeyCount": 3, "timestamp": "2025-06-09T20:02:06.632Z"}, {"file": "components/NSAIDContentSimple.tsx", "hookUsage": [], "usedKeys": ["|", "\\n"], "hardcodedStrings": ["场景1：开场 - 表现痛经的不适感", "很多女性每个月都会经历痛经，那种痉挛、疼痛的感觉让人非常不适。", "场景2：解释痛经原因 - 前列腺素", "月经期间，子宫内膜会释放一种叫做", "的物质。前列腺素会引起子宫肌肉剧烈收缩，导致疼痛。", "场景3：引出NSAIDs", "而非甾体抗炎药，简称NSAID，是缓解痛经的常用药物。它们能从源头减少前列腺素的产生。", "场景 ${scene.id} / ${scenes.length}", "抱歉，视频加载失败。请检查您的网络连接或稍后再试。"], "hasTranslationHook": false, "translationKeyCount": 2, "timestamp": "2025-06-09T20:02:06.633Z"}, {"file": "components/NSAIDInteractive.tsx", "hookUsage": [], "usedKeys": ["link"], "hardcodedStrings": [], "hasTranslationHook": false, "translationKeyCount": 1, "timestamp": "2025-06-09T20:02:06.633Z"}, {"file": "components/NavigationTabs.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": ["📚 专业文章", "📥 PDF下载"], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.633Z"}, {"file": "components/ReadingProgress.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": [", updateProgress);\n    updateProgress(); // 初始化\n\n    return () => window.removeEventListener(", "返回顶部", "阅读进度", "\n    }\n  };\n\n  const text = t[locale];\n\n  return (\n    <>\n      {/* 阅读进度条 */}\n      <div className=", "\n          aria-valuenow={Math.round(progress)}\n          aria-valuemin={0}\n          aria-valuemax={100}\n          aria-label={text.readingProgress}\n        />\n      </div>\n\n      {/* 返回顶部按钮 */}\n      {showBackToTop && (\n        <button\n          onClick={scrollToTop}\n          className="], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.633Z"}, {"file": "components/SearchBox.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": [");\n  const [results, setResults] = useState<SearchResult[]>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  const searchRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // 搜索函数\n  const searchArticles = (searchQuery: string): SearchResult[] => {\n    if (!searchQuery.trim()) return [];\n\n    const query = searchQuery.toLowerCase();\n    const searchResults: SearchResult[] = [];\n\n    articles.forEach(article => {\n      const title = article.title.toLowerCase();\n      const summary = article.summary.toLowerCase();\n      const tags = article.tags.map(tag => tag.toLowerCase());\n      const content = article.content.toLowerCase();\n\n      // 标题匹配（最高优先级）\n      if (title.includes(query)) {\n        searchResults.push({\n          ...article,\n          matchType: ", ",\n          matchText: article.title\n        });\n        return;\n      }\n\n      // 摘要匹配\n      if (summary.includes(query)) {\n        const matchIndex = summary.indexOf(query);\n        const start = Math.max(0, matchIndex - 50);\n        const end = Math.min(summary.length, matchIndex + query.length + 50);\n        const matchText = ", ",\n          matchText\n        });\n        return;\n      }\n\n      // 标签匹配\n      const matchingTag = tags.find(tag => tag.includes(query));\n      if (matchingTag) {\n        searchResults.push({\n          ...article,\n          matchType: ", "\n        });\n        return;\n      }\n\n      // 内容匹配（最低优先级）\n      if (content.includes(query)) {\n        const matchIndex = content.indexOf(query);\n        const start = Math.max(0, matchIndex - 100);\n        const end = Math.min(content.length, matchIndex + query.length + 100);\n        const matchText = ", ",\n          matchText\n        });\n      }\n    });\n\n    // 按匹配类型排序：title > summary > tag > content\n    const priorityOrder = { title: 4, summary: 3, tag: 2, content: 1 };\n    return searchResults\n      .sort((a, b) => priorityOrder[b.matchType] - priorityOrder[a.matchType])\n      .slice(0, 8); // 限制结果数量\n  };\n\n  // 处理搜索\n  useEffect(() => {\n    if (query.trim()) {\n      const searchResults = searchArticles(query);\n      setResults(searchResults);\n      setIsOpen(true);\n      setSelectedIndex(-1);\n    } else {\n      setResults([]);\n      setIsOpen(false);\n    }\n  }, [query, articles]);\n\n  // 处理键盘导航\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (!isOpen || results.length === 0) return;\n\n    switch (e.key) {\n      case ", ":\n        setIsOpen(false);\n        setSelectedIndex(-1);\n        inputRef.current?.blur();\n        break;\n    }\n  };\n\n  // 点击外部关闭\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n        setSelectedIndex(-1);\n      }\n    };\n\n    document.addEventListener(", ", handleClickOutside);\n  }, []);\n\n  // 清除搜索\n  const clearSearch = () => {\n    setQuery(", ");\n    setResults([]);\n    setIsOpen(false);\n    setSelectedIndex(-1);\n    inputRef.current?.focus();\n  };\n\n  // 获取匹配类型的显示文本\n  const getMatchTypeLabel = (matchType: string) => {\n    const labels = {\n      title: locale === ", "标题", "摘要", "标签", "内容", "}>\n      {/* 搜索输入框 */}\n      <div className=", "搜索文章...", " />\n          </button>\n        )}\n      </div>\n\n      {/* 搜索结果下拉框 */}\n      {isOpen && results.length > 0 && (\n        <div className=", ">\n                      {result.reading_time}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </Link>\n          ))}\n        </div>\n      )}\n\n      {/* 无结果提示 */}\n      {isOpen && query && results.length === 0 && (\n        <div className=", "未找到相关文章"], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.634Z"}, {"file": "components/StructuredData.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": ["痛经", "月经疼痛", "经期疼痛", "月经期间或前后出现的疼痛症状", "妇科学", "痛经管理", "痛经的预防、治疗和管理方法", "女性健康", "女性生殖健康和月经健康相关话题", "妇科学"], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.634Z"}, {"file": "components/TableOfContents.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": [");\n  const [isOpen, setIsOpen] = useState(false);\n\n  useEffect(() => {\n    // 获取所有标题元素\n    const headingElements = document.querySelectorAll(", ");\n    const headingList: Heading[] = [];\n\n    headingElements.forEach((heading, index) => {\n      // 为没有 id 的标题添加 id\n      if (!heading.id) {\n        const text = heading.textContent || ", ",\n        level: parseInt(heading.tagName.charAt(1))\n      });\n    });\n\n    setHeadings(headingList);\n  }, []);\n\n  useEffect(() => {\n    // 监听滚动，高亮当前章节\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            setActiveId(entry.target.id);\n          }\n        });\n      },\n      {\n        rootMargin: ", ",\n        threshold: 0\n      }\n    );\n\n    headings.forEach(({ id }) => {\n      const element = document.getElementById(id);\n      if (element) {\n        observer.observe(element);\n      }\n    });\n\n    return () => observer.disconnect();\n  }, [headings]);\n\n  const scrollToHeading = (id: string) => {\n    const element = document.getElementById(id);\n    if (element) {\n      const offset = 80; // 考虑固定头部的高度\n      const elementPosition = element.offsetTop - offset;\n      \n      window.scrollTo({\n        top: elementPosition,\n        behavior: ", "\n      });\n    }\n    setIsOpen(false); // 移动端点击后关闭目录\n  };\n\n  const t = {\n    zh: {\n      tableOfContents: ", "切换目录显示", "}>\n      {/* 移动端可折叠标题 */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=", " />}\n      </button>\n\n      {/* 桌面端固定标题 */}\n      <div className=", " />\n          {text.tableOfContents}\n        </div>\n      </div>\n\n      {/* 目录内容 */}\n      <div className={"], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.634Z"}, {"file": "components/ToolsCollectionButton.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": ["访问完整工具集页面"], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.635Z"}, {"file": "components/UserSuccessStories.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": ["李小雅", "IT从业者，25岁", "李", "通过个性化评估发现我属于前列腺素过度分泌型痛经，按照平台建议调整饮食和运动，3个月后疼痛强度从8分降到3分，工作效率大幅提升！", "张婷婷", "大学生，20岁", "张", "青少年专区的内容太有用了！学会了热敷、瑜伽和呼吸法，现在考试期间来大姨妈也不怕了。还帮助室友一起改善，大家感情更好了。", "王芳", "职场妈妈，32岁", "王", "疼痛日志功能帮我发现了痛经与压力的关联性。配合医生治疗使用平台建议，现在基本告别了每月的痛苦，生活质量改善明显。", "用户成功案例", "已有超过10,000+女性在这里找到了属于自己的解决方案", "加入她们，开始您的康复之旅"], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.635Z"}, {"file": "components/advanced/AppProvider.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": [">\n        {children}\n        <ToastContainer />\n        <ModalManager />\n      </div>\n    </ErrorBoundary>\n  );\n};\n\n// 应用主题\nfunction applyTheme(theme: ", ");\n  root.classList.add(theme);\n  \n  // 更新meta标签\n  const metaTheme = document.querySelector(", ");\n  }\n}\n\n// 应用字体大小\nfunction applyFontSize(fontSize: ", ");\n  }\n}\n\n// 应用可访问性设置\nfunction applyAccessibilitySettings(accessibility: {\n  highContrast: boolean;\n  reducedMotion: boolean;\n  screenReader: boolean;\n}) {\n  const root = document.documentElement;\n  \n  // 高对比度\n  if (accessibility.highContrast) {\n    root.classList.add(", ");\n  }\n  \n  // 减少动画\n  if (accessibility.reducedMotion) {\n    root.classList.add(", ");\n  }\n  \n  // 屏幕阅读器优化\n  if (accessibility.screenReader) {\n    root.classList.add(", ");\n  }\n}\n\n// 页面级别的Provider\ninterface PageProviderProps {\n  children: ReactNode;\n  title?: string;\n  description?: string;\n  keywords?: string[];\n}\n\nexport const PageProvider: React.FC<PageProviderProps> = ({\n  children,\n  title,\n  description,\n  keywords = [],\n}) => {\n  useEffect(() => {\n    // 更新页面标题\n    if (title) {\n      document.title = ", ";\n    }\n\n    // 更新meta描述\n    if (description) {\n      const metaDescription = document.querySelector(", ", description);\n      }\n    }\n\n    // 更新meta关键词\n    if (keywords.length > 0) {\n      const metaKeywords = document.querySelector(", ">\n      {children}\n    </ErrorBoundary>\n  );\n};\n\n// 组件级别的Provider\ninterface ComponentProviderProps {\n  children: ReactNode;\n  name?: string;\n  fallback?: ReactNode;\n}\n\nexport const ComponentProvider: React.FC<ComponentProviderProps> = ({\n  children,\n  name,\n  fallback,\n}) => {\n  return (\n    <ErrorBoundary \n      level=", ", error, errorInfo);\n      }}\n    >\n      {children}\n    </ErrorBoundary>\n  );\n};\n\n// 性能监控Hook\nexport const usePagePerformance = (pageName: string) => {\n  useEffect(() => {\n    const startTime = performance.now();\n    \n    // 记录页面访问\n    performanceMonitor.recordCustomInteraction(", ", pageName);\n    \n    return () => {\n      const endTime = performance.now();\n      const duration = endTime - startTime;\n      \n      // 记录页面停留时间\n      performanceMonitor.recordCustomInteraction(", ", duration);\n    };\n  }, [pageName]);\n};\n\n// 用户行为追踪Hook\nexport const useUserTracking = () => {\n  const recordInteraction = (type: string, element: string, data?: any) => {\n    performanceMonitor.recordCustomInteraction(type as any, element);\n    \n    // 可以在这里添加更多的用户行为分析\n    console.log("], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.635Z"}, {"file": "components/advanced/DataVisualization.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": [">\n        {/* 网格线 */}\n        {showGrid && (\n          <g className=", "\n              />\n            ))}\n          </g>\n        )}\n\n        {/* 数据线 */}\n        <polyline\n          points={points}\n          fill=", "\n        />\n\n        {/* 数据点 */}\n        {data.map((point, index) => {\n          const x = (index / (data.length - 1)) * (width - 40) + 20;\n          const y = height - 20 - ((point.value - minValue) / (maxValue - minValue || 1)) * (height - 40);\n          \n          return (\n            <circle\n              key={index}\n              cx={x}\n              cy={y}\n              r=", "}</title>\n            </circle>\n          );\n        })}\n\n        {/* 标签 */}\n        {showLabels && (\n          <g className=", ">{minValue}</text>\n          </g>\n        )}\n      </svg>\n    </div>\n  );\n};\n\n// 柱状图组件\ninterface BarChartProps {\n  data: DataPoint[];\n  config?: ChartConfig;\n  className?: string;\n}\n\nexport const BarChart: React.FC<BarChartProps> = ({\n  data,\n  config = {},\n  className = ", ">\n        {data.map((item, index) => {\n          const barHeight = (item.value / maxValue) * (height - 40);\n          const x = 20 + index * (barWidth + barSpacing);\n          const y = height - 20 - barHeight;\n          const color = item.color || colors[index % colors.length];\n\n          return (\n            <g key={index}>\n              {/* 柱子 */}\n              <rect\n                x={x}\n                y={y}\n                width={barWidth}\n                height={barHeight}\n                fill={color}\n                className=", "}</title>\n              </rect>\n\n              {/* 标签 */}\n              {showL<PERSON><PERSON> && (\n                <text\n                  x={x + barWidth / 2}\n                  y={height - 5}\n                  textAnchor=", "\n                >\n                  {item.label}\n                </text>\n              )}\n\n              {/* 数值 */}\n              <text\n                x={x + barWidth / 2}\n                y={y - 5}\n                textAnchor=", "\n              >\n                {item.value}\n              </text>\n            </g>\n          );\n        })}\n      </svg>\n    </div>\n  );\n};\n\n// 饼图组件\ninterface PieChartProps {\n  data: DataPoint[];\n  config?: ChartConfig;\n  className?: string;\n}\n\nexport const PieChart: React.FC<PieChartProps> = ({\n  data,\n  config = {},\n  className = ", "}</title>\n            </path>\n          ))}\n        </svg>\n\n        {/* 图例 */}\n        {showLegend && (\n          <div className=", ">\n                  {slice.label} ({slice.percentage}%)\n                </span>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// 统计卡片组件\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  change?: number;\n  icon?: React.ReactNode;\n  color?: "], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.636Z"}, {"file": "components/advanced/ErrorBoundary.tsx", "hookUsage": [], "usedKeys": ["\nError ID: ${errorId}\nURL: ${window.location.href}\nTime: ${new Date().toISOString()}\n\nError Details:\n${error?.message}\n\nStack Trace:\n${error?.stack}\n\nComponent Stack:\n${errorInfo?.componentStack}\n\nPlease describe what you were doing when this error occurred:\n[Your description here]\n    "], "hardcodedStrings": [",\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    this.setState({\n      errorInfo,\n    });\n\n    // 记录错误到应用状态\n    useAppStore.getState().incrementErrorCount();\n\n    // 调用自定义错误处理\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // 发送错误报告（在生产环境中）\n    if (process.env.NODE_ENV === ", ", error, errorInfo);\n  }\n\n  private reportError = async (error: Error, errorInfo: ErrorInfo) => {\n    try {\n      // 这里可以发送到错误监控服务\n      const errorReport = {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        timestamp: new Date().toISOString(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n        errorId: this.state.errorId,\n        level: this.props.level || ", ",\n      };\n\n      // 发送到监控服务（示例）\n      // await fetch(", ", reportingError);\n    }\n  };\n\n  private handleRetry = () => {\n    if (this.retryCount < this.maxRetries) {\n      this.retryCount++;\n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null,\n        errorId: null,\n      });\n    } else {\n      // 达到最大重试次数，刷新页面\n      window.location.reload();\n    }\n  };\n\n  private handleGoHome = () => {\n    window.location.href = ", ";\n  };\n\n  private handleReportBug = () => {\n    const { error, errorInfo, errorId } = this.state;\n    const bugReport = {\n      errorId,\n      message: error?.message,\n      stack: error?.stack,\n      componentStack: errorInfo?.componentStack,\n      url: window.location.href,\n      timestamp: new Date().toISOString(),\n    };\n\n    // 打开邮件客户端或错误报告页面\n    const subject = encodeURIComponent(", ");\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // 如果提供了自定义fallback，使用它\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      const { error, errorInfo, errorId } = this.state;\n      const { level = ", ">\n              {/* 错误图标和标题 */}\n              <div className=", "严重错误", "出现错误", "应用遇到了严重问题", "这个组件遇到了问题", ">\n                  我们很抱歉给您带来不便。错误已被记录，我们会尽快修复。\n                </p>\n                \n                {errorId && (\n                  <p className=", ">\n                    错误ID: {errorId}\n                  </p>\n                )}\n\n                {/* 详细错误信息（开发模式或显式启用） */}\n                {(showDetails || process.env.NODE_ENV === ", ">\n                      查看技术详情\n                    </summary>\n                    <div className=", ">\n                        <strong>错误信息:</strong>\n                        <div className=", ">\n                          <strong>堆栈跟踪:</strong>\n                          <pre className=", ">\n                            {error.stack}\n                          </pre>\n                        </div>\n                      )}\n                      \n                      {errorInfo?.componentStack && (\n                        <div>\n                          <strong>组件堆栈:</strong>\n                          <pre className=", ">\n                            {errorInfo.componentStack}\n                          </pre>\n                        </div>\n                      )}\n                    </div>\n                  </details>\n                )}\n              </div>\n\n              {/* 操作按钮 */}\n              <div className=", " />\n                    <span>重试 ({this.maxRetries - this.retryCount} 次剩余)</span>\n                  </button>\n                ) : (\n                  <button\n                    onClick={() => window.location.reload()}\n                    className=", " />\n                    <span>刷新页面</span>\n                  </button>\n                )}\n\n                <div className=", " />\n                    <span>返回首页</span>\n                  </button>\n\n                  <button\n                    onClick={this.handleReportBug}\n                    className=", " />\n                    <span>报告问题</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// 高阶组件包装器\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  errorBoundaryProps?: Omit<Props, ", ";\n  \n  return WrappedComponent;\n}\n\n// Hook版本\nexport function useErrorHandler() {\n  const incrementErrorCount = useAppStore(state => state.incrementErrorCount);\n\n  return (error: Error, errorInfo?: ErrorInfo) => {\n    incrementErrorCount();\n    console.error(", ", error, errorInfo);\n    \n    // 可以在这里添加更多错误处理逻辑\n    if (process.env.NODE_ENV === "], "hasTranslationHook": false, "translationKeyCount": 1, "timestamp": "2025-06-09T20:02:06.636Z"}, {"file": "components/advanced/FormSystem.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": [";\n\n// 验证规则\nexport interface ValidationRule {\n  required?: boolean;\n  minLength?: number;\n  maxLength?: number;\n  min?: number;\n  max?: number;\n  pattern?: RegExp;\n  custom?: (value: any) => string | null;\n}\n\n// 字段配置\nexport interface FieldConfig {\n  name: string;\n  label: string;\n  type: FieldType;\n  placeholder?: string;\n  options?: { label: string; value: string | number }[];\n  validation?: ValidationRule;\n  disabled?: boolean;\n  className?: string;\n  description?: string;\n}\n\n// 表单状态\nexport interface FormState {\n  values: Record<string, any>;\n  errors: Record<string, string>;\n  touched: Record<string, boolean>;\n  isSubmitting: boolean;\n  isValid: boolean;\n}\n\n// 表单Hook\nexport const useForm = (initialValues: Record<string, any> = {}) => {\n  const [state, setState] = useState<FormState>({\n    values: initialValues,\n    errors: {},\n    touched: {},\n    isSubmitting: false,\n    isValid: true,\n  });\n\n  const validateField = useCallback((name: string, value: any, rules?: ValidationRule): string | null => {\n    if (!rules) return null;\n\n    if (rules.required && (!value || value.toString().trim() === ", "此字段为必填项", "最少需要 ${rules.minLength} 个字符", "最多允许 ${rules.maxLength} 个字符", "最小值为 ${rules.min}", "最大值为 ${rules.max}", "格式不正确", "请选择...", "选择文件", ">\n        {renderInput()}\n        \n        {/* 状态图标 */}\n        {![", " />}\n          </div>\n        )}\n      </div>\n\n      {/* 错误信息 */}\n      {hasError && (\n        <div className=", " />\n          <span>{error}</span>\n        </div>\n      )}\n\n      {/* 描述信息 */}\n      {config.description && (\n        <p className="], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.636Z"}, {"file": "components/advanced/ModalSystem.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": [";\n\n// 模态框配置\nexport interface ModalConfig {\n  type?: ModalType;\n  title?: string;\n  content?: React.ReactNode;\n  size?: ", ";\n  closable?: boolean;\n  maskClosable?: boolean;\n  showFooter?: boolean;\n  confirmText?: string;\n  cancelText?: string;\n  onConfirm?: () => void | Promise<void>;\n  onCancel?: () => void;\n  className?: string;\n  zIndex?: number;\n}\n\n// 确认对话框配置\nexport interface ConfirmConfig {\n  title?: string;\n  content: React.ReactNode;\n  confirmText?: string;\n  cancelText?: string;\n  type?: ", ";\n  onConfirm?: () => void | Promise<void>;\n  onCancel?: () => void;\n}\n\n// 模态框组件\ninterface ModalProps extends ModalConfig {\n  isOpen: boolean;\n  onClose: () => void;\n  children?: React.ReactNode;\n}\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  type = ", "确认", "取消", "}\n      style={{ zIndex }}\n      onClick={handleMaskClick}\n    >\n      {/* 背景遮罩 */}\n      <div\n        className={", "}\n      />\n\n      {/* 模态框内容 */}\n      <div\n        className={", "}\n      >\n        {/* 头部 */}\n        {(title || closable) && (\n          <div className=", " />\n              </button>\n            )}\n          </div>\n        )}\n\n        {/* 内容 */}\n        <div className=", ">\n          {content || children}\n        </div>\n\n        {/* 底部 */}\n        {showFooter && (\n          <div className=", "\n            >\n              {confirmText}\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n\n  return createPortal(modalContent, document.body);\n};\n\n// 模态框管理器\nexport const ModalManager: React.FC = () => {\n  const modal = useAppStore(state => state.ui.modal);\n  const closeModal = useAppStore(state => state.closeModal);\n\n  return (\n    <Modal\n      isOpen={modal.isOpen}\n      onClose={closeModal}\n      {...(modal.data as ModalConfig)}\n    />\n  );\n};\n\n// 模态框Hook\nexport const useModal = () => {\n  const openModal = useAppStore(state => state.openModal);\n  const closeModal = useAppStore(state => state.closeModal);\n\n  const modal = {\n    open: (config: ModalConfig) => {\n      openModal(", "确认操作", "确认", "取消", "提示", "确定", ", modalConfig);\n      });\n    },\n\n    // 便捷方法\n    success: (content: React.ReactNode, title?: string) => {\n      return modal.alert(\n        <div className=", "成功", "错误", "警告"], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.636Z"}, {"file": "components/advanced/SearchSystem.tsx", "hookUsage": [], "usedKeys": [], "hardcodedStrings": [";\n  category?: string;\n  tags?: string[];\n  url: string;\n  score?: number;\n  lastModified?: string;\n}\n\n// 搜索过滤器\nexport interface SearchFilters {\n  type?: string[];\n  category?: string[];\n  tags?: string[];\n  dateRange?: {\n    start: string;\n    end: string;\n  };\n}\n\n// 排序选项\nexport type SortOption = ", ";\n\n// 搜索配置\nexport interface SearchConfig {\n  placeholder?: string;\n  showFilters?: boolean;\n  showSort?: boolean;\n  showHistory?: boolean;\n  maxResults?: number;\n  debounceMs?: number;\n}\n\n// 搜索Hook\nexport const useSearch = (\n  searchFunction: (query: string, filters: SearchFilters) => Promise<SearchResult[]>,\n  config: SearchConfig = {}\n) => {\n  const [query, setQuery] = useState(", ");\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [searchHistory, setSearchHistory] = useState<string[]>([]);\n\n  const { debounceMs = 300 } = config;\n\n  // 防抖搜索\n  const debouncedSearch = useCallback(\n    debounce(async (searchQuery: string, searchFilters: SearchFilters) => {\n      if (!searchQuery.trim()) {\n        setResults([]);\n        return;\n      }\n\n      setIsLoading(true);\n      setError(null);\n\n      try {\n        const searchResults = await searchFunction(searchQuery, searchFilters);\n        setResults(searchResults);\n        \n        // 添加到搜索历史\n        if (searchQuery.trim() && !searchHistory.includes(searchQuery)) {\n          setSearchHistory(prev => [searchQuery, ...prev.slice(0, 9)]); // 保留最近10条\n        }\n      } catch (err) {\n        setError(", ", err);\n      } finally {\n        setIsLoading(false);\n      }\n    }, debounceMs),\n    [searchFunction, debounceMs, searchHistory]\n  );\n\n  // 执行搜索\n  useEffect(() => {\n    debouncedSearch(query, filters);\n  }, [query, filters, debouncedSearch]);\n\n  // 排序结果\n  const sortedResults = useMemo(() => {\n    const sorted = [...results].sort((a, b) => {\n      let comparison = 0;\n\n      switch (sortBy) {\n        case ", ");\n    setResults([]);\n    setError(null);\n  }, []);\n\n  const clearHistory = useCallback(() => {\n    setSearchHistory([]);\n  }, []);\n\n  return {\n    query,\n    setQuery,\n    results: sortedResults,\n    filters,\n    setFilters,\n    sortBy,\n    setSortBy,\n    sortDirection,\n    setSortDirection,\n    isLoading,\n    error,\n    searchHistory,\n    clearSearch,\n    clearHistory,\n  };\n};\n\n// 防抖函数\nfunction debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// 搜索框组件\ninterface SearchBoxProps {\n  value: string;\n  onChange: (value: string) => void;\n  onClear: () => void;\n  placeholder?: string;\n  isLoading?: boolean;\n  className?: string;\n}\n\nexport const SearchBox: React.FC<SearchBoxProps> = ({\n  value,\n  onChange,\n  onClear,\n  placeholder = ", "></div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// 搜索结果组件\ninterface SearchResultsProps {\n  results: SearchResult[];\n  isLoading: boolean;\n  error: string | null;\n  onResultClick?: (result: SearchResult) => void;\n  className?: string;\n}\n\nexport const SearchResults: React.FC<SearchResultsProps> = ({\n  results,\n  isLoading,\n  error,\n  onResultClick,\n  className = ", "文章", "工具", "疗法", "指南", "内容", ">没有找到相关结果</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className={", ">\n                {result.category && (\n                  <span>分类: {result.category}</span>\n                )}\n                {result.lastModified && (\n                  <span className=", " />\n                    <span>{new Date(result.lastModified).toLocaleDateString()}</span>\n                  </span>\n                )}\n                {result.score && (\n                  <span>相关度: {Math.round(result.score * 100)}%</span>\n                )}\n              </div>\n              \n              {result.tags && result.tags.length > 0 && (\n                <div className=", ">\n                      +{result.tags.length - 3}\n                    </span>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\n// 搜索历史组件\ninterface SearchHistoryProps {\n  history: string[];\n  onSelect: (query: string) => void;\n  onClear: () => void;\n  className?: string;\n}\n\nexport const SearchHistory: React.FC<SearchHistoryProps> = ({\n  history,\n  onSelect,\n  onClear,\n  className = ", " />\n          <span>搜索历史</span>\n        </h4>\n        <button\n          onClick={onClear}\n          className=", "\n        >\n          清除\n        </button>\n      </div>\n      \n      <div className="], "hasTranslationHook": false, "translationKeyCount": 0, "timestamp": "2025-06-09T20:02:06.637Z"}, {"file": "components/advanced/ToastSystem.tsx", "hookUsage": [], "usedKeys": ["style"], "hardcodedStrings": [";\n\n// Toast配置\nexport interface ToastConfig {\n  type: ToastType;\n  message: string;\n  title?: string;\n  duration?: number; // 0 表示不自动关闭\n  action?: {\n    label: string;\n    onClick: () => void;\n  };\n  closable?: boolean;\n}\n\n// 单个Toast组件\ninterface ToastProps {\n  id: string;\n  type: ToastType;\n  message: string;\n  title?: string;\n  duration?: number;\n  action?: ToastConfig[", "];\n  closable?: boolean;\n  onClose: (id: string) => void;\n}\n\nconst Toast: React.FC<ToastProps> = ({\n  id,\n  type,\n  message,\n  title,\n  duration = 5000,\n  action,\n  closable = true,\n  onClose,\n}) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isLeaving, setIsLeaving] = useState(false);\n\n  useEffect(() => {\n    // 入场动画\n    const timer = setTimeout(() => setIsVisible(true), 10);\n    return () => clearTimeout(timer);\n  }, []);\n\n  useEffect(() => {\n    // 自动关闭\n    if (duration > 0) {\n      const timer = setTimeout(() => {\n        handleClose();\n      }, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [duration]);\n\n  const handleClose = () => {\n    setIsLeaving(true);\n    setTimeout(() => {\n      onClose(id);\n    }, 300); // 等待退场动画完成\n  };\n\n  const getIcon = () => {\n    switch (type) {\n      case ", ">\n        {/* 图标 */}\n        <div className=", ">\n          {getIcon()}\n        </div>\n\n        {/* 内容 */}\n        <div className=", "}>\n            {message}\n          </p>\n\n          {/* 操作按钮 */}\n          {action && (\n            <button\n              onClick={action.onClick}\n              className={", "}\n            >\n              {action.label}\n            </button>\n          )}\n        </div>\n\n        {/* 关闭按钮 */}\n        {closable && (\n          <button\n            onClick={handleClose}\n            className={", " />\n          </button>\n        )}\n      </div>\n\n      {/* 进度条（如果有持续时间） */}\n      {duration > 0 && (\n        <div className=", ",\n            }}\n          />\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Toast容器组件\nexport const ToastContainer: React.FC = () => {\n  const [mounted, setMounted] = useState(false);\n  const toasts = useToasts();\n  const removeToast = useAppStore(state => state.removeToast);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) return null;\n\n  const container = (\n    <div className=", ",\n        message,\n        duration: 0, // 错误消息默认不自动关闭\n        ...options,\n      });\n    },\n\n    warning: (message: string, options?: Partial<ToastConfig>) => {\n      return addToast({\n        type: ", ",\n        message,\n        ...options,\n      });\n    },\n\n    custom: (config: ToastConfig) => {\n      return addToast(config);\n    },\n\n    dismiss: (id: string) => {\n      removeToast(id);\n    },\n\n    dismissAll: () => {\n      clearToasts();\n    },\n  };\n\n  return toast;\n};\n\n// 添加CSS动画\nconst toastStyles = ", ";\n\n// 注入样式\nif (typeof document !== "], "hasTranslationHook": false, "translationKeyCount": 1, "timestamp": "2025-06-09T20:02:06.637Z"}], "issues": [{"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "messages.testComplete", "description": "Translation key \"messages.testComplete\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "messages.testCompleteDesc", "description": "Translation key \"messages.testCompleteDesc\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "messages.testFailed", "description": "Translation key \"messages.testFailed\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "messages.testFailedDesc", "description": "Translation key \"messages.testFailedDesc\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "title", "description": "Translation key \"title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "subtitle", "description": "Translation key \"subtitle\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "features.quick.title", "description": "Translation key \"features.quick.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "features.quick.description", "description": "Translation key \"features.quick.description\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "features.professional.title", "description": "Translation key \"features.professional.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "features.professional.description", "description": "Translation key \"features.professional.description\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "features.personalized.title", "description": "Translation key \"features.personalized.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "features.personalized.description", "description": "Translation key \"features.personalized.description\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "features.practical.title", "description": "Translation key \"features.practical.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "features.practical.description", "description": "Translation key \"features.practical.description\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "instructions.title", "description": "Translation key \"instructions.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "instructions.item1", "description": "Translation key \"instructions.item1\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "instructions.item2", "description": "Translation key \"instructions.item2\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "instructions.item3", "description": "Translation key \"instructions.item3\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "instructions.item4", "description": "Translation key \"instructions.item4\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "navigation.startTest", "description": "Translation key \"navigation.startTest\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "result.title", "description": "Translation key \"result.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "result.subtitle", "description": "Translation key \"result.subtitle\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "result.match", "description": "Translation key \"result.match\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "result.constitutionFeatures", "description": "Translation key \"result.constitutionFeatures\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "result.commonSymptoms", "description": "Translation key \"result.commonSymptoms\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "result.menstrualFeatures", "description": "Translation key \"result.menstrualFeatures\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.acupoints.title", "description": "Translation key \"recommendations.acupoints.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.acupoints.primaryAcupoints", "description": "Translation key \"recommendations.acupoints.primaryAcupoints\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.acupoints.location", "description": "Translation key \"recommendations.acupoints.location\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.acupoints.function", "description": "Translation key \"recommendations.acupoints.function\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.acupoints.method", "description": "Translation key \"recommendations.acupoints.method\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.acupoints.guidelines", "description": "Translation key \"recommendations.acupoints.guidelines\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.acupoints.technique", "description": "Translation key \"recommendations.acupoints.technique\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.acupoints.frequency", "description": "Translation key \"recommendations.acupoints.frequency\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.acupoints.duration", "description": "Translation key \"recommendations.acupoints.duration\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.dietary.title", "description": "Translation key \"recommendations.dietary.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.dietary.beneficialFoods", "description": "Translation key \"recommendations.dietary.beneficialFoods\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.dietary.foodsToAvoid", "description": "Translation key \"recommendations.dietary.foodsToAvoid\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.dietary.dietaryPrinciples", "description": "Translation key \"recommendations.dietary.dietaryPrinciples\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.lifestyle.title", "description": "Translation key \"recommendations.lifestyle.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.lifestyle.description", "description": "Translation key \"recommendations.lifestyle.description\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.lifestyle.reminder", "description": "Translation key \"recommendations.lifestyle.reminder\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.lifestyle.reminderText", "description": "Translation key \"recommendations.lifestyle.reminderText\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.menstrualPain.title", "description": "Translation key \"recommendations.menstrualPain.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.menstrualPain.acupointTherapy", "description": "Translation key \"recommendations.menstrualPain.acupointTherapy\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "recommendations.menstrualPain.lifestyleAdjustments", "description": "Translation key \"recommendations.menstrualPain.lifestyleAdjustments\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "emergencyKit.title", "description": "Translation key \"emergencyKit.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "navigation.retakeTest", "description": "Translation key \"navigation.retakeTest\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "painScale.levels.none", "description": "Translation key \"painScale.levels.none\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "painScale.levels.mild", "description": "Translation key \"painScale.levels.mild\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "painScale.levels.moderate", "description": "Translation key \"painScale.levels.moderate\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "painScale.levels.severe", "description": "Translation key \"painScale.levels.severe\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "painScale.levels.extreme", "description": "Translation key \"painScale.levels.extreme\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "painScale.title", "description": "Translation key \"painScale.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "painScale.reference", "description": "Translation key \"painScale.reference\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "painScale.descriptions.0-2", "description": "Translation key \"painScale.descriptions.0-2\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "painScale.descriptions.3-4", "description": "Translation key \"painScale.descriptions.3-4\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "painScale.descriptions.5-7", "description": "Translation key \"painScale.descriptions.5-7\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "painScale.descriptions.8-10", "description": "Translation key \"painScale.descriptions.8-10\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "navigation.previous", "description": "Translation key \"navigation.previous\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "navigation.completeTest", "description": "Translation key \"navigation.completeTest\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "navigation.next", "description": "Translation key \"navigation.next\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "testComplete", "description": "Translation key \"testComplete\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "testCompleteDesc", "description": "Translation key \"testCompleteDesc\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "testFailed", "description": "Translation key \"testFailed\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "key": "testFailedDesc", "description": "Translation key \"testFailedDesc\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", "strings": [");\n  const [selectedAnswers, setSelectedAnswers] = useState<Record<string, string>>({});\n\n  const {\n    currentSession,\n    currentQuestion,\n    currentQuestionIndex,\n    totalQuestions,\n    progress,\n    isComplete,\n    result,\n    isLoading,\n    error,\n    startTest,\n    answerQuestion,\n    goToPreviousQuestion,\n    goToNextQuestion,\n    completeTest,\n    resetTest\n  } = useConstitutionTest();\n\n  const {\n    notifications,\n    removeNotification,\n    addSuccessNotification,\n    addErrorNotification\n  } = useNotifications();\n\n  const handleStartTest = () => {\n    startTest(locale);\n    setSelectedAnswers({});\n  };\n\n  // 处理单选答案\n  const handleAnswerSelect = (questionId: string, value: string | number) => {\n    const stringValue = String(value);\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: stringValue\n    }));\n\n    const answer: ConstitutionAnswer = {\n      questionId,\n      selectedValues: [stringValue],\n      timestamp: new Date().toISOString()\n    };\n\n    answerQuestion(answer);\n  };\n\n  // 处理多选答案\n  const handleMultipleAnswerSelect = (questionId: string, value: string) => {\n    const currentValues = Array.isArray(selectedAnswers[questionId])\n      ? selectedAnswers[questionId] as string[]\n      : selectedAnswers[questionId]\n        ? [selectedAnswers[questionId] as string]\n        : [];\n\n    let newValues: string[];\n\n    // 处理", ");\n\n    if (isNoneOption) {\n      // 如果选择", "];\n    } else {\n      // 如果选择其他选项，先移除", ");\n      newValues = filteredValues.includes(value)\n        ? filteredValues.filter(v => v !== value)\n        : [...filteredValues, value];\n    }\n\n    setSelectedAnswers(prev => ({\n      ...prev,\n      [questionId]: newValues\n    }));\n\n    const answer: ConstitutionAnswer = {\n      questionId,\n      selectedValues: newValues,\n      timestamp: new Date().toISOString()\n    };\n\n    answerQuestion(answer);\n  };\n\n  const handleNext = () => {\n    if (currentQuestionIndex >= totalQuestions - 1) {\n      // 完成测试\n      const testResult = completeTest();\n      if (testResult) {\n        addSuccessNotification(\n          t(", ")\n        );\n      }\n    } else {\n      goToNextQuestion();\n    }\n  };\n\n  const handlePrevious = () => {\n    goToPreviousQuestion();\n  };\n\n  const getCurrentAnswer = () => {\n    return currentQuestion ? selectedAnswers[currentQuestion.id] : undefined;\n  };\n\n  const canProceed = () => {\n    if (!currentQuestion) return false;\n\n    const answer = getCurrentAnswer();\n\n    // 对于多选题，检查是否有选择（可以为空数组，因为有些多选题不是必填的）\n    if (currentQuestion.type === ", ") {\n      return true; // 多选题允许不选择任何选项\n    }\n\n    // 对于单选题和滑块题，必须有选择\n    return answer !== undefined && answer !== null && answer !== ", ";\n  };\n\n  // 检查是否有痛经症状\n  const hasMenstrualPainSymptoms = (answers: ConstitutionAnswer[]): boolean => {\n    return answers.some(answer =>\n      answer.questionId === ", ")\n    );\n  };\n\n  // 获取痛经穴位建议\n  const getMenstrualPainAcupoints = (constitutionType: ConstitutionType, locale: string): any[] => {\n    const localeData = (menstrualPainAcupoints as any)[locale] || menstrualPainAcupoints.zh;\n    return localeData[constitutionType] || [];\n  };\n\n  // 获取痛经生活方式建议\n  const getMenstrualPainLifestyleTips = (constitutionType: ConstitutionType, locale: string): any[] => {\n    const localeData = (menstrualPainLifestyleTips as any)[locale] || menstrualPainLifestyleTips.zh;\n    return localeData[constitutionType] || [];\n  };\n\n  // 如果没有开始测试，显示介绍页面\n  if (!currentSession) {\n    return (\n      <div className=", ">\n        <NotificationContainer \n          notifications={notifications}\n          onRemove={removeNotification}\n        />\n        \n        {/* 介绍页面 - 紫色主题 */}\n        <div className=", ")}\n          </p>\n        </div>\n\n        {/* 测试特点 - 紫色主题卡片 */}\n        <div className=", ")}\n            </p>\n          </div>\n        </div>\n\n        {/* 测试说明 - 紫色主题 */}\n        <div className=", ")}\n            </li>\n          </ul>\n        </div>\n\n        {/* 开始按钮 - 紫色渐变 */}\n        <div className=", ")}</span>\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // 如果测试完成，显示结果\n  if (result) {\n    const typeInfo = constitutionTypeInfo[locale]?.[result.primaryType] || constitutionTypeInfo.zh[result.primaryType];\n    \n    return (\n      <div className=", ">\n        <NotificationContainer \n          notifications={notifications}\n          onRemove={removeNotification}\n        />\n        \n        {/* 结果标题 */}\n        <div className=", ")}\n          </p>\n        </div>\n\n        {/* 体质类型结果 */}\n        <div className=", ">{result.confidence}%</span>\n            </div>\n          </div>\n\n          {/* 体质特征 */}\n          <div className=", ">• {feature}</li>\n                ))}\n              </ul>\n            </div>\n          </div>\n        </div>\n\n        {/* 个性化建议 */}\n        <div className=", ">\n          {/* 穴位建议 */}\n          <div className=", ")}</strong>\n                {result.recommendations.acupoints.duration}\n              </p>\n            </div>\n          </div>\n\n          {/* 饮食建议 */}\n          <div className=", ">• {principle}</li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        </div>\n\n        {/* 场景化生活建议 */}\n        <div className=", ")}\n            </p>\n          </div>\n        </div>\n\n        {/* 痛经专项建议 */}\n        {hasMenstrualPainSymptoms(currentSession?.answers || []) && (\n          <div className=", ">\n              {/* 基于体质的痛经建议 */}\n              <div className=", ">{point.description}</p>\n                  </div>\n                ))}\n              </div>\n\n              {/* 生活方式建议 */}\n              <div className=", "></span>\n                      {tip}\n                    </li>\n                  ))}\n                </ul>\n              </div>\n            </div>\n          </div>\n        )}\n\n        {/* 个性化应急包推荐 */}\n        {hasMenstrualPainSymptoms(currentSession?.answers || []) && (\n          <div className=", "根据您的体质特点，为您推荐专属的应急包物品清单。提前准备，让经期更从容。", "必需", "推荐", "可选", "📦 打包建议：", " 优先携带", "物品，根据外出时间和场景选择", "和", "物品。建议准备一个专用的小包，方便随时取用。", "}\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* 相关文章推荐 */}\n        <div className=", "为您推荐的健康文章", "阅读全文", " />\n                </a>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {/* 社交沟通模板 */}\n        {hasMenstrualPainSymptoms(currentSession?.answers || []) && (\n          <div className=", "沟通模板助手", "经期不适时，与身边的人沟通很重要。这些模板可以帮助你更好地表达需求和寻求理解。", "亲密", "随意", "正式", "\n                        </p>\n\n                        <button\n                          onClick={() => {\n                            navigator.clipboard.writeText(template.content);\n                            // 可以添加复制成功的提示\n                          }}\n                          className=", "复制文本", "💡 使用提示：", " 这些模板仅供参考，请根据你的实际情况和关系亲密度进行调整。真诚的沟通是建立理解的关键。", "}\n              </p>\n            </div>\n          </div>\n        )}\n\n        {/* 重新测试按钮 */}\n        <div className=", ")}\n          </button>\n        </div>\n      </div>\n    );\n  }\n\n  // 显示问题\n  return (\n    <div className=", ">\n      <NotificationContainer \n        notifications={notifications}\n        onRemove={removeNotification}\n      />\n      \n      {isLoading && <LoadingSpinner />}\n      \n      {/* 进度条 - 紫色主题 */}\n      <div className=", "第 ${currentQuestionIndex + 1} 题，共 ${totalQuestions} 题", "完成", ">\n          {/* 问题标题 - 紫色主题 */}\n          <div className=", ">\n                {currentQuestion.description}\n              </p>\n            )}\n          </div>\n\n          {/* 选项 */}\n          <div className=", " ? (\n              // 滑块类型问题\n              <div className=", ")}</span>\n                  </div>\n                </div>\n\n                {/* 当前选择的值显示 - 增强紫色主题 */}\n                <div className=", ">\n                        ({currentQuestion.options.find(opt => opt.value == (selectedAnswers[currentQuestion.id] || 0))?.label})\n                      </span>\n                    </span>\n                  </div>\n                </div>\n\n                {/* 疼痛程度说明 - 紫色主题 */}\n                <div className=", " ? (\n              // 多选问题 - 紫色主题\n              <div className=", ">{option.label}</span>\n                      </div>\n                    </label>\n                  );\n                })}\n              </div>\n            ) : (\n              // 普通单选问题 - 紫色主题\n              <div className=", ">{option.label}</span>\n                    </div>\n                  </label>\n                ))}\n              </div>\n            )}\n          </div>\n\n          {/* 导航按钮 - 紫色主题 */}\n          <div className="], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "messages.saveSuccess", "description": "Translation key \"messages.saveSuccess\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "form.save", "description": "Translation key \"form.save\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "messages.saveError", "description": "Translation key \"messages.saveError\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "messages.validationError", "description": "Translation key \"messages.validationError\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "messages.updateSuccess", "description": "Translation key \"messages.updateSuccess\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "messages.updateError", "description": "Translation key \"messages.updateError\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "messages.confirmDelete", "description": "Translation key \"messages.confirmDelete\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "messages.deleteSuccess", "description": "Translation key \"messages.deleteSuccess\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "entries.delete", "description": "Translation key \"entries.delete\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "messages.deleteError", "description": "Translation key \"messages.deleteError\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "navigation.overview", "description": "Translation key \"navigation.overview\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "navigation.addEntry", "description": "Translation key \"navigation.addEntry\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "navigation.viewEntries", "description": "Translation key \"navigation.viewEntries\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "navigation.statistics", "description": "Translation key \"navigation.statistics\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "navigation.export", "description": "Translation key \"navigation.export\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "statistics.overview", "description": "Translation key \"statistics.overview\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "entries.noEntries", "description": "Translation key \"entries.noEntries\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "entries.noEntriesDescription", "description": "Translation key \"entries.noEntriesDescription\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "entries.add<PERSON><PERSON><PERSON>", "description": "Translation key \"entries.addFirst\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "statistics.totalEntries", "description": "Translation key \"statistics.totalEntries\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "statistics.averagePain", "description": "Translation key \"statistics.averagePain\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "statistics.trendDirection", "description": "Translation key \"statistics.trendDirection\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "statistics.${statistics.trendDirection}", "description": "Translation key \"statistics.${statistics.trendDirection}\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "form.editTitle", "description": "Translation key \"form.editTitle\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "form.title", "description": "Translation key \"form.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "entries.title", "description": "Translation key \"entries.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "entries.painIntensity", "description": "Translation key \"entries.painIntensity\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "entries.duration", "description": "Translation key \"entries.duration\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "entries.minutes", "description": "Translation key \"entries.minutes\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "entries.edit", "description": "Translation key \"entries.edit\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "statistics.title", "description": "Translation key \"statistics.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "statistics.inDevelopment", "description": "Translation key \"statistics.inDevelopment\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "export.title", "description": "Translation key \"export.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "export.inDevelopment", "description": "Translation key \"export.inDevelopment\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "messages.close", "description": "Translation key \"messages.close\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "saveSuccess", "description": "Translation key \"saveSuccess\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "saveError", "description": "Translation key \"saveError\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "validationError", "description": "Translation key \"validationError\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "updateSuccess", "description": "Translation key \"updateSuccess\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "updateError", "description": "Translation key \"updateError\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "confirmDelete", "description": "Translation key \"confirmDelete\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "deleteSuccess", "description": "Translation key \"deleteSuccess\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "deleteError", "description": "Translation key \"deleteError\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PainTrackerTool.tsx", "key": "close", "description": "Translation key \"close\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "results.validationMessage", "description": "Translation key \"results.validationMessage\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "results.assessments.severe_symptoms", "description": "Translation key \"results.assessments.severe_symptoms\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "results.assessments.severe_late", "description": "Translation key \"results.assessments.severe_late\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "results.assessments.severe_early", "description": "Translation key \"results.assessments.severe_early\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "results.assessments.moderate_late", "description": "Translation key \"results.assessments.moderate_late\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "results.assessments.normal", "description": "Translation key \"results.assessments.normal\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "title", "description": "Translation key \"title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "subtitle", "description": "Translation key \"subtitle\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.intensity.title", "description": "Translation key \"questions.intensity.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.intensity.options.mild", "description": "Translation key \"questions.intensity.options.mild\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.intensity.options.moderate", "description": "Translation key \"questions.intensity.options.moderate\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.intensity.options.severe", "description": "Translation key \"questions.intensity.options.severe\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.onset.title", "description": "Translation key \"questions.onset.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.onset.options.recent", "description": "Translation key \"questions.onset.options.recent\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.onset.options.later", "description": "Translation key \"questions.onset.options.later\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.symptoms.title", "description": "Translation key \"questions.symptoms.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.symptoms.options.fever", "description": "Translation key \"questions.symptoms.options.fever\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.symptoms.options.vomiting", "description": "Translation key \"questions.symptoms.options.vomiting\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.symptoms.options.dizziness", "description": "Translation key \"questions.symptoms.options.dizziness\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.symptoms.options.bleeding", "description": "Translation key \"questions.symptoms.options.bleeding\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "questions.symptoms.options.nonMenstrual", "description": "Translation key \"questions.symptoms.options.nonMenstrual\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "actions.assess", "description": "Translation key \"actions.assess\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "results.title", "description": "Translation key \"results.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "results.consultAdvice", "description": "Translation key \"results.consultAdvice\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "actions.reset", "description": "Translation key \"actions.reset\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "key": "actions.moreInfo", "description": "Translation key \"actions.moreInfo\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", "strings": [");\n  const [severeSymptoms, setSevereSymptoms] = useState<string[]>([]);\n  const [result, setResult] = useState<AssessmentResult | null>(null);\n  const [showResult, setShowResult] = useState(false);\n\n  // 使用统一翻译Hook\n  const { t } = useInteractiveToolTranslations(", ";\n\n    // 检查是否有严重症状\n    if (severeSymptoms.length > 0) {\n      advice = t(", ";\n    }\n    // 根据痛经强度和开始时间评估\n    else if (intensity === ", ">\n          {/* 痛经强度 */}\n          <div className=", ">{option.label}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* 痛经开始时间 */}\n          <div className=", ">{option.label}</span>\n                </label>\n              ))}\n            </div>\n          </div>\n\n          {/* 严重症状 */}\n          <div>\n            <h3 className="], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "messages.assessmentComplete", "description": "Translation key \"messages.assessmentComplete\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "messages.assessmentCompleteDesc", "description": "Translation key \"messages.assessmentCompleteDesc\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "messages.assessmentFailed", "description": "Translation key \"messages.assessmentFailed\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "messages.assessmentFailedDesc", "description": "Translation key \"messages.assessmentFailedDesc\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "result.severity", "description": "Translation key \"result.severity\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "severity.${result.severity}", "description": "Translation key \"severity.${result.severity}\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "severity.${result.type}", "description": "Translation key \"severity.${result.type}\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "result.summary", "description": "Translation key \"result.summary\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "result.recommendations", "description": "Translation key \"result.recommendations\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "priority.${recommendation.priority}", "description": "Translation key \"priority.${recommendation.priority}\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "result.timeframe", "description": "Translation key \"result.timeframe\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "result.actionSteps", "description": "Translation key \"result.actionSteps\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "result.retakeAssessment", "description": "Translation key \"result.retakeAssessment\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "messages.resultsSaved", "description": "Translation key \"messages.resultsSaved\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "messages.resultsSavedDesc", "description": "Translation key \"messages.resultsSavedDesc\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "result.saveResults", "description": "Translation key \"result.saveResults\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "navigation.previous", "description": "Translation key \"navigation.previous\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "navigation.skip", "description": "Translation key \"navigation.skip\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "navigation.finish", "description": "Translation key \"navigation.finish\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "navigation.next", "description": "Translation key \"navigation.next\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "title", "description": "Translation key \"title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "subtitle", "description": "Translation key \"subtitle\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "start.title", "description": "Translation key \"start.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "start.description", "description": "Translation key \"start.description\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "start.feature1", "description": "Translation key \"start.feature1\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "start.feature2", "description": "Translation key \"start.feature2\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "start.feature3", "description": "Translation key \"start.feature3\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "start.feature4", "description": "Translation key \"start.feature4\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "start.startButton", "description": "Translation key \"start.startButton\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "start.disclaimer", "description": "Translation key \"start.disclaimer\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "result.title", "description": "Translation key \"result.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "result.yourScore", "description": "Translation key \"result.yourScore\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "progress.questionOf", "description": "Translation key \"progress.questionOf\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "assessmentComplete", "description": "Translation key \"assessmentComplete\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "assessmentCompleteDesc", "description": "Translation key \"assessmentCompleteDesc\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "assessmentFailed", "description": "Translation key \"assessmentFailed\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "assessmentFailedDesc", "description": "Translation key \"assessmentFailedDesc\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "resultsSaved", "description": "Translation key \"resultsSaved\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "key": "resultsSavedDesc", "description": "Translation key \"resultsSavedDesc\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", "strings": [");\n  const [selectedAnswers, setSelectedAnswers] = useState<Record<string, any>>({});\n\n  const {\n    currentSession,\n    currentQuestion,\n    currentQuestionIndex,\n    totalQuestions,\n    progress,\n    isComplete,\n    result,\n    isLoading,\n    error,\n    startAssessment,\n    answerQuestion,\n    goToPreviousQuestion,\n    goToNextQuestion,\n    completeAssessment,\n    resetAssessment\n  } = useSymptomAssessment();\n\n  // 监听result变化\n  useEffect(() => {\n    console.log(", ", {\n      currentQuestionIndex,\n      totalQuestions,\n      isLastQuestion: currentQuestionIndex >= totalQuestions - 1\n    });\n\n    if (currentQuestionIndex >= totalQuestions - 1) {\n      // 已经是最后一题，完成评估\n      console.log(", ")\n        );\n        // 强制重新渲染以显示结果\n        setTimeout(() => {\n          console.log(", "症状评估工具", "专业的症状分析工具，帮助您了解自己的健康状况", "开始评估", "这个评估工具将帮助您了解症状的严重程度并提供个性化建议", ">\n              {(() => {\n                try {\n                  // 直接使用翻译键而不是数组\n                  const features = [\n                    t(", "12个专业问题", "个性化建议", "科学评估", "即时结果", "开始评估", "此工具仅供参考，不能替代专业医疗建议", "评估结果", "您的得分", ">\n        {/* Progress Bar - 移动端优化 */}\n        <div className=", " }}\n            />\n          </div>\n        </div>\n\n        {/* Question - 移动端优化 */}\n        {currentQuestion && (\n          <div className=", ">\n                {currentQuestion.description}\n              </p>\n            )}\n\n            {/* Question Input - 移动端优化 */}\n            <div className=", "无痛", "轻微", "中等", "严重", "极重", "}</span>\n                    </div>\n                  </div>\n\n                  {/* 当前选择的值显示 - 与中医体质测试保持一致的样式 */}\n                  <div className=", "疼痛程度：", "无痛", "轻微", "中等", "严重", "极重", ";\n                            }\n                          })()})\n                        </span>\n                      </span>\n                    </div>\n                  </div>\n\n                  {/* 疼痛程度说明 - 与中医体质测试保持一致的样式 */}\n                  <div className=", "疼痛程度参考", "无痛或极轻微不适", "轻微疼痛，不影响日常活动", "中等疼痛，影响部分活动", "严重疼痛，严重影响生活", "}</span>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n              )}\n            </div>\n          </div>\n        )}\n\n        {/* Navigation - 移动端优化 */}\n        <div className="], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useAppTranslations.ts", "key": "${toolName}.${key}", "description": "Translation key \"${toolName}.${key}\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useAppTranslations.ts", "key": "${optionsKey}.${option}", "description": "Translation key \"${optionsKey}.${option}\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "app/[locale]/interactive-tools/shared/hooks/useAppTranslations.ts", "strings": ["\n  };\n};\n\n/**\n * 专门用于交互工具的翻译Hook\n */\nexport const useInteractiveToolTranslations = (toolName?: string) => {\n  const namespace = toolName ? ", "\n  };\n};\n\n/**\n * 获取多语言选项的工具函数\n */\nexport const useTranslatedOptions = (namespace: string, optionsKey: string) => {\n  const { t } = useAppTranslations(namespace);\n  \n  return (options: string[]) => {\n    return options.map(option => ({\n      value: option,\n      label: t("], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "app/[locale]/interactive-tools/shared/hooks/useConstitutionTest.ts", "strings": ["测试未完成，请回答所有问题", "计算结果时出错，请重试"], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/usePainTracker.ts", "key": "a", "description": "Translation key \"a\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/usePainTracker.ts", "key": "T", "description": "Translation key \"T\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "app/[locale]/interactive-tools/shared/hooks/usePainTracker.ts", "strings": [");\n      }\n    }\n  }, [entries, storageKey, isLoading]);\n\n  // Recalculate statistics when entries change\n  useEffect(() => {\n    setStatistics(calculateStatistics(entries));\n  }, [entries]);\n\n  const addEntry = useCallback(async (data: PainEntryFormData): Promise<{ success: boolean; errors?: ValidationError[] }> => {\n    try {\n      setError(null);\n      \n      // Validate the entry\n      const validationErrors = validatePainEntry(data);\n      if (validationErrors.length > 0) {\n        return { success: false, errors: validationErrors };\n      }\n\n      // Check for duplicate dates - 允许用户选择是否覆盖\n      const existingEntry = entries.find(entry => entry.date === data.date);\n      if (existingEntry) {\n        // 如果用户明确表示要覆盖，则删除旧记录\n        if ((data as any).overwrite === true) {\n          setEntries(prev => prev.filter(entry => entry.date !== data.date));\n        } else {\n          return {\n            success: false,\n            errors: [{\n              field: "], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.emergencyMedical.title", "description": "Translation key \"recommendations.emergencyMedical.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.emergencyMedical.description", "description": "Translation key \"recommendations.emergencyMedical.description\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.emergencyMedical.timeframe", "description": "Translation key \"recommendations.emergencyMedical.timeframe\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.emergencyMedical.actionSteps", "description": "Translation key \"recommendations.emergencyMedical.actionSteps\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.painManagement.title", "description": "Translation key \"recommendations.painManagement.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.painManagement.description", "description": "Translation key \"recommendations.painManagement.description\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.painManagement.timeframe", "description": "Translation key \"recommendations.painManagement.timeframe\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.painManagement.actionSteps", "description": "Translation key \"recommendations.painManagement.actionSteps\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.lifestyleChanges.title", "description": "Translation key \"recommendations.lifestyleChanges.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.lifestyleChanges.description", "description": "Translation key \"recommendations.lifestyleChanges.description\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.lifestyleChanges.timeframe", "description": "Translation key \"recommendations.lifestyleChanges.timeframe\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.lifestyleChanges.actionSteps", "description": "Translation key \"recommendations.lifestyleChanges.actionSteps\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.selfcarePractices.title", "description": "Translation key \"recommendations.selfcarePractices.title\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.selfcarePractices.description", "description": "Translation key \"recommendations.selfcarePractices.description\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.selfcarePractices.timeframe", "description": "Translation key \"recommendations.selfcarePractices.timeframe\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "recommendations.selfcarePractices.actionSteps", "description": "Translation key \"recommendations.selfcarePractices.actionSteps\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "resultMessages.emergency", "description": "Translation key \"resultMessages.emergency\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "resultMessages.emergencySummary", "description": "Translation key \"resultMessages.emergencySummary\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "resultMessages.severe", "description": "Translation key \"resultMessages.severe\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "resultMessages.severeSummary", "description": "Translation key \"resultMessages.severeSummary\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "resultMessages.moderate", "description": "Translation key \"resultMessages.moderate\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "resultMessages.moderate<PERSON><PERSON><PERSON><PERSON>", "description": "Translation key \"resultMessages.moderateSummary\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "resultMessages.mild", "description": "Translation key \"resultMessages.mild\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "resultMessages.mildSummary", "description": "Translation key \"resultMessages.mildSummary\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "result.nextSteps.trackSymptoms", "description": "Translation key \"result.nextSteps.trackSymptoms\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "result.nextSteps.tryRecommendations", "description": "Translation key \"result.nextSteps.tryRecommendations\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "result.nextSteps.consultDoctor", "description": "Translation key \"result.nextSteps.consultDoctor\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "messages.assessmentFailed", "description": "Translation key \"messages.assessmentFailed\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "key": "assessmentFailed", "description": "Translation key \"assessmentFailed\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", "strings": ["建议立即就医", "您的症状可能需要专业医疗评估和治疗", "立即", "联系您的妇科医生", "如果疼痛剧烈，考虑急诊就医", "记录详细的症状日志", "建议立即就医", "您的症状可能需要专业医疗评估和治疗", "立即", "联系您的妇科医生", "如果疼痛剧烈，考虑急诊就医", "记录详细的症状日志", "疼痛管理策略", "多种方法可以帮助缓解经期疼痛", "立即可用", "使用热敷垫或热水袋", "尝试轻度运动如散步", "考虑非处方止痛药（按说明使用）", "疼痛管理策略", "多种方法可以帮助缓解经期疼痛", "立即可用", "使用热敷垫或热水袋", "尝试轻度运动如散步", "考虑非处方止痛药（按说明使用）", "生活方式调整", "长期的生活方式改变可以显著改善症状", "2-3个月见效", "保持规律的运动习惯", "确保充足的睡眠", "学习压力管理技巧", "保持均衡饮食", "生活方式调整", "长期的生活方式改变可以显著改善症状", "2-3个月见效", "保持规律的运动习惯", "确保充足的睡眠", "学习压力管理技巧", "保持均衡饮食", "自我护理实践", "日常的自我护理可以帮助您更好地管理症状", "持续进行", "练习深呼吸和冥想", "使用疼痛追踪器记录症状", "建立支持网络", "学习放松技巧", "自我护理实践", "日常的自我护理可以帮助您更好地管理症状", "持续进行", "练习深呼吸和冥想", "使用疼痛追踪器记录症状", "建立支持网络", "学习放松技巧", "您的症状较为严重，建议尽快咨询医疗专业人士。", "评估显示您可能需要专业医疗关注。", "您的症状比较严重，建议采取综合管理策略。", "您的症状需要积极的管理和可能的医疗干预。", "您有中等程度的症状，可以通过多种方法进行管理。", "您的症状是可以管理的，建议采用多种缓解策略。", "您的症状相对较轻，通过简单的自我护理就能很好地管理。", "您的症状较轻，可以通过生活方式调整来改善。", "使用疼痛追踪器记录症状", "尝试推荐的缓解方法", "如果症状持续或恶化，请咨询医生", "使用疼痛追踪器记录症状", "尝试推荐的缓解方法", "如果症状持续或恶化，请咨询医生", "评估完成时出现错误，请重试。"], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/ArticleInteractions.tsx", "strings": [" \n}: ArticleInteractionsProps) {\n  const [likes, setLikes] = useState(0);\n  const [isLiked, setIsLiked] = useState(false);\n  const [isBookmarked, setIsBookmarked] = useState(false);\n  const [views, setViews] = useState(0);\n  const [showShareMenu, setShowShareMenu] = useState(false);\n  const [copySuccess, setCopySuccess] = useState(false);\n\n  // 初始化数据\n  useEffect(() => {\n    // 从 localStorage 获取数据\n    const storedLikes = localStorage.getItem(", ");\n    if (storedViews) {\n      setViews(parseInt(storedViews));\n    } else {\n      // 首次访问，增加浏览量\n      const newViews = Math.floor(Math.random() * 50) + 20; // 模拟初始浏览量\n      setViews(newViews);\n      localStorage.setItem(", ", newViews.toString());\n    }\n\n    // 记录本次访问\n    const currentViews = parseInt(localStorage.getItem(", ", newViews.toString());\n  }, [articleId]);\n\n  // 点赞功能\n  const handleLike = () => {\n    const newLikedState = !isLiked;\n    const newLikes = newLikedState ? likes + 1 : likes - 1;\n    \n    setIsLiked(newLikedState);\n    setLikes(newLikes);\n    \n    localStorage.setItem(", ", newLikes.toString());\n  };\n\n  // 收藏功能\n  const handleBookmark = () => {\n    const newBookmarkedState = !isBookmarked;\n    setIsBookmarked(newBookmarkedState);\n    \n    localStorage.setItem(", ", newBookmarkedState.toString());\n    \n    // 管理收藏列表\n    const bookmarks = JSON.parse(localStorage.getItem(", ", JSON.stringify(bookmarks));\n  };\n\n  // 复制链接\n  const handleCopyLink = async () => {\n    try {\n      await navigator.clipboard.writeText(window.location.href);\n      setCopySuccess(true);\n      setTimeout(() => setCopySuccess(false), 2000);\n    } catch (err) {\n      console.error(", ", err);\n    }\n  };\n\n  // 分享到社交媒体\n  const handleShare = (platform: string) => {\n    const url = encodeURIComponent(window.location.href);\n    const title = encodeURIComponent(articleTitle);\n    \n    const shareUrls = {\n      facebook: ", "点赞文章", "已点赞", "收藏", "已收藏", "分享", "阅读量", "点赞", "复制链接", "已复制", "分享到 Facebook", "分享到 Twitter", "分享到 LinkedIn", "分享到 WhatsApp", "分享到 Telegram", "}>\n      {/* 统计信息 */}\n      <div className=", " />\n            {likes} {text.likes}\n          </span>\n        </div>\n      </div>\n\n      {/* 操作按钮 */}\n      <div className=", ">\n          {/* 点赞按钮 */}\n          <button\n            onClick={handleLike}\n            className={", ">\n              {isLiked ? text.liked : text.like}\n            </span>\n          </button>\n\n          {/* 收藏按钮 */}\n          <button\n            onClick={handleBookmark}\n            className={", ">\n              {isBookmarked ? text.bookmarked : text.bookmark}\n            </span>\n          </button>\n        </div>\n\n        {/* 分享按钮 */}\n        <div className=", ">{text.share}</span>\n          </button>\n\n          {/* 分享菜单 */}\n          {showShareMenu && (\n            <div className=", "\n              >\n                ✈️ {text.shareToTelegram}\n              </button>\n            </div>\n          )}\n        </div>\n      </div>\n\n      {/* 点击外部关闭分享菜单 */}\n      {showShareMenu && (\n        <div\n          className="], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/BreathingExercise.tsx", "strings": ["吸气", "屏息", "呼气", "4-7-8 深呼吸练习", "通过调节神经系统自然缓解疼痛", "练习方法：", "吸气 4秒", "屏息 7秒", "呼气 8秒", "正在进行：${getCurrentPhase().name}", "🫁 开始引导练习", "✅ 一轮练习完成！", "再次练习", "停止练习", "科学效果：", "疼痛感知", "肌肉紧张", "放松感受", "💡 建议：找一个舒适的坐位或躺位，放松全身肌肉。初学者建议进行3-4个循环。"], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/DownloadButton.tsx", "strings": ["查看文档"], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/EmbeddedPainAssessment.tsx", "strings": ["请先选择痛经强度", "您的痛经程度较轻，可以尝试热敷、轻度运动等自然缓解方法。", "您的痛经程度中等，建议结合多种缓解方法，如有需要可考虑非处方止痛药。", "您的痛经程度较重，建议咨询医生获得专业评估和治疗建议。", "💡 痛经快速自测", "1分钟了解您的痛经程度，获得初步建议", "您的痛经强度如何？", "轻微（可以忍受，不影响日常活动）", "中度（影响部分活动，但能坚持）", "重度（完全影响日常活动，需要休息）", "获取建议", "详细评估", "评估结果", "重新测试", "完整评估", "⚠️ 此工具仅供参考，不能替代专业医疗建议"], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/Footer.tsx", "key": "disclaimer", "description": "Translation key \"disclaimer\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/Footer.tsx", "key": "privacy", "description": "Translation key \"privacy\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/Footer.tsx", "key": "terms", "description": "Translation key \"terms\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/Footer.tsx", "key": "contact_email", "description": "Translation key \"contact_email\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/Footer.tsx", "key": "copyright", "description": "Translation key \"copyright\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/Footer.tsx", "strings": ["医疗免责声明", "文章PDF下载中心", "平时调理"], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/Header.tsx", "key": "home", "description": "Translation key \"home\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/Header.tsx", "key": "scenarioSolutions", "description": "Translation key \"scenarioSolutions\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/Header.tsx", "strings": ["互动解决方案", "文章PDF下载中心", "平时调理", "痛经健康指南", ">\n        {/* 📱 移动端优化头部高度 */}\n        <div className=", ">\n          {/* 📱 移动端优化Logo */}\n          <div className=", ">\n                periodhub.health\n              </span>\n            </Link>\n          </div>\n\n          {/* 📱 移动端优化桌面导航 */}\n          <nav className=", "}\n              >\n                {item.name}\n              </Link>\n            ))}\n          </nav>\n\n          {/* 📱 移动端优化右侧控件 */}\n          <div className=", ">\n            <LanguageSwitcher />\n\n            {/* 📱 移动端优化菜单按钮 */}\n            <button\n              onClick={() => setIsMenuOpen(!isMenuOpen)}\n              className=", " />\n              )}\n            </button>\n          </div>\n        </div>\n\n        {/* 📱 移动端优化导航菜单 */}\n        {isMenuOpen && (\n          <div className=", "中文", ">\n      {/* 📱 移动端优化语言切换按钮 */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=", "} />\n      </button>\n\n      {/* 📱 移动端优化下拉菜单 */}\n      {isOpen && (\n        <div className="], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/NSAIDContent.tsx", "key": "|", "description": "Translation key \"|\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/NSAIDContent.tsx", "key": "\\n", "description": "Translation key \"\\n\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/NSAIDContent.tsx", "key": "style", "description": "Translation key \"style\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/NSAIDContent.tsx", "strings": [");\n\n      if (calculateButton) {\n        // 强制设置按钮样式\n        const btn = calculateButton as HTMLButtonElement;\n        btn.style.setProperty(", "场景1：开场 - 表现痛经的不适感", "很多女性每个月都会经历痛经，那种痉挛、疼痛的感觉让人非常不适。", "场景2：解释痛经原因 - 前列腺素", "月经期间，子宫内膜会释放一种叫做", "的物质。前列腺素会引起子宫肌肉剧烈收缩，导致疼痛。", "场景3：引出NSAIDs", "而非甾体抗炎药，简称NSAID，是缓解痛经的常用药物。它们能从源头减少前列腺素的产生。", "场景4：药物服用", "当您服下NSAID药片后，它会进入消化系统。", "场景5：吸收进入血液", "然后通过消化道被吸收到血液里，随着血液流向全身。", "场景6：分布到作用部位", "药物分子随着血液循环，最终抵达引起疼痛的部位——比如您的子宫周围。", "场景7：作用机制 - 抑制COX酶", "在这里，NSAID药物找到了产生前列腺素的关键", "——环氧合酶，并抑制了它的活性。", "场景8：减少前列腺素", "环氧合酶的工作被打断，前列腺素的合成量就大大降低了。", "场景9：疼痛缓解", "随着前列腺素减少，子宫收缩变得温和，疼痛感明显减轻。", "场景10：药物代谢", "完成任务后，NSAID药物会被肝脏代谢，最终通过肾脏排出体外。", "场景11：总结", "这就是NSAID缓解痛经的完整过程：从服用到吸收，从作用到代谢，科学而有效。", "场景 ${scene.id} / ${scenes.length}", "抱歉，视频加载失败。请检查您的网络连接或稍后再试。", "视频加载错误", "没有可播放的场景", "请检查数据配置。", "场景 0 / 0", ", {\n            id: video.id,\n            className: video.className,\n            src: video.src,\n            parentElement: video.parentElement\n          });\n        });\n      }\n\n\n\n    }, 100); // Small delay to ensure DOM is ready\n\n    return () => {\n      clearTimeout(timer);\n      // styleInterval会在组件卸载时自动清理\n    };\n  }, []);\n\n  return (\n    <>\n      <style jsx>{"], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/NSAIDContentSimple.tsx", "key": "|", "description": "Translation key \"|\" not found in either language file"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/NSAIDContentSimple.tsx", "key": "\\n", "description": "Translation key \"\\n\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/NSAIDContentSimple.tsx", "strings": ["场景1：开场 - 表现痛经的不适感", "很多女性每个月都会经历痛经，那种痉挛、疼痛的感觉让人非常不适。", "场景2：解释痛经原因 - 前列腺素", "月经期间，子宫内膜会释放一种叫做", "的物质。前列腺素会引起子宫肌肉剧烈收缩，导致疼痛。", "场景3：引出NSAIDs", "而非甾体抗炎药，简称NSAID，是缓解痛经的常用药物。它们能从源头减少前列腺素的产生。", "场景 ${scene.id} / ${scenes.length}", "抱歉，视频加载失败。请检查您的网络连接或稍后再试。"], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/NSAIDInteractive.tsx", "key": "link", "description": "Translation key \"link\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/NavigationTabs.tsx", "strings": ["📚 专业文章", "📥 PDF下载"], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/ReadingProgress.tsx", "strings": [", updateProgress);\n    updateProgress(); // 初始化\n\n    return () => window.removeEventListener(", "返回顶部", "阅读进度", "\n    }\n  };\n\n  const text = t[locale];\n\n  return (\n    <>\n      {/* 阅读进度条 */}\n      <div className=", "\n          aria-valuenow={Math.round(progress)}\n          aria-valuemin={0}\n          aria-valuemax={100}\n          aria-label={text.readingProgress}\n        />\n      </div>\n\n      {/* 返回顶部按钮 */}\n      {showBackToTop && (\n        <button\n          onClick={scrollToTop}\n          className="], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/SearchBox.tsx", "strings": [");\n  const [results, setResults] = useState<SearchResult[]>([]);\n  const [isOpen, setIsOpen] = useState(false);\n  const [selectedIndex, setSelectedIndex] = useState(-1);\n  const searchRef = useRef<HTMLDivElement>(null);\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // 搜索函数\n  const searchArticles = (searchQuery: string): SearchResult[] => {\n    if (!searchQuery.trim()) return [];\n\n    const query = searchQuery.toLowerCase();\n    const searchResults: SearchResult[] = [];\n\n    articles.forEach(article => {\n      const title = article.title.toLowerCase();\n      const summary = article.summary.toLowerCase();\n      const tags = article.tags.map(tag => tag.toLowerCase());\n      const content = article.content.toLowerCase();\n\n      // 标题匹配（最高优先级）\n      if (title.includes(query)) {\n        searchResults.push({\n          ...article,\n          matchType: ", ",\n          matchText: article.title\n        });\n        return;\n      }\n\n      // 摘要匹配\n      if (summary.includes(query)) {\n        const matchIndex = summary.indexOf(query);\n        const start = Math.max(0, matchIndex - 50);\n        const end = Math.min(summary.length, matchIndex + query.length + 50);\n        const matchText = ", ",\n          matchText\n        });\n        return;\n      }\n\n      // 标签匹配\n      const matchingTag = tags.find(tag => tag.includes(query));\n      if (matchingTag) {\n        searchResults.push({\n          ...article,\n          matchType: ", "\n        });\n        return;\n      }\n\n      // 内容匹配（最低优先级）\n      if (content.includes(query)) {\n        const matchIndex = content.indexOf(query);\n        const start = Math.max(0, matchIndex - 100);\n        const end = Math.min(content.length, matchIndex + query.length + 100);\n        const matchText = ", ",\n          matchText\n        });\n      }\n    });\n\n    // 按匹配类型排序：title > summary > tag > content\n    const priorityOrder = { title: 4, summary: 3, tag: 2, content: 1 };\n    return searchResults\n      .sort((a, b) => priorityOrder[b.matchType] - priorityOrder[a.matchType])\n      .slice(0, 8); // 限制结果数量\n  };\n\n  // 处理搜索\n  useEffect(() => {\n    if (query.trim()) {\n      const searchResults = searchArticles(query);\n      setResults(searchResults);\n      setIsOpen(true);\n      setSelectedIndex(-1);\n    } else {\n      setResults([]);\n      setIsOpen(false);\n    }\n  }, [query, articles]);\n\n  // 处理键盘导航\n  const handleKeyDown = (e: React.KeyboardEvent) => {\n    if (!isOpen || results.length === 0) return;\n\n    switch (e.key) {\n      case ", ":\n        setIsOpen(false);\n        setSelectedIndex(-1);\n        inputRef.current?.blur();\n        break;\n    }\n  };\n\n  // 点击外部关闭\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {\n        setIsOpen(false);\n        setSelectedIndex(-1);\n      }\n    };\n\n    document.addEventListener(", ", handleClickOutside);\n  }, []);\n\n  // 清除搜索\n  const clearSearch = () => {\n    setQuery(", ");\n    setResults([]);\n    setIsOpen(false);\n    setSelectedIndex(-1);\n    inputRef.current?.focus();\n  };\n\n  // 获取匹配类型的显示文本\n  const getMatchTypeLabel = (matchType: string) => {\n    const labels = {\n      title: locale === ", "标题", "摘要", "标签", "内容", "}>\n      {/* 搜索输入框 */}\n      <div className=", "搜索文章...", " />\n          </button>\n        )}\n      </div>\n\n      {/* 搜索结果下拉框 */}\n      {isOpen && results.length > 0 && (\n        <div className=", ">\n                      {result.reading_time}\n                    </span>\n                  </div>\n                </div>\n              </div>\n            </Link>\n          ))}\n        </div>\n      )}\n\n      {/* 无结果提示 */}\n      {isOpen && query && results.length === 0 && (\n        <div className=", "未找到相关文章"], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/StructuredData.tsx", "strings": ["痛经", "月经疼痛", "经期疼痛", "月经期间或前后出现的疼痛症状", "妇科学", "痛经管理", "痛经的预防、治疗和管理方法", "女性健康", "女性生殖健康和月经健康相关话题", "妇科学"], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/TableOfContents.tsx", "strings": [");\n  const [isOpen, setIsOpen] = useState(false);\n\n  useEffect(() => {\n    // 获取所有标题元素\n    const headingElements = document.querySelectorAll(", ");\n    const headingList: Heading[] = [];\n\n    headingElements.forEach((heading, index) => {\n      // 为没有 id 的标题添加 id\n      if (!heading.id) {\n        const text = heading.textContent || ", ",\n        level: parseInt(heading.tagName.charAt(1))\n      });\n    });\n\n    setHeadings(headingList);\n  }, []);\n\n  useEffect(() => {\n    // 监听滚动，高亮当前章节\n    const observer = new IntersectionObserver(\n      (entries) => {\n        entries.forEach((entry) => {\n          if (entry.isIntersecting) {\n            setActiveId(entry.target.id);\n          }\n        });\n      },\n      {\n        rootMargin: ", ",\n        threshold: 0\n      }\n    );\n\n    headings.forEach(({ id }) => {\n      const element = document.getElementById(id);\n      if (element) {\n        observer.observe(element);\n      }\n    });\n\n    return () => observer.disconnect();\n  }, [headings]);\n\n  const scrollToHeading = (id: string) => {\n    const element = document.getElementById(id);\n    if (element) {\n      const offset = 80; // 考虑固定头部的高度\n      const elementPosition = element.offsetTop - offset;\n      \n      window.scrollTo({\n        top: elementPosition,\n        behavior: ", "\n      });\n    }\n    setIsOpen(false); // 移动端点击后关闭目录\n  };\n\n  const t = {\n    zh: {\n      tableOfContents: ", "切换目录显示", "}>\n      {/* 移动端可折叠标题 */}\n      <button\n        onClick={() => setIsOpen(!isOpen)}\n        className=", " />}\n      </button>\n\n      {/* 桌面端固定标题 */}\n      <div className=", " />\n          {text.tableOfContents}\n        </div>\n      </div>\n\n      {/* 目录内容 */}\n      <div className={"], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/ToolsCollectionButton.tsx", "strings": ["访问完整工具集页面"], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/UserSuccessStories.tsx", "strings": ["李小雅", "IT从业者，25岁", "李", "通过个性化评估发现我属于前列腺素过度分泌型痛经，按照平台建议调整饮食和运动，3个月后疼痛强度从8分降到3分，工作效率大幅提升！", "张婷婷", "大学生，20岁", "张", "青少年专区的内容太有用了！学会了热敷、瑜伽和呼吸法，现在考试期间来大姨妈也不怕了。还帮助室友一起改善，大家感情更好了。", "王芳", "职场妈妈，32岁", "王", "疼痛日志功能帮我发现了痛经与压力的关联性。配合医生治疗使用平台建议，现在基本告别了每月的痛苦，生活质量改善明显。", "用户成功案例", "已有超过10,000+女性在这里找到了属于自己的解决方案", "加入她们，开始您的康复之旅"], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/advanced/AppProvider.tsx", "strings": [">\n        {children}\n        <ToastContainer />\n        <ModalManager />\n      </div>\n    </ErrorBoundary>\n  );\n};\n\n// 应用主题\nfunction applyTheme(theme: ", ");\n  root.classList.add(theme);\n  \n  // 更新meta标签\n  const metaTheme = document.querySelector(", ");\n  }\n}\n\n// 应用字体大小\nfunction applyFontSize(fontSize: ", ");\n  }\n}\n\n// 应用可访问性设置\nfunction applyAccessibilitySettings(accessibility: {\n  highContrast: boolean;\n  reducedMotion: boolean;\n  screenReader: boolean;\n}) {\n  const root = document.documentElement;\n  \n  // 高对比度\n  if (accessibility.highContrast) {\n    root.classList.add(", ");\n  }\n  \n  // 减少动画\n  if (accessibility.reducedMotion) {\n    root.classList.add(", ");\n  }\n  \n  // 屏幕阅读器优化\n  if (accessibility.screenReader) {\n    root.classList.add(", ");\n  }\n}\n\n// 页面级别的Provider\ninterface PageProviderProps {\n  children: ReactNode;\n  title?: string;\n  description?: string;\n  keywords?: string[];\n}\n\nexport const PageProvider: React.FC<PageProviderProps> = ({\n  children,\n  title,\n  description,\n  keywords = [],\n}) => {\n  useEffect(() => {\n    // 更新页面标题\n    if (title) {\n      document.title = ", ";\n    }\n\n    // 更新meta描述\n    if (description) {\n      const metaDescription = document.querySelector(", ", description);\n      }\n    }\n\n    // 更新meta关键词\n    if (keywords.length > 0) {\n      const metaKeywords = document.querySelector(", ">\n      {children}\n    </ErrorBoundary>\n  );\n};\n\n// 组件级别的Provider\ninterface ComponentProviderProps {\n  children: ReactNode;\n  name?: string;\n  fallback?: ReactNode;\n}\n\nexport const ComponentProvider: React.FC<ComponentProviderProps> = ({\n  children,\n  name,\n  fallback,\n}) => {\n  return (\n    <ErrorBoundary \n      level=", ", error, errorInfo);\n      }}\n    >\n      {children}\n    </ErrorBoundary>\n  );\n};\n\n// 性能监控Hook\nexport const usePagePerformance = (pageName: string) => {\n  useEffect(() => {\n    const startTime = performance.now();\n    \n    // 记录页面访问\n    performanceMonitor.recordCustomInteraction(", ", pageName);\n    \n    return () => {\n      const endTime = performance.now();\n      const duration = endTime - startTime;\n      \n      // 记录页面停留时间\n      performanceMonitor.recordCustomInteraction(", ", duration);\n    };\n  }, [pageName]);\n};\n\n// 用户行为追踪Hook\nexport const useUserTracking = () => {\n  const recordInteraction = (type: string, element: string, data?: any) => {\n    performanceMonitor.recordCustomInteraction(type as any, element);\n    \n    // 可以在这里添加更多的用户行为分析\n    console.log("], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/advanced/DataVisualization.tsx", "strings": [">\n        {/* 网格线 */}\n        {showGrid && (\n          <g className=", "\n              />\n            ))}\n          </g>\n        )}\n\n        {/* 数据线 */}\n        <polyline\n          points={points}\n          fill=", "\n        />\n\n        {/* 数据点 */}\n        {data.map((point, index) => {\n          const x = (index / (data.length - 1)) * (width - 40) + 20;\n          const y = height - 20 - ((point.value - minValue) / (maxValue - minValue || 1)) * (height - 40);\n          \n          return (\n            <circle\n              key={index}\n              cx={x}\n              cy={y}\n              r=", "}</title>\n            </circle>\n          );\n        })}\n\n        {/* 标签 */}\n        {showLabels && (\n          <g className=", ">{minValue}</text>\n          </g>\n        )}\n      </svg>\n    </div>\n  );\n};\n\n// 柱状图组件\ninterface BarChartProps {\n  data: DataPoint[];\n  config?: ChartConfig;\n  className?: string;\n}\n\nexport const BarChart: React.FC<BarChartProps> = ({\n  data,\n  config = {},\n  className = ", ">\n        {data.map((item, index) => {\n          const barHeight = (item.value / maxValue) * (height - 40);\n          const x = 20 + index * (barWidth + barSpacing);\n          const y = height - 20 - barHeight;\n          const color = item.color || colors[index % colors.length];\n\n          return (\n            <g key={index}>\n              {/* 柱子 */}\n              <rect\n                x={x}\n                y={y}\n                width={barWidth}\n                height={barHeight}\n                fill={color}\n                className=", "}</title>\n              </rect>\n\n              {/* 标签 */}\n              {showL<PERSON><PERSON> && (\n                <text\n                  x={x + barWidth / 2}\n                  y={height - 5}\n                  textAnchor=", "\n                >\n                  {item.label}\n                </text>\n              )}\n\n              {/* 数值 */}\n              <text\n                x={x + barWidth / 2}\n                y={y - 5}\n                textAnchor=", "\n              >\n                {item.value}\n              </text>\n            </g>\n          );\n        })}\n      </svg>\n    </div>\n  );\n};\n\n// 饼图组件\ninterface PieChartProps {\n  data: DataPoint[];\n  config?: ChartConfig;\n  className?: string;\n}\n\nexport const PieChart: React.FC<PieChartProps> = ({\n  data,\n  config = {},\n  className = ", "}</title>\n            </path>\n          ))}\n        </svg>\n\n        {/* 图例 */}\n        {showLegend && (\n          <div className=", ">\n                  {slice.label} ({slice.percentage}%)\n                </span>\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    </div>\n  );\n};\n\n// 统计卡片组件\ninterface StatCardProps {\n  title: string;\n  value: string | number;\n  change?: number;\n  icon?: React.ReactNode;\n  color?: "], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/advanced/ErrorBoundary.tsx", "key": "\nError ID: ${errorId}\nURL: ${window.location.href}\nTime: ${new Date().toISOString()}\n\nError Details:\n${error?.message}\n\nStack Trace:\n${error?.stack}\n\nComponent Stack:\n${errorInfo?.componentStack}\n\nPlease describe what you were doing when this error occurred:\n[Your description here]\n    ", "description": "Translation key \"\nError ID: ${errorId}\nURL: ${window.location.href}\nTime: ${new Date().toISOString()}\n\nError Details:\n${error?.message}\n\nStack Trace:\n${error?.stack}\n\nComponent Stack:\n${errorInfo?.componentStack}\n\nPlease describe what you were doing when this error occurred:\n[Your description here]\n    \" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/advanced/ErrorBoundary.tsx", "strings": [",\n    };\n  }\n\n  componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    this.setState({\n      errorInfo,\n    });\n\n    // 记录错误到应用状态\n    useAppStore.getState().incrementErrorCount();\n\n    // 调用自定义错误处理\n    if (this.props.onError) {\n      this.props.onError(error, errorInfo);\n    }\n\n    // 发送错误报告（在生产环境中）\n    if (process.env.NODE_ENV === ", ", error, errorInfo);\n  }\n\n  private reportError = async (error: Error, errorInfo: ErrorInfo) => {\n    try {\n      // 这里可以发送到错误监控服务\n      const errorReport = {\n        message: error.message,\n        stack: error.stack,\n        componentStack: errorInfo.componentStack,\n        timestamp: new Date().toISOString(),\n        url: window.location.href,\n        userAgent: navigator.userAgent,\n        errorId: this.state.errorId,\n        level: this.props.level || ", ",\n      };\n\n      // 发送到监控服务（示例）\n      // await fetch(", ", reportingError);\n    }\n  };\n\n  private handleRetry = () => {\n    if (this.retryCount < this.maxRetries) {\n      this.retryCount++;\n      this.setState({\n        hasError: false,\n        error: null,\n        errorInfo: null,\n        errorId: null,\n      });\n    } else {\n      // 达到最大重试次数，刷新页面\n      window.location.reload();\n    }\n  };\n\n  private handleGoHome = () => {\n    window.location.href = ", ";\n  };\n\n  private handleReportBug = () => {\n    const { error, errorInfo, errorId } = this.state;\n    const bugReport = {\n      errorId,\n      message: error?.message,\n      stack: error?.stack,\n      componentStack: errorInfo?.componentStack,\n      url: window.location.href,\n      timestamp: new Date().toISOString(),\n    };\n\n    // 打开邮件客户端或错误报告页面\n    const subject = encodeURIComponent(", ");\n  };\n\n  render() {\n    if (this.state.hasError) {\n      // 如果提供了自定义fallback，使用它\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      const { error, errorInfo, errorId } = this.state;\n      const { level = ", ">\n              {/* 错误图标和标题 */}\n              <div className=", "严重错误", "出现错误", "应用遇到了严重问题", "这个组件遇到了问题", ">\n                  我们很抱歉给您带来不便。错误已被记录，我们会尽快修复。\n                </p>\n                \n                {errorId && (\n                  <p className=", ">\n                    错误ID: {errorId}\n                  </p>\n                )}\n\n                {/* 详细错误信息（开发模式或显式启用） */}\n                {(showDetails || process.env.NODE_ENV === ", ">\n                      查看技术详情\n                    </summary>\n                    <div className=", ">\n                        <strong>错误信息:</strong>\n                        <div className=", ">\n                          <strong>堆栈跟踪:</strong>\n                          <pre className=", ">\n                            {error.stack}\n                          </pre>\n                        </div>\n                      )}\n                      \n                      {errorInfo?.componentStack && (\n                        <div>\n                          <strong>组件堆栈:</strong>\n                          <pre className=", ">\n                            {errorInfo.componentStack}\n                          </pre>\n                        </div>\n                      )}\n                    </div>\n                  </details>\n                )}\n              </div>\n\n              {/* 操作按钮 */}\n              <div className=", " />\n                    <span>重试 ({this.maxRetries - this.retryCount} 次剩余)</span>\n                  </button>\n                ) : (\n                  <button\n                    onClick={() => window.location.reload()}\n                    className=", " />\n                    <span>刷新页面</span>\n                  </button>\n                )}\n\n                <div className=", " />\n                    <span>返回首页</span>\n                  </button>\n\n                  <button\n                    onClick={this.handleReportBug}\n                    className=", " />\n                    <span>报告问题</span>\n                  </button>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\n// 高阶组件包装器\nexport function withErrorBoundary<P extends object>(\n  Component: React.ComponentType<P>,\n  errorBoundaryProps?: Omit<Props, ", ";\n  \n  return WrappedComponent;\n}\n\n// Hook版本\nexport function useErrorHandler() {\n  const incrementErrorCount = useAppStore(state => state.incrementErrorCount);\n\n  return (error: Error, errorInfo?: ErrorInfo) => {\n    incrementErrorCount();\n    console.error(", ", error, errorInfo);\n    \n    // 可以在这里添加更多错误处理逻辑\n    if (process.env.NODE_ENV === "], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/advanced/FormSystem.tsx", "strings": [";\n\n// 验证规则\nexport interface ValidationRule {\n  required?: boolean;\n  minLength?: number;\n  maxLength?: number;\n  min?: number;\n  max?: number;\n  pattern?: RegExp;\n  custom?: (value: any) => string | null;\n}\n\n// 字段配置\nexport interface FieldConfig {\n  name: string;\n  label: string;\n  type: FieldType;\n  placeholder?: string;\n  options?: { label: string; value: string | number }[];\n  validation?: ValidationRule;\n  disabled?: boolean;\n  className?: string;\n  description?: string;\n}\n\n// 表单状态\nexport interface FormState {\n  values: Record<string, any>;\n  errors: Record<string, string>;\n  touched: Record<string, boolean>;\n  isSubmitting: boolean;\n  isValid: boolean;\n}\n\n// 表单Hook\nexport const useForm = (initialValues: Record<string, any> = {}) => {\n  const [state, setState] = useState<FormState>({\n    values: initialValues,\n    errors: {},\n    touched: {},\n    isSubmitting: false,\n    isValid: true,\n  });\n\n  const validateField = useCallback((name: string, value: any, rules?: ValidationRule): string | null => {\n    if (!rules) return null;\n\n    if (rules.required && (!value || value.toString().trim() === ", "此字段为必填项", "最少需要 ${rules.minLength} 个字符", "最多允许 ${rules.maxLength} 个字符", "最小值为 ${rules.min}", "最大值为 ${rules.max}", "格式不正确", "请选择...", "选择文件", ">\n        {renderInput()}\n        \n        {/* 状态图标 */}\n        {![", " />}\n          </div>\n        )}\n      </div>\n\n      {/* 错误信息 */}\n      {hasError && (\n        <div className=", " />\n          <span>{error}</span>\n        </div>\n      )}\n\n      {/* 描述信息 */}\n      {config.description && (\n        <p className="], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/advanced/ModalSystem.tsx", "strings": [";\n\n// 模态框配置\nexport interface ModalConfig {\n  type?: ModalType;\n  title?: string;\n  content?: React.ReactNode;\n  size?: ", ";\n  closable?: boolean;\n  maskClosable?: boolean;\n  showFooter?: boolean;\n  confirmText?: string;\n  cancelText?: string;\n  onConfirm?: () => void | Promise<void>;\n  onCancel?: () => void;\n  className?: string;\n  zIndex?: number;\n}\n\n// 确认对话框配置\nexport interface ConfirmConfig {\n  title?: string;\n  content: React.ReactNode;\n  confirmText?: string;\n  cancelText?: string;\n  type?: ", ";\n  onConfirm?: () => void | Promise<void>;\n  onCancel?: () => void;\n}\n\n// 模态框组件\ninterface ModalProps extends ModalConfig {\n  isOpen: boolean;\n  onClose: () => void;\n  children?: React.ReactNode;\n}\n\nconst Modal: React.FC<ModalProps> = ({\n  isOpen,\n  onClose,\n  type = ", "确认", "取消", "}\n      style={{ zIndex }}\n      onClick={handleMaskClick}\n    >\n      {/* 背景遮罩 */}\n      <div\n        className={", "}\n      />\n\n      {/* 模态框内容 */}\n      <div\n        className={", "}\n      >\n        {/* 头部 */}\n        {(title || closable) && (\n          <div className=", " />\n              </button>\n            )}\n          </div>\n        )}\n\n        {/* 内容 */}\n        <div className=", ">\n          {content || children}\n        </div>\n\n        {/* 底部 */}\n        {showFooter && (\n          <div className=", "\n            >\n              {confirmText}\n            </button>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n\n  return createPortal(modalContent, document.body);\n};\n\n// 模态框管理器\nexport const ModalManager: React.FC = () => {\n  const modal = useAppStore(state => state.ui.modal);\n  const closeModal = useAppStore(state => state.closeModal);\n\n  return (\n    <Modal\n      isOpen={modal.isOpen}\n      onClose={closeModal}\n      {...(modal.data as ModalConfig)}\n    />\n  );\n};\n\n// 模态框Hook\nexport const useModal = () => {\n  const openModal = useAppStore(state => state.openModal);\n  const closeModal = useAppStore(state => state.closeModal);\n\n  const modal = {\n    open: (config: ModalConfig) => {\n      openModal(", "确认操作", "确认", "取消", "提示", "确定", ", modalConfig);\n      });\n    },\n\n    // 便捷方法\n    success: (content: React.ReactNode, title?: string) => {\n      return modal.alert(\n        <div className=", "成功", "错误", "警告"], "description": "Found hardcoded strings that should be translated"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/advanced/SearchSystem.tsx", "strings": [";\n  category?: string;\n  tags?: string[];\n  url: string;\n  score?: number;\n  lastModified?: string;\n}\n\n// 搜索过滤器\nexport interface SearchFilters {\n  type?: string[];\n  category?: string[];\n  tags?: string[];\n  dateRange?: {\n    start: string;\n    end: string;\n  };\n}\n\n// 排序选项\nexport type SortOption = ", ";\n\n// 搜索配置\nexport interface SearchConfig {\n  placeholder?: string;\n  showFilters?: boolean;\n  showSort?: boolean;\n  showHistory?: boolean;\n  maxResults?: number;\n  debounceMs?: number;\n}\n\n// 搜索Hook\nexport const useSearch = (\n  searchFunction: (query: string, filters: SearchFilters) => Promise<SearchResult[]>,\n  config: SearchConfig = {}\n) => {\n  const [query, setQuery] = useState(", ");\n  const [isLoading, setIsLoading] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [searchHistory, setSearchHistory] = useState<string[]>([]);\n\n  const { debounceMs = 300 } = config;\n\n  // 防抖搜索\n  const debouncedSearch = useCallback(\n    debounce(async (searchQuery: string, searchFilters: SearchFilters) => {\n      if (!searchQuery.trim()) {\n        setResults([]);\n        return;\n      }\n\n      setIsLoading(true);\n      setError(null);\n\n      try {\n        const searchResults = await searchFunction(searchQuery, searchFilters);\n        setResults(searchResults);\n        \n        // 添加到搜索历史\n        if (searchQuery.trim() && !searchHistory.includes(searchQuery)) {\n          setSearchHistory(prev => [searchQuery, ...prev.slice(0, 9)]); // 保留最近10条\n        }\n      } catch (err) {\n        setError(", ", err);\n      } finally {\n        setIsLoading(false);\n      }\n    }, debounceMs),\n    [searchFunction, debounceMs, searchHistory]\n  );\n\n  // 执行搜索\n  useEffect(() => {\n    debouncedSearch(query, filters);\n  }, [query, filters, debouncedSearch]);\n\n  // 排序结果\n  const sortedResults = useMemo(() => {\n    const sorted = [...results].sort((a, b) => {\n      let comparison = 0;\n\n      switch (sortBy) {\n        case ", ");\n    setResults([]);\n    setError(null);\n  }, []);\n\n  const clearHistory = useCallback(() => {\n    setSearchHistory([]);\n  }, []);\n\n  return {\n    query,\n    setQuery,\n    results: sortedResults,\n    filters,\n    setFilters,\n    sortBy,\n    setSortBy,\n    sortDirection,\n    setSortDirection,\n    isLoading,\n    error,\n    searchHistory,\n    clearSearch,\n    clearHistory,\n  };\n};\n\n// 防抖函数\nfunction debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout;\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout);\n    timeout = setTimeout(() => func(...args), wait);\n  };\n}\n\n// 搜索框组件\ninterface SearchBoxProps {\n  value: string;\n  onChange: (value: string) => void;\n  onClear: () => void;\n  placeholder?: string;\n  isLoading?: boolean;\n  className?: string;\n}\n\nexport const SearchBox: React.FC<SearchBoxProps> = ({\n  value,\n  onChange,\n  onClear,\n  placeholder = ", "></div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// 搜索结果组件\ninterface SearchResultsProps {\n  results: SearchResult[];\n  isLoading: boolean;\n  error: string | null;\n  onResultClick?: (result: SearchResult) => void;\n  className?: string;\n}\n\nexport const SearchResults: React.FC<SearchResultsProps> = ({\n  results,\n  isLoading,\n  error,\n  onResultClick,\n  className = ", "文章", "工具", "疗法", "指南", "内容", ">没有找到相关结果</p>\n      </div>\n    );\n  }\n\n  return (\n    <div className={", ">\n                {result.category && (\n                  <span>分类: {result.category}</span>\n                )}\n                {result.lastModified && (\n                  <span className=", " />\n                    <span>{new Date(result.lastModified).toLocaleDateString()}</span>\n                  </span>\n                )}\n                {result.score && (\n                  <span>相关度: {Math.round(result.score * 100)}%</span>\n                )}\n              </div>\n              \n              {result.tags && result.tags.length > 0 && (\n                <div className=", ">\n                      +{result.tags.length - 3}\n                    </span>\n                  )}\n                </div>\n              )}\n            </div>\n          </div>\n        </div>\n      ))}\n    </div>\n  );\n};\n\n// 搜索历史组件\ninterface SearchHistoryProps {\n  history: string[];\n  onSelect: (query: string) => void;\n  onClear: () => void;\n  className?: string;\n}\n\nexport const SearchHistory: React.FC<SearchHistoryProps> = ({\n  history,\n  onSelect,\n  onClear,\n  className = ", " />\n          <span>搜索历史</span>\n        </h4>\n        <button\n          onClick={onClear}\n          className=", "\n        >\n          清除\n        </button>\n      </div>\n      \n      <div className="], "description": "Found hardcoded strings that should be translated"}, {"type": "MISSING_TRANSLATION_KEY", "severity": "HIGH", "file": "components/advanced/ToastSystem.tsx", "key": "style", "description": "Translation key \"style\" not found in either language file"}, {"type": "HARDCODED_STRINGS", "severity": "MEDIUM", "file": "components/advanced/ToastSystem.tsx", "strings": [";\n\n// Toast配置\nexport interface ToastConfig {\n  type: ToastType;\n  message: string;\n  title?: string;\n  duration?: number; // 0 表示不自动关闭\n  action?: {\n    label: string;\n    onClick: () => void;\n  };\n  closable?: boolean;\n}\n\n// 单个Toast组件\ninterface ToastProps {\n  id: string;\n  type: ToastType;\n  message: string;\n  title?: string;\n  duration?: number;\n  action?: ToastConfig[", "];\n  closable?: boolean;\n  onClose: (id: string) => void;\n}\n\nconst Toast: React.FC<ToastProps> = ({\n  id,\n  type,\n  message,\n  title,\n  duration = 5000,\n  action,\n  closable = true,\n  onClose,\n}) => {\n  const [isVisible, setIsVisible] = useState(false);\n  const [isLeaving, setIsLeaving] = useState(false);\n\n  useEffect(() => {\n    // 入场动画\n    const timer = setTimeout(() => setIsVisible(true), 10);\n    return () => clearTimeout(timer);\n  }, []);\n\n  useEffect(() => {\n    // 自动关闭\n    if (duration > 0) {\n      const timer = setTimeout(() => {\n        handleClose();\n      }, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [duration]);\n\n  const handleClose = () => {\n    setIsLeaving(true);\n    setTimeout(() => {\n      onClose(id);\n    }, 300); // 等待退场动画完成\n  };\n\n  const getIcon = () => {\n    switch (type) {\n      case ", ">\n        {/* 图标 */}\n        <div className=", ">\n          {getIcon()}\n        </div>\n\n        {/* 内容 */}\n        <div className=", "}>\n            {message}\n          </p>\n\n          {/* 操作按钮 */}\n          {action && (\n            <button\n              onClick={action.onClick}\n              className={", "}\n            >\n              {action.label}\n            </button>\n          )}\n        </div>\n\n        {/* 关闭按钮 */}\n        {closable && (\n          <button\n            onClick={handleClose}\n            className={", " />\n          </button>\n        )}\n      </div>\n\n      {/* 进度条（如果有持续时间） */}\n      {duration > 0 && (\n        <div className=", ",\n            }}\n          />\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Toast容器组件\nexport const ToastContainer: React.FC = () => {\n  const [mounted, setMounted] = useState(false);\n  const toasts = useToasts();\n  const removeToast = useAppStore(state => state.removeToast);\n\n  useEffect(() => {\n    setMounted(true);\n  }, []);\n\n  if (!mounted) return null;\n\n  const container = (\n    <div className=", ",\n        message,\n        duration: 0, // 错误消息默认不自动关闭\n        ...options,\n      });\n    },\n\n    warning: (message: string, options?: Partial<ToastConfig>) => {\n      return addToast({\n        type: ", ",\n        message,\n        ...options,\n      });\n    },\n\n    custom: (config: ToastConfig) => {\n      return addToast(config);\n    },\n\n    dismiss: (id: string) => {\n      removeToast(id);\n    },\n\n    dismissAll: () => {\n      clearToasts();\n    },\n  };\n\n  return toast;\n};\n\n// 添加CSS动画\nconst toastStyles = ", ";\n\n// 注入样式\nif (typeof document !== "], "description": "Found hardcoded strings that should be translated"}], "recommendations": ["Replace all hardcoded strings with translation keys", "Ensure all components use translation hooks", "Add translation key validation to build process", "Implement automated translation coverage monitoring", "Create translation key naming conventions"]}