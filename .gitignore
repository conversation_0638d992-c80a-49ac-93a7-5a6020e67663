# =============================
# 依赖包与构建产物
# =============================
node_modules/
.next/              # Next.js 编译输出目录
dist/               # 构建打包目录（如使用自定义构建脚本）
build/              # 常见构建输出目录
out/                # 输出目录（如 next export 的静态站点）

# =============================
# 环境配置文件（本地/开发/测试/生产）
# =============================
.env.local
.env.development.local
.env.test.local
.env.production.local
.env.*.local        # 忽略其他环境变量文件，如 .env.staging.local

# =============================
# 系统与编辑器生成文件
# =============================
.DS_Store           # macOS 文件
*.swp               # Vim 交换文件
*.swo               # Vim 交换文件
*.swn               # Vim 交换文件
Thumbs.db           # Windows 缩略图缓存
desktop.ini         # Windows 配置文件
*.tmp               # 临时文件
*~                  # Emacs/Vim 产生的临时文件
*.bak               # 备份文件

# =============================
# 编辑器/IDE 配置目录
# =============================
.idea/              # JetBrains 系列 IDE（如 WebStorm）
.vscode/            # VS Code
*.sublime-project   # Sublime Text 项目文件
*.sublime-workspace # Sublime Text 工作区文件

# =============================
# 临时文件与日志
# =============================
temp/
tmp/
*.log               # 日志文件

# =============================
# 测试与覆盖率报告
# =============================
coverage/           # Jest / Istanbul 覆盖率报告
jest-results.json   # Jest 测试结果文件
__pycache__/        # Python 缓存目录（如有混合使用）
.pytest_cache/      # Pytest 缓存目录（如有混合使用）

# =============================
# 项目特定的临时目录（根据结构添加）
# =============================
app/[locale]/build-temp/
app/[locale]/temp/
components/ui/temp/
components/ui/build-temp/
content/articles/temp/
content/articles/build-temp/
lib/temp/
lib/build-temp/

# =============================
# 参考文件和开发文档（不需要上传）
# =============================
# 参考设计文件
DmlIfK7/
Fe0vZnx/
I4VMuV8/
JYGUpPc/
K-RaAtJ/
LtbKQFw/
Vd3TY4R/
i1Y6OJY/
iu8oy9h/
lRaT0pS/
m2Nw3KX/
qIbnD7v/
zfrg_wn_副本/

# 开发报告和文档
*.md
!README.md          # 保留 README.md
!USER_GUIDE.md      # 保留用户指南

# 设计参考文档
*.txt
Periodhub.health*.txt

# =============================
# TypeScript 和构建相关
# =============================
*.tsbuildinfo
next-env.d.ts

# =============================
# 部署和生产环境
# =============================
.vercel
.deployment
deploy.sh

# =============================
# 缓存和性能
# =============================
.cache/
.tmp/
.temp/
.clinic/
.0x/

# =============================
# 安全相关
# =============================
.secrets
*.key
*.pem
*.p12
*.pfx

# =============================
# 用户数据和上传内容
# =============================
/public/uploads/
/public/user-uploads/
/public/generated/
/public/cache/
/data/exports/
/data/backups/
user-data.json
analytics.json

# =============================
# 开发工具配置
# =============================
.eslintrc.local.js
.prettierrc.local.js
webpack.config.local.js

# =============================
# 监控和日志
# =============================
/logs/
*.access.log
*.error.log
monitoring.json

# =============================
# 包管理器锁定文件（根据使用的包管理器选择保留）
# =============================
# package-lock.json  # 如果使用 yarn，取消注释此行
# yarn.lock          # 如果使用 npm，取消注释此行
# pnpm-lock.yaml     # 如果使用 pnpm，取消注释此行
