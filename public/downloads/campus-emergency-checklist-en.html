<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Campus Emergency Checklist - periodhub.health</title>
    <style>
        body {
            font-family: 'Arial', 'Helvetica', sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            background: #fff;
        }
        .header {
            text-align: center;
            border-bottom: 3px solid #e91e63;
            padding-bottom: 20px;
            margin-bottom: 30px;
        }
        .header h1 {
            color: #e91e63;
            font-size: 28px;
            margin: 0;
        }
        .header p {
            color: #666;
            font-size: 14px;
            margin: 10px 0 0 0;
        }
        .section {
            margin-bottom: 25px;
            page-break-inside: avoid;
        }
        .section h2 {
            color: #e91e63;
            font-size: 18px;
            border-left: 4px solid #e91e63;
            padding-left: 15px;
            margin-bottom: 15px;
        }
        .section h3 {
            color: #333;
            font-size: 16px;
            margin-bottom: 10px;
        }
        .checklist {
            list-style: none;
            padding: 0;
        }
        .checklist li {
            margin-bottom: 8px;
            padding-left: 25px;
            position: relative;
        }
        .checklist li:before {
            content: "☐";
            position: absolute;
            left: 0;
            color: #e91e63;
            font-weight: bold;
        }
        .emergency-box {
            background: #fff3e0;
            border: 2px solid #ff9800;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .emergency-box h3 {
            color: #ff9800;
            margin-top: 0;
        }
        .contact-info {
            background: #f3e5f5;
            border: 2px solid #9c27b0;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .contact-info h3 {
            color: #9c27b0;
            margin-top: 0;
        }
        .tips {
            background: #e8f5e8;
            border-left: 4px solid #4caf50;
            padding: 15px;
            margin: 15px 0;
        }
        .tips h3 {
            color: #4caf50;
            margin-top: 0;
        }
        .footer {
            text-align: center;
            margin-top: 40px;
            padding-top: 20px;
            border-top: 1px solid #ddd;
            color: #666;
            font-size: 12px;
        }
        @media print {
            body { margin: 0; padding: 15px; }
            .section { page-break-inside: avoid; }
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>Campus Emergency Checklist</h1>
        <p>Menstrual Emergency Response Guide | periodhub.health</p>
        <p>For Middle School & University Campus Environments</p>
    </div>

    <div class="section">
        <h2>📦 Emergency Kit Essentials</h2>
        
        <h3>Basic Hygiene Products</h3>
        <ul class="checklist">
            <li>Sanitary pads (2-3 regular, 1-2 overnight)</li>
            <li>Tampons (if you use them)</li>
            <li>Panty liners (2-3 pieces)</li>
            <li>Wet wipes (1 pack)</li>
            <li>Small hand sanitizer</li>
            <li>Disposable underwear (1-2 pairs)</li>
            <li>Sealed bags (for used products)</li>
        </ul>

        <h3>Pain Relief Items</h3>
        <ul class="checklist">
            <li>Ibuprofen or acetaminophen (follow dosage instructions)</li>
            <li>Heat patches (2-3 pieces)</li>
            <li>Small hot water bottle</li>
            <li>Peppermint balm (for headaches)</li>
        </ul>

        <h3>Other Necessities</h3>
        <ul class="checklist">
            <li>Chocolate or energy bars</li>
            <li>Insulated water bottle</li>
            <li>Small towel</li>
            <li>Emergency cash</li>
            <li>Emergency contact card</li>
        </ul>
    </div>

    <div class="emergency-box">
        <h3>🚨 Emergency Situations</h3>
        <p><strong>Seek immediate help if you experience:</strong></p>
        <ul>
            <li>Severe pain preventing normal walking or standing</li>
            <li>Heavy bleeding (soaking through a super pad in 1 hour)</li>
            <li>Fever, vomiting, or fainting</li>
            <li>Worsening pain unrelieved by pain medication</li>
        </ul>
    </div>

    <div class="section">
        <h2>🏫 Campus Help Resources</h2>
        
        <h3>First Contact</h3>
        <ul class="checklist">
            <li>School health center/nurse's office</li>
            <li>Dormitory supervisor</li>
            <li>Homeroom teacher or advisor</li>
            <li>Roommates or classmates</li>
        </ul>

        <h3>Off-Campus Resources</h3>
        <ul class="checklist">
            <li>Nearby pharmacy locations</li>
            <li>Nearest hospital emergency room</li>
            <li>Parent/guardian contact information</li>
            <li>Emergency services (911 for severe situations)</li>
        </ul>
    </div>

    <div class="contact-info">
        <h3>📞 Emergency Contact Information</h3>
        <p><strong>Please fill out and carry with you:</strong></p>
        <p>Name: ___________________ Class: ___________________</p>
        <p>Dorm Address: ___________________________________________</p>
        <p>Parent/Guardian: _____________ Phone: ___________________</p>
        <p>Roommate: _____________ Phone: ___________________</p>
        <p>School Health Center: _____________________________</p>
        <p>Nearby Hospital: _______________________________</p>
    </div>

    <div class="section">
        <h2>💡 Emergency Response Steps</h2>
        
        <h3>When Pain Strikes</h3>
        <ol>
            <li><strong>Find a safe place to rest</strong> - classroom, library, dorm</li>
            <li><strong>Apply heat</strong> - use heat patch on lower abdomen or lower back</li>
            <li><strong>Take pain medication</strong> - follow dosage instructions, don't take on empty stomach</li>
            <li><strong>Notify someone nearby</strong> - tell a classmate or teacher about your situation</li>
            <li><strong>Record symptoms</strong> - pain level, duration, accompanying symptoms</li>
        </ol>

        <h3>When Period Starts Unexpectedly</h3>
        <ol>
            <li><strong>Stay calm</strong> - this is a normal physiological process</li>
            <li><strong>Use hygiene products immediately</strong> - ask classmates or teachers for help if needed</li>
            <li><strong>Check clothing</strong> - rinse with cold water or change if stained</li>
            <li><strong>Adjust your plans</strong> - avoid intense exercise, rest appropriately</li>
        </ol>
    </div>

    <div class="tips">
        <h3>💚 Daily Prevention Tips</h3>
        <ul>
            <li><strong>Track your cycle</strong> - use apps or calendar to monitor</li>
            <li><strong>Prepare in advance</strong> - carry supplies 3 days before expected period</li>
            <li><strong>Maintain good habits</strong> - adequate sleep, regular meals</li>
            <li><strong>Exercise moderately</strong> - maintain light exercise routine</li>
            <li><strong>Learn to ask for help</strong> - don't suffer in silence due to embarrassment</li>
        </ul>
    </div>

    <div class="section">
        <h2>🏥 When to See a Doctor</h2>
        <ul class="checklist">
            <li>Severe menstrual pain affecting studies and daily life</li>
            <li>Pain medication doesn't provide relief</li>
            <li>Abnormally heavy or light menstrual flow</li>
            <li>Severely irregular menstrual cycles</li>
            <li>Other abnormal symptoms present</li>
        </ul>
    </div>

    <div class="footer">
        <p>This checklist is created by the periodhub.health professional team</p>
        <p>For more resources visit: www.periodhub.health</p>
        <p>Seek medical attention for emergencies - this checklist is for reference only</p>
    </div>
</body>
</html>
