/* NSAID Interactive Components Styles */

/* Animation Player Styles */
.nsaid-animation-player {
  margin: 2rem 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.animation-container {
  padding: 1.5rem;
}

.video-player-container {
  position: relative;
  width: 100%;
  aspect-ratio: 16 / 9;
  background: #1e293b;
  border-radius: 8px;
  overflow: hidden;
  margin-bottom: 1.5rem;
}

.animation-video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.narration-section {
  background: #f0f9ff;
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid #0ea5e9;
  margin-bottom: 1.5rem;
}

.scene-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: #0c4a6e;
  margin-bottom: 0.5rem;
}

.narration-text {
  color: #475569;
  line-height: 1.6;
  margin: 0;
}

.navigation-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.nav-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1rem;
  background: #0ea5e9;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
}

.nav-button:hover:not(:disabled) {
  background: #0284c7;
  transform: translateY(-1px);
}

.nav-button:disabled {
  background: #94a3b8;
  cursor: not-allowed;
  transform: none;
}

.scene-indicator {
  font-weight: 600;
  color: #0ea5e9;
  font-size: 0.875rem;
}

/* Calculator Styles */
.nsaid-calculator {
  margin: 2rem 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;
}

.calculator-container {
  padding: 1.5rem;
  width: 100%;
}

.calculator-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: #0ea5e9;
  text-align: center;
  margin-bottom: 1.5rem;
}

.calculator-form {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  margin-bottom: 0.375rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
}

.form-select,
.form-input {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.form-select:focus,
.form-input:focus {
  outline: none;
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.calculate-button {
  width: 100%;
  background: #dc2626;
  color: white;
  padding: 0.75rem 1rem;
  border: none;
  border-radius: 6px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  margin: 0.5rem 0 0 0;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  font-size: 1rem;
  box-shadow: 0 2px 4px rgba(220, 38, 38, 0.2);
}

.calculate-button:hover {
  background: #b91c1c;
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(220, 38, 38, 0.3);
}

.calculate-button:active {
  transform: translateY(0);
  box-shadow: 0 1px 2px rgba(220, 38, 38, 0.2);
}

.dose-result {
  margin-top: 1.5rem;
  background: #f0f9ff;
  border: 1px solid #0ea5e9;
  border-radius: 8px;
  padding: 1rem;
}

.dose-result.hidden {
  display: none;
}

.result-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #0ea5e9;
  margin-bottom: 0.75rem;
}

.result-content p {
  margin: 0.5rem 0;
  color: #374151;
}

.result-notes {
  font-size: 0.875rem;
  color: #6b7280;
  margin-top: 0.5rem;
}

.disclaimer {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  margin-top: 1.5rem;
  padding: 0.75rem;
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 6px;
  font-size: 0.875rem;
  color: #92400e;
}

.disclaimer i {
  flex-shrink: 0;
  width: 1rem;
  height: 1rem;
  margin-top: 0.125rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .animation-container,
  .calculator-container {
    padding: 1rem;
  }

  .nsaid-calculator {
    margin: 1rem 0;
    max-width: 100%;
  }

  .calculator-title {
    font-size: 1.25rem;
    margin-bottom: 1rem;
  }

  .calculator-form {
    gap: 0.5rem;
  }

  .form-group {
    margin-bottom: 0.375rem;
  }

  .form-label {
    font-size: 0.8125rem;
    margin-bottom: 0.25rem;
  }

  .form-select, .form-input {
    padding: 0.625rem;
    font-size: 0.9375rem;
  }

  .calculate-button {
    padding: 0.625rem;
    font-size: 0.9375rem;
    margin: 0.5rem 0 0 0;
  }

  .navigation-controls {
    flex-direction: column;
    gap: 0.75rem;
  }

  .nav-button {
    width: 100%;
    justify-content: center;
  }

  .scene-indicator {
    order: -1;
  }
}

@media (max-width: 480px) {
  .nsaid-calculator {
    padding: 0.75rem;
  }

  .calculator-title {
    font-size: 1.125rem;
  }

  .form-select, .form-input {
    padding: 0.5rem;
    font-size: 0.875rem;
  }

  .calculate-button {
    padding: 0.5rem;
    font-size: 0.875rem;
  }
}

/* Loading States */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid #0ea5e9;
  border-radius: 50%;
  border-top-color: transparent;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error States */
.error {
  background: #fef2f2;
  border-color: #ef4444;
  color: #dc2626;
}

.error .scene-title,
.error .result-title {
  color: #dc2626;
}
