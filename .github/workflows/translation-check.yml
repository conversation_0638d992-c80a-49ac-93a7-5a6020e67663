name: Translation System Check

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]
    paths:
      - 'messages/**'
      - 'app/**'
      - 'components/**'
      - 'hooks/**'

jobs:
  translation-validation:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run translation validation
      run: npm run validate:translations

    - name: Run translation quality check
      run: node scripts/translation-quality-check.js

    - name: Check for hardcoded text
      run: |
        echo "Checking for hardcoded Chinese text..."
        if grep -r "[\u4e00-\u9fff]" app/ --include="*.tsx" --include="*.ts" --exclude-dir=node_modules | grep -v "// " | grep -v "* " | head -10; then
          echo "⚠️ Found potential hardcoded Chinese text. Please review."
          exit 1
        else
          echo "✅ No hardcoded Chinese text found."
        fi

  unit-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Run translation unit tests
      run: npm test tests/translation-system.test.js

  e2e-tests:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Install Playwright
      run: npx playwright install

    - name: Build application
      run: npm run build

    - name: Start application
      run: npm start &
      
    - name: Wait for application
      run: npx wait-on http://localhost:3000

    - name: Run E2E translation tests
      run: npm test tests/e2e/translation-e2e.test.js
      env:
        TEST_URL: http://localhost:3000

  translation-coverage:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Setup Node.js
      uses: actions/setup-node@v3
      with:
        node-version: '20'
        cache: 'npm'

    - name: Install dependencies
      run: npm ci

    - name: Generate translation coverage report
      run: |
        node scripts/translation-quality-check.js > translation-report.txt
        cat translation-report.txt

    - name: Upload coverage report
      uses: actions/upload-artifact@v3
      with:
        name: translation-coverage-report
        path: translation-report.txt

    - name: Comment PR with results
      if: github.event_name == 'pull_request'
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const report = fs.readFileSync('translation-report.txt', 'utf8');
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: `## 🌐 Translation Quality Report\n\n\`\`\`\n${report}\n\`\`\``
          });

  security-check:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3

    - name: Check for sensitive data in translations
      run: |
        echo "Checking for sensitive data in translation files..."
        if grep -r -i "password\|secret\|key\|token" messages/ || grep -r "[\w\.-]+@[\w\.-]+\.\w+" messages/; then
          echo "⚠️ Found potential sensitive data in translation files."
          exit 1
        else
          echo "✅ No sensitive data found in translation files."
        fi
