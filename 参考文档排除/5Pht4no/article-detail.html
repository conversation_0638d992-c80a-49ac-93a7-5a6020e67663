<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>[在此处填写文章标题] | period.health</title>
    <meta name="description" content="[在此处填写文章描述]">
    <meta name="keywords" content="[在此处填写文章关键词]">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- Navigation Header -->
    <header class="bg-white shadow-sm border-b">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-purple-600">period.health</div>
                <ul class="hidden md:flex space-x-6">
                    <li><a href="index.html" class="text-gray-600 hover:text-purple-600 transition-colors">首页</a></li>
                    <li><a href="interactive-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">互动解决方案</a></li>
                    <li><a href="instant-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">即时解决方案</a></li>
                    <li><a href="regular-conditioning.html" class="text-gray-600 hover:text-purple-600 transition-colors">平时调理</a></li>
                    <li><a href="medical-research.html" class="text-gray-600 hover:text-purple-600 transition-colors">医学原理</a></li>
                    <li><a href="blog-center.html" class="text-gray-600 hover:text-purple-600 transition-colors">文章中心</a></li>
                </ul>
                <button class="md:hidden p-2" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <div class="md:hidden hidden mt-4 pb-4" id="mobile-menu">
                <a href="index.html" class="block py-2 text-gray-600">首页</a>
                <a href="interactive-solutions.html" class="block py-2 text-gray-600">互动解决方案</a>
                <a href="instant-solutions.html" class="block py-2 text-gray-600">即时解决方案</a>
                <a href="regular-conditioning.html" class="block py-2 text-gray-600">平时调理</a>
                <a href="medical-research.html" class="block py-2 text-gray-600">医学原理</a>
                <a href="blog-center.html" class="block py-2 text-gray-600">文章中心</a>
            </div>
        </nav>
    </header>

    <main>
        <!-- Breadcrumb -->
        <section class="bg-white py-4 border-b">
            <div class="container mx-auto px-4">
                <nav class="text-sm text-gray-600">
                    <a href="index.html" class="hover:text-purple-600">首页</a>
                    <span class="mx-2">/</span>
                    <a href="blog-center.html" class="hover:text-purple-600">文章中心</a>
                    <span class="mx-2">/</span>
                    <span class="text-gray-800">当前文章</span>
                </nav>
            </div>
        </section>

        <!-- Article Content -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <!-- Article Header -->
                    <div class="mb-8">
                        <div class="flex items-center mb-4">
                            <span class="bg-purple-100 text-purple-700 px-3 py-1 rounded-full text-sm font-semibold">[文章分类占位符]</span>
                            <span class="ml-3 text-gray-500 text-sm">[发布日期占位符] • [阅读时长]分钟阅读</span>
                        </div>
                        
                        <h1 class="text-4xl font-bold mb-4 leading-tight">[文章标题占位符]</h1>
                        
                        <div class="flex items-center mb-6">
                            <div class="flex items-center mr-6">
                                <div class="w-10 h-10 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                                    <span class="text-purple-600 font-semibold text-sm">[作者首字母]</span>
                                </div>
                                <div>
                                    <p class="font-semibold text-sm">[作者姓名占位符]</p>
                                    <p class="text-gray-600 text-xs">[作者职称或描述]</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center text-gray-500 text-sm gap-4">
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                    </svg>
                                    [阅读量占位符]
                                </span>
                                <span class="flex items-center">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                    [点赞数占位符]
                                </span>
                            </div>
                        </div>
                        
                        <!-- Featured Image -->
                        <div class="w-full h-64 bg-gradient-to-br from-purple-100 to-pink-100 rounded-lg flex items-center justify-center mb-8">
                            <p class="text-gray-500">[文章主图占位符]</p>
                        </div>
                    </div>

                    <!-- Article Body -->
                    <div class="prose prose-lg max-w-none">
                        <!-- Introduction -->
                        <div class="bg-blue-50 border-l-4 border-blue-400 p-6 mb-8">
                            <h2 class="text-lg font-semibold mb-2">文章摘要</h2>
                            <p class="text-gray-700">[在此处填写文章摘要或引言，简要概述文章的主要内容和要点]</p>
                        </div>

                        <!-- Content Sections Placeholder -->
                        <div class="space-y-8">
                            <section>
                                <h2 class="text-2xl font-semibold mb-4">[章节标题占位符]</h2>
                                <p class="mb-4">[正文内容占位符 - 这里将填充具体的文章内容，包括段落、列表、引用等。内容应包含有价值的信息、科学依据、实用建议等。]</p>
                                
                                <ul class="list-disc list-inside mb-4 space-y-2">
                                    <li>[列表项目占位符 1]</li>
                                    <li>[列表项目占位符 2]</li>
                                    <li>[列表项目占位符 3]</li>
                                </ul>
                                
                                <p>[续接段落占位符...]</p>
                            </section>
                            
                            <section>
                                <h2 class="text-2xl font-semibold mb-4">[第二章节标题占位符]</h2>
                                <p class="mb-4">[第二章节内容占位符...]</p>
                                
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-4">
                                    <h4 class="font-semibold text-yellow-800 mb-2">重要提示</h4>
                                    <p class="text-yellow-700 text-sm">[重要信息或注意事项占位符]</p>
                                </div>
                            </section>
                            
                            <section>
                                <h2 class="text-2xl font-semibold mb-4">[第三章节标题占位符]</h2>
                                <p class="mb-4">[第三章节内容占位符...]</p>
                                
                                <!-- 引用框 -->
                                <blockquote class="border-l-4 border-purple-400 pl-6 py-4 bg-purple-50 italic mb-4">
                                    "[专家引言或重要引用占位符]" —— [引用来源]
                                </blockquote>
                            </section>
                        </div>

                        <!-- Key Takeaways -->
                        <div class="bg-green-50 border border-green-200 rounded-lg p-6 mt-8">
                            <h3 class="text-lg font-semibold text-green-800 mb-3">要点总结</h3>
                            <ul class="space-y-2 text-green-700">
                                <li class="flex items-start">
                                    <span class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                    [要点总结 1 占位符]
                                </li>
                                <li class="flex items-start">
                                    <span class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                    [要点总结 2 占位符]
                                </li>
                                <li class="flex items-start">
                                    <span class="w-2 h-2 bg-green-500 rounded-full mt-2 mr-3 flex-shrink-0"></span>
                                    [要点总结 3 占位符]
                                </li>
                            </ul>
                        </div>

                        <!-- Medical Disclaimer -->
                        <div class="bg-red-50 border border-red-200 rounded-lg p-6 mt-8">
                            <h4 class="font-semibold text-red-800 mb-2">医学免责声明</h4>
                            <p class="text-red-700 text-sm">本文内容仅供教育和信息目的，不能替代专业医疗建议、诊断或治疗。如有任何健康问题或疑虑，请咨询合格的医疗专业人员。</p>
                        </div>
                    </div>

                    <!-- Article Tags -->
                    <div class="mt-8 pt-8 border-t border-gray-200">
                        <h4 class="font-semibold mb-3">标签：</h4>
                        <div class="flex flex-wrap gap-2">
                            <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">[标签1]</span>
                            <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">[标签2]</span>
                            <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">[标签3]</span>
                            <span class="bg-gray-100 text-gray-700 px-3 py-1 rounded-full text-sm">[标签4]</span>
                        </div>
                    </div>

                    <!-- Share and Like -->
                    <div class="mt-8 pt-8 border-t border-gray-200">
                        <div class="flex items-center justify-between">
                            <div class="flex items-center gap-4">
                                <button class="flex items-center bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                    点赞文章
                                </button>
                                
                                <button class="flex items-center border border-gray-300 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-50 transition-colors">
                                    <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z"></path>
                                    </svg>
                                    收藏
                                </button>
                            </div>
                            
                            <div class="flex items-center gap-2">
                                <span class="text-gray-600 text-sm">分享到：</span>
                                <button class="w-8 h-8 bg-blue-600 text-white rounded-full flex items-center justify-center hover:bg-blue-700">
                                    <span class="text-xs font-bold">f</span>
                                </button>
                                <button class="w-8 h-8 bg-green-600 text-white rounded-full flex items-center justify-center hover:bg-green-700">
                                    <span class="text-xs font-bold">W</span>
                                </button>
                                <button class="w-8 h-8 bg-red-600 text-white rounded-full flex items-center justify-center hover:bg-red-700">
                                    <span class="text-xs font-bold">微</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Related Articles -->
        <section class="py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-2xl font-bold mb-8">相关文章推荐</h2>
                    
                    <div class="grid md:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-lg transition-shadow">
                            <div class="h-32 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
                                <p class="text-gray-500 text-sm">[相关文章图片]</p>
                            </div>
                            <div class="p-4">
                                <div class="flex items-center mb-2">
                                    <span class="bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs font-semibold">医学科普</span>
                                </div>
                                <h3 class="font-semibold mb-2">[相关文章标题占位符 1]</h3>
                                <p class="text-gray-600 text-sm mb-3">[文章简介占位符...]</p>
                                <a href="article-detail.html" class="text-purple-600 text-sm font-semibold hover:underline">阅读全文 →</a>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-lg transition-shadow">
                            <div class="h-32 bg-gradient-to-br from-blue-100 to-cyan-100 flex items-center justify-center">
                                <p class="text-gray-500 text-sm">[相关文章图片]</p>
                            </div>
                            <div class="p-4">
                                <div class="flex items-center mb-2">
                                    <span class="bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-semibold">用户故事</span>
                                </div>
                                <h3 class="font-semibold mb-2">[相关文章标题占位符 2]</h3>
                                <p class="text-gray-600 text-sm mb-3">[文章简介占位符...]</p>
                                <a href="article-detail.html" class="text-purple-600 text-sm font-semibold hover:underline">阅读全文 →</a>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow-sm overflow-hidden hover:shadow-lg transition-shadow">
                            <div class="h-32 bg-gradient-to-br from-green-100 to-teal-100 flex items-center justify-center">
                                <p class="text-gray-500 text-sm">[相关文章图片]</p>
                            </div>
                            <div class="p-4">
                                <div class="flex items-center mb-2">
                                    <span class="bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-semibold">生活方式</span>
                                </div>
                                <h3 class="font-semibold mb-2">[相关文章标题占位符 3]</h3>
                                <p class="text-gray-600 text-sm mb-3">[文章简介占位符...]</p>
                                <a href="article-detail.html" class="text-purple-600 text-sm font-semibold hover:underline">阅读全文 →</a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">period.health</h3>
                    <p class="text-gray-400 mb-4">专业的经期疼痛管理平台，为女性健康保驾护航。</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">核心功能</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="interactive-solutions.html" class="hover:text-white">症状评估器</a></li>
                        <li><a href="interactive-solutions.html" class="hover:text-white">个性化方案</a></li>
                        <li><a href="instant-solutions.html" class="hover:text-white">即时缓解</a></li>
                        <li><a href="regular-conditioning.html" class="hover:text-white">平时调理</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">专业内容</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="medical-research.html" class="hover:text-white">医学研究</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">健康资讯</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">用户故事</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">专家观点</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">法律信息</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="privacy-policy.html" class="hover:text-white">隐私政策</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white">服务条款</a></li>
                        <li><a href="#" class="hover:text-white">联系我们</a></li>
                        <li><a href="#" class="hover:text-white">免责声明</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 period.health. 本网站内容仅供参考，不能替代专业医疗建议。</p>
            </div>
        </div>
    </footer>

    <script src="main.js"></script>
</body>
</html>













