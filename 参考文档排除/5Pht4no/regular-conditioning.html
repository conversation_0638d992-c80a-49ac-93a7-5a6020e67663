<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>平时调理 - 长期痛经预防与生活方式调节 | period.health</title>
    <meta name="description" content="科学的长期痛经预防方案，包括营养调理、运动疗法、压力管理和生活方式优化，从根本上改善经期健康。">
    <meta name="keywords" content="痛经预防, 生活方式调理, 营养疗法, 运动疗法, 压力管理, 经期健康">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- Navigation Header -->
    <header class="bg-white shadow-sm border-b">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-purple-600">period.health</div>
                <ul class="hidden md:flex space-x-6">
                    <li><a href="index.html" class="text-gray-600 hover:text-purple-600 transition-colors">首页</a></li>
                    <li><a href="interactive-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">互动解决方案</a></li>
                    <li><a href="instant-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">即时解决方案</a></li>
                    <li><a href="regular-conditioning.html" class="text-purple-600 font-semibold border-b-2 border-purple-600 pb-1" aria-current="page">平时调理</a></li>
                    <li><a href="medical-research.html" class="text-gray-600 hover:text-purple-600 transition-colors">医学原理</a></li>
                    <li><a href="blog-center.html" class="text-gray-600 hover:text-purple-600 transition-colors">文章中心</a></li>
                </ul>
                <button class="md:hidden p-2" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <div class="md:hidden hidden mt-4 pb-4" id="mobile-menu">
                <a href="index.html" class="block py-2 text-gray-600">首页</a>
                <a href="interactive-solutions.html" class="block py-2 text-gray-600">互动解决方案</a>
                <a href="instant-solutions.html" class="block py-2 text-gray-600">即时解决方案</a>
                <a href="regular-conditioning.html" class="block py-2 text-purple-600 font-semibold" aria-current="page">平时调理</a>
                <a href="medical-research.html" class="block py-2 text-gray-600">医学原理</a>
                <a href="blog-center.html" class="block py-2 text-gray-600">文章中心</a>
            </div>
        </nav>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="bg-gradient-to-r from-green-500 to-teal-600 text-white py-16">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto text-center">
                    <h1 class="text-4xl md:text-5xl font-bold mb-6">平时调理方案</h1>
                    <p class="text-xl md:text-2xl opacity-90 mb-8">通过科学的生活方式调整，从根本上预防和减轻痛经</p>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 max-w-2xl mx-auto">
                        <h2 class="text-lg font-semibold mb-3">🌱 长期改善</h2>
                        <p class="text-sm opacity-90">坚持科学的调理方法，3-6个月内显著改善痛经症状</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Lifestyle Hero Image -->
        <section class="py-12 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <img src="https://r2.flowith.net/files/o/<EMAIL>" 
                         alt="展示健康生活方式的概念图，包括均衡饮食、规律运动、充足睡眠和压力管理等日常调理元素" 
                         class="w-full h-auto rounded-lg shadow-lg">
                </div>
            </div>
        </section>

        <!-- Four Pillars of Conditioning -->
        <section class="py-16 bg-gray-100" id="nutritional-therapy">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <h2 class="text-3xl font-bold text-center mb-12">四大调理支柱</h2>
                    
                    <!-- Nutritional Therapy -->
                    <div class="bg-white rounded-lg shadow-lg p-8 mb-12">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mr-6">
                                <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-green-700 mb-2">营养疗法</h3>
                                <p class="text-green-600">通过科学饮食调节激素平衡，减少炎症</p>
                            </div>
                        </div>
                        
                        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <!-- Anti-inflammatory Foods -->
                            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                                <h4 class="text-lg font-semibold text-green-700 mb-4">抗炎食物</h4>
                                <div class="space-y-3">
                                    <div class="flex items-start">
                                        <span class="text-green-500 mr-3 mt-1">🐟</span>
                                        <div>
                                            <strong>深海鱼类</strong>
                                            <p class="text-sm text-green-600">富含Omega-3脂肪酸，每周2-3次</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-green-500 mr-3 mt-1">🥬</span>
                                        <div>
                                            <strong>绿叶蔬菜</strong>
                                            <p class="text-sm text-green-600">菠菜、羽衣甘蓝，富含镁和叶酸</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-green-500 mr-3 mt-1">🫐</span>
                                        <div>
                                            <strong>莓类水果</strong>
                                            <p class="text-sm text-green-600">蓝莓、草莓，抗氧化剂丰富</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-green-500 mr-3 mt-1">🥜</span>
                                        <div>
                                            <strong>坚果种子</strong>
                                            <p class="text-sm text-green-600">核桃、亚麻籽，健康脂肪源</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Key Nutrients -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                                <h4 class="text-lg font-semibold text-blue-700 mb-4">关键营养素</h4>
                                <div class="space-y-3">
                                    <div class="bg-white rounded p-3">
                                        <strong class="text-blue-600">镁 (300-400mg/天)</strong>
                                        <p class="text-sm text-blue-600 mt-1">放松肌肉，减少痉挛</p>
                                        <p class="text-xs text-gray-600">来源：黑巧克力、香蕉、杏仁</p>
                                    </div>
                                    <div class="bg-white rounded p-3">
                                        <strong class="text-blue-600">维生素D (1000-2000IU/天)</strong>
                                        <p class="text-sm text-blue-600 mt-1">调节免疫，减少炎症</p>
                                        <p class="text-xs text-gray-600">来源：阳光照射、鱼肝油</p>
                                    </div>
                                    <div class="bg-white rounded p-3">
                                        <strong class="text-blue-600">Omega-3 (1-2g/天)</strong>
                                        <p class="text-sm text-blue-600 mt-1">抑制前列腺素合成</p>
                                        <p class="text-xs text-gray-600">来源：鱼油、亚麻籽油</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Foods to Limit -->
                            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                                <h4 class="text-lg font-semibold text-red-700 mb-4">建议限制</h4>
                                <div class="space-y-3">
                                    <div class="flex items-start">
                                        <span class="text-red-500 mr-3 mt-1">⚠️</span>
                                        <div>
                                            <strong>精制糖</strong>
                                            <p class="text-sm text-red-600">加重炎症反应，影响激素平衡</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-red-500 mr-3 mt-1">☕</span>
                                        <div>
                                            <strong>过量咖啡因</strong>
                                            <p class="text-sm text-red-600">可能增加紧张感，限制在2杯/天</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-red-500 mr-3 mt-1">🧂</span>
                                        <div>
                                            <strong>高钠食物</strong>
                                            <p class="text-sm text-red-600">加重水肿和腹胀</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-red-500 mr-3 mt-1">🥩</span>
                                        <div>
                                            <strong>过多红肉</strong>
                                            <p class="text-sm text-red-600">可能促进炎症，适量食用</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Weekly Meal Plan -->
                        <div class="mt-8 bg-green-50 border border-green-200 rounded-lg p-6">
                            <h4 class="text-lg font-semibold text-green-700 mb-4">💡 调理期推荐食谱</h4>
                            <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-4">
                                <div class="bg-white rounded p-3">
                                    <strong class="text-green-600">早餐</strong>
                                    <p class="text-sm text-gray-700">燕麦 + 蓝莓 + 核桃 + 豆奶</p>
                                </div>
                                <div class="bg-white rounded p-3">
                                    <strong class="text-green-600">午餐</strong>
                                    <p class="text-sm text-gray-700">三文鱼 + 糙米 + 蒸蔬菜</p>
                                </div>
                                <div class="bg-white rounded p-3">
                                    <strong class="text-green-600">晚餐</strong>
                                    <p class="text-sm text-gray-700">豆腐 + 菠菜汤 + 少量糙米</p>
                                </div>
                                <div class="bg-white rounded p-3">
                                    <strong class="text-green-600">零食</strong>
                                    <p class="text-sm text-gray-700">杏仁 + 绿茶 + 黑巧克力</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Exercise Therapy -->
                    <div class="bg-white rounded-lg shadow-lg p-8 mb-12" id="exercise-therapy">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mr-6">
                                <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-blue-700 mb-2">运动疗法</h3>
                                <p class="text-blue-600">规律运动促进内啡肽释放，改善血液循环</p>
                            </div>
                        </div>
                        
                        <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
                            <!-- Aerobic Exercise -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                                <h4 class="text-lg font-semibold text-blue-700 mb-4">有氧运动</h4>
                                <div class="space-y-3">
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                            <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                            </svg>
                                        </div>
                                        <p class="text-sm font-semibold">强度</p>
                                        <p class="text-xs text-blue-600">中等强度</p>
                                    </div>
                                    
                                    <div class="space-y-2">
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>快走：</strong>30分钟，每周4-5次
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>游泳：</strong>25分钟，每周3次
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>骑行：</strong>45分钟，每周2-3次
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>舞蹈：</strong>30分钟，每周2-3次
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Yoga -->
                            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                                <h4 class="text-lg font-semibold text-purple-700 mb-4">瑜伽练习</h4>
                                <div class="space-y-3">
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                            <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                            </svg>
                                        </div>
                                        <p class="text-sm font-semibold">频率</p>
                                        <p class="text-xs text-purple-600">每周3-4次</p>
                                    </div>
                                    
                                    <div class="space-y-2">
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>扭转体式：</strong>缓解腹部紧张
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>前屈体式：</strong>放松神经系统
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>髋部开启：</strong>改善盆腔血流
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>倒立体式：</strong>调节内分泌
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Strength Training -->
                            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                                <h4 class="text-lg font-semibold text-green-700 mb-4">力量训练</h4>
                                <div class="space-y-3">
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                            <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <p class="text-sm font-semibold">重点</p>
                                        <p class="text-xs text-green-600">核心稳定</p>
                                    </div>
                                    
                                    <div class="space-y-2">
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>平板支撑：</strong>30-60秒×3组
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>桥式：</strong>15-20次×3组
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>深蹲：</strong>12-15次×3组
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>鸟狗式：</strong>10次/侧×2组
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Pelvic Floor -->
                            <div class="bg-orange-50 border border-orange-200 rounded-lg p-6">
                                <h4 class="text-lg font-semibold text-orange-700 mb-4">盆底训练</h4>
                                <div class="space-y-3">
                                    <div class="text-center">
                                        <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-2">
                                            <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                            </svg>
                                        </div>
                                        <p class="text-sm font-semibold">专项</p>
                                        <p class="text-xs text-orange-600">每日练习</p>
                                    </div>
                                    
                                    <div class="space-y-2">
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>凯格尔运动：</strong>收缩5秒×10次
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>深呼吸：</strong>配合盆底收放
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>臀桥变式：</strong>加强盆底肌群
                                        </div>
                                        <div class="bg-white rounded p-2 text-sm">
                                            <strong>猫牛式：</strong>活动骨盆
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Exercise Schedule -->
                        <div class="mt-8 bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <h4 class="text-lg font-semibold text-blue-700 mb-4">📅 周运动计划</h4>
                            <div class="overflow-x-auto">
                                <table class="w-full text-sm">
                                    <thead>
                                        <tr class="bg-blue-100">
                                            <th class="p-2 text-left">日期</th>
                                            <th class="p-2 text-left">运动类型</th>
                                            <th class="p-2 text-left">时长</th>
                                            <th class="p-2 text-left">注意事项</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr class="bg-white">
                                            <td class="p-2 font-semibold">周一</td>
                                            <td class="p-2">有氧运动 + 盆底训练</td>
                                            <td class="p-2">45分钟</td>
                                            <td class="p-2">记录心率</td>
                                        </tr>
                                        <tr class="bg-blue-25">
                                            <td class="p-2 font-semibold">周二</td>
                                            <td class="p-2">瑜伽 + 伸展</td>
                                            <td class="p-2">60分钟</td>
                                            <td class="p-2">专注呼吸</td>
                                        </tr>
                                        <tr class="bg-white">
                                            <td class="p-2 font-semibold">周三</td>
                                            <td class="p-2">力量训练</td>
                                            <td class="p-2">30分钟</td>
                                            <td class="p-2">轻重量多次数</td>
                                        </tr>
                                        <tr class="bg-blue-25">
                                            <td class="p-2 font-semibold">周四</td>
                                            <td class="p-2">有氧运动</td>
                                            <td class="p-2">40分钟</td>
                                            <td class="p-2">保持中等强度</td>
                                        </tr>
                                        <tr class="bg-white">
                                            <td class="p-2 font-semibold">周五</td>
                                            <td class="p-2">瑜伽 + 冥想</td>
                                            <td class="p-2">50分钟</td>
                                            <td class="p-2">放松为主</td>
                                        </tr>
                                        <tr class="bg-blue-25">
                                            <td class="p-2 font-semibold">周末</td>
                                            <td class="p-2">户外活动 / 休息</td>
                                            <td class="p-2">自由安排</td>
                                            <td class="p-2">享受运动乐趣</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>

                    <!-- Stress Management -->
                    <div class="bg-white rounded-lg shadow-lg p-8 mb-12">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mr-6">
                                <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-purple-700 mb-2">压力管理</h3>
                                <p class="text-purple-600">慢性压力是痛经加重的重要因素</p>
                            </div>
                        </div>
                        
                        <div class="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                            <!-- Mindfulness -->
                            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                                <h4 class="text-lg font-semibold text-purple-700 mb-4">正念练习</h4>
                                <div class="space-y-3">
                                    <div class="bg-white rounded p-3">
                                        <strong>正念冥想</strong>
                                        <p class="text-sm text-purple-600 mt-1">每日15-20分钟，专注当下</p>
                                    </div>
                                    <div class="bg-white rounded p-3">
                                        <strong>身体扫描</strong>
                                        <p class="text-sm text-purple-600 mt-1">觉察身体感受，释放紧张</p>
                                    </div>
                                    <div class="bg-white rounded p-3">
                                        <strong>正念饮食</strong>
                                        <p class="text-sm text-purple-600 mt-1">慢食，品味每一口</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Relaxation Techniques -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                                <h4 class="text-lg font-semibold text-blue-700 mb-4">放松技巧</h4>
                                <div class="space-y-3">
                                    <div class="bg-white rounded p-3">
                                        <strong>渐进性肌肉放松</strong>
                                        <p class="text-sm text-blue-600 mt-1">从头到脚，逐一放松</p>
                                    </div>
                                    <div class="bg-white rounded p-3">
                                        <strong>芳香疗法</strong>
                                        <p class="text-sm text-blue-600 mt-1">薰衣草、洋甘菊精油</p>
                                    </div>
                                    <div class="bg-white rounded p-3">
                                        <strong>音乐治疗</strong>
                                        <p class="text-sm text-blue-600 mt-1">舒缓音乐，降低皮质醇</p>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Lifestyle Adjustments -->
                            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                                <h4 class="text-lg font-semibold text-green-700 mb-4">生活调整</h4>
                                <div class="space-y-3">
                                    <div class="bg-white rounded p-3">
                                        <strong>时间管理</strong>
                                        <p class="text-sm text-green-600 mt-1">合理安排，避免过度疲劳</p>
                                    </div>
                                    <div class="bg-white rounded p-3">
                                        <strong>社交支持</strong>
                                        <p class="text-sm text-green-600 mt-1">与朋友分享，获得理解</p>
                                    </div>
                                    <div class="bg-white rounded p-3">
                                        <strong>兴趣爱好</strong>
                                        <p class="text-sm text-green-600 mt-1">培养爱好，转移注意力</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Sleep Optimization -->
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <div class="flex items-center mb-6">
                            <div class="w-16 h-16 bg-indigo-100 rounded-full flex items-center justify-center mr-6">
                                <svg class="w-8 h-8 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-2xl font-bold text-indigo-700 mb-2">睡眠优化</h3>
                                <p class="text-indigo-600">优质睡眠是激素平衡的基础</p>
                            </div>
                        </div>
                        
                        <div class="grid md:grid-cols-2 gap-8">
                            <div>
                                <h4 class="text-lg font-semibold text-indigo-700 mb-4">睡眠卫生</h4>
                                <div class="space-y-3">
                                    <div class="flex items-start">
                                        <span class="text-indigo-500 mr-3 mt-1">🕙</span>
                                        <div>
                                            <strong>固定作息</strong>
                                            <p class="text-sm text-indigo-600">每天同一时间睡觉和起床</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-indigo-500 mr-3 mt-1">🌙</span>
                                        <div>
                                            <strong>睡前仪式</strong>
                                            <p class="text-sm text-indigo-600">洗澡、阅读、轻柔音乐</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-indigo-500 mr-3 mt-1">📱</span>
                                        <div>
                                            <strong>电子设备</strong>
                                            <p class="text-sm text-indigo-600">睡前1小时停用手机平板</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-indigo-500 mr-3 mt-1">🌡️</span>
                                        <div>
                                            <strong>睡眠环境</strong>
                                            <p class="text-sm text-indigo-600">凉爽(18-20°C)、黑暗、安静</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-lg font-semibold text-indigo-700 mb-4">经期睡眠调整</h4>
                                <div class="bg-indigo-50 border border-indigo-200 rounded-lg p-4">
                                    <div class="space-y-3">
                                        <div class="border-l-3 border-indigo-400 pl-3">
                                            <strong>经前期</strong>
                                            <p class="text-sm text-indigo-600">可能需要额外30分钟睡眠</p>
                                        </div>
                                        <div class="border-l-3 border-indigo-400 pl-3">
                                            <strong>经期中</strong>
                                            <p class="text-sm text-indigo-600">使用热垫缓解不适，保证睡眠质量</p>
                                        </div>
                                        <div class="border-l-3 border-indigo-400 pl-3">
                                            <strong>经期后</strong>
                                            <p class="text-sm text-indigo-600">恢复正常作息，补充体力</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-4 bg-gray-50 border border-gray-300 rounded p-4">
                                    <h5 class="font-semibold mb-2">促进睡眠的天然方法</h5>
                                    <ul class="text-sm space-y-1">
                                        <li>• 洋甘菊茶（睡前1小时）</li>
                                        <li>• 镁补充剂（200-300mg）</li>
                                        <li>• 薰衣草精油芳香疗法</li>
                                        <li>• 温水泡脚（睡前20分钟）</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Progress Tracking -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-3xl font-bold text-center mb-12">调理进度跟踪</h2>
                    
                    <div class="bg-gray-50 rounded-lg p-8">
                        <h3 class="text-xl font-bold mb-6 text-center">📊 expected improvement timeline</h3>
                        
                        <div class="space-y-6">
                            <div class="flex items-center">
                                <div class="w-20 flex-shrink-0 text-center">
                                    <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-1">
                                        <span class="text-yellow-600 font-bold">2周</span>
                                    </div>
                                </div>
                                <div class="flex-1 bg-yellow-50 border border-yellow-200 rounded p-4 ml-4">
                                    <h4 class="font-semibold text-yellow-700">初期适应</h4>
                                    <p class="text-sm text-yellow-600">身体开始适应新的生活方式，睡眠质量可能改善</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-20 flex-shrink-0 text-center">
                                    <div class="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mx-auto mb-1">
                                        <span class="text-orange-600 font-bold">1月</span>
                                    </div>
                                </div>
                                <div class="flex-1 bg-orange-50 border border-orange-200 rounded p-4 ml-4">
                                    <h4 class="font-semibold text-orange-700">初步改善</h4>
                                    <p class="text-sm text-orange-600">压力水平降低，部分女性开始感受到疼痛强度减轻</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-20 flex-shrink-0 text-center">
                                    <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-1">
                                        <span class="text-blue-600 font-bold">3月</span>
                                    </div>
                                </div>
                                <div class="flex-1 bg-blue-50 border border-blue-200 rounded p-4 ml-4">
                                    <h4 class="font-semibold text-blue-700">显著改善</h4>
                                    <p class="text-sm text-blue-600">激素平衡改善，疼痛频率和强度明显下降，整体健康状况提升</p>
                                </div>
                            </div>
                            
                            <div class="flex items-center">
                                <div class="w-20 flex-shrink-0 text-center">
                                    <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-1">
                                        <span class="text-green-600 font-bold">6月</span>
                                    </div>
                                </div>
                                <div class="flex-1 bg-green-50 border border-green-200 rounded p-4 ml-4">
                                    <h4 class="font-semibold text-green-700">稳定改善</h4>
                                    <p class="text-sm text-green-600">新的健康习惯完全建立，痛经症状达到预期改善效果</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 bg-blue-50 border border-blue-200 rounded p-4">
                            <h4 class="font-semibold text-blue-700 mb-2">💡 成功的关键</h4>
                            <ul class="text-sm text-blue-700 space-y-1">
                                <li>• 坚持性：每天执行计划，形成习惯</li>
                                <li>• 耐心：改善需要时间，不要急于求成</li>
                                <li>• 记录：使用痛经追踪器记录进展</li>
                                <li>• 调整：根据效果和感受及时调整方案</li>
                                <li>• 专业指导：必要时咨询医生</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="py-16 bg-green-600 text-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto text-center">
                    <h2 class="text-3xl font-bold mb-6">开始您的调理之旅</h2>
                    <p class="text-xl opacity-90 mb-8">选择适合的工具，制定个性化的调理计划</p>
                    
                    <div class="grid md:grid-cols-3 gap-6 mb-8">
                        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-2">症状评估</h3>
                            <p class="text-sm opacity-90 mb-4">了解当前状况，制定调理目标</p>
                            <a href="interactive-solutions.html" class="bg-white text-green-600 px-4 py-2 rounded font-semibold hover:bg-gray-100 transition-colors">
                                开始评估
                            </a>
                        </div>
                        
                        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-2">疼痛追踪</h3>
                            <p class="text-sm opacity-90 mb-4">记录调理进展，量化改善效果</p>
                            <a href="resources/pain-tracker.html" class="bg-white text-green-600 px-4 py-2 rounded font-semibold hover:bg-gray-100 transition-colors">
                                开始记录
                            </a>
                        </div>
                        
                        <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6">
                            <h3 class="text-lg font-semibold mb-2">即时缓解</h3>
                            <p class="text-sm opacity-90 mb-4">调理期间的疼痛管理方法</p>
                            <a href="instant-solutions.html" class="bg-white text-green-600 px-4 py-2 rounded font-semibold hover:bg-gray-100 transition-colors">
                                查看方法
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">period.health</h3>
                    <p class="text-gray-400 mb-4">专业的经期疼痛管理平台，为女性健康保驾护航。</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">核心功能</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="interactive-solutions.html" class="hover:text-white">症状评估器</a></li>
                        <li><a href="interactive-solutions.html" class="hover:text-white">个性化方案</a></li>
                        <li><a href="instant-solutions.html" class="hover:text-white">即时缓解</a></li>
                        <li><a href="regular-conditioning.html" class="hover:text-white">平时调理</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">专业内容</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="medical-research.html" class="hover:text-white">医学研究</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">健康资讯</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">用户故事</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">专家观点</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">法律信息</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="privacy-policy.html" class="hover:text-white">隐私政策</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white">服务条款</a></li>
                        <li><a href="faq.html" class="hover:text-white">常见问题</a></li>
                        <li><a href="#" class="hover:text-white">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 period.health. 本网站内容仅供参考，不能替代专业医疗建议。</p>
            </div>
        </div>
    </footer>

    <script src="main.js"></script>
</body>
</html>

