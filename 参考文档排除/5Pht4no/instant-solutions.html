<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>即时解决方案 - 经期疼痛快速缓解方法 | period.health</title>
    <meta name="description" content="科学验证的经期疼痛即时缓解方法：热敷、按摩、呼吸法、体位调整等。快速有效的痛经缓解技巧，帮您立即获得舒适。">
    <meta name="keywords" content="痛经即时缓解, 经期疼痛快速缓解, 痛经急救, 立即止痛方法, 热敷缓解痛经, 痛经按摩, menstrual pain relief, period pain relief fast">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- Navigation Header -->
    <header class="bg-white shadow-sm border-b">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-purple-600">period.health</div>
                <ul class="hidden md:flex space-x-6">
                    <li><a href="index.html" class="text-gray-600 hover:text-purple-600 transition-colors">首页</a></li>
                    <li><a href="interactive-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">互动解决方案</a></li>
                    <li><a href="instant-solutions.html" class="text-purple-600 font-semibold border-b-2 border-purple-600 pb-1" aria-current="page">即时解决方案</a></li>
                    <li><a href="regular-conditioning.html" class="text-gray-600 hover:text-purple-600 transition-colors">平时调理</a></li>
                    <li><a href="medical-research.html" class="text-gray-600 hover:text-purple-600 transition-colors">医学原理</a></li>
                    <li><a href="blog-center.html" class="text-gray-600 hover:text-purple-600 transition-colors">文章中心</a></li>
                </ul>
                <button class="md:hidden p-2" id="mobile-menu-btn" aria-label="打开移动菜单">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <div class="md:hidden hidden mt-4 pb-4" id="mobile-menu">
                <a href="index.html" class="block py-2 text-gray-600">首页</a>
                <a href="interactive-solutions.html" class="block py-2 text-gray-600">互动解决方案</a>
                <a href="instant-solutions.html" class="block py-2 text-purple-600 font-semibold" aria-current="page">即时解决方案</a>
                <a href="regular-conditioning.html" class="block py-2 text-gray-600">平时调理</a>
                <a href="medical-research.html" class="block py-2 text-gray-600">医学原理</a>
                <a href="blog-center.html" class="block py-2 text-gray-600">文章中心</a>
            </div>
        </nav>
    </header>

    <main>
        <!-- Page Header -->
        <section class="bg-gradient-to-r from-red-500 to-pink-500 text-white py-12 lg:py-16">
            <div class="container mx-auto px-4">
                <h1 class="text-3xl md:text-4xl font-bold mb-4">即时解决方案</h1>
                <p class="text-lg md:text-xl opacity-90 max-w-2xl">疼痛来袭时的急救指南 - 科学验证的快速缓解方法</p>
            </div>
        </section>

        <!-- Emergency Alert -->
        <section class="py-6 bg-red-50 border-b border-red-200">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <div class="bg-red-100 border border-red-300 rounded-lg p-4">
                        <div class="flex items-center">
                            <svg class="w-6 h-6 text-red-600 mr-3 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                            <div>
                                <h3 class="font-semibold text-red-800">紧急情况提醒</h3>
                                <p class="text-red-700 text-sm mt-1">如果出现剧烈腹痛伴有发热、大量出血、晕厥或呕吐，请立即就医或拨打急救电话。</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Relief Methods -->
        <section class="py-12 lg:py-16 bg-white" id="heat-therapy">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <h2 class="text-2xl md:text-3xl font-bold mb-8 text-center">快速缓解方法</h2>
                    
                    <!-- Heat Therapy -->
                    <div class="mb-12">
                        <div class="bg-gradient-to-r from-orange-50 to-red-50 rounded-lg p-8 border border-orange-200">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl md:text-2xl font-bold text-orange-700">热敷疗法</h3>
                                <span class="ml-3 bg-green-100 text-green-700 px-2 py-1 rounded-full text-xs font-semibold">最有效</span>
                            </div>
                            
                            <div class="grid md:grid-cols-2 gap-8">
                                <div>
                                    <h4 class="font-semibold mb-4 text-orange-600">使用方法</h4>
                                    <div class="space-y-3">
                                        <div class="flex items-start">
                                            <span class="bg-orange-100 text-orange-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold mr-3 mt-0.5">1</span>
                                            <div>
                                                <p class="font-medium text-gray-800">准备热源</p>
                                                <p class="text-sm text-gray-600">热水袋、暖宝宝、电热垫等，温度控制在40-45°C</p>
                                            </div>
                                        </div>
                                        <div class="flex items-start">
                                            <span class="bg-orange-100 text-orange-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold mr-3 mt-0.5">2</span>
                                            <div>
                                                <p class="font-medium text-gray-800">正确位置</p>
                                                <p class="text-sm text-gray-600">敷在下腹部、腰骶部或疼痛最明显的区域</p>
                                            </div>
                                        </div>
                                        <div class="flex items-start">
                                            <span class="bg-orange-100 text-orange-600 rounded-full w-6 h-6 flex items-center justify-center text-sm font-semibold mr-3 mt-0.5">3</span>
                                            <div>
                                                <p class="font-medium text-gray-800">持续时间</p>
                                                <p class="text-sm text-gray-600">每次15-20分钟，可重复使用，间隔10分钟</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div>
                                    <h4 class="font-semibold mb-4 text-orange-600">科学原理</h4>
                                    <div class="bg-white p-4 rounded-lg border border-orange-100">
                                        <ul class="space-y-2 text-sm text-gray-700">
                                            <li class="flex items-start">
                                                <svg class="w-4 h-4 text-orange-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                放松子宫肌肉，减少痉挛
                                            </li>
                                            <li class="flex items-start">
                                                <svg class="w-4 h-4 text-orange-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                促进血液循环，缓解缺血性疼痛
                                            </li>
                                            <li class="flex items-start">
                                                <svg class="w-4 h-4 text-orange-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                激活疼痛门控机制，缓解疼痛感知
                                            </li>
                                            <li class="flex items-start">
                                                <svg class="w-4 h-4 text-orange-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                                </svg>
                                                促进内啡肽释放，天然镇痛
                                            </li>
                                        </ul>
                                    </div>
                                    
                                    <div class="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded">
                                        <p class="text-xs text-yellow-700">
                                            <strong>安全提示：</strong>避免温度过高导致烫伤，使用电热设备时注意安全。
                                        </p>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Massage Therapy -->
                    <div class="mb-12" id="massage">
                        <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-lg p-8 border border-purple-200">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 11.5V14m0-2.5v-6a1.5 1.5 0 113 0m-3 6a1.5 1.5 0 00-3 0v2a7.5 7.5 0 0015 0v-5a1.5 1.5 0 00-3 0m-6-3V11m0-5.5v-1a1.5 1.5 0 013 0v1m0 0V11m0-5.5a1.5 1.5 0 013 0v3m0 0V11"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl md:text-2xl font-bold text-purple-700">按摩缓解</h3>
                                <span class="ml-3 bg-blue-100 text-blue-700 px-2 py-1 rounded-full text-xs font-semibold">立即见效</span>
                            </div>
                            
                            <div class="grid md:grid-cols-3 gap-6">
                                <div class="bg-white p-6 rounded-lg border border-purple-100">
                                    <h4 class="font-semibold mb-4 text-purple-600">腹部按摩</h4>
                                    <div class="space-y-3 text-sm">
                                        <div>
                                            <p class="font-medium">环形按摩法</p>
                                            <p class="text-gray-600">双手重叠于脐周，顺时针方向轻柔按摩5-10圈</p>
                                        </div>
                                        <div>
                                            <p class="font-medium">推拿手法</p>
                                            <p class="text-gray-600">从上腹部向下腹部轻推，重复10-15次</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-white p-6 rounded-lg border border-pink-100">
                                    <h4 class="font-semibold mb-4 text-pink-600">腰部按摩</h4>
                                    <div class="space-y-3 text-sm">
                                        <div>
                                            <p class="font-medium">腰骶按摩</p>
                                            <p class="text-gray-600">用拳头轻敲腰骶部，或画圆按摩腰眼穴位</p>
                                        </div>
                                        <div>
                                            <p class="font-medium">背部放松</p>
                                            <p class="text-gray-600">沿脊柱两侧从上向下按摩，缓解肌肉紧张</p>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="bg-white p-6 rounded-lg border border-green-100">
                                    <h4 class="font-semibold mb-4 text-green-600">穴位按摩</h4>
                                    <div class="space-y-3 text-sm">
                                        <div>
                                            <p class="font-medium">三阴交穴</p>
                                            <p class="text-gray-600">内踝上3寸，按压3-5分钟，有酸胀感</p>
                                        </div>
                                        <div>
                                            <p class="font-medium">关元穴</p>
                                            <p class="text-gray-600">脐下3寸，按压并配合腹式呼吸</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Breathing Exercises -->
                    <div class="mb-12" id="breathing">
                        <div class="bg-gradient-to-r from-blue-50 to-cyan-50 rounded-lg p-8 border border-blue-200">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl md:text-2xl font-bold text-blue-700">深度呼吸</h3>
                                <span class="ml-3 bg-purple-100 text-purple-700 px-2 py-1 rounded-full text-xs font-semibold">缓解焦虑</span>
                            </div>
                            
                            <div class="grid md:grid-cols-2 gap-8">
                                <div>
                                    <h4 class="font-semibold mb-4 text-blue-600">4-7-8呼吸法</h4>
                                    <div class="bg-white p-6 rounded-lg border border-blue-100">
                                        <div class="space-y-4">
                                            <div class="flex items-center">
                                                <span class="bg-blue-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-lg font-bold mr-3">4</span>
                                                <div>
                                                    <p class="font-medium">吸气</p>
                                                    <p class="text-sm text-gray-600">通过鼻子深吸气4秒</p>
                                                </div>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="bg-green-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-lg font-bold mr-3">7</span>
                                                <div>
                                                    <p class="font-medium">屏气</p>
                                                    <p class="text-sm text-gray-600">闭气保持7秒钟</p>
                                                </div>
                                            </div>
                                            <div class="flex items-center">
                                                <span class="bg-red-500 text-white rounded-full w-8 h-8 flex items-center justify-center text-lg font-bold mr-3">8</span>
                                                <div>
                                                    <p class="font-medium">呼气</p>
                                                    <p class="text-sm text-gray-600">通过嘴缓慢呼气8秒</p>
                                                </div>
                                            </div>
                                        </div>
                                        <p class="mt-4 text-xs text-blue-600">重复3-5个循环，专注于呼吸的节奏</p>
                                    </div>
                                </div>
                                
                                <div>
                                    <h4 class="font-semibold mb-4 text-cyan-600">腹式呼吸</h4>
                                    <div class="bg-white p-6 rounded-lg border border-cyan-100">
                                        <div class="space-y-3">
                                            <p class="text-sm text-gray-700">
                                                <strong>步骤：</strong>平躺或坐下，一手放胸前，一手放腹部
                                            </p>
                                            <p class="text-sm text-gray-700">
                                                <strong>吸气：</strong>腹部手上升，胸部手保持相对静止
                                            </p>
                                            <p class="text-sm text-gray-700">
                                                <strong>呼气：</strong>缓慢呼气，腹部手下降
                                            </p>
                                            <p class="text-sm text-gray-700">
                                                <strong>节奏：</strong>吸气3秒，呼气6秒
                                            </p>
                                        </div>
                                        
                                        <div class="mt-4 p-3 bg-cyan-50 rounded">
                                            <p class="text-xs text-cyan-700">
                                                <strong>益处：</strong>激活副交感神经系统，降低皮质醇水平，缓解肌肉紧张
                                            </p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Position Therapy -->
                    <div class="mb-12">
                        <div class="bg-gradient-to-r from-green-50 to-teal-50 rounded-lg p-8 border border-green-200">
                            <div class="flex items-center mb-6">
                                <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                                    <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                </div>
                                <h3 class="text-xl md:text-2xl font-bold text-green-700">舒缓体位</h3>
                                <span class="ml-3 bg-orange-100 text-orange-700 px-2 py-1 rounded-full text-xs font-semibold">简单易行</span>
                            </div>
                            
                            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                                <div class="bg-white p-4 rounded-lg border border-green-100 text-center">
                                    <h4 class="font-semibold text-green-600 mb-3">胎儿体位</h4>
                                    <div class="h-20 bg-green-50 rounded mb-3 flex items-center justify-center">
                                        <span class="text-2xl">🤱</span>
                                    </div>
                                    <p class="text-sm text-gray-600">侧卧，双膝抱至胸前，缓解子宫压力</p>
                                </div>
                                
                                <div class="bg-white p-4 rounded-lg border border-green-100 text-center">
                                    <h4 class="font-semibold text-green-600 mb-3">猫式伸展</h4>
                                    <div class="h-20 bg-green-50 rounded mb-3 flex items-center justify-center">
                                        <span class="text-2xl">🐱</span>
                                    </div>
                                    <p class="text-sm text-gray-600">四肢着地，背部拉伸，缓解腰背紧张</p>
                                </div>
                                
                                <div class="bg-white p-4 rounded-lg border border-green-100 text-center">
                                    <h4 class="font-semibold text-green-600 mb-3">臀部抬高</h4>
                                    <div class="h-20 bg-green-50 rounded mb-3 flex items-center justify-center">
                                        <span class="text-2xl">⬆️</span>
                                    </div>
                                    <p class="text-sm text-gray-600">仰卧，臀部垫高，改善盆腔血流</p>
                                </div>
                                
                                <div class="bg-white p-4 rounded-lg border border-green-100 text-center">
                                    <h4 class="font-semibold text-green-600 mb-3">婴儿式</h4>
                                    <div class="h-20 bg-green-50 rounded mb-3 flex items-center justify-center">
                                        <span class="text-2xl">🧘‍♀️</span>
                                    </div>
                                    <p class="text-sm text-gray-600">跪坐，前倾伏地，安全感与放松</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Quick Action Checklist -->
        <section class="py-12 lg:py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-2xl md:text-3xl font-bold mb-8 text-center">疼痛发作时的行动清单</h2>
                    
                    <div class="bg-white rounded-lg p-8 shadow-sm border border-gray-200">
                        <div class="grid md:grid-cols-2 gap-8">
                            <div>
                                <h3 class="text-lg font-semibold mb-6 text-purple-600">立即行动（0-5分钟）</h3>
                                <div class="space-y-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox h-4 w-4 text-purple-600">
                                        <span class="ml-3 text-gray-700">找到温暖环境，准备热敷工具</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox h-4 w-4 text-purple-600">
                                        <span class="ml-3 text-gray-700">调整到舒适体位（胎儿位或婴儿式）</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox h-4 w-4 text-purple-600">
                                        <span class="ml-3 text-gray-700">开始深度呼吸练习</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox h-4 w-4 text-purple-600">
                                        <span class="ml-3 text-gray-700">如需要，考虑服用止痛药</span>
                                    </label>
                                </div>
                            </div>
                            
                            <div>
                                <h3 class="text-lg font-semibold mb-6 text-green-600">持续缓解（10-30分钟）</h3>
                                <div class="space-y-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox h-4 w-4 text-green-600">
                                        <span class="ml-3 text-gray-700">持续热敷15-20分钟</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox h-4 w-4 text-green-600">
                                        <span class="ml-3 text-gray-700">轻柔按摩腹部和腰部</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox h-4 w-4 text-green-600">
                                        <span class="ml-3 text-gray-700">保持充足水分摄入</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" class="form-checkbox h-4 w-4 text-green-600">
                                        <span class="ml-3 text-gray-700">避免剧烈活动，适当休息</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-8 p-4 bg-blue-50 rounded-lg border border-blue-200">
                            <p class="text-blue-700 text-sm">
                                <strong>记录疼痛：</strong>建议记录疼痛强度（1-10分）、持续时间、缓解效果，这将帮助您了解个人模式并在就医时提供有价值信息。
                                <a href="resources/pain-tracker/pain-tracker.html" class="underline ml-1">使用疼痛追踪器</a>
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- When to Seek Help -->
        <section class="py-12 lg:py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-2xl md:text-3xl font-bold mb-8 text-center text-red-600">何时需要专业帮助</h2>
                    
                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-red-700 mb-4">立即就医情况</h3>
                            <ul class="space-y-2 text-red-600">
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    剧烈腹痛，无法缓解
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    发热超过38.5°C
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    大量异常出血
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                                    </svg>
                                    晕厥或严重呕吐
                                </li>
                            </ul>
                        </div>
                        
                        <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-yellow-700 mb-4">建议咨询医生</h3>
                            <ul class="space-y-2 text-gray-700">
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    生活质量受严重影响
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    常规方法无效
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    症状逐渐加重
                                </li>
                                <li class="flex items-start">
                                    <svg class="w-5 h-5 text-yellow-500 mr-2 mt-0.5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                    需要制定长期治疗方案
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Related Resources -->
        <section class="py-12 lg:py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-2xl md:text-3xl font-bold mb-8 text-center">相关资源</h2>
                    
                    <div class="grid md:grid-cols-3 gap-6">
                        <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                            <h3 class="text-lg font-semibold mb-4 text-purple-600">长期调理方案</h3>
                            <p class="text-gray-600 mb-4">了解如何通过生活方式调整，从根本上改善经期健康</p>
                            <a href="regular-conditioning.html" class="inline-flex items-center text-purple-600 font-semibold hover:text-purple-700">
                                了解详情
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                        
                        <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                            <h3 class="text-lg font-semibold mb-4 text-green-600">疼痛追踪器</h3>
                            <p class="text-gray-600 mb-4">记录和分析您的疼痛模式，为治疗提供重要参考</p>
                            <a href="resources/pain-tracker/pain-tracker.html" class="inline-flex items-center text-green-600 font-semibold hover:text-green-700">
                                开始记录
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                        
                        <div class="bg-white rounded-lg p-6 shadow-sm border border-gray-200 hover:shadow-lg transition-shadow">
                            <h3 class="text-lg font-semibold mb-4 text-blue-600">常见问题</h3>
                            <p class="text-gray-600 mb-4">查看其他用户常问的痛经相关问题和专业解答</p>
                            <a href="faq.html" class="inline-flex items-center text-blue-600 font-semibold hover:text-blue-700">
                                查看FAQ
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">period.health</h3>
                    <p class="text-gray-400 mb-4">专业的经期疼痛管理平台，为女性健康保驾护航。</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">核心功能</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="interactive-solutions.html" class="hover:text-white">症状评估器</a></li>
                        <li><a href="interactive-solutions.html" class="hover:text-white">个性化方案</a></li>
                        <li><a href="instant-solutions.html" class="hover:text-white">即时缓解</a></li>
                        <li><a href="regular-conditioning.html" class="hover:text-white">平时调理</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">专业内容</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="medical-research.html" class="hover:text-white">医学研究</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">健康资讯</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">用户故事</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">专家观点</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">法律信息</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="privacy-policy.html" class="hover:text-white">隐私政策</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white">服务条款</a></li>
                        <li><a href="faq.html" class="hover:text-white">常见问题</a></li>
                        <li><a href="#" class="hover:text-white">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 period.health. 本网站内容仅供参考，不能替代专业医疗建议。</p>
            </div>
        </div>
    </footer>

    <script src="main.js"></script>
</body>
</html>
