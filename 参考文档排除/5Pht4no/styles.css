/* Custom styles for period.health */
:root {
    /* Primary color palette */
    --primary-purple: #7c3aed;
    --primary-purple-light: #8b5cf6;
    --primary-purple-dark: #5b21b6;
    
    /* Secondary colors */
    --secondary-pink: #ec4899;
    --secondary-blue: #3b82f6;
    --secondary-green: #10b981;
    --secondary-orange: #f59e0b;
    
    /* Neutral colors */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;
    
    /* Spacing and sizing */
    --container-max-width: 1200px;
    --border-radius: 0.5rem;
    --border-radius-lg: 1rem;
    --shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* Base styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    line-height: 1.6;
    color: var(--gray-800);
    background-color: var(--gray-50);
}

/* Container and layout */
.container {
    width: 100%;
    max-width: var(--container-max-width);
    margin: 0 auto;
    padding: 0 1rem;
}

/* Typography enhancements */
h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 0.5rem;
}

h1 {
    font-size: 2.5rem;
}

h2 {
    font-size: 2rem;
}

h3 {
    font-size: 1.5rem;
}

p {
    margin-bottom: 1rem;
}

/* Button enhancements */
button, .btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1.5rem;
    border-radius: var(--border-radius);
    font-weight: 600;
    text-decoration: none;
    transition: all var(--transition-fast);
    cursor: pointer;
    border: none;
    white-space: nowrap;
}

button:hover, .btn:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

button:active, .btn:active {
    transform: translateY(0);
}

/* Custom slider styles for pain intensity */
.slider-red::-webkit-slider-thumb {
    appearance: none;
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #dc2626;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.slider-red::-moz-range-thumb {
    height: 20px;
    width: 20px;
    border-radius: 50%;
    background: #dc2626;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

/* Card and component styles */
.card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow);
    padding: 1.5rem;
    margin-bottom: 1rem;
    transition: box-shadow var(--transition-normal);
}

.card:hover {
    box-shadow: var(--shadow-lg);
}

/* Form enhancements */
input[type="text"],
input[type="email"], 
input[type="password"],
input[type="date"],
input[type="number"],
select,
textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid var(--gray-300);
    border-radius: var(--border-radius);
    background-color: white;
    font-size: 1rem;
    transition: border-color var(--transition-fast), box-shadow var(--transition-fast);
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: var(--primary-purple);
    box-shadow: 0 0 0 3px rgba(124, 58, 237, 0.1);
}

/* Radio and checkbox enhancements */
input[type="radio"],
input[type="checkbox"] {
    margin-right: 0.5rem;
    accent-color: var(--primary-purple);
}

/* Navigation enhancements */
nav a {
    text-decoration: none;
    transition: color var(--transition-fast);
    position: relative;
}

nav a:hover {
    color: var(--primary-purple);
}

nav a[aria-current="page"] {
    color: var(--primary-purple);
    font-weight: 600;
}

/* Mobile menu animation */
#mobile-menu {
    transition: all var(--transition-normal);
}

#mobile-menu.hidden {
    opacity: 0;
    transform: translateY(-10px);
}

#mobile-menu:not(.hidden) {
    opacity: 1;
    transform: translateY(0);
}

/* Scroll animations */
.fade-on-scroll {
    opacity: 0;
    transform: translateY(30px);
    transition: opacity 0.8s ease-out, transform 0.8s ease-out;
}

.fade-on-scroll.animate-fade-in {
    opacity: 1;
    transform: translateY(0);
}

/* Loading spinner */
.loading-spinner {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

/* Gradient text effect */
.gradient-text {
    background: linear-gradient(45deg, var(--primary-purple), var(--secondary-pink));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Custom scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--gray-100);
}

::-webkit-scrollbar-thumb {
    background: var(--gray-400);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--gray-500);
}

/* Responsive utilities */
.hide-mobile {
    display: block;
}

.show-mobile {
    display: none;
}

@media (max-width: 768px) {
    .hide-mobile {
        display: none;
    }
    
    .show-mobile {
        display: block;
    }
    
    h1 {
        font-size: 2rem;
    }
    
    h2 {
        font-size: 1.75rem;
    }
    
    h3 {
        font-size: 1.25rem;
    }
}

/* Pain intensity color coding */
.pain-none { color: #10b981; }
.pain-mild { color: #f59e0b; }
.pain-moderate { color: #f97316; }
.pain-severe { color: #dc2626; }

/* Menstrual status color coding */
.status-no-period { background-color: #f3f4f6; color: #374151; }
.status-period { background-color: #fef2f2; color: #b91c1c; }
.status-pms { background-color: #f3e8ff; color: #7c2d12; }
.status-ovulation { background-color: #eff6ff; color: #1d4ed8; }

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Focus indicators for keyboard navigation */
button:focus-visible,
a:focus-visible,
input:focus-visible,
select:focus-visible,
textarea:focus-visible {
    outline: 2px solid var(--primary-purple);
    outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
    :root {
        --gray-600: #000000;
        --gray-700: #000000;
        --gray-800: #000000;
    }
    
    body {
        color: #000000;
        background-color: #ffffff;
    }
    
    .card {
        border: 2px solid #000000;
    }
    
    button, .btn {
        border: 2px solid #000000;
    }
}

/* Print styles */
@media print {
    body {
        background: white;
        color: black;
    }
    
    .no-print {
        display: none !important;
    }
    
    nav, footer, .mobile-menu {
        display: none !important;
    }
    
    .card {
        box-shadow: none;
        border: 1px solid #ccc;
        page-break-inside: avoid;
    }
    
    h1, h2, h3 {
        page-break-after: avoid;
    }
}

/* Custom component: Progress bars */
.progress-bar {
    width: 100%;
    height: 8px;
    background-color: var(--gray-200);
    border-radius: var(--border-radius);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    transition: width var(--transition-normal);
    border-radius: var(--border-radius);
}

.progress-fill.pain-low { background-color: #10b981; }
.progress-fill.pain-medium { background-color: #f59e0b; }
.progress-fill.pain-high { background-color: #dc2626; }

/* Custom component: Status badges */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.875rem;
    font-weight: 500;
}

.status-badge.success {
    background-color: #d1fae5;
    color: #065f46;
}

.status-badge.warning {
    background-color: #fef3cd;
    color: #92400e;
}

.status-badge.error {
    background-color: #fee2e2;
    color: #991b1b;
}

.status-badge.info {
    background-color: #dbeafe;
    color: #1e40af;
}

/* Animation utilities */
.slide-in-left {
    animation: slideInLeft 0.5s ease-out forwards;
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out forwards;
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

/* Utility classes */
.text-gradient {
    background: linear-gradient(135deg, var(--primary-purple) 0%, var(--secondary-pink) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.backdrop-blur {
    backdrop-filter: blur(10px);
}

.glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

