<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>疼痛追踪器 - 记录分析经期疼痛模式 | period.health</title>
    <meta name="description" content="智能疼痛追踪器帮您记录每日痛经情况，分析疼痛模式，评估治疗效果，生成个人健康报告。">
    <meta name="keywords" content="疼痛追踪器, 痛经记录, 症状日记, 疼痛分析, 健康监测">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../styles.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- Navigation Header -->
    <header class="bg-white shadow-sm border-b">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-purple-600">period.health</div>
                <ul class="hidden md:flex space-x-6">
                    <li><a href="../index.html" class="text-gray-600 hover:text-purple-600 transition-colors">首页</a></li>
                    <li><a href="../interactive-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">互动解决方案</a></li>
                    <li><a href="../instant-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">即时解决方案</a></li>
                    <li><a href="../regular-conditioning.html" class="text-gray-600 hover:text-purple-600 transition-colors">平时调理</a></li>
                    <li><a href="../medical-research.html" class="text-gray-600 hover:text-purple-600 transition-colors">医学原理</a></li>
                    <li><a href="../blog-center.html" class="text-gray-600 hover:text-purple-600 transition-colors">文章中心</a></li>
                </ul>
                <button class="md:hidden p-2" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <div class="md:hidden hidden mt-4 pb-4" id="mobile-menu">
                <a href="../index.html" class="block py-2 text-gray-600">首页</a>
                <a href="../interactive-solutions.html" class="block py-2 text-gray-600">互动解决方案</a>
                <a href="../instant-solutions.html" class="block py-2 text-gray-600">即时解决方案</a>
                <a href="../regular-conditioning.html" class="block py-2 text-gray-600">平时调理</a>
                <a href="../medical-research.html" class="block py-2 text-gray-600">医学原理</a>
                <a href="../blog-center.html" class="block py-2 text-gray-600">文章中心</a>
            </div>
        </nav>
    </header>

    <main>
        <!-- Page Header -->
        <section class="bg-gradient-to-r from-red-500 to-pink-600 text-white py-12">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto text-center">
                    <h1 class="text-3xl md:text-4xl font-bold mb-4">疼痛追踪器</h1>
                    <p class="text-lg md:text-xl opacity-90">记录每日疼痛情况，发现模式，改善管理效果</p>
                </div>
            </div>
        </section>

        <!-- Pain Entry Form -->
        <section class="py-12 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <div class="bg-white rounded-lg shadow-lg p-8">
                        <h2 class="text-2xl font-bold mb-6 text-center">今日疼痛记录</h2>
                        
                        <form id="pain-entry-form" class="space-y-6">
                            <!-- Date Selection -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                                <label class="block text-lg font-semibold text-blue-700 mb-4">记录日期</label>
                                <input type="date" id="pain-date" name="date" 
                                       class="w-full p-3 border border-blue-300 rounded-lg focus:ring-2 focus:ring-blue-500"
                                       required>
                            </div>

                            <!-- Pain Intensity -->
                            <div class="bg-red-50 border border-red-200 rounded-lg p-6">
                                <label class="block text-lg font-semibold text-red-700 mb-4">疼痛强度</label>
                                <p class="text-sm text-red-600 mb-4">请选择今日最高疼痛强度（0=无痛，10=难以忍受）</p>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-600">0</span>
                                    <input type="range" id="pain-intensity-slider" name="intensity" min="0" max="10" step="1" value="0" 
                                           class="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-red">
                                    <span class="text-sm text-gray-600">10</span>
                                </div>
                                <div class="mt-2 text-center">
                                    <span class="text-2xl font-bold text-red-600" id="intensity-display">0</span>
                                    <span class="text-sm text-gray-600 ml-2" id="intensity-label">无痛</span>
                                </div>
                            </div>

                            <!-- Menstrual Status -->
                            <div class="bg-purple-50 border border-purple-200 rounded-lg p-6">
                                <label class="block text-lg font-semibold text-purple-700 mb-4">月经状态</label>
                                <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                    <label class="flex items-center">
                                        <input type="radio" name="menstrual_status" value="no_period" class="mr-2">
                                        <span class="text-sm">非经期</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="menstrual_status" value="day_1" class="mr-2">
                                        <span class="text-sm">经期第1天</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="menstrual_status" value="day_2" class="mr-2">
                                        <span class="text-sm">经期第2天</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="menstrual_status" value="day_3" class="mr-2">
                                        <span class="text-sm">经期第3天</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="menstrual_status" value="day_4_plus" class="mr-2">
                                        <span class="text-sm">经期第4天+</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="menstrual_status" value="pms" class="mr-2">
                                        <span class="text-sm">经前期</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="menstrual_status" value="ovulation" class="mr-2">
                                        <span class="text-sm">排卵期</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="radio" name="menstrual_status" value="other" class="mr-2">
                                        <span class="text-sm">其他</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Symptoms -->
                            <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                                <label class="block text-lg font-semibold text-yellow-700 mb-4">伴随症状（可多选）</label>
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="lower_abdominal_pain" class="mr-2">
                                        <span class="text-sm">下腹痛</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="lower_back_pain" class="mr-2">
                                        <span class="text-sm">腰痛</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="headache" class="mr-2">
                                        <span class="text-sm">头痛</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="nausea" class="mr-2">
                                        <span class="text-sm">恶心</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="fatigue" class="mr-2">
                                        <span class="text-sm">疲劳</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="mood_changes" class="mr-2">
                                        <span class="text-sm">情绪变化</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="bloating" class="mr-2">
                                        <span class="text-sm">腹胀</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="breast_tenderness" class="mr-2">
                                        <span class="text-sm">乳房胀痛</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="diarrhea" class="mr-2">
                                        <span class="text-sm">腹泻</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Treatment Methods Used -->
                            <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                                <label class="block text-lg font-semibold text-green-700 mb-4">使用的缓解方法（可多选）</label>
                                <div class="grid grid-cols-2 md:grid-cols-3 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="treatments" value="heat_therapy" class="mr-2">
                                        <span class="text-sm">热敷</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="treatments" value="pain_medication" class="mr-2">
                                        <span class="text-sm">止痛药</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="treatments" value="massage" class="mr-2">
                                        <span class="text-sm">按摩</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="treatments" value="exercise" class="mr-2">
                                        <span class="text-sm">运动</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="treatments" value="breathing_exercises" class="mr-2">
                                        <span class="text-sm">呼吸练习</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="treatments" value="yoga" class="mr-2">
                                        <span class="text-sm">瑜伽</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="treatments" value="rest" class="mr-2">
                                        <span class="text-sm">休息</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="treatments" value="herbal_tea" class="mr-2">
                                        <span class="text-sm">草本茶</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="treatments" value="none" class="mr-2">
                                        <span class="text-sm">无</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Treatment Effectiveness -->
                            <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                                <label class="block text-lg font-semibold text-blue-700 mb-4">治疗效果评价</label>
                                <div class="flex items-center space-x-2">
                                    <span class="text-sm text-gray-600">无效</span>
                                    <input type="range" id="effectiveness-slider" name="effectiveness" min="0" max="10" step="1" value="5" 
                                           class="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer">
                                    <span class="text-sm text-gray-600">非常有效</span>
                                </div>
                                <div class="mt-2 text-center">
                                    <span class="text-lg font-bold text-blue-600" id="effectiveness-display">5</span>
                                    <span class="text-sm text-gray-600 ml-2" id="effectiveness-label">中等</span>
                                </div>
                            </div>

                            <!-- Notes -->
                            <div class="bg-gray-50 border border-gray-200 rounded-lg p-6">
                                <label class="block text-lg font-semibold text-gray-700 mb-4">备注</label>
                                <textarea name="notes" rows="3" placeholder="记录任何额外信息，如触发因素、情绪状态、睡眠质量等..."
                                         class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500"></textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="text-center">
                                <button type="submit" 
                                        class="bg-red-600 text-white px-8 py-3 rounded-lg font-semibold text-lg hover:bg-red-700 transition-colors">
                                    保存今日记录
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- Pain History -->
        <section class="py-12 bg-gray-100">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <div class="flex justify-between items-center mb-8">
                        <h2 class="text-2xl md:text-3xl font-bold">历史记录</h2>
                        <div class="flex gap-4">
                            <button id="export-data" class="bg-blue-600 text-white px-4 py-2 rounded font-semibold hover:bg-blue-700 transition-colors">
                                导出数据
                            </button>
                            <button id="clear-data" class="bg-gray-600 text-white px-4 py-2 rounded font-semibold hover:bg-gray-700 transition-colors">
                                清除记录
                            </button>
                        </div>
                    </div>

                    <!-- Pain Chart -->
                    <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
                        <h3 class="text-xl font-bold mb-4">疼痛趋势图</h3>
                        <div id="pain-chart" class="h-64 flex items-center justify-center text-gray-500">
                            <div class="text-center">
                                <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                </svg>
                                <p>开始记录数据后，这里将显示疼痛趋势图</p>
                            </div>
                        </div>
                    </div>

                    <!-- Recent Entries -->
                    <div class="bg-white rounded-lg shadow-lg p-6">
                        <h3 class="text-xl font-bold mb-6">最近记录</h3>
                        <div id="recent-entries">
                            <div class="text-center text-gray-500 py-8">
                                <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                </svg>
                                <p>还没有记录，开始添加您的第一条疼痛记录吧</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Tips Section -->
        <section class="py-12 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-2xl md:text-3xl font-bold text-center mb-8">使用小贴士</h2>
                    
                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-blue-700 mb-4">📅 记录建议</h3>
                            <ul class="text-sm text-blue-600 space-y-2">
                                <li>• 每天固定时间记录，形成习惯</li>
                                <li>• 诚实记录真实感受，不要美化或夸大</li>
                                <li>• 记录至少3个月经周期以发现模式</li>
                                <li>• 包含尽可能多的相关信息</li>
                                <li>• 记录治疗方法的使用时机和效果</li>
                            </ul>
                        </div>
                        
                        <div class="bg-green-50 border border-green-200 rounded-lg p-6">
                            <h3 class="text-lg font-semibold text-green-700 mb-4">📊 数据分析</h3>
                            <ul class="text-sm text-green-600 space-y-2">
                                <li>• 观察疼痛强度和月经周期的关系</li>
                                <li>• 识别最有效的缓解方法</li>
                                <li>• 寻找疼痛触发因素</li>
                                <li>• 评估治疗进展</li>
                                <li>• 为就医准备详细数据</li>
                            </ul>
                        </div>
                    </div>
                    
                    <div class="mt-8 bg-yellow-50 border border-yellow-200 rounded-lg p-6">
                        <h3 class="text-lg font-semibold text-yellow-700 mb-3">⚠️ 重要提醒</h3>
                        <div class="text-sm text-yellow-700 space-y-2">
                            <p>• 此追踪器仅用于记录和监测，不能替代专业医疗诊断</p>
                            <p>• 如果疼痛突然加重或出现新症状，请及时就医</p>
                            <p>• 数据存储在您的设备本地，请定期导出备份</p>
                            <p>• 就医时可将导出的数据提供给医生参考</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">period.health</h3>
                    <p class="text-gray-400 mb-4">专业的经期疼痛管理平台，为女性健康保驾护航。</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">核心功能</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="../interactive-solutions.html" class="hover:text-white">症状评估器</a></li>
                        <li><a href="../interactive-solutions.html" class="hover:text-white">个性化方案</a></li>
                        <li><a href="../instant-solutions.html" class="hover:text-white">即时缓解</a></li>
                        <li><a href="../regular-conditioning.html" class="hover:text-white">平时调理</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">专业内容</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="../medical-research.html" class="hover:text-white">医学研究</a></li>
                        <li><a href="../blog-center.html" class="hover:text-white">健康资讯</a></li>
                        <li><a href="../blog-center.html" class="hover:text-white">用户故事</a></li>
                        <li><a href="../blog-center.html" class="hover:text-white">专家观点</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">法律信息</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="../privacy-policy.html" class="hover:text-white">隐私政策</a></li>
                        <li><a href="../terms-of-service.html" class="hover:text-white">服务条款</a></li>
                        <li><a href="../faq.html" class="hover:text-white">常见问题</a></li>
                        <li><a href="#" class="hover:text-white">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 period.health. 本网站内容仅供参考，不能替代专业医疗建议。</p>
            </div>
        </div>
    </footer>

    <script src="../main.js"></script>
    <script src="pain-tracker-logic.js"></script>
</body>
</html>

