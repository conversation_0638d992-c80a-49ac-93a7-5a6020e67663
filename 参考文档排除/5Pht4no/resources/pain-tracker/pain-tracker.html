<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>疼痛追踪器 - 记录和分析您的经期疼痛模式 | period.health</title>
    <meta name="description" content="科学记录经期疼痛数据，分析疼痛模式，为个性化治疗提供数据支持。专业的疼痛日志管理工具。">
    <meta name="keywords" content="疼痛日志, 痛经记录, 疼痛追踪, 症状分析, 经期管理, 数据分析">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../../styles.css">
    
    <!-- Schema.org WebApplication Structured Data (Suggestion 4) -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebApplication",
      "name": "period.health 疼痛追踪器",
      "description": "科学记录和分析经期疼痛模式的专业工具",
      "url": "https://period.health/resources/pain-tracker/pain-tracker.html",
      "applicationCategory": "HealthApplication",
      "operatingSystem": "Web Browser",
      "offers": {
        "@type": "Offer",
        "price": "0",
        "priceCurrency": "CNY"
      },
      "featureList": [
        "疼痛强度记录",
        "症状详细跟踪",
        "药物使用记录",
        "生活因素关联",
        "数据可视化分析",
        "模式识别"
      ],
      "about": {
        "@type": "MedicalCondition",
        "name": "Dysmenorrhea",
        "alternateName": ["痛经", "经期疼痛"]
      }
    }
    </script>

    <!-- Schema.org MedicalWebPage Structured Data (Suggestion 4) -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "MedicalWebPage",
      "name": "疼痛日志追踪系统",
      "description": "专业的经期疼痛记录和分析工具，帮助识别疼痛模式",
      "url": "https://period.health/resources/pain-tracker/pain-tracker.html",
      "medicalAudience": {
        "@type": "PatientsAudience",
        "name": "需要记录经期疼痛数据的女性"
      },
      "about": {
        "@type": "MedicalCondition",
        "name": "Chronic Pain Management"
      },
      "lastReviewed": "2024-01-15",
      "reviewedBy": {
        "@type": "Organization",
        "name": "period.health 医学顾问团队"
      }
    }
    </script>
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- Navigation Header -->
    <header class="bg-white shadow-sm border-b">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-purple-600">period.health</div>
                <ul class="hidden md:flex space-x-6">
                    <li><a href="../../index.html" class="text-gray-600 hover:text-purple-600 transition-colors">首页</a></li>
                    <li><a href="../../interactive-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">互动解决方案</a></li>
                    <li><a href="../../instant-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">即时解决方案</a></li>
                    <li><a href="../../regular-conditioning.html" class="text-gray-600 hover:text-purple-600 transition-colors">平时调理</a></li>
                    <li><a href="../../medical-research.html" class="text-gray-600 hover:text-purple-600 transition-colors">医学原理</a></li>
                    <li><a href="../../blog-center.html" class="text-gray-600 hover:text-purple-600 transition-colors">文章中心</a></li>
                </ul>
                <button class="md:hidden p-2" id="mobile-menu-btn" aria-label="打开移动菜单">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <div class="md:hidden hidden mt-4 pb-4" id="mobile-menu">
                <a href="../../index.html" class="block py-2 text-gray-600">首页</a>
                <a href="../../interactive-solutions.html" class="block py-2 text-gray-600">互动解决方案</a>
                <a href="../../instant-solutions.html" class="block py-2 text-gray-600">即时解决方案</a>
                <a href="../../regular-conditioning.html" class="block py-2 text-gray-600">平时调理</a>
                <a href="../../medical-research.html" class="block py-2 text-gray-600">医学原理</a>
                <a href="../../blog-center.html" class="block py-2 text-gray-600">文章中心</a>
            </div>
        </nav>
    </header>

    <main>
        <!-- Page Header -->
        <section class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white py-12 lg:py-16">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto text-center">
                    <h1 class="text-3xl md:text-4xl font-bold mb-4">疼痛追踪器</h1>
                    <p class="text-lg md:text-xl opacity-90 mb-6">科学记录疼痛数据，发现个人模式，为治疗提供有价值的参考</p>
                    
                    <!-- Quick Stats -->
                    <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mt-8">
                        <div class="bg-white bg-opacity-20 rounded-lg p-4">
                            <div class="text-2xl font-bold" id="total-records">0</div>
                            <div class="text-sm opacity-90">总记录数</div>
                        </div>
                        <div class="bg-white bg-opacity-20 rounded-lg p-4">
                            <div class="text-2xl font-bold" id="avg-pain-level">-</div>
                            <div class="text-sm opacity-90">平均疼痛强度</div>
                        </div>
                        <div class="bg-white bg-opacity-20 rounded-lg p-4">
                            <div class="text-2xl font-bold" id="last-record-date">-</div>
                            <div class="text-sm opacity-90">上次记录</div>
                        </div>
                        <div class="bg-white bg-opacity-20 rounded-lg p-4">
                            <div class="text-2xl font-bold" id="monthly-trend">-</div>
                            <div class="text-sm opacity-90">本月趋势</div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Navigation Tabs -->
        <section class="bg-white border-b">
            <div class="container mx-auto px-4">
                <div class="flex flex-wrap justify-center">
                    <button class="tab-btn active px-6 py-4 font-semibold border-b-2 border-purple-600 text-purple-600" data-tab="record">
                        新增记录
                    </button>
                    <button class="tab-btn px-6 py-4 font-semibold border-b-2 border-transparent text-gray-600 hover:text-purple-600" data-tab="history">
                        历史记录
                    </button>
                    <button class="tab-btn px-6 py-4 font-semibold border-b-2 border-transparent text-gray-600 hover:text-purple-600" data-tab="analysis">
                        数据分析
                    </button>
                    <button class="tab-btn px-6 py-4 font-semibold border-b-2 border-transparent text-gray-600 hover:text-purple-600" data-tab="export">
                        导出数据
                    </button>
                </div>
            </div>
        </section>

        <!-- Record Tab -->
        <section id="record-tab" class="tab-content py-12 lg:py-16">
            <div class="container mx-auto px-4">
                <div class="max-w-3xl mx-auto">
                    <div class="bg-white rounded-lg shadow-sm p-6 lg:p-8">
                        <h2 class="text-2xl font-bold mb-6">记录今日疼痛情况</h2>
                        
                        <!-- Success Message -->
                        <div id="success-message" class="hidden bg-green-50 border border-green-200 rounded-lg p-4 mb-6">
                            <div class="flex items-center">
                                <svg class="w-5 h-5 text-green-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                                </svg>
                                <span class="text-green-700 font-semibold">记录已成功保存！</span>
                            </div>
                            <p class="text-green-600 text-sm mt-1">您的疼痛数据已添加到痛经日志中。</p>
                        </div>

                        <form id="pain-log-form" class="space-y-6">
                            <!-- Date and Time -->
                            <div class="grid md:grid-cols-2 gap-6">
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">日期</label>
                                    <input type="date" name="date" required 
                                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                </div>
                                <div>
                                    <label class="block text-sm font-medium text-gray-700 mb-2">时间</label>
                                    <input type="time" name="time" required 
                                           class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                </div>
                            </div>

                            <!-- Pain Level -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">疼痛强度（0-10分）</label>
                                <div class="flex items-center space-x-4">
                                    <span class="text-sm text-gray-500">无痛</span>
                                    <input type="range" name="pain_level" min="0" max="10" value="0" 
                                           class="flex-1 h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer" 
                                           id="pain-level-slider">
                                    <span class="text-sm text-gray-500">剧痛</span>
                                    <div class="text-2xl font-bold text-purple-600 w-12 text-center" id="pain-level-display">0</div>
                                </div>
                                <div class="mt-2 grid grid-cols-11 text-xs text-gray-400">
                                    <span>0</span><span>1</span><span>2</span><span>3</span><span>4</span><span>5</span><span>6</span><span>7</span><span>8</span><span>9</span><span>10</span>
                                </div>
                            </div>

                            <!-- Pain Type -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">疼痛类型（可多选）</label>
                                <div class="grid md:grid-cols-2 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="pain_type" value="cramping" class="mr-2">
                                        <span>痉挛性疼痛</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="pain_type" value="aching" class="mr-2">
                                        <span>钝痛</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="pain_type" value="sharp" class="mr-2">
                                        <span>尖锐疼痛</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="pain_type" value="throbbing" class="mr-2">
                                        <span>跳动性疼痛</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="pain_type" value="burning" class="mr-2">
                                        <span>灼痛</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="pain_type" value="pressure" class="mr-2">
                                        <span>压迫性疼痛</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Location -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">疼痛位置（可多选）</label>
                                <div class="grid md:grid-cols-2 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="location" value="lower_abdomen" class="mr-2">
                                        <span>下腹部</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="location" value="lower_back" class="mr-2">
                                        <span>腰部</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="location" value="upper_thighs" class="mr-2">
                                        <span>大腿上部</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="location" value="pelvis" class="mr-2">
                                        <span>骨盆</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="location" value="side" class="mr-2">
                                        <span>侧腹</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="location" value="whole_abdomen" class="mr-2">
                                        <span>全腹部</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Associated Symptoms -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">伴随症状（可多选）</label>
                                <div class="grid md:grid-cols-2 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="nausea" class="mr-2">
                                        <span>恶心</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="vomiting" class="mr-2">
                                        <span>呕吐</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="diarrhea" class="mr-2">
                                        <span>腹泻</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="headache" class="mr-2">
                                        <span>头痛</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="fatigue" class="mr-2">
                                        <span>疲劳</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="mood_changes" class="mr-2">
                                        <span>情绪变化</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="bloating" class="mr-2">
                                        <span>腹胀</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="symptoms" value="breast_tenderness" class="mr-2">
                                        <span>乳房胀痛</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Menstrual Status -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">月经状态</label>
                                <select name="menstrual_status" required class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                    <option value="">请选择</option>
                                    <option value="before_period">月经前</option>
                                    <option value="day_1">月经第1天</option>
                                    <option value="day_2_3">月经第2-3天</option>
                                    <option value="day_4_plus">月经第4天及以后</option>
                                    <option value="after_period">月经后</option>
                                    <option value="mid_cycle">月经周期中期</option>
                                    <option value="irregular">不确定/不规律</option>
                                </select>
                            </div>

                            <!-- Medications Taken -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">使用的药物或治疗方法（可多选）</label>
                                <div class="grid md:grid-cols-2 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="medications" value="ibuprofen" class="mr-2">
                                        <span>布洛芬</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="medications" value="acetaminophen" class="mr-2">
                                        <span>对乙酰氨基酚</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="medications" value="naproxen" class="mr-2">
                                        <span>萘普生</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="medications" value="heat_pack" class="mr-2">
                                        <span>热敷</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="medications" value="massage" class="mr-2">
                                        <span>按摩</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="medications" value="exercise" class="mr-2">
                                        <span>运动</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="medications" value="birth_control" class="mr-2">
                                        <span>避孕药</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="medications" value="none" class="mr-2">
                                        <span>未使用任何方法</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Effectiveness Rating -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">治疗效果</label>
                                <select name="effectiveness" class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent">
                                    <option value="">请选择</option>
                                    <option value="very_effective">非常有效（疼痛明显减轻）</option>
                                    <option value="moderately_effective">有一定效果（部分缓解）</option>
                                    <option value="slightly_effective">效果轻微（略有改善）</option>
                                    <option value="not_effective">无效果</option>
                                    <option value="not_applicable">未使用治疗方法</option>
                                </select>
                            </div>

                            <!-- Lifestyle Factors -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-3">生活因素（可多选）</label>
                                <div class="grid md:grid-cols-2 gap-3">
                                    <label class="flex items-center">
                                        <input type="checkbox" name="lifestyle_factors" value="high_stress" class="mr-2">
                                        <span>高压力状态</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="lifestyle_factors" value="poor_sleep" class="mr-2">
                                        <span>睡眠不佳</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="lifestyle_factors" value="irregular_meals" class="mr-2">
                                        <span>饮食不规律</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="lifestyle_factors" value="caffeine" class="mr-2">
                                        <span>摄入咖啡因</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="lifestyle_factors" value="alcohol" class="mr-2">
                                        <span>饮酒</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" name="lifestyle_factors" value="sedentary" class="mr-2">
                                        <span>久坐不动</span>
                                    </label>
                                </div>
                            </div>

                            <!-- Additional Notes -->
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">备注</label>
                                <textarea name="notes" rows="3" 
                                          placeholder="记录任何其他相关信息，如特殊情况、环境因素等..."
                                          class="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent"></textarea>
                            </div>

                            <!-- Submit Button -->
                            <div class="flex justify-end space-x-4">
                                <button type="button" id="clear-form" 
                                        class="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors">
                                    清空表单
                                </button>
                                <button type="submit" 
                                        class="px-8 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors">
                                    保存记录
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </section>

        <!-- History Tab -->
        <section id="history-tab" class="tab-content hidden py-12 lg:py-16">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <h2 class="text-2xl font-bold mb-6">历史记录</h2>
                    
                    <!-- Filter Controls -->
                    <div class="bg-white rounded-lg p-6 mb-6">
                        <div class="flex flex-wrap gap-4 items-center">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">开始日期</label>
                                <input type="date" id="filter-start-date" class="p-2 border border-gray-300 rounded">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">结束日期</label>
                                <input type="date" id="filter-end-date" class="p-2 border border-gray-300 rounded">
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-1">最低疼痛级别</label>
                                <select id="filter-min-pain" class="p-2 border border-gray-300 rounded">
                                    <option value="">全部</option>
                                    <option value="1">1分及以上</option>
                                    <option value="3">3分及以上</option>
                                    <option value="5">5分及以上</option>
                                    <option value="7">7分及以上</option>
                                </select>
                            </div>
                            <div class="pt-6">
                                <button id="apply-filters" class="px-4 py-2 bg-purple-600 text-white rounded hover:bg-purple-700">
                                    应用筛选
                                </button>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Records List -->
                    <div id="records-list" class="space-y-4">
                        <!-- Records will be populated by JavaScript -->
                    </div>
                    
                    <!-- Pagination -->
                    <div id="pagination" class="flex justify-center mt-8">
                        <!-- Pagination will be populated by JavaScript -->
                    </div>
                </div>
            </div>
        </section>

        <!-- Analysis Tab -->
        <section id="analysis-tab" class="tab-content hidden py-12 lg:py-16">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <h2 class="text-2xl font-bold mb-6">数据分析</h2>
                    
                    <!-- Analysis Summary -->
                    <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                        <div class="bg-white rounded-lg p-6 shadow-sm">
                            <h3 class="text-sm font-medium text-gray-600 mb-2">平均疼痛级别</h3>
                            <div class="text-3xl font-bold text-purple-600" id="analysis-avg-pain">-</div>
                        </div>
                        <div class="bg-white rounded-lg p-6 shadow-sm">
                            <h3 class="text-sm font-medium text-gray-600 mb-2">最常见疼痛类型</h3>
                            <div class="text-lg font-semibold text-gray-800" id="analysis-common-type">-</div>
                        </div>
                        <div class="bg-white rounded-lg p-6 shadow-sm">
                            <h3 class="text-sm font-medium text-gray-600 mb-2">最有效治疗方法</h3>
                            <div class="text-lg font-semibold text-gray-800" id="analysis-effective-treatment">-</div>
                        </div>
                        <div class="bg-white rounded-lg p-6 shadow-sm">
                            <h3 class="text-sm font-medium text-gray-600 mb-2">月经周期模式</h3>
                            <div class="text-lg font-semibold text-gray-800" id="analysis-cycle-pattern">-</div>
                        </div>
                    </div>
                    
                    <!-- Pattern Insights -->
                    <div class="bg-white rounded-lg p-6 shadow-sm mb-8">
                        <h3 class="text-xl font-semibold mb-4">模式识别</h3>
                        <div id="pattern-insights" class="space-y-4">
                            <p class="text-gray-600">记录更多数据后，系统将为您显示个人疼痛模式的深入分析。</p>
                        </div>
                    </div>
                    
                    <!-- Recommendations -->
                    <div class="bg-white rounded-lg p-6 shadow-sm">
                        <h3 class="text-xl font-semibold mb-4">基于数据的建议</h3>
                        <div id="data-recommendations" class="space-y-4">
                            <p class="text-gray-600">基于您的记录数据，我们将提供个性化的治疗建议。</p>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Export Tab -->
        <section id="export-tab" class="tab-content hidden py-12 lg:py-16">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-2xl font-bold mb-6">导出数据</h2>
                    
                    <div class="bg-white rounded-lg p-6 lg:p-8 shadow-sm">
                        <div class="grid md:grid-cols-2 gap-8">
                            <div>
                                <h3 class="text-lg font-semibold mb-4">导出选项</h3>
                                <div class="space-y-4">
                                    <label class="flex items-center">
                                        <input type="checkbox" id="export-basic-data" checked class="mr-2">
                                        <span>基本疼痛数据</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="export-symptoms" checked class="mr-2">
                                        <span>伴随症状</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="export-medications" checked class="mr-2">
                                        <span>用药记录</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="export-lifestyle" class="mr-2">
                                        <span>生活方式因素</span>
                                    </label>
                                    <label class="flex items-center">
                                        <input type="checkbox" id="export-notes" class="mr-2">
                                        <span>备注信息</span>
                                    </label>
                                </div>
                                
                                <div class="mt-6">
                                    <label class="block text-sm font-medium text-gray-700 mb-2">日期范围</label>
                                    <div class="grid grid-cols-2 gap-4">
                                        <input type="date" id="export-start-date" class="p-2 border border-gray-300 rounded">
                                        <input type="date" id="export-end-date" class="p-2 border border-gray-300 rounded">
                                    </div>
                                </div>
                                
                                <div class="mt-6 space-y-3">
                                    <button id="export-csv" class="w-full bg-green-600 text-white py-3 rounded-lg hover:bg-green-700">
                                        导出为 CSV 格式
                                    </button>
                                    <button id="export-json" class="w-full bg-blue-600 text-white py-3 rounded-lg hover:bg-blue-700">
                                        导出为 JSON 格式
                                    </button>
                                </div>
                            </div>
                            
                            <div>
                                <h3 class="text-lg font-semibold mb-4">医生就诊准备</h3>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                                    <h4 class="font-semibold text-blue-700 mb-2">建议导出周期</h4>
                                    <p class="text-blue-600 text-sm">建议至少记录3个完整的月经周期（约3个月）后再导出数据给医生参考，这样能提供更准确的疼痛模式信息。</p>
                                </div>
                                
                                <div class="space-y-3">
                                    <div id="current-data-summary" class="bg-gray-50 rounded-lg p-4">
                                        <h4 class="font-semibold mb-2">当前数据概览</h4>
                                        <p class="text-sm text-gray-600">总记录数: <span id="summary-total-records">0</span></p>
                                        <p class="text-sm text-gray-600">记录时间跨度: <span id="summary-date-range">未记录</span></p>
                                        <p class="text-sm text-gray-600">数据完整度: <span id="summary-completeness">-</span></p>
                                    </div>
                                    
                                    <button id="generate-summary-report" class="w-full bg-purple-600 text-white py-3 rounded-lg hover:bg-purple-700">
                                        生成就诊摘要报告
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">period.health</h3>
                    <p class="text-gray-400 mb-4">专业的经期疼痛管理平台，为女性健康保驾护航。</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">核心功能</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="../../interactive-solutions.html" class="hover:text-white">症状评估器</a></li>
                        <li><a href="../../interactive-solutions.html" class="hover:text-white">个性化方案</a></li>
                        <li><a href="../../instant-solutions.html" class="hover:text-white">即时缓解</a></li>
                        <li><a href="../../regular-conditioning.html" class="hover:text-white">平时调理</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">专业内容</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="../../medical-research.html" class="hover:text-white">医学研究</a></li>
                        <li><a href="../../blog-center.html" class="hover:text-white">健康资讯</a></li>
                        <li><a href="../../blog-center.html" class="hover:text-white">用户故事</a></li>
                        <li><a href="../../blog-center.html" class="hover:text-white">专家观点</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">法律信息</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="../../privacy-policy.html" class="hover:text-white">隐私政策</a></li>
                        <li><a href="../../terms-of-service.html" class="hover:text-white">服务条款</a></li>
                        <li><a href="../../faq.html" class="hover:text-white">常见问题</a></li>
                        <li><a href="#" class="hover:text-white">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 period.health. 本网站内容仅供参考，不能替代专业医疗建议。</p>
            </div>
        </div>
    </footer>

    <script src="../../main.js"></script>
    <script src="pain-tracker-logic.js"></script>
    <script>
        // Enhanced Pain Tracker UI with better user feedback
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize pain level slider
            const painSlider = document.getElementById('pain-level-slider');
            const painDisplay = document.getElementById('pain-level-display');
            
            if (painSlider && painDisplay) {
                painSlider.addEventListener('input', function() {
                    painDisplay.textContent = this.value;
                    
                    // Update color based on pain level
                    const level = parseInt(this.value);
                    if (level <= 3) {
                        painDisplay.className = 'text-2xl font-bold text-green-600 w-12 text-center';
                    } else if (level <= 6) {
                        painDisplay.className = 'text-2xl font-bold text-yellow-600 w-12 text-center';
                    } else {
                        painDisplay.className = 'text-2xl font-bold text-red-600 w-12 text-center';
                    }
                });
            }

            // Auto-fill current date and time
            const dateInput = document.querySelector('input[name="date"]');
            const timeInput = document.querySelector('input[name="time"]');
            
            if (dateInput) {
                dateInput.value = new Date().toISOString().split('T')[0];
            }
            if (timeInput) {
                const now = new Date();
                const timeString = now.toTimeString().split(' ')[0].substring(0, 5);
                timeInput.value = timeString;
            }

            // Tab switching functionality
            const tabButtons = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');
            
            tabButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const tabName = this.getAttribute('data-tab');
                    
                    // Update button states
                    tabButtons.forEach(btn => {
                        btn.classList.remove('active', 'border-purple-600', 'text-purple-600');
                        btn.classList.add('border-transparent', 'text-gray-600');
                    });
                    this.classList.add('active', 'border-purple-600', 'text-purple-600');
                    this.classList.remove('border-transparent', 'text-gray-600');
                    
                    // Update content visibility
                    tabContents.forEach(content => {
                        if (content.id === tabName + '-tab') {
                            content.classList.remove('hidden');
                        } else {
                            content.classList.add('hidden');
                        }
                    });
                    
                    // Load tab-specific data
                    if (tabName === 'history') {
                        loadHistoryRecords();
                    } else if (tabName === 'analysis') {
                        loadAnalysisData();
                    } else if (tabName === 'export') {
                        updateExportSummary();
                    }
                });
            });

            // Clear form functionality
            const clearFormBtn = document.getElementById('clear-form');
            if (clearFormBtn) {
                clearFormBtn.addEventListener('click', function() {
                    if (confirm('确定要清空所有表单数据吗？')) {
                        document.getElementById('pain-log-form').reset();
                        
                        // Reset date and time to current
                        if (dateInput) {
                            dateInput.value = new Date().toISOString().split('T')[0];
                        }
                        if (timeInput) {
                            const now = new Date();
                            const timeString = now.toTimeString().split(' ')[0].substring(0, 5);
                            timeInput.value = timeString;
                        }
                        
                        // Reset pain level display
                        if (painSlider && painDisplay) {
                            painSlider.value = 0;
                            painDisplay.textContent = 0;
                            painDisplay.className = 'text-2xl font-bold text-green-600 w-12 text-center';
                        }
                    }
                });
            }

            // Load initial data
            updateDashboardStats();
            updateExportSummary();
        });

        // Enhanced functions for better UX
        function showSuccessMessage(message = '记录已成功保存！') {
            const successDiv = document.getElementById('success-message');
            if (successDiv) {
                successDiv.classList.remove('hidden');
                setTimeout(() => {
                    successDiv.classList.add('hidden');
                }, 5000);
                
                // Scroll to top to show the message
                successDiv.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }

        function updateDashboardStats() {
            const records = loadPainRecords();
            
            // Total records
            document.getElementById('total-records').textContent = records.length;
            
            // Average pain level
            if (records.length > 0) {
                const avgPain = records.reduce((sum, record) => sum + parseInt(record.pain_level || 0), 0) / records.length;
                document.getElementById('avg-pain-level').textContent = avgPain.toFixed(1);
                
                // Last record date
                const lastRecord = records[records.length - 1];
                if (lastRecord) {
                    const lastDate = new Date(lastRecord.date);
                    const today = new Date();
                    const diffDays = Math.floor((today - lastDate) / (1000 * 60 * 60 * 24));
                    
                    if (diffDays === 0) {
                        document.getElementById('last-record-date').textContent = '今天';
                    } else if (diffDays === 1) {
                        document.getElementById('last-record-date').textContent = '昨天';
                    } else {
                        document.getElementById('last-record-date').textContent = `${diffDays}天前`;
                    }
                }
                
                // Monthly trend (simplified calculation)
                const thisMonth = new Date().getMonth();
                const thisYear = new Date().getFullYear();
                const thisMonthRecords = records.filter(record => {
                    const recordDate = new Date(record.date);
                    return recordDate.getMonth() === thisMonth && recordDate.getFullYear() === thisYear;
                });
                
                if (thisMonthRecords.length > 0) {
                    const avgThisMonth = thisMonthRecords.reduce((sum, record) => sum + parseInt(record.pain_level || 0), 0) / thisMonthRecords.length;
                    const trend = avgThisMonth < 5 ? '良好' : avgThisMonth < 7 ? '一般' : '需关注';
                    document.getElementById('monthly-trend').textContent = trend;
                } else {
                    document.getElementById('monthly-trend').textContent = '暂无数据';
                }
            } else {
                document.getElementById('avg-pain-level').textContent = '-';
                document.getElementById('last-record-date').textContent = '-';
                document.getElementById('monthly-trend').textContent = '-';
            }
        }

        function loadHistoryRecords() {
            const records = loadPainRecords();
            const recordsList = document.getElementById('records-list');
            
            if (!recordsList) return;
            
            if (records.length === 0) {
                recordsList.innerHTML = `
                    <div class="bg-white rounded-lg p-8 text-center">
                        <p class="text-gray-600 mb-4">暂无疼痛记录</p>
                        <button onclick="document.querySelector('[data-tab=record]').click()" 
                                class="px-6 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700">
                            添加第一条记录
                        </button>
                    </div>
                `;
                return;
            }
            
            const sortedRecords = records.sort((a, b) => new Date(b.date + ' ' + b.time) - new Date(a.date + ' ' + a.time));
            
            recordsList.innerHTML = sortedRecords.map((record, index) => `
                <div class="bg-white rounded-lg p-6 shadow-sm">
                    <div class="flex justify-between items-start mb-4">
                        <div>
                            <h3 class="text-lg font-semibold">${record.date} ${record.time}</h3>
                            <p class="text-gray-600">${record.menstrual_status || '未记录'}</p>
                        </div>
                        <div class="flex items-center space-x-4">
                            <div class="text-right">
                                <div class="text-2xl font-bold ${parseInt(record.pain_level) <= 3 ? 'text-green-600' : parseInt(record.pain_level) <= 6 ? 'text-yellow-600' : 'text-red-600'}">
                                    ${record.pain_level}
                                </div>
                                <div class="text-sm text-gray-500">疼痛级别</div>
                            </div>
                            <button onclick="deleteRecord(${index})" 
                                    class="text-red-500 hover:text-red-700 p-2"
                                    title="删除记录">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                            </button>
                        </div>
                    </div>
                    
                    ${record.pain_type && record.pain_type.length > 0 ? `
                        <div class="mb-3">
                            <span class="text-sm font-medium text-gray-700">疼痛类型: </span>
                            <span class="text-sm text-gray-600">${Array.isArray(record.pain_type) ? record.pain_type.join(', ') : record.pain_type}</span>
                        </div>
                    ` : ''}
                    
                    ${record.symptoms && record.symptoms.length > 0 ? `
                        <div class="mb-3">
                            <span class="text-sm font-medium text-gray-700">伴随症状: </span>
                            <span class="text-sm text-gray-600">${Array.isArray(record.symptoms) ? record.symptoms.join(', ') : record.symptoms}</span>
                        </div>
                    ` : ''}
                    
                    ${record.medications && record.medications.length > 0 ? `
                        <div class="mb-3">
                            <span class="text-sm font-medium text-gray-700">治疗方法: </span>
                            <span class="text-sm text-gray-600">${Array.isArray(record.medications) ? record.medications.join(', ') : record.medications}</span>
                            ${record.effectiveness ? `<span class="text-sm text-gray-500 ml-2">(${record.effectiveness})</span>` : ''}
                        </div>
                    ` : ''}
                    
                    ${record.notes ? `
                        <div class="mt-3 p-3 bg-gray-50 rounded text-sm">
                            <strong>备注:</strong> ${record.notes}
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }

        function deleteRecord(index) {
            if (confirm('确定要删除这条记录吗？此操作无法撤销。')) {
                const records = loadPainRecords();
                records.splice(index, 1);
                savePainRecords(records);
                loadHistoryRecords();
                updateDashboardStats();
            }
        }

        function loadAnalysisData() {
            const records = loadPainRecords();
            
            if (records.length === 0) {
                document.getElementById('pattern-insights').innerHTML = '<p class="text-gray-600">暂无数据可供分析。请先记录一些疼痛数据。</p>';
                document.getElementById('data-recommendations').innerHTML = '<p class="text-gray-600">记录更多数据后，我们将为您提供个性化建议。</p>';
                return;
            }
            
            // Calculate statistics
            const avgPain = records.reduce((sum, record) => sum + parseInt(record.pain_level || 0), 0) / records.length;
            document.getElementById('analysis-avg-pain').textContent = avgPain.toFixed(1);
            
            // Most common pain type
            const painTypes = {};
            records.forEach(record => {
                if (record.pain_type) {
                    const types = Array.isArray(record.pain_type) ? record.pain_type : [record.pain_type];
                    types.forEach(type => {
                        painTypes[type] = (painTypes[type] || 0) + 1;
                    });
                }
            });
            const mostCommonType = Object.keys(painTypes).reduce((a, b) => painTypes[a] > painTypes[b] ? a : b, '未知');
            document.getElementById('analysis-common-type').textContent = mostCommonType;
            
            // Most effective treatment
            const treatments = {};
            records.forEach(record => {
                if (record.medications && record.effectiveness === 'very_effective') {
                    const meds = Array.isArray(record.medications) ? record.medications : [record.medications];
                    meds.forEach(med => {
                        treatments[med] = (treatments[med] || 0) + 1;
                    });
                }
            });
            const mostEffective = Object.keys(treatments).length > 0 
                ? Object.keys(treatments).reduce((a, b) => treatments[a] > treatments[b] ? a : b)
                : '暂无明显有效方法';
            document.getElementById('analysis-effective-treatment').textContent = mostEffective;
            
            // Simple pattern analysis
            const patterns = [];
            if (avgPain > 7) {
                patterns.push('您的平均疼痛级别较高，建议寻求专业医疗建议。');
            } else if (avgPain < 4) {
                patterns.push('您的疼痛级别相对较轻，当前的管理方法较为有效。');
            }
            
            const highPainDays = records.filter(record => parseInt(record.pain_level) >= 7).length;
            if (highPainDays > records.length * 0.3) {
                patterns.push('您有较多高强度疼痛的记录，建议评估当前治疗方案的有效性。');
            }
            
            document.getElementById('pattern-insights').innerHTML = patterns.length > 0 
                ? patterns.map(pattern => `<p class="text-gray-700">• ${pattern}</p>`).join('')
                : '<p class="text-gray-600">继续记录数据，我们将为您识别更多模式。</p>';
        }

        function updateExportSummary() {
            const records = loadPainRecords();
            
            document.getElementById('summary-total-records').textContent = records.length;
            
            if (records.length > 0) {
                const dates = records.map(record => new Date(record.date)).sort((a, b) => a - b);
                const startDate = dates[0].toLocaleDateString('zh-CN');
                const endDate = dates[dates.length - 1].toLocaleDateString('zh-CN');
                document.getElementById('summary-date-range').textContent = `${startDate} 至 ${endDate}`;
                
                const completeness = records.filter(record => 
                    record.pain_level && record.pain_type && record.menstrual_status
                ).length / records.length * 100;
                document.getElementById('summary-completeness').textContent = `${completeness.toFixed(0)}%`;
            } else {
                document.getElementById('summary-date-range').textContent = '未记录';
                document.getElementById('summary-completeness').textContent = '0%';
            }
        }
    </script>
</body>
</html>


