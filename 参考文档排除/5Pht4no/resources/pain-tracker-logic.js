// Pain Tracker Logic for period.health
// This file handles all functionality for the pain tracking system

let painEntries = [];

// Initialize the pain tracker when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializePainTracker();
    loadPainHistory();
    setupSliders();
    setupFormSubmission();
    setupDataManagement();
    
    // Set today's date as default
    document.getElementById('pain-date').valueAsDate = new Date();
});

// Initialize pain tracker functionality
function initializePainTracker() {
    console.log('Pain tracker initialized');
    painEntries = loadPainRecordsFromStorage();
    renderRecentEntries();
    renderPainChart();
}

// Setup slider functionality
function setupSliders() {
    // Pain intensity slider
    const painSlider = document.getElementById('pain-intensity-slider');
    const painDisplay = document.getElementById('intensity-display');
    const painLabel = document.getElementById('intensity-label');
    
    const painLabels = [
        '无痛', '非常轻微', '轻微', '轻度', '轻中度', 
        '中等', '中度', '重度', '严重', '非常严重', '极度疼痛'
    ];
    
    function updatePainDisplay() {
        const value = parseInt(painSlider.value);
        painDisplay.textContent = value;
        painLabel.textContent = painLabels[value] || '';
    }
    
    painSlider.addEventListener('input', updatePainDisplay);
    updatePainDisplay();
    
    // Effectiveness slider
    const effectivenessSlider = document.getElementById('effectiveness-slider');
    const effectivenessDisplay = document.getElementById('effectiveness-display');
    const effectivenessLabel = document.getElementById('effectiveness-label');
    
    const effectivenessLabels = [
        '无效', '几乎无效', '轻微有效', '稍有效果', '一般', 
        '中等', '较有效', '很有效', '非常有效', '极其有效', '完全缓解'
    ];
    
    function updateEffectivenessDisplay() {
        const value = parseInt(effectivenessSlider.value);
        effectivenessDisplay.textContent = value;
        effectivenessLabel.textContent = effectivenessLabels[value] || '';
    }
    
    effectivenessSlider.addEventListener('input', updateEffectivenessDisplay);
    updateEffectivenessDisplay();
}

// Setup form submission
function setupFormSubmission() {
    const form = document.getElementById('pain-entry-form');
    
    form.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const formData = new FormData(form);
        const entry = {
            id: Date.now(),
            date: formData.get('date'),
            intensity: parseInt(formData.get('intensity')),
            menstrualStatus: formData.get('menstrual_status'),
            symptoms: formData.getAll('symptoms'),
            treatments: formData.getAll('treatments'),
            effectiveness: parseInt(formData.get('effectiveness')),
            notes: formData.get('notes'),
            timestamp: new Date().toISOString()
        };
        
        // Validate required fields
        if (!entry.date || entry.intensity === null || !entry.menstrualStatus) {
            showNotification('请填写所有必填字段', 'error');
            return;
        }
        
        // Check if entry for this date already exists
        const existingEntryIndex = painEntries.findIndex(e => e.date === entry.date);
        
        if (existingEntryIndex !== -1) {
            // Update existing entry
            if (confirm('该日期已有记录，是否覆盖？')) {
                painEntries[existingEntryIndex] = entry;
                showNotification('记录已更新', 'success');
            } else {
                return;
            }
        } else {
            // Add new entry
            painEntries.push(entry);
            showNotification('记录已保存', 'success');
        }
        
        // Save to storage
        savePainRecordsToStorage();
        
        // Update displays
        renderRecentEntries();
        renderPainChart();
        
        // Reset form
        form.reset();
        document.getElementById('pain-date').valueAsDate = new Date();
        
        // Reset slider displays
        document.getElementById('intensity-display').textContent = '0';
        document.getElementById('intensity-label').textContent = '无痛';
        document.getElementById('effectiveness-display').textContent = '5';
        document.getElementById('effectiveness-label').textContent = '中等';
    });
}

// Setup data management buttons
function setupDataManagement() {
    // Export data button
    document.getElementById('export-data').addEventListener('click', function() {
        exportPainData();
    });
    
    // Clear data button
    document.getElementById('clear-data').addEventListener('click', function() {
        if (confirm('确定要清除所有记录吗？此操作无法撤销。')) {
            if (confirm('请再次确认，这将永久删除您的所有疼痛记录。')) {
                painEntries = [];
                savePainRecordsToStorage();
                renderRecentEntries();
                renderPainChart();
                showNotification('所有记录已清除', 'success');
            }
        }
    });
}

// Load pain history from localStorage
function loadPainHistory() {
    painEntries = loadPainRecordsFromStorage();
    renderRecentEntries();
    renderPainChart();
}

// Render recent entries
function renderRecentEntries() {
    const container = document.getElementById('recent-entries');
    
    if (painEntries.length === 0) {
        container.innerHTML = `
            <div class="text-center text-gray-500 py-8">
                <svg class="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                </svg>
                <p>还没有记录，开始添加您的第一条疼痛记录吧</p>
            </div>
        `;
        return;
    }
    
    // Sort entries by date (newest first)
    const sortedEntries = [...painEntries].sort((a, b) => new Date(b.date) - new Date(a.date));
    
    // Show last 10 entries
    const recentEntries = sortedEntries.slice(0, 10);
    
    container.innerHTML = recentEntries.map(entry => `
        <div class="border border-gray-200 rounded-lg p-4 mb-4">
            <div class="flex justify-between items-start mb-2">
                <div class="flex items-center">
                    <span class="text-lg font-semibold">${formatDate(entry.date)}</span>
                    <span class="ml-3 px-2 py-1 text-xs rounded-full ${getMenstrualStatusColor(entry.menstrualStatus)}">
                        ${getMenstrualStatusText(entry.menstrualStatus)}
                    </span>
                </div>
                <div class="flex items-center">
                    <span class="text-2xl font-bold ${getPainIntensityColor(entry.intensity)}">${entry.intensity}</span>
                    <span class="text-sm text-gray-600 ml-1">/10</span>
                </div>
            </div>
            
            ${entry.symptoms.length > 0 ? `
                <div class="mb-2">
                    <span class="text-sm font-semibold text-gray-700">症状：</span>
                    <div class="flex flex-wrap gap-1 mt-1">
                        ${entry.symptoms.map(symptom => `
                            <span class="px-2 py-1 bg-yellow-100 text-yellow-700 text-xs rounded">${getSymptomText(symptom)}</span>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
            
            ${entry.treatments.length > 0 ? `
                <div class="mb-2">
                    <span class="text-sm font-semibold text-gray-700">治疗方法：</span>
                    <div class="flex flex-wrap gap-1 mt-1">
                        ${entry.treatments.map(treatment => `
                            <span class="px-2 py-1 bg-green-100 text-green-700 text-xs rounded">${getTreatmentText(treatment)}</span>
                        `).join('')}
                    </div>
                </div>
            ` : ''}
            
            <div class="flex justify-between items-center text-sm text-gray-600">
                <span>治疗效果：${entry.effectiveness}/10</span>
                <button onclick="deletePainEntry(${entry.id})" class="text-red-600 hover:text-red-800 text-xs">删除</button>
            </div>
            
            ${entry.notes ? `
                <div class="mt-2 text-sm text-gray-600">
                    <strong>备注：</strong>${entry.notes}
                </div>
            ` : ''}
        </div>
    `).join('');
}

// Render pain chart (simple text-based for now)
function renderPainChart() {
    const chartContainer = document.getElementById('pain-chart');
    
    if (painEntries.length === 0) {
        chartContainer.innerHTML = `
            <div class="text-center">
                <svg class="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                </svg>
                <p>开始记录数据后，这里将显示疼痛趋势图</p>
            </div>
        `;
        return;
    }
    
    // Sort entries by date
    const sortedEntries = [...painEntries].sort((a, b) => new Date(a.date) - new Date(b.date));
    
    // Simple pain statistics
    const totalEntries = sortedEntries.length;
    const averagePain = (sortedEntries.reduce((sum, entry) => sum + entry.intensity, 0) / totalEntries).toFixed(1);
    const maxPain = Math.max(...sortedEntries.map(entry => entry.intensity));
    const minPain = Math.min(...sortedEntries.map(entry => entry.intensity));
    
    // Get last 7 entries for trend
    const recentEntries = sortedEntries.slice(-7);
    
    chartContainer.innerHTML = `
        <div class="grid grid-cols-2 md:grid-cols-4 gap-4 h-full">
            <div class="bg-blue-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-blue-600">${totalEntries}</div>
                <div class="text-sm text-blue-600">记录天数</div>
            </div>
            <div class="bg-yellow-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-yellow-600">${averagePain}</div>
                <div class="text-sm text-yellow-600">平均疼痛</div>
            </div>
            <div class="bg-red-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-red-600">${maxPain}</div>
                <div class="text-sm text-red-600">最高疼痛</div>
            </div>
            <div class="bg-green-50 rounded-lg p-4 text-center">
                <div class="text-2xl font-bold text-green-600">${minPain}</div>
                <div class="text-sm text-green-600">最低疼痛</div>
            </div>
        </div>
        
        <div class="mt-4">
            <h4 class="text-sm font-semibold mb-2 text-gray-700">最近7天趋势</h4>
            <div class="flex justify-between items-end h-16 bg-gray-50 rounded p-2">
                ${recentEntries.map(entry => `
                    <div class="flex flex-col items-center">
                        <div class="bg-red-500 rounded-t" style="height: ${(entry.intensity / 10) * 40}px; width: 20px; margin-bottom: 2px;"></div>
                        <div class="text-xs text-gray-600">${entry.date.split('-')[2]}</div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

// Delete pain entry
function deletePainEntry(id) {
    if (confirm('确定要删除这条记录吗？')) {
        painEntries = painEntries.filter(entry => entry.id !== id);
        savePainRecordsToStorage();
        renderRecentEntries();
        renderPainChart();
        showNotification('记录已删除', 'success');
    }
}

// Export pain data
function exportPainData() {
    if (painEntries.length === 0) {
        showNotification('没有数据可导出', 'error');
        return;
    }
    
    // Prepare data for export
    const exportData = {
        exportDate: new Date().toISOString(),
        totalEntries: painEntries.length,
        entries: painEntries.map(entry => ({
            date: entry.date,
            intensity: entry.intensity,
            menstrualStatus: getMenstrualStatusText(entry.menstrualStatus),
            symptoms: entry.symptoms.map(s => getSymptomText(s)),
            treatments: entry.treatments.map(t => getTreatmentText(t)),
            effectiveness: entry.effectiveness,
            notes: entry.notes
        }))
    };
    
    // Create downloadable file
    const dataStr = JSON.stringify(exportData, null, 2);
    const dataBlob = new Blob([dataStr], {type: 'application/json'});
    const url = URL.createObjectURL(dataBlob);
    
    const link = document.createElement('a');
    link.href = url;
    link.download = `痛经记录_${new Date().toISOString().split('T')[0]}.json`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
    
    showNotification('数据已导出', 'success');
}

// Utility functions
function formatDate(dateString) {
    const date = new Date(dateString + 'T00:00:00');
    return date.toLocaleDateString('zh-CN', { 
        year: 'numeric', 
        month: 'long', 
        day: 'numeric' 
    });
}

function getMenstrualStatusColor(status) {
    const colors = {
        'no_period': 'bg-gray-100 text-gray-700',
        'day_1': 'bg-red-100 text-red-700',
        'day_2': 'bg-red-100 text-red-700',
        'day_3': 'bg-orange-100 text-orange-700',
        'day_4_plus': 'bg-orange-100 text-orange-700',
        'pms': 'bg-purple-100 text-purple-700',
        'ovulation': 'bg-blue-100 text-blue-700',
        'other': 'bg-gray-100 text-gray-700'
    };
    return colors[status] || 'bg-gray-100 text-gray-700';
}

function getMenstrualStatusText(status) {
    const texts = {
        'no_period': '非经期',
        'day_1': '经期第1天',
        'day_2': '经期第2天',
        'day_3': '经期第3天',
        'day_4_plus': '经期第4天+',
        'pms': '经前期',
        'ovulation': '排卵期',
        'other': '其他'
    };
    return texts[status] || '未知';
}

function getPainIntensityColor(intensity) {
    if (intensity <= 2) return 'text-green-600';
    if (intensity <= 4) return 'text-yellow-600';
    if (intensity <= 6) return 'text-orange-600';
    return 'text-red-600';
}

function getSymptomText(symptom) {
    const texts = {
        'lower_abdominal_pain': '下腹痛',
        'lower_back_pain': '腰痛',
        'headache': '头痛',
        'nausea': '恶心',
        'fatigue': '疲劳',
        'mood_changes': '情绪变化',
        'bloating': '腹胀',
        'breast_tenderness': '乳房胀痛',
        'diarrhea': '腹泻'
    };
    return texts[symptom] || symptom;
}

function getTreatmentText(treatment) {
    const texts = {
        'heat_therapy': '热敷',
        'pain_medication': '止痛药',
        'massage': '按摩',
        'exercise': '运动',
        'breathing_exercises': '呼吸练习',
        'yoga': '瑜伽',
        'rest': '休息',
        'herbal_tea': '草本茶',
        'none': '无'
    };
    return texts[treatment] || treatment;
}

// Storage functions
function loadPainRecordsFromStorage() {
    try {
        const stored = localStorage.getItem('periodHealthPainRecords');
        return stored ? JSON.parse(stored) : [];
    } catch (error) {
        console.error('Error loading pain records:', error);
        return [];
    }
}

function savePainRecordsToStorage() {
    try {
        localStorage.setItem('periodHealthPainRecords', JSON.stringify(painEntries));
    } catch (error) {
        console.error('Error saving pain records:', error);
        showNotification('保存失败，请检查存储空间', 'error');
    }
}

// Notification function (uses the global one from main.js if available)
function showNotification(message, type = 'info') {
    if (typeof window.periodHealth?.showNotification === 'function') {
        window.periodHealth.showNotification(message, type);
    } else {
        // Fallback notification
        alert(message);
    }
}

