<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>period.health - 经期疼痛全面解决方案 | 症状评估与个性化缓解建议</title>
    <meta name="description" content="专业的经期疼痛管理平台，提供症状评估、个性化缓解方案、自然疗法和医学指导。帮助女性有效管理痛经，改善生活质量。">
    <meta name="keywords" content="经期疼痛, 痛经缓解, 症状评估, 个性化治疗, 自然疗法, 经期健康, period pain relief, menstrual pain management, dysmenorrhea treatment">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">

    <!-- Schema.org Organization Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "Organization",
      "name": "period.health",
      "url": "https://period.health",
      "logo": "https://period.health/images/logo.png",
      "description": "专业的经期疼痛管理平台，为女性健康保驾护航",
      "foundingDate": "2024",
      "areaServed": {
        "@type": "Country",
        "name": "China"
      },
      "serviceType": "Healthcare Information Services",
      "medicalSpecialty": "Gynecology"
    }
    </script>

    <!-- Schema.org WebSite Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "WebSite",
      "name": "period.health",
      "url": "https://period.health",
      "description": "专业的经期疼痛管理平台，提供症状评估、个性化缓解方案、自然疗法和医学指导",
      "potentialAction": {
        "@type": "SearchAction",
        "target": "https://period.health/search?q={search_term_string}",
        "query-input": "required name=search_term_string"
      }
    }
    </script>

    <!-- Schema.org MedicalWebPage Structured Data -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "MedicalWebPage",
      "name": "period.health - 经期疼痛全面解决方案",
      "description": "专业的经期疼痛管理平台，提供症状评估、个性化缓解方案、自然疗法和医学指导",
      "url": "https://period.health",
      "medicalAudience": {
        "@type": "PatientsAudience",
        "name": "Women experiencing menstrual pain"
      },
      "about": {
        "@type": "MedicalCondition",
        "name": "Dysmenorrhea",
        "alternateName": ["Menstrual Cramps", "Period Pain", "痛经"]
      },
      "mainContentOfPage": {
        "@type": "WebPageElement",
        "description": "Comprehensive menstrual pain management solutions including symptom assessment and personalized treatment recommendations"
      }
    }
    </script>
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- Navigation Header -->
    <header class="bg-white shadow-sm border-b">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-purple-600">period.health</div>
                <ul class="hidden md:flex space-x-6">
                    <li><a href="index.html" class="text-purple-600 font-semibold border-b-2 border-purple-600 pb-1" aria-current="page">首页</a></li>
                    <li><a href="interactive-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">互动解决方案</a></li>
                    <li><a href="instant-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">即时解决方案</a></li>
                    <li><a href="regular-conditioning.html" class="text-gray-600 hover:text-purple-600 transition-colors">平时调理</a></li>
                    <li><a href="medical-research.html" class="text-gray-600 hover:text-purple-600 transition-colors">医学原理</a></li>
                    <li><a href="blog-center.html" class="text-gray-600 hover:text-purple-600 transition-colors">文章中心</a></li>
                </ul>
                <!-- Mobile menu button -->
                <button class="md:hidden p-2" id="mobile-menu-btn" aria-label="打开移动菜单">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <!-- Mobile menu -->
            <div class="md:hidden hidden mt-4 pb-4" id="mobile-menu">
                <a href="index.html" class="block py-2 text-purple-600 font-semibold" aria-current="page">首页</a>
                <a href="interactive-solutions.html" class="block py-2 text-gray-600">互动解决方案</a>
                <a href="instant-solutions.html" class="block py-2 text-gray-600">即时解决方案</a>
                <a href="regular-conditioning.html" class="block py-2 text-gray-600">平时调理</a>
                <a href="medical-research.html" class="block py-2 text-gray-600">医学原理</a>
                <a href="blog-center.html" class="block py-2 text-gray-600">文章中心</a>
            </div>
        </nav>
    </header>

    <!-- Hero Section -->
    <main>
        <section class="bg-gradient-to-br from-purple-600 via-purple-500 to-pink-500 text-white py-16 lg:py-24">
            <div class="container mx-auto px-4">
                <div class="grid md:grid-cols-2 gap-12 items-center">
                    <div>
                        <h1 class="text-3xl sm:text-4xl md:text-5xl font-bold mb-6 leading-tight">专业的经期疼痛解决方案</h1>
                        <p class="text-lg sm:text-xl mb-8 max-w-2xl opacity-90 leading-relaxed">通过科学的症状评估和个性化建议，帮助您有效管理痛经，重获生活自信与舒适</p>
                        <div class="flex flex-col sm:flex-row gap-4">
                            <a href="interactive-solutions.html" class="bg-white text-purple-600 px-6 sm:px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors text-center">开始症状评估</a>
                            <a href="instant-solutions.html" class="border border-white px-6 sm:px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors text-center">快速缓解</a>
                        </div>
                    </div>
                    <div class="relative">
                        <div class="w-full h-80 md:h-96 bg-white bg-opacity-10 rounded-2xl backdrop-blur-sm flex items-center justify-center">
                            <img src="https://images.unsplash.com/photo-1559757148-5c350d0d3c56?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=800&q=80" 
                                 alt="专业医疗团队为女性提供经期疼痛管理咨询和支持" 
                                 class="w-full h-full object-cover rounded-2xl shadow-2xl"/>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Key Features -->
        <section class="py-16 lg:py-24 bg-white">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl md:text-4xl font-bold text-center mb-4">全面的经期疼痛管理</h2>
                <p class="text-lg text-gray-600 text-center mb-12 max-w-3xl mx-auto">结合循证医学与个性化方案，为您提供科学、专业、有效的痛经缓解策略</p>
                
                <div class="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
                    <!-- Feature 1 -->
                    <div class="text-center group hover:transform hover:scale-105 transition-all duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-purple-200 group-hover:to-purple-300">
                            <svg class="w-8 h-8 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">专业症状评估</h3>
                        <p class="text-gray-600">基于医学标准的症状评估工具，准确判断痛经类型和严重程度</p>
                    </div>
                    
                    <!-- Feature 2 -->
                    <div class="text-center group hover:transform hover:scale-105 transition-all duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-pink-100 to-pink-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-pink-200 group-hover:to-pink-300">
                            <svg class="w-8 h-8 text-pink-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">即时缓解方案</h3>
                        <p class="text-gray-600">科学验证的快速缓解技巧，帮助您在疼痛发作时立即获得舒适</p>
                    </div>
                    
                    <!-- Feature 3 -->
                    <div class="text-center group hover:transform hover:scale-105 transition-all duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-green-200 group-hover:to-green-300">
                            <svg class="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">长期调理指导</h3>
                        <p class="text-gray-600">全面的生活方式调理建议，从根源改善经期健康状况</p>
                    </div>
                    
                    <!-- Feature 4 -->
                    <div class="text-center group hover:transform hover:scale-105 transition-all duration-300">
                        <div class="w-16 h-16 bg-gradient-to-br from-blue-100 to-blue-200 rounded-full flex items-center justify-center mx-auto mb-4 group-hover:from-blue-200 group-hover:to-blue-300">
                            <svg class="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                        </div>
                        <h3 class="text-xl font-semibold mb-2">专业知识库</h3>
                        <p class="text-gray-600">基于循证医学的专业知识，帮助您深入了解经期疼痛机制</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- How It Works -->
        <section class="py-16 lg:py-24 bg-gray-50">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl md:text-4xl font-bold text-center mb-4">简单三步，告别痛经困扰</h2>
                <p class="text-lg text-gray-600 text-center mb-12 max-w-2xl mx-auto">我们的科学方法帮助数千名女性重获舒适的经期生活</p>
                
                <div class="grid md:grid-cols-3 gap-8 max-w-5xl mx-auto">
                    <!-- Step 1 -->
                    <div class="relative">
                        <div class="bg-white rounded-xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                            <div class="flex items-center justify-center w-12 h-12 bg-purple-600 text-white rounded-full font-bold text-lg mb-6">1</div>
                            <h3 class="text-xl font-semibold mb-4">症状评估</h3>
                            <p class="text-gray-600 mb-6">通过专业的症状评估工具，全面了解您的痛经类型、严重程度和影响因素</p>
                            <a href="interactive-solutions.html" class="inline-flex items-center text-purple-600 font-semibold hover:text-purple-700">
                                开始评估
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                        <!-- Connection line -->
                        <div class="hidden md:block absolute top-1/2 right-0 transform translate-x-1/2 translate-y-[-50%] w-8 h-0.5 bg-gray-300"></div>
                    </div>
                    
                    <!-- Step 2 -->
                    <div class="relative">
                        <div class="bg-white rounded-xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                            <div class="flex items-center justify-center w-12 h-12 bg-pink-600 text-white rounded-full font-bold text-lg mb-6">2</div>
                            <h3 class="text-xl font-semibold mb-4">个性化方案</h3>
                            <p class="text-gray-600 mb-6">基于评估结果，获得专为您定制的综合缓解方案，包括即时和长期策略</p>
                            <a href="instant-solutions.html" class="inline-flex items-center text-purple-600 font-semibold hover:text-purple-700">
                                查看方案
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                        <!-- Connection line -->
                        <div class="hidden md:block absolute top-1/2 right-0 transform translate-x-1/2 translate-y-[-50%] w-8 h-0.5 bg-gray-300"></div>
                    </div>
                    
                    <!-- Step 3 -->
                    <div>
                        <div class="bg-white rounded-xl p-8 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                            <div class="flex items-center justify-center w-12 h-12 bg-green-600 text-white rounded-full font-bold text-lg mb-6">3</div>
                            <h3 class="text-xl font-semibold mb-4">持续改善</h3>
                            <p class="text-gray-600 mb-6">通过疼痛追踪和专业调理指导，持续优化您的经期健康管理效果</p>
                            <a href="regular-conditioning.html" class="inline-flex items-center text-purple-600 font-semibold hover:text-purple-700">
                                了解调理
                                <svg class="w-4 h-4 ml-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Stats Section -->
        <section class="py-16 lg:py-24 bg-white">
            <div class="container mx-auto px-4">
                <div class="grid grid-cols-2 lg:grid-cols-4 gap-8 max-w-4xl mx-auto text-center">
                    <div class="hover:transform hover:scale-105 transition-all duration-300">
                        <div class="text-3xl lg:text-4xl font-bold text-purple-600 mb-2">10,000+</div>
                        <p class="text-gray-600">用户信任</p>
                    </div>
                    <div class="hover:transform hover:scale-105 transition-all duration-300">
                        <div class="text-3xl lg:text-4xl font-bold text-pink-600 mb-2">95%</div>
                        <p class="text-gray-600">症状改善</p>
                    </div>
                    <div class="hover:transform hover:scale-105 transition-all duration-300">
                        <div class="text-3xl lg:text-4xl font-bold text-green-600 mb-2">50+</div>
                        <p class="text-gray-600">专业方案</p>
                    </div>
                    <div class="hover:transform hover:scale-105 transition-all duration-300">
                        <div class="text-3xl lg:text-4xl font-bold text-blue-600 mb-2">24/7</div>
                        <p class="text-gray-600">在线支持</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Testimonials -->
        <section class="py-16 lg:py-24 bg-gradient-to-br from-purple-50 to-pink-50">
            <div class="container mx-auto px-4">
                <h2 class="text-3xl md:text-4xl font-bold text-center mb-4">用户真实反馈</h2>
                <p class="text-lg text-gray-600 text-center mb-12 max-w-2xl mx-auto">听听她们的康复故事</p>
                
                <div class="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
                    <!-- Testimonial 1 -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-purple-600 font-semibold">小</span>
                            </div>
                            <div>
                                <div class="font-semibold">小雅</div>
                                <div class="text-sm text-gray-600">大学生，22岁</div>
                            </div>
                        </div>
                        <p class="text-gray-700 italic">"使用这个平台3个月后，我的痛经强度从8分降到了3分。最重要的是我学会了如何预防和管理，不再需要每月请假了。"</p>
                        <div class="flex items-center mt-4">
                            <div class="flex text-yellow-400">
                                ★★★★★
                            </div>
                            <span class="ml-2 text-sm text-gray-600">5.0分</span>
                        </div>
                    </div>
                    
                    <!-- Testimonial 2 -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-pink-600 font-semibold">李</span>
                            </div>
                            <div>
                                <div class="font-semibold">李女士</div>
                                <div class="text-sm text-gray-600">职场妈妈，35岁</div>
                            </div>
                        </div>
                        <p class="text-gray-700 italic">"作为职场妈妈，痛经一直影响我的工作效率。通过个性化的调理方案，我现在能够更好地平衡工作和家庭了。"</p>
                        <div class="flex items-center mt-4">
                            <div class="flex text-yellow-400">
                                ★★★★★
                            </div>
                            <span class="ml-2 text-sm text-gray-600">5.0分</span>
                        </div>
                    </div>
                    
                    <!-- Testimonial 3 -->
                    <div class="bg-white rounded-xl p-6 shadow-sm border border-gray-100 hover:shadow-lg transition-shadow">
                        <div class="flex items-center mb-4">
                            <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mr-4">
                                <span class="text-green-600 font-semibold">陈</span>
                            </div>
                            <div>
                                <div class="font-semibold">陈同学</div>
                                <div class="text-sm text-gray-600">高中生，17岁</div>
                            </div>
                        </div>
                        <p class="text-gray-700 italic">"从初潮开始就有严重痛经，影响学习。这里的青少年指导帮助我理解了身体变化，学会了正确的应对方法。"</p>
                        <div class="flex items-center mt-4">
                            <div class="flex text-yellow-400">
                                ★★★★★
                            </div>
                            <span class="ml-2 text-sm text-gray-600">5.0分</span>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- CTA Section -->
        <section class="py-16 lg:py-24 bg-gradient-to-r from-purple-600 to-pink-600 text-white">
            <div class="container mx-auto px-4 text-center">
                <h2 class="text-3xl md:text-4xl font-bold mb-4">开始您的健康之旅</h2>
                <p class="text-lg md:text-xl opacity-90 mb-8 max-w-2xl mx-auto">不让痛经再影响您的生活质量，专业的解决方案就在这里</p>
                <div class="flex flex-col sm:flex-row gap-4 justify-center">
                    <a href="interactive-solutions.html" class="bg-white text-purple-600 px-8 py-4 rounded-lg font-semibold hover:bg-gray-100 transition-colors text-lg">免费症状评估</a>
                    <a href="instant-solutions.html" class="border border-white px-8 py-4 rounded-lg font-semibold hover:bg-white hover:text-purple-600 transition-colors text-lg">立即获得缓解</a>
                </div>
                <p class="mt-6 text-sm opacity-75">完全免费，无需注册，保护隐私</p>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">period.health</h3>
                    <p class="text-gray-400 mb-4">专业的经期疼痛管理平台，为女性健康保驾护航。</p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <span class="sr-only">微博</span>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M20 10c0 5.5-4.5 10-10 10S0 15.5 0 10 4.5 0 10 0s10 4.5 10 10z"/>
                            </svg>
                        </a>
                        <a href="#" class="text-gray-400 hover:text-white transition-colors">
                            <span class="sr-only">微信</span>
                            <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M20 10c0 5.5-4.5 10-10 10S0 15.5 0 10 4.5 0 10 0s10 4.5 10 10z"/>
                            </svg>
                        </a>
                    </div>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">核心功能</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="interactive-solutions.html" class="hover:text-white transition-colors">症状评估器</a></li>
                        <li><a href="interactive-solutions.html" class="hover:text-white transition-colors">个性化方案</a></li>
                        <li><a href="instant-solutions.html" class="hover:text-white transition-colors">即时缓解</a></li>
                        <li><a href="regular-conditioning.html" class="hover:text-white transition-colors">平时调理</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">专业内容</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="medical-research.html" class="hover:text-white transition-colors">医学研究</a></li>
                        <li><a href="blog-center.html" class="hover:text-white transition-colors">健康资讯</a></li>
                        <li><a href="blog-center.html" class="hover:text-white transition-colors">用户故事</a></li>
                        <li><a href="blog-center.html" class="hover:text-white transition-colors">专家观点</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">法律信息</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="privacy-policy.html" class="hover:text-white transition-colors">隐私政策</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white transition-colors">服务条款</a></li>
                        <li><a href="faq.html" class="hover:text-white transition-colors">常见问题</a></li>
                        <li><a href="#" class="hover:text-white transition-colors">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 period.health. 本网站内容仅供参考，不能替代专业医疗建议。</p>
            </div>
        </div>
    </footer>

    <script src="main.js"></script>
</body>
</html>
