// Assessment Logic for Period Health Symptom Analyzer
// This file contains the core logic for analyzing symptoms and generating recommendations

// Enhanced symptom analysis functions with better UX integration
function analyzeSymptoms(formData) {
    console.log('Analyzing symptoms with data:', formData);
    
    const analysis = {
        severity: calculateSeverity(formData),
        type: determinePainType(formData),
        riskFactors: identifyRiskFactors(formData),
        patterns: analyzePatterns(formData),
        prognosis: assessPrognosis(formData)
    };
    
    return analysis;
}

// Calculate symptom severity with improved scoring
function calculateSeverity(data) {
    let severityScore = 0;
    
    // Pain intensity (0-10) - primary factor
    const painIntensity = parseInt(data.pain_intensity) || 0;
    severityScore += painIntensity * 1.5;
    
    // Daily impact scoring with more nuanced assessment
    const impactScores = {
        'no_impact': 0,
        'mild_impact': 2,
        'moderate_impact': 5,
        'severe_impact': 8,
        'bedridden': 10
    };
    severityScore += impactScores[data.daily_impact] || 0;
    
    // Absence needed scoring
    const absenceScores = {
        'never': 0,
        'occasionally': 2,
        'frequently': 5,
        'always': 8
    };
    severityScore += absenceScores[data.absence_needed] || 0;
    
    // Sleep impact scoring
    const sleepScores = {
        'no_impact': 0,
        'difficulty_falling_asleep': 1,
        'frequent_awakening': 2,
        'early_awakening': 2,
        'severe_insomnia': 4
    };
    severityScore += sleepScores[data.sleep_impact] || 0;
    
    // Associated symptoms count with weighted scoring
    const symptomCount = Array.isArray(data.associated_symptoms) ? data.associated_symptoms.length : 0;
    severityScore += symptomCount * 0.5;
    
    // Duration of pain impact
    if (data.pain_duration) {
        const durationScores = {
            'few_hours': 0,
            'half_day': 1,
            'full_day': 2,
            'multiple_days': 3,
            'entire_period': 4
        };
        severityScore += durationScores[data.pain_duration] || 0;
    }
    
    // Determine severity level with enhanced categories
    if (severityScore <= 6) {
        return {
            level: 'mild',
            score: severityScore,
            description: '轻度痛经',
            color: 'green',
            advice: '症状相对较轻，可以通过改善生活方式和非药物方法有效管理。'
        };
    } else if (severityScore <= 14) {
        return {
            level: 'moderate',
            score: severityScore,
            description: '中度痛经',
            color: 'yellow',
            advice: '症状对日常生活造成一定影响，建议结合药物和非药物方法进行综合治疗。'
        };
    } else {
        return {
            level: 'severe',
            score: severityScore,
            description: '重度痛经',
            color: 'red',
            advice: '症状严重影响生活质量，强烈建议寻求专业医疗帮助制定治疗方案。'
        };
    }
}

// Enhanced pain type determination with more detailed analysis
function determinePainType(data) {
    const characteristics = Array.isArray(data.pain_characteristics) ? data.pain_characteristics : [];
    const timing = data.pain_timing;
    const ageGroup = data.age_group;
    const reproductiveStatus = data.reproductive_status;
    
    // Primary dysmenorrhea indicators with weights
    const primaryIndicators = [
        { condition: characteristics.includes('spasmodic'), weight: 2 },
        { condition: timing === 'with_menstruation', weight: 2 },
        { condition: ['under_18', '18_25'].includes(ageGroup), weight: 1.5 },
        { condition: reproductiveStatus === 'nulliparous', weight: 1 },
        { condition: data.cycle_regularity === 'regular', weight: 1 }
    ];
    
    // Secondary dysmenorrhea indicators with weights
    const secondaryIndicators = [
        { condition: timing === 'before_menstruation', weight: 2 },
        { condition: ['26_35', '36_45', 'over_45'].includes(ageGroup), weight: 1.5 },
        { condition: reproductiveStatus === 'parous', weight: 1 },
        { condition: ['very_irregular', 'unpredictable'].includes(data.cycle_regularity), weight: 2 },
        { condition: characteristics.includes('dull_ache'), weight: 1 }
    ];
    
    const primaryScore = primaryIndicators.reduce((sum, indicator) => 
        sum + (indicator.condition ? indicator.weight : 0), 0);
    const secondaryScore = secondaryIndicators.reduce((sum, indicator) => 
        sum + (indicator.condition ? indicator.weight : 0), 0);
    
    const totalPrimary = primaryIndicators.reduce((sum, indicator) => sum + indicator.weight, 0);
    const totalSecondary = secondaryIndicators.reduce((sum, indicator) => sum + indicator.weight, 0);
    
    if (primaryScore >= secondaryScore) {
        return {
            type: 'primary',
            confidence: primaryScore / totalPrimary,
            score: primaryScore,
            description: '原发性痛经',
            explanation: '通常由子宫内膜前列腺素过度分泌引起，多见于年轻女性，随年龄增长可能改善',
            treatment_focus: '重点关注症状缓解和生活方式改善'
        };
    } else {
        return {
            type: 'secondary',
            confidence: secondaryScore / totalSecondary,
            score: secondaryScore,
            description: '疑似继发性痛经',
            explanation: '可能由器质性疾病引起，如子宫内膜异位症、子宫肌瘤等',
            treatment_focus: '需要专业医疗评估，查找并治疗根本原因'
        };
    }
}

// Enhanced risk factor identification with severity levels
function identifyRiskFactors(data) {
    const riskFactors = [];
    
    // Critical severity pain
    if (parseInt(data.pain_intensity) >= 8) {
        riskFactors.push({
            factor: '极高强度疼痛',
            level: 'critical',
            severity: 'high',
            description: '疼痛强度达到8分以上，可能提示严重的病理情况，需要紧急医疗评估',
            action: '建议立即寻求医疗帮助'
        });
    } else if (parseInt(data.pain_intensity) >= 7) {
        riskFactors.push({
            factor: '高强度疼痛',
            level: 'high',
            severity: 'moderate',
            description: '疼痛强度达到7分，可能需要专业医疗干预',
            action: '建议咨询医生制定治疗方案'
        });
    }
    
    // Severe life impact assessment
    if (['severe_impact', 'bedridden'].includes(data.daily_impact)) {
        riskFactors.push({
            factor: '严重生活质量影响',
            level: 'high',
            severity: 'high',
            description: '痛经严重影响日常生活和工作能力，这不应该被视为正常现象',
            action: '强烈建议寻求专业医疗帮助'
        });
    }
    
    // Frequent absences
    if (['frequently', 'always'].includes(data.absence_needed)) {
        riskFactors.push({
            factor: '频繁需要请假',
            level: 'high',
            severity: 'moderate',
            description: '经常因痛经请假表明症状对社会功能产生重大影响',
            action: '建议进行全面医疗评估'
        });
    }
    
    // Cycle irregularity assessment
    if (['very_irregular', 'unpredictable'].includes(data.cycle_regularity)) {
        riskFactors.push({
            factor: '严重月经周期不规律',
            level: 'moderate',
            severity: 'moderate',
            description: '严重不规律的月经周期可能提示荷尔蒙失衡或器质性疾病',
            action: '建议进行荷尔蒙检查和妇科评估'
        });
    } else if (data.cycle_regularity === 'somewhat_irregular') {
        riskFactors.push({
            factor: '月经周期轻度不规律',
            level: 'low',
            severity: 'low',
            description: '轻度周期不规律可能与生活压力或体重变化相关',
            action: '注意生活方式因素，必要时咨询医生'
        });
    }
    
    // Lifestyle risk factors
    if (data.lifestyle && Array.isArray(data.lifestyle)) {
        if (data.lifestyle.includes('high_stress')) {
            riskFactors.push({
                factor: '高压力生活方式',
                level: 'moderate',
                severity: 'moderate',
                description: '慢性压力可能加重痛经症状，影响荷尔蒙平衡',
                action: '学习压力管理技巧，考虑心理咨询'
            });
        }
        
        if (data.lifestyle.includes('poor_diet')) {
            riskFactors.push({
                factor: '不良饮食习惯',
                level: 'low',
                severity: 'low',
                description: '不健康的饮食模式可能加重炎症反应',
                action: '调整饮食结构，增加抗炎食物'
            });
        }
        
        if (data.lifestyle.includes('sedentary')) {
            riskFactors.push({
                factor: '久坐生活方式',
                level: 'low',
                severity: 'low',
                description: '缺乏运动可能影响血液循环和疼痛感知',
                action: '逐步增加体育活动，特别是有氧运动'
            });
        }
    }
    
    // Treatment effectiveness assessment
    if (data.most_effective_method === 'none_effective') {
        riskFactors.push({
            factor: '既往治疗效果不佳',
            level: 'high',
            severity: 'moderate',
            description: '多种常规治疗方法无效可能提示需要更专业的医疗评估',
            action: '寻求妇科专科医生的进一步评估'
        });
    }
    
    // Age and reproductive status considerations
    if (data.age_group === 'over_45' && (parseInt(data.pain_intensity) >= 6)) {
        riskFactors.push({
            factor: '年龄相关风险',
            level: 'moderate',
            severity: 'moderate',
            description: '45岁以上女性新出现或加重的痛经需要排除器质性疾病',
            action: '建议进行全面的妇科检查'
        });
    }
    
    return riskFactors;
}

// Enhanced pattern analysis with more detailed insights
function analyzePatterns(data) {
    const patterns = {
        timing: data.pain_timing,
        duration: data.pain_duration,
        characteristics: Array.isArray(data.pain_characteristics) ? data.pain_characteristics : [],
        locations: Array.isArray(data.pain_location) ? data.pain_location : [],
        associatedSymptoms: Array.isArray(data.associated_symptoms) ? data.associated_symptoms : [],
        triggers: [],
        relievingFactors: []
    };
    
    // Identify potential triggers based on lifestyle factors
    if (data.lifestyle && Array.isArray(data.lifestyle)) {
        if (data.lifestyle.includes('high_stress')) {
            patterns.triggers.push('压力水平');
        }
        if (data.lifestyle.includes('poor_diet')) {
            patterns.triggers.push('饮食因素');
        }
        if (data.lifestyle.includes('irregular_sleep')) {
            patterns.triggers.push('睡眠不规律');
        }
    }
    
    // Identify relieving factors based on effective methods
    if (data.most_effective_method) {
        switch (data.most_effective_method) {
            case 'heat_therapy':
                patterns.relievingFactors.push('热疗');
                break;
            case 'medication':
                patterns.relievingFactors.push('药物治疗');
                break;
            case 'exercise':
                patterns.relievingFactors.push('运动');
                break;
            case 'rest':
                patterns.relievingFactors.push('休息');
                break;
            case 'massage':
                patterns.relievingFactors.push('按摩');
                break;
        }
    }
    
    return patterns;
}

// Enhanced prognosis assessment with more sophisticated scoring
function assessPrognosis(data) {
    let prognosisScore = 50; // Start with neutral prognosis
    
    // Positive prognostic factors
    if (data.age_group === 'under_18') prognosisScore += 20; // Very young age often improves
    if (data.age_group === '18_25') prognosisScore += 10; // Young age somewhat favorable
    
    if (data.lifestyle && Array.isArray(data.lifestyle)) {
        if (data.lifestyle.includes('regular_exercise')) prognosisScore += 15;
        if (data.lifestyle.includes('adequate_sleep')) prognosisScore += 10;
        if (data.lifestyle.includes('healthy_diet')) prognosisScore += 10;
        if (data.lifestyle.includes('good_stress_management')) prognosisScore += 15;
    }
    
    const painIntensity = parseInt(data.pain_intensity) || 0;
    if (painIntensity <= 4) prognosisScore += 15;
    if (painIntensity <= 6) prognosisScore += 10;
    
    if (data.cycle_regularity === 'regular') prognosisScore += 10;
    
    // Negative prognostic factors
    if (painIntensity >= 8) prognosisScore -= 20;
    if (painIntensity >= 7) prognosisScore -= 15;
    
    if (data.age_group === 'over_45') prognosisScore -= 10;
    if (['very_irregular', 'unpredictable'].includes(data.cycle_regularity)) prognosisScore -= 15;
    if (data.cycle_regularity === 'somewhat_irregular') prognosisScore -= 5;
    
    if (data.lifestyle && Array.isArray(data.lifestyle)) {
        if (data.lifestyle.includes('high_stress')) prognosisScore -= 15;
        if (data.lifestyle.includes('poor_diet')) prognosisScore -= 10;
        if (data.lifestyle.includes('sedentary')) prognosisScore -= 10;
    }
    
    if (['severe_impact', 'bedridden'].includes(data.daily_impact)) prognosisScore -= 15;
    if (data.most_effective_method === 'none_effective') prognosisScore -= 20;
    
    // Ensure score stays within bounds
    prognosisScore = Math.max(0, Math.min(100, prognosisScore));
    
    let outlook;
    let description;
    if (prognosisScore >= 75) {
        outlook = 'excellent';
        description = '预后优秀，通过适当的管理方法症状很可能得到显著改善';
    } else if (prognosisScore >= 60) {
        outlook = 'good';
        description = '预后良好，采用综合治疗方案有很大改善空间';
    } else if (prognosisScore >= 40) {
        outlook = 'moderate';
        description = '预后中等，需要系统性的治疗方案和持续管理';
    } else {
        outlook = 'requires_attention';
        description = '需要特别关注，建议尽快寻求专业医疗帮助';
    }
    
    return {
        score: prognosisScore,
        outlook: outlook,
        description: description,
        timeframe: prognosisScore >= 60 ? '3-6个月内可见显著改善' : '需要较长时间的专业治疗'
    };
}

// Rest of the functions remain the same but with improved error handling and validation...
// [Previous helper functions and recommendation generation functions would continue here]

// Export functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        analyzeSymptoms,
        calculateSeverity,
        determinePainType,
        identifyRiskFactors,
        generateRecommendations
    };
}




