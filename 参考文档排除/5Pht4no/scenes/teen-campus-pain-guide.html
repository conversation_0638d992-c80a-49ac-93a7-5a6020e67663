<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>青少年校园痛经指南 - 学生专属经期健康管理 | period.health</title>
    <meta name="description" content="专为青少年设计的校园痛经管理指南，包括课堂应对策略、与老师家长沟通技巧、校园生活调节方法等实用建议。">
    <meta name="keywords" content="青少年痛经, 校园痛经管理, 学生健康, 青春期健康, 校园生活">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="../styles.css">
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- Navigation Header -->
    <header class="bg-white shadow-sm border-b">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-purple-600">period.health</div>
                <ul class="hidden md:flex space-x-6">
                    <li><a href="../index.html" class="text-gray-600 hover:text-purple-600 transition-colors">首页</a></li>
                    <li><a href="../interactive-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">互动解决方案</a></li>
                    <li><a href="../instant-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">即时解决方案</a></li>
                    <li><a href="../regular-conditioning.html" class="text-gray-600 hover:text-purple-600 transition-colors">平时调理</a></li>
                    <li><a href="../medical-research.html" class="text-gray-600 hover:text-purple-600 transition-colors">医学原理</a></li>
                    <li><a href="../blog-center.html" class="text-gray-600 hover:text-purple-600 transition-colors">文章中心</a></li>
                </ul>
                <button class="md:hidden p-2" id="mobile-menu-btn">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <div class="md:hidden hidden mt-4 pb-4" id="mobile-menu">
                <a href="../index.html" class="block py-2 text-gray-600">首页</a>
                <a href="../interactive-solutions.html" class="block py-2 text-gray-600">互动解决方案</a>
                <a href="../instant-solutions.html" class="block py-2 text-gray-600">即时解决方案</a>
                <a href="../regular-conditioning.html" class="block py-2 text-gray-600">平时调理</a>
                <a href="../medical-research.html" class="block py-2 text-gray-600">医学原理</a>
                <a href="../blog-center.html" class="block py-2 text-gray-600">文章中心</a>
            </div>
        </nav>
    </header>

    <main>
        <!-- Hero Section -->
        <section class="bg-gradient-to-r from-pink-500 to-rose-600 text-white py-16">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto text-center">
                    <h1 class="text-4xl md:text-5xl font-bold mb-6">青少年校园痛经指南</h1>
                    <p class="text-xl md:text-2xl opacity-90 mb-8">专为学生设计的经期健康管理方案</p>
                    <div class="bg-white/10 backdrop-blur-sm rounded-lg p-6 max-w-2xl mx-auto">
                        <h2 class="text-lg font-semibold mb-3">🎒 校园专用</h2>
                        <p class="text-sm opacity-90">在学校也能轻松应对经期不适，不让疼痛影响学习生活</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Campus Life Image -->
        <section class="py-12 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <img src="https://r2.flowith.net/files/o/<EMAIL>" 
                         alt="青少年在校园环境中管理经期不适的场景图，展示在教室、图书馆等学习场所的应对策略" 
                         class="w-full h-auto rounded-lg shadow-lg">
                </div>
            </div>
        </section>

        <!-- Age-specific Guidance -->
        <section class="py-16 bg-gray-100">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <h2 class="text-3xl font-bold text-center mb-12">不同年龄段的特点与应对</h2>
                    
                    <div class="grid md:grid-cols-3 gap-8">
                        <!-- Middle School -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <div class="text-center mb-6">
                                <div class="w-16 h-16 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-2xl font-bold text-pink-600">初</span>
                                </div>
                                <h3 class="text-xl font-bold text-pink-700">初中阶段 (12-15岁)</h3>
                                <p class="text-sm text-pink-600">月经初潮，身体适应期</p>
                            </div>
                            
                            <div class="space-y-4">
                                <div class="bg-pink-50 border border-pink-200 rounded p-4">
                                    <h4 class="font-semibold text-pink-700 mb-2">生理特点</h4>
                                    <ul class="text-sm text-pink-600 space-y-1">
                                        <li>• 月经周期不规律</li>
                                        <li>• 痛经可能较重</li>
                                        <li>• 身体正在适应激素变化</li>
                                        <li>• 情绪波动较大</li>
                                    </ul>
                                </div>
                                
                                <div class="bg-pink-50 border border-pink-200 rounded p-4">
                                    <h4 class="font-semibold text-pink-700 mb-2">重点关注</h4>
                                    <ul class="text-sm text-pink-600 space-y-1">
                                        <li>• 建立正确的经期认知</li>
                                        <li>• 学习基本的自我护理</li>
                                        <li>• 与家长开放沟通</li>
                                        <li>• 关注营养和睡眠</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- High School -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <div class="text-center mb-6">
                                <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-2xl font-bold text-purple-600">高</span>
                                </div>
                                <h3 class="text-xl font-bold text-purple-700">高中阶段 (15-18岁)</h3>
                                <p class="text-sm text-purple-600">周期趋于稳定，学习压力大</p>
                            </div>
                            
                            <div class="space-y-4">
                                <div class="bg-purple-50 border border-purple-200 rounded p-4">
                                    <h4 class="font-semibold text-purple-700 mb-2">生理特点</h4>
                                    <ul class="text-sm text-purple-600 space-y-1">
                                        <li>• 月经周期逐渐规律</li>
                                        <li>• 痛经程度相对稳定</li>
                                        <li>• 身体发育基本成熟</li>
                                        <li>• 对疼痛认识更清楚</li>
                                    </ul>
                                </div>
                                
                                <div class="bg-purple-50 border border-purple-200 rounded p-4">
                                    <h4 class="font-semibold text-purple-700 mb-2">重点关注</h4>
                                    <ul class="text-sm text-purple-600 space-y-1">
                                        <li>• 平衡学习与健康</li>
                                        <li>• 学会压力管理</li>
                                        <li>• 独立处理经期问题</li>
                                        <li>• 建立健康的生活方式</li>
                                    </ul>
                                </div>
                            </div>
                        </div>

                        <!-- University -->
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <div class="text-center mb-6">
                                <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                                    <span class="text-2xl font-bold text-blue-600">大</span>
                                </div>
                                <h3 class="text-xl font-bold text-blue-700">大学阶段 (18-22岁)</h3>
                                <p class="text-sm text-blue-600">独立生活，自主管理</p>
                            </div>
                            
                            <div class="space-y-4">
                                <div class="bg-blue-50 border border-blue-200 rounded p-4">
                                    <h4 class="font-semibold text-blue-700 mb-2">生理特点</h4>
                                    <ul class="text-sm text-blue-600 space-y-1">
                                        <li>• 月经周期稳定</li>
                                        <li>• 痛经模式固定</li>
                                        <li>• 荷尔蒙水平相对平衡</li>
                                        <li>• 身体完全发育</li>
                                    </ul>
                                </div>
                                
                                <div class="bg-blue-50 border border-blue-200 rounded p-4">
                                    <h4 class="font-semibold text-blue-700 mb-2">重点关注</h4>
                                    <ul class="text-sm text-blue-600 space-y-1">
                                        <li>• 完全自主管理</li>
                                        <li>• 考虑专业治疗</li>
                                        <li>• 维持健康生活习惯</li>
                                        <li>• 为未来生活做规划</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Campus Management Strategies -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <h2 class="text-3xl font-bold text-center mb-12">校园痛经管理策略</h2>

                    <!-- Classroom Strategies -->
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-8 mb-8">
                        <h3 class="text-2xl font-bold mb-6 text-yellow-700">📚 课堂应对技巧</h3>
                        
                        <div class="grid md:grid-cols-2 gap-8">
                            <div>
                                <h4 class="text-lg font-semibold mb-4">立即缓解方法</h4>
                                <div class="space-y-3">
                                    <div class="flex items-start">
                                        <span class="text-yellow-500 mr-3 mt-1">🍃</span>
                                        <div>
                                            <strong>深呼吸练习</strong>
                                            <p class="text-sm text-yellow-600">课堂上不被察觉的缓解方法，通过腹式呼吸放松肌肉</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-yellow-500 mr-3 mt-1">🪑</span>
                                        <div>
                                            <strong>调整坐姿</strong>
                                            <p class="text-sm text-yellow-600">使用软垫，尝试不同坐姿找到最舒适的位置</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-yellow-500 mr-3 mt-1">🤲</span>
                                        <div>
                                            <strong>隐蔽按压</strong>
                                            <p class="text-sm text-yellow-600">用手掌轻压下腹部，或按压手部穴位</p>
                                        </div>
                                    </div>
                                    <div class="flex items-start">
                                        <span class="text-yellow-500 mr-3 mt-1">🌡️</span>
                                        <div>
                                            <strong>暖宝宝使用</strong>
                                            <p class="text-sm text-yellow-600">贴在衣服内侧，提供持续的温热缓解</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-lg font-semibold mb-4">课堂注意事项</h4>
                                <div class="bg-white rounded-lg border border-yellow-300 p-4">
                                    <div class="space-y-3">
                                        <div class="text-sm">
                                            <strong class="text-yellow-700">提前准备：</strong>
                                            <p class="text-yellow-600">经期前在书包里准备暖宝宝、止痛药等应急用品</p>
                                        </div>
                                        <div class="text-sm">
                                            <strong class="text-yellow-700">合理请假：</strong>
                                            <p class="text-yellow-600">疼痛严重时不要硬撑，及时向老师说明情况</p>
                                        </div>
                                        <div class="text-sm">
                                            <strong class="text-yellow-700">选择座位：</strong>
                                            <p class="text-yellow-600">如可能，选择靠近门口或过道的座位，方便离开</p>
                                        </div>
                                        <div class="text-sm">
                                            <strong class="text-yellow-700">课前服药：</strong>
                                            <p class="text-yellow-600">在医生指导下，可在疼痛出现前预防性服药</p>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- School Facilities -->
                    <div class="bg-green-50 border border-green-200 rounded-lg p-8 mb-8">
                        <h3 class="text-2xl font-bold mb-6 text-green-700">🏫 善用校园设施</h3>
                        
                        <div class="grid md:grid-cols-3 gap-6">
                            <div class="bg-white rounded-lg p-4 border border-green-300">
                                <h4 class="font-semibold text-green-600 mb-3">医务室/保健室</h4>
                                <ul class="text-sm text-green-600 space-y-2">
                                    <li>• 寻求专业建议</li>
                                    <li>• 获得基础药物</li>
                                    <li>• 短暂休息场所</li>
                                    <li>• 了解健康知识</li>
                                </ul>
                            </div>
                            
                            <div class="bg-white rounded-lg p-4 border border-green-300">
                                <h4 class="font-semibold text-green-600 mb-3">宿舍/休息室</h4>
                                <ul class="text-sm text-green-600 space-y-2">
                                    <li>• 充分休息调整</li>
                                    <li>• 使用热敷设备</li>
                                    <li>• 放松情绪</li>
                                    <li>• 补充营养</li>
                                </ul>
                            </div>
                            
                            <div class="bg-white rounded-lg p-4 border border-green-300">
                                <h4 class="font-semibold text-green-600 mb-3">食堂/小卖部</h4>
                                <ul class="text-sm text-green-600 space-y-2">
                                    <li>• 选择温热食物</li>
                                    <li>• 购买生理用品</li>
                                    <li>• 补充热水</li>
                                    <li>• 避免生冷食物</li>
                                </ul>
                            </div>
                        </div>
                    </div>

                    <!-- Communication Guide -->
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-8">
                        <h3 class="text-2xl font-bold mb-6 text-blue-700">💬 沟通指南</h3>
                        
                        <div class="grid md:grid-cols-2 gap-8">
                            <div>
                                <h4 class="text-lg font-semibold mb-4 text-blue-600">与老师沟通</h4>
                                <div class="space-y-4">
                                    <div class="bg-white rounded p-4 border border-blue-300">
                                        <h5 class="font-semibold text-blue-600 mb-2">沟通要点</h5>
                                        <ul class="text-sm text-blue-600 space-y-1">
                                            <li>• 选择合适的时机（私下交流）</li>
                                            <li>• 简洁说明情况，无需过度详细</li>
                                            <li>• 表达对学习的重视</li>
                                            <li>• 询问补课或作业安排</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="bg-white rounded p-4 border border-blue-300">
                                        <h5 class="font-semibold text-blue-600 mb-2">话术建议</h5>
                                        <p class="text-sm text-blue-600 italic">
                                            "老师，我身体不太舒服，可能需要休息一下。
                                            今天的课程内容我会课后补上，请问有什么需要特别注意的吗？"
                                        </p>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h4 class="text-lg font-semibold mb-4 text-blue-600">与家长沟通</h4>
                                <div class="space-y-4">
                                    <div class="bg-white rounded p-4 border border-blue-300">
                                        <h5 class="font-semibold text-blue-600 mb-2">沟通内容</h5>
                                        <ul class="text-sm text-blue-600 space-y-1">
                                            <li>• 描述疼痛具体症状</li>
                                            <li>• 说明对学习生活的影响</li>
                                            <li>• 请求就医或购买用品</li>
                                            <li>• 寻求情感支持</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="bg-white rounded p-4 border border-blue-300">
                                        <h5 class="font-semibold text-blue-600 mb-2">克服羞涩</h5>
                                        <ul class="text-sm text-blue-600 space-y-1">
                                            <li>• 记住这是正常的生理现象</li>
                                            <li>• 家长会理解和支持</li>
                                            <li>• 及时沟通有助于获得帮助</li>
                                            <li>• 可先与信任的人（如妈妈）谈话</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Peer Support -->
        <section class="py-16 bg-gray-100">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-3xl font-bold text-center mb-12">同伴支持与互助</h2>
                    
                    <div class="grid md:grid-cols-2 gap-8">
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-xl font-bold mb-4 text-purple-700">👭 朋友间的相互支持</h3>
                            
                            <div class="space-y-4">
                                <div class="border-l-4 border-purple-500 pl-4">
                                    <h4 class="font-semibold text-purple-600">情感支持</h4>
                                    <p class="text-sm text-purple-600">与信任的朋友分享感受，获得理解和鼓励</p>
                                </div>
                                
                                <div class="border-l-4 border-purple-500 pl-4">
                                    <h4 class="font-semibold text-purple-600">实际帮助</h4>
                                    <p class="text-sm text-purple-600">互相提醒带用品，在课堂上相互照应</p>
                                </div>
                                
                                <div class="border-l-4 border-purple-500 pl-4">
                                    <h4 class="font-semibold text-purple-600">经验分享</h4>
                                    <p class="text-sm text-purple-600">分享有效的缓解方法和应对经验</p>
                                </div>
                                
                                <div class="border-l-4 border-purple-500 pl-4">
                                    <h4 class="font-semibold text-purple-600">学习协助</h4>
                                    <p class="text-sm text-purple-600">帮助补记笔记，分享课程内容</p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="bg-white rounded-lg shadow-lg p-6">
                            <h3 class="text-xl font-bold mb-4 text-green-700">🌱 建立支持小组</h3>
                            
                            <div class="space-y-4">
                                <div class="bg-green-50 border border-green-200 rounded p-4">
                                    <h4 class="font-semibold text-green-600 mb-2">线上交流</h4>
                                    <ul class="text-sm text-green-600 space-y-1">
                                        <li>• 创建私密聊天群组</li>
                                        <li>• 分享健康知识和资源</li>
                                        <li>• 及时互相提醒和关心</li>
                                        <li>• 保护隐私，理性讨论</li>
                                    </ul>
                                </div>
                                
                                <div class="bg-green-50 border border-green-200 rounded p4">
                                    <h4 class="font-semibold text-green-600 mb-2">线下活动</h4>
                                    <ul class="text-sm text-green-600 space-y-1">
                                        <li>• 组织健康知识学习会</li>
                                        <li>• 一起进行舒缓运动</li>
                                        <li>• 购买健康用品时结伴</li>
                                        <li>• 相互陪伴就医检查</li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Emergency Kit -->
        <section class="py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto">
                    <h2 class="text-3xl font-bold text-center mb-12">校园应急包准备</h2>
                    
                    <div class="bg-orange-50 border border-orange-200 rounded-lg p-8">
                        <div class="grid md:grid-cols-2 gap-8">
                            <div>
                                <h3 class="text-xl font-bold mb-4 text-orange-700">🎒 书包常备物品</h3>
                                
                                <div class="space-y-4">
                                    <div class="bg-white rounded-lg p-4 border border-orange-300">
                                        <h4 class="font-semibold text-orange-600 mb-2">生理用品</h4>
                                        <ul class="text-sm text-orange-600 space-y-1">
                                            <li>• 卫生巾/棉条（2-3片）</li>
                                            <li>• 小包装湿巾</li>
                                            <li>• 密封袋（用于废物处理）</li>
                                            <li>• 备用内裤</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="bg-white rounded-lg p-4 border border-orange-300">
                                        <h4 class="font-semibold text-orange-600 mb-2">缓解用品</h4>
                                        <ul class="text-sm text-orange-600 space-y-1">
                                            <li>• 暖宝宝（2-3个）</li>
                                            <li>• 非处方止痛药</li>
                                            <li>• 小包装红糖</li>
                                            <li>• 薄荷糖（缓解恶心）</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                            
                            <div>
                                <h3 class="text-xl font-bold mb-4 text-orange-700">🏠 宿舍储备</h3>
                                
                                <div class="space-y-4">
                                    <div class="bg-white rounded-lg p-4 border border-orange-300">
                                        <h4 class="font-semibold text-orange-600 mb-2">护理用品</h4>
                                        <ul class="text-sm text-orange-600 space-y-1">
                                            <li>• 热水袋/电热毯</li>
                                            <li>• 充足的生理用品</li>
                                            <li>• 换洗衣物</li>
                                            <li>• 舒适的睡衣</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="bg-white rounded-lg p-4 border border-orange-300">
                                        <h4 class="font-semibold text-orange-600 mb-2">营养补充</h4>
                                        <ul class="text-sm text-orange-600 space-y-1">
                                            <li>• 红糖、蜂蜜</li>
                                            <li>• 红枣、桂圆干</li>
                                            <li>• 即热营养粥</li>
                                            <li>• 维生素补充剂</li>
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mt-6 bg-yellow-100 border border-yellow-300 rounded-lg p-4">
                            <h4 class="font-semibold text-yellow-700 mb-2">💡 贴心提醒</h4>
                            <div class="text-sm text-yellow-700 space-y-1">
                                <p>• 定期检查并补充应急用品</p>
                                <p>• 与室友分享，可以互相帮助</p>
                                <p>• 了解校园购买点的位置和时间</p>
                                <p>• 记录有效制剂的品牌和规格</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- Call to Action -->
        <section class="py-16 bg-pink-600 text-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto text-center">
                    <h2 class="text-3xl font-bold mb-6">开始建立你的校园管理计划</h2>
                    <p class="text-xl opacity-90 mb-8">使用我们的工具，制定适合校园生活的痛经管理方案</p>
                    
                    <div class="flex flex-col sm:flex-row gap-4 justify-center">
                        <a href="../interactive-solutions.html" class="bg-white text-pink-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors">
                            症状评估
                        </a>
                        <a href="../resources/pain-tracker.html" class="border border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-pink-600 transition-colors">
                            开始追踪
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">period.health</h3>
                    <p class="text-gray-400 mb-4">专业的经期疼痛管理平台，为女性健康保驾护航。</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">核心功能</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="../interactive-solutions.html" class="hover:text-white">症状评估器</a></li>
                        <li><a href="../interactive-solutions.html" class="hover:text-white">个性化方案</a></li>
                        <li><a href="../instant-solutions.html" class="hover:text-white">即时缓解</a></li>
                        <li><a href="../regular-conditioning.html" class="hover:text-white">平时调理</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">专业内容</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="../medical-research.html" class="hover:text-white">医学研究</a></li>
                        <li><a href="../blog-center.html" class="hover:text-white">健康资讯</a></li>
                        <li><a href="../blog-center.html" class="hover:text-white">用户故事</a></li>
                        <li><a href="../blog-center.html" class="hover:text-white">专家观点</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">法律信息</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="../privacy-policy.html" class="hover:text-white">隐私政策</a></li>
                        <li><a href="../terms-of-service.html" class="hover:text-white">服务条款</a></li>
                        <li><a href="../faq.html" class="hover:text-white">常见问题</a></li>
                        <li><a href="#" class="hover:text-white">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 period.health. 本网站内容仅供参考，不能替代专业医疗建议。</p>
            </div>
        </div>
    </footer>

    <script src="../main.js"></script>
</body>
</html>

