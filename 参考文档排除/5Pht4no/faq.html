<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>常见问题 - 痛经管理、治疗方法、就医指导 | period.health</title>
    <meta name="description" content="痛经相关常见问题解答，包括症状识别、治疗方法、就医时机、生活调节等专业指导，帮助女性更好管理经期健康。">
    <meta name="keywords" content="痛经FAQ, 常见问题, 治疗方法, 就医指导, 经期健康">
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles.css">

    <!-- Schema.org FAQPage Structured Data (Suggestion 4) -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "name": "period.health 常见问题",
      "description": "关于经期疼痛、治疗方法、就医时机等常见问题的专业解答",
      "url": "https://period.health/faq.html",
      "mainEntity": [
        {
          "@type": "Question",
          "name": "什么程度的痛经需要看医生？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "如果疼痛严重影响日常生活、需要经常请假、止痛药无效、症状逐渐加重，或疼痛强度达到7-10分，建议及时就医。"
          }
        },
        {
          "@type": "Question", 
          "name": "非处方止痛药安全吗？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "按说明书正确使用非甾体抗炎药(NSAIDs)如布洛芬是安全有效的。应在医生指导下使用，避免长期大量服用。"
          }
        },
        {
          "@type": "Question",
          "name": "热敷对痛经有效吗？", 
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "热敷是科学验证的有效方法，能放松子宫肌肉、改善血液循环。建议使用40-45°C的温度，每次15-20分钟。"
          }
        },
        {
          "@type": "Question",
          "name": "运动会加重痛经吗？",
          "acceptedAnswer": {
            "@type": "Answer", 
            "text": "适度运动实际上有助于缓解痛经。推荐轻到中度的有氧运动，如散步、瑜伽、游泳。应避免高强度剧烈运动。"
          }
        },
        {
          "@type": "Question",
          "name": "饮食对痛经有影响吗？",
          "acceptedAnswer": {
            "@type": "Answer",
            "text": "健康饮食有助于改善痛经。建议增加抗炎食物（omega-3、绿叶蔬菜），减少咖啡因、糖和加工食品的摄入。"
          }
        }
      ],
      "about": {
        "@type": "MedicalCondition",
        "name": "Dysmenorrhea",
        "alternateName": ["痛经", "经期疼痛", "月经痛"]
      },
      "publisher": {
        "@type": "Organization",
        "name": "period.health"
      },
      "dateModified": "2024-01-15"
    }
    </script>

    <!-- Schema.org MedicalWebPage Structured Data (Suggestion 4) -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "MedicalWebPage",
      "name": "痛经管理常见问题解答",
      "description": "专业医疗团队回答痛经相关常见问题，提供科学可靠的健康指导",
      "url": "https://period.health/faq.html",
      "medicalAudience": {
        "@type": "PatientsAudience",
        "name": "寻求痛经相关信息的女性"
      },
      "about": {
        "@type": "MedicalCondition",
        "name": "Dysmenorrhea"
      },
      "lastReviewed": "2024-01-15",
      "reviewedBy": {
        "@type": "Organization",
        "name": "period.health 医学顾问团队"
      }
    }
    </script>
</head>
<body class="bg-gray-50 text-gray-800">
    <!-- Navigation Header -->
    <header class="bg-white shadow-sm border-b">
        <nav class="container mx-auto px-4 py-4">
            <div class="flex items-center justify-between">
                <div class="text-2xl font-bold text-purple-600">period.health</div>
                <ul class="hidden md:flex space-x-6">
                    <li><a href="index.html" class="text-gray-600 hover:text-purple-600 transition-colors">首页</a></li>
                    <li><a href="interactive-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">互动解决方案</a></li>
                    <li><a href="instant-solutions.html" class="text-gray-600 hover:text-purple-600 transition-colors">即时解决方案</a></li>
                    <li><a href="regular-conditioning.html" class="text-gray-600 hover:text-purple-600 transition-colors">平时调理</a></li>
                    <li><a href="medical-research.html" class="text-gray-600 hover:text-purple-600 transition-colors">医学原理</a></li>
                    <li><a href="blog-center.html" class="text-gray-600 hover:text-purple-600 transition-colors">文章中心</a></li>
                </ul>
                <button class="md:hidden p-2" id="mobile-menu-btn" aria-label="打开移动菜单">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                    </svg>
                </button>
            </div>
            <div class="md:hidden hidden mt-4 pb-4" id="mobile-menu">
                <a href="index.html" class="block py-2 text-gray-600">首页</a>
                <a href="interactive-solutions.html" class="block py-2 text-gray-600">互动解决方案</a>
                <a href="instant-solutions.html" class="block py-2 text-gray-600">即时解决方案</a>
                <a href="regular-conditioning.html" class="block py-2 text-gray-600">平时调理</a>
                <a href="medical-research.html" class="block py-2 text-gray-600">医学原理</a>
                <a href="blog-center.html" class="block py-2 text-gray-600">文章中心</a>
            </div>
        </nav>
    </header>

    <main>
        <!-- Page Header -->
        <section class="bg-gradient-to-r from-blue-600 to-indigo-600 text-white py-12 lg:py-16">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto text-center">
                    <h1 class="text-3xl md:text-4xl font-bold mb-4">常见问题</h1>
                    <p class="text-lg md:text-xl opacity-90">专业医疗团队为您解答痛经管理相关疑问</p>
                </div>
            </div>
        </section>

        <!-- Search Section -->
        <section class="py-8 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-2xl mx-auto">
                    <div class="relative">
                        <input type="search" 
                               placeholder="搜索问题..." 
                               class="w-full pl-12 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                               id="faq-search"
                               aria-label="搜索常见问题">
                        <svg class="w-5 h-5 absolute left-4 top-3.5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </section>

        <!-- FAQ Categories -->
        <section class="py-12 lg:py-16 bg-gray-50">
            <div class="container mx-auto px-4">
                <div class="max-w-6xl mx-auto">
                    <!-- Category Navigation -->
                    <div class="flex flex-wrap justify-center mb-8 gap-4">
                        <button class="category-btn active px-6 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors" data-category="all">
                            全部问题
                        </button>
                        <button class="category-btn px-6 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors border" data-category="symptoms">
                            症状识别
                        </button>
                        <button class="category-btn px-6 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors border" data-category="treatment">
                            治疗方法
                        </button>
                        <button class="category-btn px-6 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors border" data-category="medical">
                            就医指导
                        </button>
                        <button class="category-btn px-6 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors border" data-category="lifestyle">
                            生活调节
                        </button>
                        <button class="category-btn px-6 py-2 bg-white text-gray-700 rounded-lg hover:bg-gray-50 transition-colors border" data-category="tools">
                            网站功能
                        </button>
                    </div>

                    <!-- FAQ Items -->
                    <div class="space-y-4" id="faq-container">
                        
                        <!-- Medical Questions -->
                        <div class="faq-item bg-white rounded-lg shadow-sm" data-category="medical" id="faq-8">
                            <button class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleFAQ(this)">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-800">什么程度的痛经需要看医生？</h3>
                                    <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </button>
                            <div class="hidden border-t p-6 text-gray-700">
                                <p class="mb-4">以下情况建议及时就医：</p>
                                <ul class="list-disc list-inside space-y-2 mb-4">
                                    <li><strong>疼痛严重影响生活：</strong>无法正常工作、学习或进行日常活动</li>
                                    <li><strong>止痛药无效：</strong>常规非处方止痛药不能缓解症状</li>
                                    <li><strong>症状逐渐加重：</strong>疼痛强度或持续时间在增加</li>
                                    <li><strong>疼痛强度极高：</strong>疼痛评分达到7-10分（满分10分）</li>
                                    <li><strong>伴随异常症状：</strong>如异常出血、发热、呕吐等</li>
                                    <li><strong>年龄相关变化：</strong>35岁以上女性痛经突然加重</li>
                                </ul>
                                <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                    <p class="text-red-700 text-sm">
                                        <strong>紧急就医情况：</strong>如果出现剧烈腹痛伴有发热、异常大量出血或晕厥，
                                        应立即寻求急诊医疗帮助。
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Treatment Questions -->
                        <div class="faq-item bg-white rounded-lg shadow-sm" data-category="treatment">
                            <button class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleFAQ(this)">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-800">非处方止痛药安全吗？怎样正确使用？</h3>
                                    <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </button>
                            <div class="hidden border-t p-6 text-gray-700">
                                <p class="mb-4">非甾体抗炎药（NSAIDs）如布洛芬、萘普生等，按正确方法使用是安全有效的：</p>
                                
                                <div class="grid md:grid-cols-2 gap-6 mb-4">
                                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                        <h4 class="font-semibold text-green-700 mb-2">✅ 正确使用方法</h4>
                                        <ul class="text-sm space-y-1">
                                            <li>• 按包装说明的剂量使用</li>
                                            <li>• 随餐或餐后服用，减少胃部刺激</li>
                                            <li>• 在疼痛开始时尽早服用</li>
                                            <li>• 定时服用比需要时才服用更有效</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                        <h4 class="font-semibold text-red-700 mb-2">⚠️ 注意事项</h4>
                                        <ul class="text-sm space-y-1">
                                            <li>• 避免长期大量使用</li>
                                            <li>• 胃病患者慎用</li>
                                            <li>• 不要超过最大日剂量</li>
                                            <li>• 如有疑虑请咨询医生</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <p class="text-sm text-gray-600">
                                    <strong>专业建议：</strong>如果需要连续使用超过3天或症状没有改善，建议咨询医生。
                                </p>
                            </div>
                        </div>

                        <div class="faq-item bg-white rounded-lg shadow-sm" data-category="treatment">
                            <button class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleFAQ(this)">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-800">热敷对痛经有效吗？如何正确热敷？</h3>
                                    <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </button>
                            <div class="hidden border-t p-6 text-gray-700">
                                <p class="mb-4">热敷是科学验证的有效缓解方法，可以：</p>
                                <ul class="list-disc list-inside space-y-2 mb-4">
                                    <li>放松子宫肌肉，减少痉挛</li>
                                    <li>改善血液循环</li>
                                    <li>阻断疼痛信号传导</li>
                                    <li>促进内啡肽释放</li>
                                </ul>
                                
                                <h4 class="font-semibold mb-3">正确热敷方法：</h4>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                                    <ul class="space-y-2 text-sm">
                                        <li><strong>温度：</strong>40-45°C（手感温热但不烫）</li>
                                        <li><strong>时间：</strong>每次15-20分钟，可重复使用</li>
                                        <li><strong>位置：</strong>下腹部、腰部或疼痛最明显的区域</li>
                                        <li><strong>工具：</strong>热水袋、暖宝宝、电热垫等</li>
                                    </ul>
                                </div>
                                
                                <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                    <p class="text-yellow-700 text-sm">
                                        <strong>安全提醒：</strong>避免温度过高造成烫伤，使用电热设备时注意安全。
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Lifestyle Questions -->
                        <div class="faq-item bg-white rounded-lg shadow-sm" data-category="lifestyle">
                            <button class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleFAQ(this)">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-800">运动会加重痛经吗？什么运动适合？</h3>
                                    <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </button>
                            <div class="hidden border-t p-6 text-gray-700">
                                <p class="mb-4">适度运动实际上有助于缓解痛经，但需要选择合适的类型和强度：</p>
                                
                                <div class="grid md:grid-cols-2 gap-6 mb-4">
                                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                        <h4 class="font-semibold text-green-700 mb-2">推荐运动</h4>
                                        <ul class="text-sm space-y-1">
                                            <li>• <strong>瑜伽：</strong>特别是温和的拉伸动作</li>
                                            <li>• <strong>散步：</strong>轻松的有氧运动</li>
                                            <li>• <strong>游泳：</strong>水的浮力减少压力</li>
                                            <li>• <strong>太极：</strong>缓慢柔和的动作</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                        <h4 class="font-semibold text-red-700 mb-2">避免的运动</h4>
                                        <ul class="text-sm space-y-1">
                                            <li>• 高强度间歇训练(HIIT)</li>
                                            <li>• 重量举重</li>
                                            <li>• 激烈的竞技运动</li>
                                            <li>• 颠簸性强的运动</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <h4 class="font-semibold mb-2">运动的好处：</h4>
                                <ul class="list-disc list-inside text-sm space-y-1 mb-4">
                                    <li>促进内啡肽释放，天然镇痛</li>
                                    <li>改善血液循环</li>
                                    <li>减少肌肉紧张</li>
                                    <li>转移注意力</li>
                                </ul>
                                
                                <p class="text-sm text-gray-600">
                                    <strong>个性化建议：</strong>根据自己的舒适程度调整运动强度，疼痛严重时以休息为主。
                                </p>
                            </div>
                        </div>

                        <div class="faq-item bg-white rounded-lg shadow-sm" data-category="lifestyle">
                            <button class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleFAQ(this)">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-800">饮食对痛经有影响吗？什么食物有帮助？</h3>
                                    <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </button>
                            <div class="hidden border-t p-6 text-gray-700">
                                <p class="mb-4">健康的饮食确实能帮助改善痛经症状：</p>
                                
                                <div class="space-y-4">
                                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                        <h4 class="font-semibold text-green-700 mb-2">有益食物</h4>
                                        <div class="grid md:grid-cols-2 gap-4 text-sm">
                                            <div>
                                                <strong>抗炎食物：</strong>
                                                <ul class="mt-1 space-y-1">
                                                    <li>• 深海鱼类（三文鱼、沙丁鱼）</li>
                                                    <li>• 坚果和种子（核桃、亚麻籽）</li>
                                                    <li>• 绿叶蔬菜（菠菜、羽衣甘蓝）</li>
                                                    <li>• 莓类水果（蓝莓、草莓）</li>
                                                </ul>
                                            </div>
                                            <div>
                                                <strong>补充营养：</strong>
                                                <ul class="mt-1 space-y-1">
                                                    <li>• 富含镁的食物（黑巧克力、香蕉）</li>
                                                    <li>• 全谷物（燕麦、糙米）</li>
                                                    <li>• 豆类和豆制品</li>
                                                    <li>• 姜和姜黄</li>
                                                </ul>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                        <h4 class="font-semibold text-red-700 mb-2">建议减少的食物</h4>
                                        <ul class="text-sm space-y-1">
                                            <li>• <strong>高糖食物：</strong>可能加重炎症</li>
                                            <li>• <strong>过多咖啡因：</strong>可能增加紧张感</li>
                                            <li>• <strong>加工食品：</strong>含有过多添加剂</li>
                                            <li>• <strong>反式脂肪：</strong>促进炎症反应</li>
                                            <li>• <strong>过多盐分：</strong>可能加重水肿</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mt-4">
                                    <p class="text-blue-700 text-sm">
                                        <strong>实用建议：</strong>保持规律的三餐，避免空腹时间过长，充足饮水，
                                        经期前一周开始注意饮食调整效果更好。
                                    </p>
                                </div>
                            </div>
                        </div>

                        <!-- Symptoms Questions -->
                        <div class="faq-item bg-white rounded-lg shadow-sm" data-category="symptoms">
                            <button class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleFAQ(this)">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-800">如何区分正常的经期不适和需要关注的痛经？</h3>
                                    <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </button>
                            <div class="hidden border-t p-6 text-gray-700">
                                <div class="grid md:grid-cols-2 gap-6">
                                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                        <h4 class="font-semibold text-green-700 mb-2">正常经期不适</h4>
                                        <ul class="text-sm space-y-1">
                                            <li>• 轻度到中度腹部不适</li>
                                            <li>• 疼痛持续1-2天</li>
                                            <li>• 不影响日常活动</li>
                                            <li>• 简单方法可以缓解</li>
                                            <li>• 疼痛评分通常在1-4分</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="bg-red-50 border border-red-200 rounded-lg p-4">
                                        <h4 class="font-semibold text-red-700 mb-2">需要关注的痛经</h4>
                                        <ul class="text-sm space-y-1">
                                            <li>• 剧烈疼痛影响生活</li>
                                            <li>• 持续3天以上</li>
                                            <li>• 需要止痛药才能应对</li>
                                            <li>• 伴有恶心呕吐</li>
                                            <li>• 疼痛评分在5分以上</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <p class="mt-4 text-sm text-gray-600">
                                    <strong>关键指标：</strong>如果疼痛影响到工作、学习或日常活动，就超出了"正常"范围，
                                    建议寻求医疗帮助。
                                </p>
                            </div>
                        </div>

                        <!-- Tools Questions -->
                        <div class="faq-item bg-white rounded-lg shadow-sm" data-category="tools">
                            <button class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleFAQ(this)">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-800">症状评估器的结果准确吗？</h3>
                                    <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </button>
                            <div class="hidden border-t p-6 text-gray-700">
                                <p class="mb-4">我们的症状评估器基于科学的医学标准设计，但它的作用是：</p>
                                
                                <div class="grid md:grid-cols-2 gap-6 mb-4">
                                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                        <h4 class="font-semibold text-green-700 mb-2">评估器的价值</h4>
                                        <ul class="text-sm space-y-1">
                                            <li>• 帮助您系统性地描述症状</li>
                                            <li>• 提供初步的严重程度评估</li>
                                            <li>• 生成个性化的缓解建议</li>
                                            <li>• 识别需要医疗关注的信号</li>
                                        </ul>
                                    </div>
                                    
                                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                                        <h4 class="font-semibold text-yellow-700 mb-2">使用限制</h4>
                                        <ul class="text-sm space-y-1">
                                            <li>• 不能替代医生的诊断</li>
                                            <li>• 结果仅供参考</li>
                                            <li>• 无法检测器质性疾病</li>
                                            <li>• 依赖于您提供信息的准确性</li>
                                        </ul>
                                    </div>
                                </div>
                                
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                                    <p class="text-blue-700 text-sm">
                                        <strong>最佳使用方法：</strong>将评估结果作为就医时的参考信息，
                                        帮助您更好地与医生沟通症状情况。
                                    </p>
                                </div>
                            </div>
                        </div>

                        <div class="faq-item bg-white rounded-lg shadow-sm" data-category="tools">
                            <button class="w-full text-left p-6 focus:outline-none focus:ring-2 focus:ring-blue-500" onclick="toggleFAQ(this)">
                                <div class="flex justify-between items-center">
                                    <h3 class="text-lg font-semibold text-gray-800">疼痛追踪器的数据安全吗？</h3>
                                    <svg class="w-5 h-5 text-gray-500 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </div>
                            </button>
                            <div class="hidden border-t p-6 text-gray-700">
                                <p class="mb-4">我们非常重视您的数据隐私和安全：</p>
                                
                                <h4 class="font-semibold mb-3">数据安全措施：</h4>
                                <ul class="list-disc list-inside space-y-2 mb-4">
                                    <li>数据首先存储在您的设备本地浏览器中</li>
                                    <li>使用加密技术保护数据传输</li>
                                    <li>不会与第三方共享您的个人健康数据</li>
                                    <li>您可以随时删除或导出自己的数据</li>
                                    <li>严格遵循数据保护法规</li>
                                </ul>
                                
                                <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                                    <h4 class="font-semibold text-green-700 mb-2">您的控制权</h4>
                                    <ul class="text-sm space-y-1">
                                        <li>• 可以选择哪些数据记录和保存</li>
                                        <li>• 随时可以清除历史记录</li>
                                        <li>• 导出功能让您保留数据副本</li>
                                        <li>• 详细的隐私政策说明数据使用方式</li>
                                    </ul>
                                </div>
                                
                                <p class="mt-4 text-sm text-gray-600">
                                    了解更多信息，请查看我们的
                                    <a href="privacy-policy.html" class="text-blue-600 hover:underline">隐私政策</a>。
                                </p>
                            </div>
                        </div>

                    </div>

                    <!-- No Results Message -->
                    <div id="no-results" class="hidden text-center py-8">
                        <p class="text-gray-600 mb-4">没有找到相关问题</p>
                        <p class="text-sm text-gray-500">请尝试使用不同的搜索关键词，或浏览其他分类</p>
                    </div>
                </div>
            </div>
        </section>

        <!-- Contact Section -->
        <section class="py-12 lg:py-16 bg-white">
            <div class="container mx-auto px-4">
                <div class="max-w-4xl mx-auto text-center">
                    <h2 class="text-2xl md:text-3xl font-bold mb-6">没有找到您要的答案？</h2>
                    <p class="text-gray-600 mb-8">我们的专业团队随时准备为您提供帮助</p>
                    
                    <div class="grid md:grid-cols-3 gap-6">
                        <div class="bg-blue-50 rounded-lg p-6">
                            <svg class="w-8 h-8 text-blue-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold mb-2">在线咨询</h3>
                            <p class="text-gray-600 text-sm">提交您的问题，我们会尽快回复</p>
                        </div>
                        
                        <div class="bg-green-50 rounded-lg p-6">
                            <svg class="w-8 h-8 text-green-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"></path>
                            </svg>
                            <h3 class="text-lg font-semibold mb-2">查看文章</h3>
                            <p class="text-gray-600 text-sm">浏览我们的专业文章获取更多信息</p>
                        </div>
                        
                        <div class="bg-purple-50 rounded-lg p-6">
                            <svg class="w-8 h-8 text-purple-600 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                            </svg>
                            <h3 class="text-lg font-semibold mb-2">症状评估</h3>
                            <p class="text-gray-600 text-sm">使用我们的工具获得个性化建议</p>
                        </div>
                    </div>
                    
                    <div class="flex flex-col sm:flex-row gap-4 justify-center mt-8">
                        <a href="blog-center.html" class="bg-green-600 text-white px-6 py-3 rounded-lg hover:bg-green-700 transition-colors">
                            浏览文章中心
                        </a>
                        <a href="interactive-solutions.html" class="bg-purple-600 text-white px-6 py-3 rounded-lg hover:bg-purple-700 transition-colors">
                            开始症状评估
                        </a>
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-white py-12">
        <div class="container mx-auto px-4">
            <div class="grid md:grid-cols-4 gap-8">
                <div>
                    <h3 class="text-lg font-semibold mb-4">period.health</h3>
                    <p class="text-gray-400 mb-4">专业的经期疼痛管理平台，为女性健康保驾护航。</p>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">核心功能</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="interactive-solutions.html" class="hover:text-white">症状评估器</a></li>
                        <li><a href="interactive-solutions.html" class="hover:text-white">个性化方案</a></li>
                        <li><a href="instant-solutions.html" class="hover:text-white">即时缓解</a></li>
                        <li><a href="regular-conditioning.html" class="hover:text-white">平时调理</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">专业内容</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="medical-research.html" class="hover:text-white">医学研究</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">健康资讯</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">用户故事</a></li>
                        <li><a href="blog-center.html" class="hover:text-white">专家观点</a></li>
                    </ul>
                </div>
                <div>
                    <h4 class="font-semibold mb-4">法律信息</h4>
                    <ul class="space-y-2 text-gray-400">
                        <li><a href="privacy-policy.html" class="hover:text-white">隐私政策</a></li>
                        <li><a href="terms-of-service.html" class="hover:text-white">服务条款</a></li>
                        <li><a href="faq.html" class="hover:text-white">常见问题</a></li>
                        <li><a href="#" class="hover:text-white">联系我们</a></li>
                    </ul>
                </div>
            </div>
            <div class="border-t border-gray-700 mt-8 pt-8 text-center text-gray-400">
                <p>&copy; 2024 period.health. 本网站内容仅供参考，不能替代专业医疗建议。</p>
            </div>
        </div>
    </footer>

    <script src="main.js"></script>
    <script>
        // FAQ functionality
        function toggleFAQ(button) {
            const content = button.nextElementSibling;
            const icon = button.querySelector('svg');
            
            if (content.classList.contains('hidden')) {
                content.classList.remove('hidden');
                icon.classList.add('rotate-180');
            } else {
                content.classList.add('hidden');
                icon.classList.remove('rotate-180');
            }
        }

        // Category filtering
        document.addEventListener('DOMContentLoaded', function() {
            const categoryButtons = document.querySelectorAll('.category-btn');
            const faqItems = document.querySelectorAll('.faq-item');
            const searchInput = document.getElementById('faq-search');
            const noResults = document.getElementById('no-results');

            categoryButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    
                    // Update button states
                    categoryButtons.forEach(btn => {
                        btn.classList.remove('bg-blue-600', 'text-white');
                        btn.classList.add('bg-white', 'text-gray-700', 'border');
                    });
                    this.classList.add('bg-blue-600', 'text-white');
                    this.classList.remove('bg-white', 'text-gray-700', 'border');
                    
                    // Filter FAQs
                    filterFAQs(category, searchInput.value);
                });
            });

            // Search functionality
            searchInput.addEventListener('input', function() {
                const activeCategory = document.querySelector('.category-btn.bg-blue-600').getAttribute('data-category');
                filterFAQs(activeCategory, this.value);
            });

            function filterFAQs(category, searchTerm) {
                let visibleCount = 0;
                
                faqItems.forEach(item => {
                    const itemCategory = item.getAttribute('data-category');
                    const itemText = item.textContent.toLowerCase();
                    const searchMatch = searchTerm === '' || itemText.includes(searchTerm.toLowerCase());
                    const categoryMatch = category === 'all' || itemCategory === category;
                    
                    if (searchMatch && categoryMatch) {
                        item.style.display = 'block';
                        visibleCount++;
                    } else {
                        item.style.display = 'none';
                    }
                });
                
                // Show/hide no results message
                if (visibleCount === 0) {
                    noResults.classList.remove('hidden');
                } else {
                    noResults.classList.add('hidden');
                }
            }
        });
    </script>
</body>
</html>


