{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [{"page": "/[locale]", "regex": "^/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)(?:/)?$"}, {"page": "/[locale]/articles", "regex": "^/([^/]+?)/articles(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/articles(?:/)?$"}, {"page": "/[locale]/articles/[slug]", "regex": "^/([^/]+?)/articles/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPslug": "nxtPslug"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/articles/(?<nxtPslug>[^/]+?)(?:/)?$"}, {"page": "/[locale]/assessment", "regex": "^/([^/]+?)/assessment(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/assessment(?:/)?$"}, {"page": "/[locale]/cultural-charms", "regex": "^/([^/]+?)/cultural\\-charms(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/cultural\\-charms(?:/)?$"}, {"page": "/[locale]/framework-demo", "regex": "^/([^/]+?)/framework\\-demo(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/framework\\-demo(?:/)?$"}, {"page": "/[locale]/health-guide", "regex": "^/([^/]+?)/health\\-guide(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/health\\-guide(?:/)?$"}, {"page": "/[locale]/health-guide/global-perspectives", "regex": "^/([^/]+?)/health\\-guide/global\\-perspectives(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/health\\-guide/global\\-perspectives(?:/)?$"}, {"page": "/[locale]/health-guide/lifestyle", "regex": "^/([^/]+?)/health\\-guide/lifestyle(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/health\\-guide/lifestyle(?:/)?$"}, {"page": "/[locale]/health-guide/medical-care", "regex": "^/([^/]+?)/health\\-guide/medical\\-care(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/health\\-guide/medical\\-care(?:/)?$"}, {"page": "/[locale]/health-guide/myths-facts", "regex": "^/([^/]+?)/health\\-guide/myths\\-facts(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/health\\-guide/myths\\-facts(?:/)?$"}, {"page": "/[locale]/health-guide/relief-methods", "regex": "^/([^/]+?)/health\\-guide/relief\\-methods(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/health\\-guide/relief\\-methods(?:/)?$"}, {"page": "/[locale]/health-guide/understanding-pain", "regex": "^/([^/]+?)/health\\-guide/understanding\\-pain(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/health\\-guide/understanding\\-pain(?:/)?$"}, {"page": "/[locale]/immediate-relief", "regex": "^/([^/]+?)/immediate\\-relief(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/immediate\\-relief(?:/)?$"}, {"page": "/[locale]/interactive-tools", "regex": "^/([^/]+?)/interactive\\-tools(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/interactive\\-tools(?:/)?$"}, {"page": "/[locale]/interactive-tools/[tool]", "regex": "^/([^/]+?)/interactive\\-tools/([^/]+?)(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale", "nxtPtool": "nxtPtool"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/interactive\\-tools/(?<nxtPtool>[^/]+?)(?:/)?$"}, {"page": "/[locale]/medical-disclaimer", "regex": "^/([^/]+?)/medical\\-disclaimer(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/medical\\-disclaimer(?:/)?$"}, {"page": "/[locale]/natural-therapies", "regex": "^/([^/]+?)/natural\\-therapies(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/natural\\-therapies(?:/)?$"}, {"page": "/[locale]/pain-tracker", "regex": "^/([^/]+?)/pain\\-tracker(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/pain\\-tracker(?:/)?$"}, {"page": "/[locale]/privacy-policy", "regex": "^/([^/]+?)/privacy\\-policy(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/privacy\\-policy(?:/)?$"}, {"page": "/[locale]/scenario-solutions", "regex": "^/([^/]+?)/scenario\\-solutions(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/scenario\\-solutions(?:/)?$"}, {"page": "/[locale]/scenario-solutions/commute", "regex": "^/([^/]+?)/scenario\\-solutions/commute(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/scenario\\-solutions/commute(?:/)?$"}, {"page": "/[locale]/scenario-solutions/emergency-kit", "regex": "^/([^/]+?)/scenario\\-solutions/emergency\\-kit(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/scenario\\-solutions/emergency\\-kit(?:/)?$"}, {"page": "/[locale]/scenario-solutions/exercise", "regex": "^/([^/]+?)/scenario\\-solutions/exercise(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/scenario\\-solutions/exercise(?:/)?$"}, {"page": "/[locale]/scenario-solutions/lifeStages", "regex": "^/([^/]+?)/scenario\\-solutions/lifeStages(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/scenario\\-solutions/lifeStages(?:/)?$"}, {"page": "/[locale]/scenario-solutions/office", "regex": "^/([^/]+?)/scenario\\-solutions/office(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/scenario\\-solutions/office(?:/)?$"}, {"page": "/[locale]/scenario-solutions/sleep", "regex": "^/([^/]+?)/scenario\\-solutions/sleep(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/scenario\\-solutions/sleep(?:/)?$"}, {"page": "/[locale]/scenario-solutions/social", "regex": "^/([^/]+?)/scenario\\-solutions/social(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/scenario\\-solutions/social(?:/)?$"}, {"page": "/[locale]/special-therapies", "regex": "^/([^/]+?)/special\\-therapies(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/special\\-therapies(?:/)?$"}, {"page": "/[locale]/teen-health", "regex": "^/([^/]+?)/teen\\-health(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/teen\\-health(?:/)?$"}, {"page": "/[locale]/teen-health/campus-guide", "regex": "^/([^/]+?)/teen\\-health/campus\\-guide(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/teen\\-health/campus\\-guide(?:/)?$"}, {"page": "/[locale]/teen-health/communication-guide", "regex": "^/([^/]+?)/teen\\-health/communication\\-guide(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/teen\\-health/communication\\-guide(?:/)?$"}, {"page": "/[locale]/teen-health/development-pain", "regex": "^/([^/]+?)/teen\\-health/development\\-pain(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/teen\\-health/development\\-pain(?:/)?$"}, {"page": "/[locale]/teen-health/emotional-support", "regex": "^/([^/]+?)/teen\\-health/emotional\\-support(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/teen\\-health/emotional\\-support(?:/)?$"}, {"page": "/[locale]/terms-of-service", "regex": "^/([^/]+?)/terms\\-of\\-service(?:/)?$", "routeKeys": {"nxtPlocale": "nxtPlocale"}, "namedRegex": "^/(?<nxtPlocale>[^/]+?)/terms\\-of\\-service(?:/)?$"}], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/icon", "regex": "^/icon(?:/)?$", "routeKeys": {}, "namedRegex": "^/icon(?:/)?$"}, {"page": "/image-requirements", "regex": "^/image\\-requirements(?:/)?$", "routeKeys": {}, "namedRegex": "^/image\\-requirements(?:/)?$"}, {"page": "/robots.txt", "regex": "^/robots\\.txt(?:/)?$", "routeKeys": {}, "namedRegex": "^/robots\\.txt(?:/)?$"}, {"page": "/sitemap.xml", "regex": "^/sitemap\\.xml(?:/)?$", "routeKeys": {}, "namedRegex": "^/sitemap\\.xml(?:/)?$"}, {"page": "/test-assessment", "regex": "^/test\\-assessment(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-assessment(?:/)?$"}, {"page": "/test-symptom-assessment", "regex": "^/test\\-symptom\\-assessment(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-symptom\\-assessment(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}