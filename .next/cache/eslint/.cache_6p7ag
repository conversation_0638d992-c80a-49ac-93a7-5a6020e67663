[{"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/articles/[slug]/page.tsx": "1", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/articles/page.tsx": "2", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/assessment/page.tsx": "3", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/cultural-charms/page.tsx": "4", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/framework-demo/page.tsx": "5", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/global-perspectives/page.tsx": "6", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/lifestyle/page.tsx": "7", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/medical-care/page.tsx": "8", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/myths-facts/page.tsx": "9", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/page.tsx": "10", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/relief-methods/page.tsx": "11", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/understanding-pain/page.tsx": "12", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/immediate-relief/page.tsx": "13", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/[tool]/page.tsx": "14", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx": "15", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/PainTrackerTool.tsx": "16", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx": "17", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx": "18", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx": "19", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/pain-tracker/components/PainEntryForm.tsx": "20", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/HealthDataDashboard.tsx": "21", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/LoadingSpinner.tsx": "22", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/NotificationContainer.tsx": "23", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/constants/index.ts": "24", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/assessmentQuestions.ts": "25", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionQuestions.ts": "26", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionRecommendations.ts": "27", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionTypes.ts": "28", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/menstrualPainRecommendations.ts": "29", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useAppTranslations.ts": "30", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useConstitutionTest.ts": "31", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useNotifications.ts": "32", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/usePainTracker.ts": "33", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts": "34", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/stores/healthDataStore.ts": "35", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/types/constitution.ts": "36", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/types/index.ts": "37", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/utils/index.ts": "38", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/test-assessment.tsx": "39", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx": "40", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/medical-disclaimer/page.tsx": "41", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/natural-therapies/page.tsx": "42", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/page.tsx": "43", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/pain-tracker/page.tsx": "44", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/privacy-policy/page.tsx": "45", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/commute/page.tsx": "46", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/emergency-kit/page.tsx": "47", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/exercise/page.tsx": "48", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/lifeStages/page.tsx": "49", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/office/page.tsx": "50", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/page.tsx": "51", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/sleep/page.tsx": "52", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/social/page.tsx": "53", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/special-therapies/page.tsx": "54", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/campus-guide/page.tsx": "55", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/communication-guide/page.tsx": "56", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/development-pain/page.tsx": "57", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/emotional-support/page.tsx": "58", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/page.tsx": "59", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/terms-of-service/page.tsx": "60", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/icon.tsx": "61", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/image-requirements/page.tsx": "62", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx": "63", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/page.tsx": "64", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/robots.ts": "65", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/sitemap.ts": "66", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/test-assessment/page.tsx": "67", "/Users/<USER>/Downloads/periodhub-health_副本01版/app/test-symptom-assessment/page.tsx": "68", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ArticleInteractions.tsx": "69", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx": "70", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/DownloadButton.tsx": "71", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/EmbeddedPainAssessment.tsx": "72", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx": "73", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx": "74", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ImagePlaceholder.tsx": "75", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDContent.tsx": "76", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDContentSimple.tsx": "77", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDInteractive.tsx": "78", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NavigationTabs.tsx": "79", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ReadingProgress.tsx": "80", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/SearchBox.tsx": "81", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/StructuredData.tsx": "82", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/TableOfContents.tsx": "83", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ToolsCollectionButton.tsx": "84", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/UserSuccessStories.tsx": "85", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx": "86", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/DataVisualization.tsx": "87", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx": "88", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/FormSystem.tsx": "89", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx": "90", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/SearchSystem.tsx": "91", "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx": "92", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/api/client.ts": "93", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/articles.ts": "94", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/cache/manager.ts": "95", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/performance/monitor.ts": "96", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/stores/appStore.ts": "97", "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/translation-utils.ts": "98"}, {"size": 19682, "mtime": 1749469774935, "results": "99", "hashOfConfig": "100"}, {"size": 53456, "mtime": 1749476861745, "results": "101", "hashOfConfig": "100"}, {"size": 11615, "mtime": 1749038859000, "results": "102", "hashOfConfig": "100"}, {"size": 4901, "mtime": 1749084845000, "results": "103", "hashOfConfig": "100"}, {"size": 8686, "mtime": 1749315129046, "results": "104", "hashOfConfig": "100"}, {"size": 22827, "mtime": 1749415912680, "results": "105", "hashOfConfig": "100"}, {"size": 20602, "mtime": 1749415831414, "results": "106", "hashOfConfig": "100"}, {"size": 20154, "mtime": 1749415938042, "results": "107", "hashOfConfig": "100"}, {"size": 20952, "mtime": 1749415866914, "results": "108", "hashOfConfig": "100"}, {"size": 9688, "mtime": 1749415899453, "results": "109", "hashOfConfig": "100"}, {"size": 23164, "mtime": 1749415924965, "results": "110", "hashOfConfig": "100"}, {"size": 34226, "mtime": 1749415854509, "results": "111", "hashOfConfig": "100"}, {"size": 14743, "mtime": 1749218651798, "results": "112", "hashOfConfig": "100"}, {"size": 25408, "mtime": 1749412994893, "results": "113", "hashOfConfig": "100"}, {"size": 41931, "mtime": 1749524029665, "results": "114", "hashOfConfig": "100"}, {"size": 12750, "mtime": 1749495176542, "results": "115", "hashOfConfig": "100"}, {"size": 8692, "mtime": 1749355480374, "results": "116", "hashOfConfig": "100"}, {"size": 28300, "mtime": 1749567554494, "results": "117", "hashOfConfig": "100"}, {"size": 8203, "mtime": 1749524500858, "results": "118", "hashOfConfig": "100"}, {"size": 19269, "mtime": 1749494552983, "results": "119", "hashOfConfig": "100"}, {"size": 9893, "mtime": 1749404324690, "results": "120", "hashOfConfig": "100"}, {"size": 2148, "mtime": 1748955880000, "results": "121", "hashOfConfig": "100"}, {"size": 4460, "mtime": 1748955849000, "results": "122", "hashOfConfig": "100"}, {"size": 10440, "mtime": 1748955643000, "results": "123", "hashOfConfig": "100"}, {"size": 16080, "mtime": 1749524095877, "results": "124", "hashOfConfig": "100"}, {"size": 22387, "mtime": 1749273564148, "results": "125", "hashOfConfig": "100"}, {"size": 40122, "mtime": 1749269452738, "results": "126", "hashOfConfig": "100"}, {"size": 12627, "mtime": 1749268845517, "results": "127", "hashOfConfig": "100"}, {"size": 55170, "mtime": 1749274738601, "results": "128", "hashOfConfig": "100"}, {"size": 1111, "mtime": 1749309117994, "results": "129", "hashOfConfig": "100"}, {"size": 6699, "mtime": 1749499765509, "results": "130", "hashOfConfig": "100"}, {"size": 2924, "mtime": 1748955759000, "results": "131", "hashOfConfig": "100"}, {"size": 9480, "mtime": 1749494407073, "results": "132", "hashOfConfig": "100"}, {"size": 21494, "mtime": 1749499408128, "results": "133", "hashOfConfig": "100"}, {"size": 6535, "mtime": 1749310456733, "results": "134", "hashOfConfig": "100"}, {"size": 2475, "mtime": 1749273734740, "results": "135", "hashOfConfig": "100"}, {"size": 4374, "mtime": 1748957313000, "results": "136", "hashOfConfig": "100"}, {"size": 8906, "mtime": 1749494858101, "results": "137", "hashOfConfig": "100"}, {"size": 1179, "mtime": 1748958196000, "results": "138", "hashOfConfig": "100"}, {"size": 2794, "mtime": 1749415778407, "results": "139", "hashOfConfig": "100"}, {"size": 10816, "mtime": 1749037948000, "results": "140", "hashOfConfig": "100"}, {"size": 103188, "mtime": 1749533316137, "results": "141", "hashOfConfig": "100"}, {"size": 31423, "mtime": 1749632175797, "results": "142", "hashOfConfig": "100"}, {"size": 13892, "mtime": 1749038922000, "results": "143", "hashOfConfig": "100"}, {"size": 10631, "mtime": 1749227891908, "results": "144", "hashOfConfig": "100"}, {"size": 27395, "mtime": 1749275511468, "results": "145", "hashOfConfig": "100"}, {"size": 30287, "mtime": 1749282967869, "results": "146", "hashOfConfig": "100"}, {"size": 17750, "mtime": 1748889835000, "results": "147", "hashOfConfig": "100"}, {"size": 21975, "mtime": 1749063614000, "results": "148", "hashOfConfig": "100"}, {"size": 23191, "mtime": 1749277795458, "results": "149", "hashOfConfig": "100"}, {"size": 15547, "mtime": 1749282075248, "results": "150", "hashOfConfig": "100"}, {"size": 18571, "mtime": 1748890157000, "results": "151", "hashOfConfig": "100"}, {"size": 21213, "mtime": 1749277815173, "results": "152", "hashOfConfig": "100"}, {"size": 6070, "mtime": 1749084148000, "results": "153", "hashOfConfig": "100"}, {"size": 21075, "mtime": 1749281693835, "results": "154", "hashOfConfig": "100"}, {"size": 20005, "mtime": 1749281990142, "results": "155", "hashOfConfig": "100"}, {"size": 19578, "mtime": 1749289181223, "results": "156", "hashOfConfig": "100"}, {"size": 21587, "mtime": 1749281887160, "results": "157", "hashOfConfig": "100"}, {"size": 17538, "mtime": 1749289138432, "results": "158", "hashOfConfig": "100"}, {"size": 13327, "mtime": 1749227906245, "results": "159", "hashOfConfig": "100"}, {"size": 865, "mtime": 1748874187000, "results": "160", "hashOfConfig": "100"}, {"size": 12335, "mtime": 1749085967000, "results": "161", "hashOfConfig": "100"}, {"size": 3233, "mtime": 1749356977988, "results": "162", "hashOfConfig": "100"}, {"size": 136, "mtime": 1749283345657, "results": "163", "hashOfConfig": "100"}, {"size": 915, "mtime": 1749367548101, "results": "164", "hashOfConfig": "100"}, {"size": 2116, "mtime": 1749415843004, "results": "165", "hashOfConfig": "100"}, {"size": 155, "mtime": 1748958216000, "results": "166", "hashOfConfig": "100"}, {"size": 6159, "mtime": 1748959580000, "results": "167", "hashOfConfig": "100"}, {"size": 9495, "mtime": 1749468511856, "results": "168", "hashOfConfig": "100"}, {"size": 7973, "mtime": 1749541741516, "results": "169", "hashOfConfig": "100"}, {"size": 1489, "mtime": 1749523267650, "results": "170", "hashOfConfig": "100"}, {"size": 6382, "mtime": 1749541777078, "results": "171", "hashOfConfig": "100"}, {"size": 7210, "mtime": 1749542233356, "results": "172", "hashOfConfig": "100"}, {"size": 7622, "mtime": 1749541865795, "results": "173", "hashOfConfig": "100"}, {"size": 1148, "mtime": 1749283175464, "results": "174", "hashOfConfig": "100"}, {"size": 37256, "mtime": 1749412453669, "results": "175", "hashOfConfig": "100"}, {"size": 9086, "mtime": 1749364442896, "results": "176", "hashOfConfig": "100"}, {"size": 1269, "mtime": 1749226398521, "results": "177", "hashOfConfig": "100"}, {"size": 1029, "mtime": 1749541979442, "results": "178", "hashOfConfig": "100"}, {"size": 2122, "mtime": 1749468532179, "results": "179", "hashOfConfig": "100"}, {"size": 8586, "mtime": 1749620497053, "results": "180", "hashOfConfig": "100"}, {"size": 3404, "mtime": 1749367729054, "results": "181", "hashOfConfig": "100"}, {"size": 4261, "mtime": 1749468554948, "results": "182", "hashOfConfig": "100"}, {"size": 136, "mtime": 1749523920348, "results": "183", "hashOfConfig": "100"}, {"size": 4291, "mtime": 1749541803986, "results": "184", "hashOfConfig": "100"}, {"size": 5945, "mtime": 1749312567487, "results": "185", "hashOfConfig": "100"}, {"size": 9911, "mtime": 1749314039693, "results": "186", "hashOfConfig": "100"}, {"size": 9835, "mtime": 1749312268063, "results": "187", "hashOfConfig": "100"}, {"size": 11549, "mtime": 1749412398660, "results": "188", "hashOfConfig": "100"}, {"size": 8357, "mtime": 1749312537574, "results": "189", "hashOfConfig": "100"}, {"size": 11037, "mtime": 1749313013725, "results": "190", "hashOfConfig": "100"}, {"size": 7422, "mtime": 1749312497520, "results": "191", "hashOfConfig": "100"}, {"size": 9337, "mtime": 1749312226101, "results": "192", "hashOfConfig": "100"}, {"size": 5684, "mtime": 1749129831000, "results": "193", "hashOfConfig": "100"}, {"size": 9006, "mtime": 1749412689751, "results": "194", "hashOfConfig": "100"}, {"size": 8738, "mtime": 1749412718965, "results": "195", "hashOfConfig": "100"}, {"size": 7504, "mtime": 1749312117614, "results": "196", "hashOfConfig": "100"}, {"size": 3725, "mtime": 1749496816580, "results": "197", "hashOfConfig": "100"}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "15nla8l", {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "327", "messages": "328", "suppressedMessages": "329", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "330", "messages": "331", "suppressedMessages": "332", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "333", "messages": "334", "suppressedMessages": "335", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "336", "messages": "337", "suppressedMessages": "338", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "339", "messages": "340", "suppressedMessages": "341", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "342", "messages": "343", "suppressedMessages": "344", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "345", "messages": "346", "suppressedMessages": "347", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "348", "messages": "349", "suppressedMessages": "350", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "351", "messages": "352", "suppressedMessages": "353", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "354", "messages": "355", "suppressedMessages": "356", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "357", "messages": "358", "suppressedMessages": "359", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "360", "messages": "361", "suppressedMessages": "362", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "363", "messages": "364", "suppressedMessages": "365", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "366", "messages": "367", "suppressedMessages": "368", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "369", "messages": "370", "suppressedMessages": "371", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "372", "messages": "373", "suppressedMessages": "374", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "375", "messages": "376", "suppressedMessages": "377", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "378", "messages": "379", "suppressedMessages": "380", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "381", "messages": "382", "suppressedMessages": "383", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "384", "messages": "385", "suppressedMessages": "386", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "387", "messages": "388", "suppressedMessages": "389", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "390", "messages": "391", "suppressedMessages": "392", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "393", "messages": "394", "suppressedMessages": "395", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "396", "messages": "397", "suppressedMessages": "398", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "399", "messages": "400", "suppressedMessages": "401", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "402", "messages": "403", "suppressedMessages": "404", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "405", "messages": "406", "suppressedMessages": "407", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "408", "messages": "409", "suppressedMessages": "410", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "411", "messages": "412", "suppressedMessages": "413", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "414", "messages": "415", "suppressedMessages": "416", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "417", "messages": "418", "suppressedMessages": "419", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "420", "messages": "421", "suppressedMessages": "422", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "423", "messages": "424", "suppressedMessages": "425", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "426", "messages": "427", "suppressedMessages": "428", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "429", "messages": "430", "suppressedMessages": "431", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "432", "messages": "433", "suppressedMessages": "434", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "435", "messages": "436", "suppressedMessages": "437", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "438", "messages": "439", "suppressedMessages": "440", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "441", "messages": "442", "suppressedMessages": "443", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "444", "messages": "445", "suppressedMessages": "446", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "447", "messages": "448", "suppressedMessages": "449", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "450", "messages": "451", "suppressedMessages": "452", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "453", "messages": "454", "suppressedMessages": "455", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "456", "messages": "457", "suppressedMessages": "458", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "459", "messages": "460", "suppressedMessages": "461", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "462", "messages": "463", "suppressedMessages": "464", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "465", "messages": "466", "suppressedMessages": "467", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "468", "messages": "469", "suppressedMessages": "470", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 8, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "471", "messages": "472", "suppressedMessages": "473", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "474", "messages": "475", "suppressedMessages": "476", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 18, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "477", "messages": "478", "suppressedMessages": "479", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "480", "messages": "481", "suppressedMessages": "482", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "483", "messages": "484", "suppressedMessages": "485", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "486", "messages": "487", "suppressedMessages": "488", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "489", "messages": "490", "suppressedMessages": "491", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/articles/[slug]/page.tsx", ["492"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/articles/page.tsx", ["493", "494", "495", "496", "497"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/assessment/page.tsx", ["498"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/cultural-charms/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/framework-demo/page.tsx", ["499", "500", "501"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/global-perspectives/page.tsx", ["502", "503"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/lifestyle/page.tsx", ["504", "505"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/medical-care/page.tsx", ["506", "507"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/myths-facts/page.tsx", ["508", "509"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/page.tsx", ["510", "511"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/relief-methods/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/understanding-pain/page.tsx", ["512", "513"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/immediate-relief/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/[tool]/page.tsx", ["514"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx", ["515", "516", "517", "518", "519", "520", "521", "522", "523", "524"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/PainTrackerTool.tsx", ["525", "526", "527", "528", "529", "530", "531", "532"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx", ["533", "534", "535", "536", "537", "538", "539", "540", "541"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/pain-tracker/components/PainEntryForm.tsx", ["542", "543", "544", "545"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/HealthDataDashboard.tsx", ["546"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/LoadingSpinner.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/components/NotificationContainer.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/constants/index.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/assessmentQuestions.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionQuestions.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionRecommendations.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/constitutionTypes.ts", ["547"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/data/menstrualPainRecommendations.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useAppTranslations.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useConstitutionTest.ts", ["548", "549", "550"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useNotifications.ts", ["551", "552", "553"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/usePainTracker.ts", ["554", "555"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/hooks/useSymptomAssessment.ts", ["556", "557", "558", "559"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/stores/healthDataStore.ts", ["560", "561", "562", "563", "564"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/types/constitution.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/types/index.ts", ["565"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/shared/utils/index.ts", ["566", "567"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/test-assessment.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx", ["568", "569"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/medical-disclaimer/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/natural-therapies/page.tsx", ["570", "571", "572", "573"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/page.tsx", ["574", "575"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/pain-tracker/page.tsx", ["576", "577"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/privacy-policy/page.tsx", ["578"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/commute/page.tsx", ["579"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/emergency-kit/page.tsx", ["580"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/exercise/page.tsx", ["581"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/lifeStages/page.tsx", ["582", "583", "584"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/office/page.tsx", ["585", "586", "587"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/sleep/page.tsx", ["588", "589"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/social/page.tsx", ["590", "591", "592"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/special-therapies/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/campus-guide/page.tsx", ["593", "594"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/communication-guide/page.tsx", ["595", "596", "597"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/development-pain/page.tsx", ["598", "599"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/emotional-support/page.tsx", ["600", "601", "602", "603"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/teen-health/page.tsx", ["604", "605"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/terms-of-service/page.tsx", ["606"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/icon.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/image-requirements/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/robots.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/sitemap.ts", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/test-assessment/page.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/app/test-symptom-assessment/page.tsx", ["607", "608"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ArticleInteractions.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx", ["609"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/DownloadButton.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/EmbeddedPainAssessment.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/Footer.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/Header.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ImagePlaceholder.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDContent.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDContentSimple.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDInteractive.tsx", ["610", "611"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/NavigationTabs.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ReadingProgress.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/SearchBox.tsx", ["612"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/StructuredData.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/TableOfContents.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/ToolsCollectionButton.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/UserSuccessStories.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx", ["613", "614", "615"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/DataVisualization.tsx", ["616", "617"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ErrorBoundary.tsx", ["618"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/FormSystem.tsx", ["619", "620", "621", "622", "623", "624", "625", "626", "627"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ModalSystem.tsx", [], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/SearchSystem.tsx", ["628", "629", "630", "631", "632", "633", "634", "635"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/ToastSystem.tsx", ["636"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/api/client.ts", ["637", "638", "639", "640", "641", "642", "643", "644", "645", "646", "647", "648", "649", "650", "651", "652", "653", "654"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/articles.ts", ["655", "656", "657", "658"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/cache/manager.ts", ["659", "660", "661", "662"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/performance/monitor.ts", ["663", "664"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/stores/appStore.ts", ["665", "666"], [], "/Users/<USER>/Downloads/periodhub-health_副本01版/lib/translation-utils.ts", ["667", "668"], [], {"ruleId": "669", "severity": 1, "message": "670", "line": 2, "column": 10, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "670", "line": 3, "column": 10, "nodeType": null, "messageId": "671", "endLine": 3, "endColumn": 25}, {"ruleId": "672", "severity": 1, "message": "673", "line": 23, "column": 29, "nodeType": "674", "messageId": "675", "endLine": 23, "endColumn": 32, "suggestions": "676"}, {"ruleId": "669", "severity": 1, "message": "677", "line": 856, "column": 21, "nodeType": null, "messageId": "671", "endLine": 856, "endColumn": 34}, {"ruleId": "669", "severity": 1, "message": "677", "line": 925, "column": 21, "nodeType": null, "messageId": "671", "endLine": 925, "endColumn": 34}, {"ruleId": "669", "severity": 1, "message": "677", "line": 994, "column": 21, "nodeType": null, "messageId": "671", "endLine": 994, "endColumn": 34}, {"ruleId": "672", "severity": 1, "message": "673", "line": 48, "column": 69, "nodeType": "674", "messageId": "675", "endLine": 48, "endColumn": 72, "suggestions": "678"}, {"ruleId": "669", "severity": 1, "message": "679", "line": 11, "column": 3, "nodeType": null, "messageId": "671", "endLine": 11, "endColumn": 11}, {"ruleId": "669", "severity": 1, "message": "680", "line": 19, "column": 9, "nodeType": null, "messageId": "671", "endLine": 19, "endColumn": 10}, {"ruleId": "669", "severity": 1, "message": "681", "line": 22, "column": 10, "nodeType": null, "messageId": "671", "endLine": 22, "endColumn": 18}, {"ruleId": "669", "severity": 1, "message": "682", "line": 1, "column": 10, "nodeType": null, "messageId": "671", "endLine": 1, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "670", "line": 2, "column": 10, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "682", "line": 1, "column": 10, "nodeType": null, "messageId": "671", "endLine": 1, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "670", "line": 2, "column": 10, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "682", "line": 1, "column": 10, "nodeType": null, "messageId": "671", "endLine": 1, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "670", "line": 2, "column": 10, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "682", "line": 1, "column": 10, "nodeType": null, "messageId": "671", "endLine": 1, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "670", "line": 2, "column": 10, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "680", "line": 33, "column": 9, "nodeType": null, "messageId": "671", "endLine": 33, "endColumn": 10}, {"ruleId": "669", "severity": 1, "message": "683", "line": 34, "column": 9, "nodeType": null, "messageId": "671", "endLine": 34, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "670", "line": 2, "column": 10, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "683", "line": 37, "column": 9, "nodeType": null, "messageId": "671", "endLine": 37, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "680", "line": 485, "column": 9, "nodeType": null, "messageId": "671", "endLine": 485, "endColumn": 10}, {"ruleId": "669", "severity": 1, "message": "684", "line": 55, "column": 5, "nodeType": null, "messageId": "671", "endLine": 55, "endColumn": 15}, {"ruleId": "669", "severity": 1, "message": "685", "line": 108, "column": 11, "nodeType": null, "messageId": "671", "endLine": 108, "endColumn": 26}, {"ruleId": "672", "severity": 1, "message": "673", "line": 186, "column": 91, "nodeType": "674", "messageId": "675", "endLine": 186, "endColumn": 94, "suggestions": "686"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 187, "column": 51, "nodeType": "674", "messageId": "675", "endLine": 187, "endColumn": 54, "suggestions": "687"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 192, "column": 95, "nodeType": "674", "messageId": "675", "endLine": 192, "endColumn": 98, "suggestions": "688"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 193, "column": 55, "nodeType": "674", "messageId": "675", "endLine": 193, "endColumn": 58, "suggestions": "689"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 538, "column": 84, "nodeType": "674", "messageId": "675", "endLine": 538, "endColumn": 87, "suggestions": "690"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 553, "column": 88, "nodeType": "674", "messageId": "675", "endLine": 553, "endColumn": 91, "suggestions": "691"}, {"ruleId": "692", "severity": 1, "message": "693", "line": 697, "column": 27, "nodeType": "694", "messageId": "695", "suggestions": "696"}, {"ruleId": "692", "severity": 1, "message": "693", "line": 697, "column": 46, "nodeType": "694", "messageId": "695", "suggestions": "697"}, {"ruleId": "669", "severity": 1, "message": "679", "line": 5, "column": 43, "nodeType": null, "messageId": "671", "endLine": 5, "endColumn": 51}, {"ruleId": "672", "severity": 1, "message": "673", "line": 20, "column": 9, "nodeType": "674", "messageId": "675", "endLine": 20, "endColumn": 12, "suggestions": "698"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 47, "column": 39, "nodeType": "674", "messageId": "675", "endLine": 47, "endColumn": 42, "suggestions": "699"}, {"ruleId": "669", "severity": 1, "message": "700", "line": 61, "column": 14, "nodeType": null, "messageId": "671", "endLine": 61, "endColumn": 19}, {"ruleId": "672", "severity": 1, "message": "673", "line": 72, "column": 40, "nodeType": "674", "messageId": "675", "endLine": 72, "endColumn": 43, "suggestions": "701"}, {"ruleId": "669", "severity": 1, "message": "700", "line": 89, "column": 14, "nodeType": null, "messageId": "671", "endLine": 89, "endColumn": 19}, {"ruleId": "669", "severity": 1, "message": "700", "line": 115, "column": 16, "nodeType": null, "messageId": "671", "endLine": 115, "endColumn": 21}, {"ruleId": "672", "severity": 1, "message": "673", "line": 124, "column": 34, "nodeType": "674", "messageId": "675", "endLine": 124, "endColumn": 37, "suggestions": "702"}, {"ruleId": "669", "severity": 1, "message": "682", "line": 4, "column": 10, "nodeType": null, "messageId": "671", "endLine": 4, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "703", "line": 11, "column": 3, "nodeType": null, "messageId": "671", "endLine": 11, "endColumn": 11}, {"ruleId": "669", "severity": 1, "message": "704", "line": 19, "column": 8, "nodeType": null, "messageId": "671", "endLine": 19, "endColumn": 22}, {"ruleId": "672", "severity": 1, "message": "673", "line": 29, "column": 73, "nodeType": "674", "messageId": "675", "endLine": 29, "endColumn": 76, "suggestions": "705"}, {"ruleId": "669", "severity": 1, "message": "684", "line": 37, "column": 5, "nodeType": null, "messageId": "671", "endLine": 37, "endColumn": 15}, {"ruleId": "669", "severity": 1, "message": "706", "line": 39, "column": 5, "nodeType": null, "messageId": "671", "endLine": 39, "endColumn": 14}, {"ruleId": "672", "severity": 1, "message": "673", "line": 66, "column": 38, "nodeType": "674", "messageId": "675", "endLine": 66, "endColumn": 41, "suggestions": "707"}, {"ruleId": "669", "severity": 1, "message": "685", "line": 454, "column": 35, "nodeType": null, "messageId": "671", "endLine": 454, "endColumn": 50}, {"ruleId": "672", "severity": 1, "message": "673", "line": 464, "column": 61, "nodeType": "674", "messageId": "675", "endLine": 464, "endColumn": 64, "suggestions": "708"}, {"ruleId": "669", "severity": 1, "message": "709", "line": 3, "column": 27, "nodeType": null, "messageId": "671", "endLine": 3, "endColumn": 36}, {"ruleId": "669", "severity": 1, "message": "710", "line": 16, "column": 27, "nodeType": null, "messageId": "671", "endLine": 16, "endColumn": 44}, {"ruleId": "669", "severity": 1, "message": "711", "line": 50, "column": 10, "nodeType": null, "messageId": "671", "endLine": 50, "endColumn": 17}, {"ruleId": "672", "severity": 1, "message": "673", "line": 61, "column": 69, "nodeType": "674", "messageId": "675", "endLine": 61, "endColumn": 72, "suggestions": "712"}, {"ruleId": "669", "severity": 1, "message": "680", "line": 22, "column": 11, "nodeType": null, "messageId": "671", "endLine": 22, "endColumn": 12}, {"ruleId": "669", "severity": 1, "message": "713", "line": 1, "column": 32, "nodeType": null, "messageId": "671", "endLine": 1, "endColumn": 59}, {"ruleId": "669", "severity": 1, "message": "714", "line": 13, "column": 10, "nodeType": null, "messageId": "671", "endLine": 13, "endColumn": 30}, {"ruleId": "715", "severity": 1, "message": "716", "line": 50, "column": 9, "nodeType": "717", "endLine": 50, "endColumn": 115}, {"ruleId": "669", "severity": 1, "message": "718", "line": 179, "column": 14, "nodeType": null, "messageId": "671", "endLine": 179, "endColumn": 17}, {"ruleId": "669", "severity": 1, "message": "709", "line": 3, "column": 33, "nodeType": null, "messageId": "671", "endLine": 3, "endColumn": 42}, {"ruleId": "669", "severity": 1, "message": "719", "line": 4, "column": 24, "nodeType": null, "messageId": "671", "endLine": 4, "endColumn": 42}, {"ruleId": "715", "severity": 1, "message": "720", "line": 38, "column": 6, "nodeType": "721", "endLine": 38, "endColumn": 8, "suggestions": "722"}, {"ruleId": "669", "severity": 1, "message": "723", "line": 13, "column": 10, "nodeType": null, "messageId": "671", "endLine": 13, "endColumn": 22}, {"ruleId": "672", "severity": 1, "message": "673", "line": 107, "column": 22, "nodeType": "674", "messageId": "675", "endLine": 107, "endColumn": 25, "suggestions": "724"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 31, "column": 28, "nodeType": "674", "messageId": "675", "endLine": 31, "endColumn": 31, "suggestions": "725"}, {"ruleId": "715", "severity": 1, "message": "726", "line": 67, "column": 9, "nodeType": "717", "endLine": 67, "endColumn": 111}, {"ruleId": "672", "severity": 1, "message": "673", "line": 155, "column": 116, "nodeType": "674", "messageId": "675", "endLine": 155, "endColumn": 119, "suggestions": "727"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 342, "column": 47, "nodeType": "674", "messageId": "675", "endLine": 342, "endColumn": 50, "suggestions": "728"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 23, "column": 12, "nodeType": "674", "messageId": "675", "endLine": 23, "endColumn": 15, "suggestions": "729"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 25, "column": 16, "nodeType": "674", "messageId": "675", "endLine": 25, "endColumn": 19, "suggestions": "730"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 26, "column": 11, "nodeType": "674", "messageId": "675", "endLine": 26, "endColumn": 14, "suggestions": "731"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 27, "column": 16, "nodeType": "674", "messageId": "675", "endLine": 27, "endColumn": 19, "suggestions": "732"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 70, "column": 24, "nodeType": "674", "messageId": "675", "endLine": 70, "endColumn": 27, "suggestions": "733"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 142, "column": 28, "nodeType": "674", "messageId": "675", "endLine": 142, "endColumn": 31, "suggestions": "734"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 304, "column": 46, "nodeType": "674", "messageId": "675", "endLine": 304, "endColumn": 49, "suggestions": "735"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 304, "column": 56, "nodeType": "674", "messageId": "675", "endLine": 304, "endColumn": 59, "suggestions": "736"}, {"ruleId": "669", "severity": 1, "message": "737", "line": 14, "column": 7, "nodeType": null, "messageId": "671", "endLine": 14, "endColumn": 12}, {"ruleId": "672", "severity": 1, "message": "673", "line": 102, "column": 35, "nodeType": "674", "messageId": "675", "endLine": 102, "endColumn": 38, "suggestions": "738"}, {"ruleId": "669", "severity": 1, "message": "739", "line": 6, "column": 8, "nodeType": null, "messageId": "671", "endLine": 6, "endColumn": 24}, {"ruleId": "669", "severity": 1, "message": "680", "line": 16, "column": 9, "nodeType": null, "messageId": "671", "endLine": 16, "endColumn": 10}, {"ruleId": "669", "severity": 1, "message": "683", "line": 17, "column": 9, "nodeType": null, "messageId": "671", "endLine": 17, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "740", "line": 324, "column": 9, "nodeType": null, "messageId": "671", "endLine": 324, "endColumn": 28}, {"ruleId": "669", "severity": 1, "message": "741", "line": 2, "column": 8, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 13}, {"ruleId": "669", "severity": 1, "message": "742", "line": 84, "column": 9, "nodeType": null, "messageId": "671", "endLine": 84, "endColumn": 18}, {"ruleId": "672", "severity": 1, "message": "673", "line": 61, "column": 55, "nodeType": "674", "messageId": "675", "endLine": 61, "endColumn": 58, "suggestions": "743"}, {"ruleId": "669", "severity": 1, "message": "744", "line": 76, "column": 9, "nodeType": null, "messageId": "671", "endLine": 76, "endColumn": 24}, {"ruleId": "669", "severity": 1, "message": "670", "line": 1, "column": 10, "nodeType": null, "messageId": "671", "endLine": 1, "endColumn": 25}, {"ruleId": "669", "severity": 1, "message": "683", "line": 40, "column": 9, "nodeType": null, "messageId": "671", "endLine": 40, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "683", "line": 39, "column": 9, "nodeType": null, "messageId": "671", "endLine": 39, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "683", "line": 37, "column": 9, "nodeType": null, "messageId": "671", "endLine": 37, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "745", "line": 11, "column": 3, "nodeType": null, "messageId": "671", "endLine": 11, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "746", "line": 12, "column": 3, "nodeType": null, "messageId": "671", "endLine": 12, "endColumn": 8}, {"ruleId": "669", "severity": 1, "message": "683", "line": 39, "column": 9, "nodeType": null, "messageId": "671", "endLine": 39, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "747", "line": 8, "column": 3, "nodeType": null, "messageId": "671", "endLine": 8, "endColumn": 8}, {"ruleId": "669", "severity": 1, "message": "748", "line": 12, "column": 3, "nodeType": null, "messageId": "671", "endLine": 12, "endColumn": 14}, {"ruleId": "669", "severity": 1, "message": "683", "line": 40, "column": 9, "nodeType": null, "messageId": "671", "endLine": 40, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "745", "line": 11, "column": 3, "nodeType": null, "messageId": "671", "endLine": 11, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "683", "line": 37, "column": 9, "nodeType": null, "messageId": "671", "endLine": 37, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "683", "line": 38, "column": 9, "nodeType": null, "messageId": "671", "endLine": 38, "endColumn": 16}, {"ruleId": "692", "severity": 1, "message": "693", "line": 333, "column": 53, "nodeType": "694", "messageId": "695", "suggestions": "749"}, {"ruleId": "692", "severity": 1, "message": "693", "line": 333, "column": 71, "nodeType": "694", "messageId": "695", "suggestions": "750"}, {"ruleId": "669", "severity": 1, "message": "751", "line": 2, "column": 10, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 19}, {"ruleId": "669", "severity": 1, "message": "680", "line": 30, "column": 9, "nodeType": null, "messageId": "671", "endLine": 30, "endColumn": 10}, {"ruleId": "669", "severity": 1, "message": "751", "line": 2, "column": 10, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 19}, {"ruleId": "669", "severity": 1, "message": "752", "line": 17, "column": 3, "nodeType": null, "messageId": "671", "endLine": 17, "endColumn": 7}, {"ruleId": "669", "severity": 1, "message": "680", "line": 29, "column": 9, "nodeType": null, "messageId": "671", "endLine": 29, "endColumn": 10}, {"ruleId": "669", "severity": 1, "message": "751", "line": 2, "column": 10, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 19}, {"ruleId": "669", "severity": 1, "message": "680", "line": 30, "column": 9, "nodeType": null, "messageId": "671", "endLine": 30, "endColumn": 10}, {"ruleId": "669", "severity": 1, "message": "751", "line": 2, "column": 10, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 19}, {"ruleId": "669", "severity": 1, "message": "753", "line": 8, "column": 3, "nodeType": null, "messageId": "671", "endLine": 8, "endColumn": 8}, {"ruleId": "669", "severity": 1, "message": "754", "line": 12, "column": 3, "nodeType": null, "messageId": "671", "endLine": 12, "endColumn": 14}, {"ruleId": "669", "severity": 1, "message": "680", "line": 31, "column": 9, "nodeType": null, "messageId": "671", "endLine": 31, "endColumn": 10}, {"ruleId": "669", "severity": 1, "message": "751", "line": 2, "column": 10, "nodeType": null, "messageId": "671", "endLine": 2, "endColumn": 19}, {"ruleId": "669", "severity": 1, "message": "680", "line": 40, "column": 9, "nodeType": null, "messageId": "671", "endLine": 40, "endColumn": 10}, {"ruleId": "669", "severity": 1, "message": "670", "line": 1, "column": 10, "nodeType": null, "messageId": "671", "endLine": 1, "endColumn": 25}, {"ruleId": "672", "severity": 1, "message": "673", "line": 20, "column": 57, "nodeType": "674", "messageId": "675", "endLine": 20, "endColumn": 60, "suggestions": "755"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 26, "column": 32, "nodeType": "674", "messageId": "675", "endLine": 26, "endColumn": 35, "suggestions": "756"}, {"ruleId": "715", "severity": 1, "message": "757", "line": 22, "column": 9, "nodeType": "717", "endLine": 41, "endColumn": 4}, {"ruleId": "669", "severity": 1, "message": "758", "line": 10, "column": 44, "nodeType": null, "messageId": "671", "endLine": 10, "endColumn": 50}, {"ruleId": "759", "severity": 1, "message": "760", "line": 32, "column": 7, "nodeType": "761", "endLine": 37, "endColumn": 9}, {"ruleId": "715", "severity": 1, "message": "762", "line": 119, "column": 6, "nodeType": "721", "endLine": 119, "endColumn": 23, "suggestions": "763"}, {"ruleId": "715", "severity": 1, "message": "764", "line": 43, "column": 6, "nodeType": "721", "endLine": 43, "endColumn": 8, "suggestions": "765"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 229, "column": 68, "nodeType": "674", "messageId": "675", "endLine": 229, "endColumn": 71, "suggestions": "766"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 230, "column": 56, "nodeType": "674", "messageId": "675", "endLine": 230, "endColumn": 59, "suggestions": "767"}, {"ruleId": "669", "severity": 1, "message": "768", "line": 4, "column": 36, "nodeType": null, "messageId": "671", "endLine": 4, "endColumn": 45}, {"ruleId": "669", "severity": 1, "message": "769", "line": 214, "column": 5, "nodeType": null, "messageId": "671", "endLine": 214, "endColumn": 15}, {"ruleId": "669", "severity": 1, "message": "770", "line": 113, "column": 11, "nodeType": null, "messageId": "671", "endLine": 113, "endColumn": 20}, {"ruleId": "669", "severity": 1, "message": "771", "line": 4, "column": 57, "nodeType": null, "messageId": "671", "endLine": 4, "endColumn": 58}, {"ruleId": "672", "severity": 1, "message": "673", "line": 17, "column": 20, "nodeType": "674", "messageId": "675", "endLine": 17, "endColumn": 23, "suggestions": "772"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 35, "column": 26, "nodeType": "674", "messageId": "675", "endLine": 35, "endColumn": 29, "suggestions": "773"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 43, "column": 55, "nodeType": "674", "messageId": "675", "endLine": 43, "endColumn": 58, "suggestions": "774"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 52, "column": 59, "nodeType": "674", "messageId": "675", "endLine": 52, "endColumn": 62, "suggestions": "775"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 86, "column": 54, "nodeType": "674", "messageId": "675", "endLine": 86, "endColumn": 57, "suggestions": "776"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 117, "column": 56, "nodeType": "674", "messageId": "675", "endLine": 117, "endColumn": 59, "suggestions": "777"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 159, "column": 10, "nodeType": "674", "messageId": "675", "endLine": 159, "endColumn": 13, "suggestions": "778"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 162, "column": 21, "nodeType": "674", "messageId": "675", "endLine": 162, "endColumn": 24, "suggestions": "779"}, {"ruleId": "669", "severity": 1, "message": "780", "line": 4, "column": 21, "nodeType": null, "messageId": "671", "endLine": 4, "endColumn": 27}, {"ruleId": "669", "severity": 1, "message": "781", "line": 4, "column": 29, "nodeType": null, "messageId": "671", "endLine": 4, "endColumn": 36}, {"ruleId": "669", "severity": 1, "message": "782", "line": 4, "column": 38, "nodeType": null, "messageId": "671", "endLine": 4, "endColumn": 46}, {"ruleId": "669", "severity": 1, "message": "783", "line": 4, "column": 55, "nodeType": null, "messageId": "671", "endLine": 4, "endColumn": 63}, {"ruleId": "669", "severity": 1, "message": "784", "line": 5, "column": 10, "nodeType": null, "messageId": "671", "endLine": 5, "endColumn": 21}, {"ruleId": "715", "severity": 1, "message": "785", "line": 62, "column": 27, "nodeType": "786", "endLine": 62, "endColumn": 38}, {"ruleId": "672", "severity": 1, "message": "673", "line": 150, "column": 39, "nodeType": "674", "messageId": "675", "endLine": 150, "endColumn": 42, "suggestions": "787"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 150, "column": 49, "nodeType": "674", "messageId": "675", "endLine": 150, "endColumn": 52, "suggestions": "788"}, {"ruleId": "715", "severity": 1, "message": "789", "line": 63, "column": 6, "nodeType": "721", "endLine": 63, "endColumn": 16, "suggestions": "790"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 7, "column": 34, "nodeType": "674", "messageId": "675", "endLine": 7, "endColumn": 37, "suggestions": "791"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 21, "column": 19, "nodeType": "674", "messageId": "675", "endLine": 21, "endColumn": 22, "suggestions": "792"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 33, "column": 10, "nodeType": "674", "messageId": "675", "endLine": 33, "endColumn": 13, "suggestions": "793"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 58, "column": 40, "nodeType": "674", "messageId": "675", "endLine": 58, "endColumn": 43, "suggestions": "794"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 60, "column": 50, "nodeType": "674", "messageId": "675", "endLine": 60, "endColumn": 53, "suggestions": "795"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 60, "column": 58, "nodeType": "674", "messageId": "675", "endLine": 60, "endColumn": 61, "suggestions": "796"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 76, "column": 57, "nodeType": "674", "messageId": "675", "endLine": 76, "endColumn": 60, "suggestions": "797"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 76, "column": 65, "nodeType": "674", "messageId": "675", "endLine": 76, "endColumn": 68, "suggestions": "798"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 144, "column": 30, "nodeType": "674", "messageId": "675", "endLine": 144, "endColumn": 33, "suggestions": "799"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 158, "column": 28, "nodeType": "674", "messageId": "675", "endLine": 158, "endColumn": 31, "suggestions": "800"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 282, "column": 24, "nodeType": "674", "messageId": "675", "endLine": 282, "endColumn": 27, "suggestions": "801"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 290, "column": 25, "nodeType": "674", "messageId": "675", "endLine": 290, "endColumn": 28, "suggestions": "802"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 292, "column": 12, "nodeType": "674", "messageId": "675", "endLine": 292, "endColumn": 15, "suggestions": "803"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 298, "column": 24, "nodeType": "674", "messageId": "675", "endLine": 298, "endColumn": 27, "suggestions": "804"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 300, "column": 12, "nodeType": "674", "messageId": "675", "endLine": 300, "endColumn": 15, "suggestions": "805"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 306, "column": 27, "nodeType": "674", "messageId": "675", "endLine": 306, "endColumn": 30, "suggestions": "806"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 313, "column": 26, "nodeType": "674", "messageId": "675", "endLine": 313, "endColumn": 29, "suggestions": "807"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 315, "column": 12, "nodeType": "674", "messageId": "675", "endLine": 315, "endColumn": 15, "suggestions": "808"}, {"ruleId": "669", "severity": 1, "message": "809", "line": 104, "column": 9, "nodeType": null, "messageId": "671", "endLine": 104, "endColumn": 20}, {"ruleId": "669", "severity": 1, "message": "810", "line": 114, "column": 9, "nodeType": null, "messageId": "671", "endLine": 114, "endColumn": 16}, {"ruleId": "669", "severity": 1, "message": "809", "line": 163, "column": 9, "nodeType": null, "messageId": "671", "endLine": 163, "endColumn": 20}, {"ruleId": "669", "severity": 1, "message": "810", "line": 174, "column": 9, "nodeType": null, "messageId": "671", "endLine": 174, "endColumn": 16}, {"ruleId": "672", "severity": 1, "message": "673", "line": 35, "column": 45, "nodeType": "674", "messageId": "675", "endLine": 35, "endColumn": 48, "suggestions": "811"}, {"ruleId": "669", "severity": 1, "message": "812", "line": 67, "column": 54, "nodeType": null, "messageId": "671", "endLine": 67, "endColumn": 62}, {"ruleId": "672", "severity": 1, "message": "673", "line": 192, "column": 35, "nodeType": "674", "messageId": "675", "endLine": 192, "endColumn": 38, "suggestions": "813"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 278, "column": 40, "nodeType": "674", "messageId": "675", "endLine": 278, "endColumn": 43, "suggestions": "814"}, {"ruleId": "669", "severity": 1, "message": "815", "line": 95, "column": 16, "nodeType": null, "messageId": "671", "endLine": 95, "endColumn": 17}, {"ruleId": "672", "severity": 1, "message": "673", "line": 165, "column": 27, "nodeType": "674", "messageId": "675", "endLine": 165, "endColumn": 30, "suggestions": "816"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 46, "column": 13, "nodeType": "674", "messageId": "675", "endLine": 46, "endColumn": 16, "suggestions": "817"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 84, "column": 36, "nodeType": "674", "messageId": "675", "endLine": 84, "endColumn": 39, "suggestions": "818"}, {"ruleId": "672", "severity": 1, "message": "673", "line": 15, "column": 55, "nodeType": "674", "messageId": "675", "endLine": 15, "endColumn": 58, "suggestions": "819"}, {"ruleId": "669", "severity": 1, "message": "820", "line": 44, "column": 9, "nodeType": null, "messageId": "671", "endLine": 44, "endColumn": 16}, "@typescript-eslint/no-unused-vars", "'getTranslations' is defined but never used.", "unusedVar", "@typescript-eslint/no-explicit-any", "Unexpected any. Specify a different type.", "TSAnyKeyword", "unexpectedAny", ["821", "822"], "'IconComponent' is assigned a value but never used.", ["823", "824"], "'Settings' is defined but never used.", "'t' is assigned a value but never used.", "'demoData' is assigned a value but never used.", "'useTranslations' is defined but never used.", "'commonT' is assigned a value but never used.", "'isComplete' is assigned a value but never used.", "'hasNoneSelected' is assigned a value but never used.", ["825", "826"], ["827", "828"], ["829", "830"], ["831", "832"], ["833", "834"], ["835", "836"], "react/no-unescaped-entities", "`\"` can be escaped with `&quot;`, `&ldquo;`, `&#34;`, `&rdquo;`.", "JSXText", "unescapedEntityAlts", ["837", "838", "839", "840"], ["841", "842", "843", "844"], ["845", "846"], ["847", "848"], "'error' is defined but never used.", ["849", "850"], ["851", "852"], "'FileText' is defined but never used.", "'LoadingSpinner' is defined but never used.", ["853", "854"], "'isLoading' is assigned a value but never used.", ["855", "856"], ["857", "858"], "'useEffect' is defined but never used.", "'getPainLevelColor' is defined but never used.", "'touched' is assigned a value but never used.", ["859", "860"], "'ConstitutionRecommendations' is defined but never used.", "'constitutionTypeInfo' is defined but never used.", "react-hooks/exhaustive-deps", "The 'questions' conditional could make the dependencies of useCallback Hook (at line 136) change on every render. To fix this, wrap the initialization of 'questions' in its own useMemo() Hook.", "VariableDeclarator", "'err' is defined but never used.", "'NotificationAction' is defined but never used.", "React Hook useCallback has a missing dependency: 'removeNotification'. Either include it or remove the dependency array.", "ArrayExpression", ["861"], "'STORAGE_KEYS' is defined but never used.", ["862", "863"], ["864", "865"], "The 'questions' conditional could make the dependencies of useCallback Hook (at line 457) change on every render. To fix this, wrap the initialization of 'questions' in its own useMemo() Hook.", ["866", "867"], ["868", "869"], ["870", "871"], ["872", "873"], ["874", "875"], ["876", "877"], ["878", "879"], ["880", "881"], ["882", "883"], ["884", "885"], "'inter' is assigned a value but never used.", ["886", "887"], "'ImagePlaceholder' is defined but never used.", "'conditioningPillars' is assigned a value but never used.", "'Image' is defined but never used.", "'homePageT' is assigned a value but never used.", ["888", "889"], "'toggleTreatment' is assigned a value but never used.", "'AlertTriangle' is defined but never used.", "'Clock' is defined but never used.", "'Users' is defined but never used.", "'Thermometer' is defined but never used.", ["890", "891", "892", "893"], ["894", "895", "896", "897"], "'useLocale' is defined but never used.", "'Mail' is defined but never used.", "'Music' is defined but never used.", "'CheckCircle' is defined but never used.", ["898", "899"], ["900", "901"], "The 'phases' array makes the dependencies of useEffect Hook (at line 66) change on every render. To fix this, wrap the initialization of 'phases' in its own useMemo() Hook.", "'locale' is defined but never used.", "@next/next/no-before-interactive-script-outside-document", "`next/script`'s `beforeInteractive` strategy should not be used outside of `pages/_document.js`. See: https://nextjs.org/docs/messages/no-before-interactive-script-outside-document", "JSXOpeningElement", "React Hook useEffect has a missing dependency: 'searchArticles'. Either include it or remove the dependency array.", ["902"], "React Hook useEffect has missing dependencies: 'preferences.accessibility', 'preferences.fontSize', 'preferences.privacy.analytics', 'preferences.theme', and 'recordPageLoadTime'. Either include them or remove the dependency array.", ["903"], ["904", "905"], ["906", "907"], "'BarChart3' is defined but never used.", "'showLabels' is assigned a value but never used.", "'bugReport' is assigned a value but never used.", "'X' is defined but never used.", ["908", "909"], ["910", "911"], ["912", "913"], ["914", "915"], ["916", "917"], ["918", "919"], ["920", "921"], ["922", "923"], "'Filter' is defined but never used.", "'SortAsc' is defined but never used.", "'SortDesc' is defined but never used.", "'Bookmark' is defined but never used.", "'useAppStore' is defined but never used.", "React Hook useCallback received a function whose dependencies are unknown. Pass an inline function instead.", "Identifier", ["924", "925"], ["926", "927"], "React Hook useEffect has a missing dependency: 'handleClose'. Either include it or remove the dependency array.", ["928"], ["929", "930"], ["931", "932"], ["933", "934"], ["935", "936"], ["937", "938"], ["939", "940"], ["941", "942"], ["943", "944"], ["945", "946"], ["947", "948"], ["949", "950"], ["951", "952"], ["953", "954"], ["955", "956"], ["957", "958"], ["959", "960"], ["961", "962"], ["963", "964"], "'categoryKey' is assigned a value but never used.", "'tagsKey' is assigned a value but never used.", ["965", "966"], "'priority' is assigned a value but never used.", ["967", "968"], ["969", "970"], "'e' is defined but never used.", ["971", "972"], ["973", "974"], ["975", "976"], ["977", "978"], "'fullKey' is assigned a value but never used.", {"messageId": "979", "fix": "980", "desc": "981"}, {"messageId": "982", "fix": "983", "desc": "984"}, {"messageId": "979", "fix": "985", "desc": "981"}, {"messageId": "982", "fix": "986", "desc": "984"}, {"messageId": "979", "fix": "987", "desc": "981"}, {"messageId": "982", "fix": "988", "desc": "984"}, {"messageId": "979", "fix": "989", "desc": "981"}, {"messageId": "982", "fix": "990", "desc": "984"}, {"messageId": "979", "fix": "991", "desc": "981"}, {"messageId": "982", "fix": "992", "desc": "984"}, {"messageId": "979", "fix": "993", "desc": "981"}, {"messageId": "982", "fix": "994", "desc": "984"}, {"messageId": "979", "fix": "995", "desc": "981"}, {"messageId": "982", "fix": "996", "desc": "984"}, {"messageId": "979", "fix": "997", "desc": "981"}, {"messageId": "982", "fix": "998", "desc": "984"}, {"messageId": "999", "data": "1000", "fix": "1001", "desc": "1002"}, {"messageId": "999", "data": "1003", "fix": "1004", "desc": "1005"}, {"messageId": "999", "data": "1006", "fix": "1007", "desc": "1008"}, {"messageId": "999", "data": "1009", "fix": "1010", "desc": "1011"}, {"messageId": "999", "data": "1012", "fix": "1013", "desc": "1002"}, {"messageId": "999", "data": "1014", "fix": "1015", "desc": "1005"}, {"messageId": "999", "data": "1016", "fix": "1017", "desc": "1008"}, {"messageId": "999", "data": "1018", "fix": "1019", "desc": "1011"}, {"messageId": "979", "fix": "1020", "desc": "981"}, {"messageId": "982", "fix": "1021", "desc": "984"}, {"messageId": "979", "fix": "1022", "desc": "981"}, {"messageId": "982", "fix": "1023", "desc": "984"}, {"messageId": "979", "fix": "1024", "desc": "981"}, {"messageId": "982", "fix": "1025", "desc": "984"}, {"messageId": "979", "fix": "1026", "desc": "981"}, {"messageId": "982", "fix": "1027", "desc": "984"}, {"messageId": "979", "fix": "1028", "desc": "981"}, {"messageId": "982", "fix": "1029", "desc": "984"}, {"messageId": "979", "fix": "1030", "desc": "981"}, {"messageId": "982", "fix": "1031", "desc": "984"}, {"messageId": "979", "fix": "1032", "desc": "981"}, {"messageId": "982", "fix": "1033", "desc": "984"}, {"messageId": "979", "fix": "1034", "desc": "981"}, {"messageId": "982", "fix": "1035", "desc": "984"}, {"desc": "1036", "fix": "1037"}, {"messageId": "979", "fix": "1038", "desc": "981"}, {"messageId": "982", "fix": "1039", "desc": "984"}, {"messageId": "979", "fix": "1040", "desc": "981"}, {"messageId": "982", "fix": "1041", "desc": "984"}, {"messageId": "979", "fix": "1042", "desc": "981"}, {"messageId": "982", "fix": "1043", "desc": "984"}, {"messageId": "979", "fix": "1044", "desc": "981"}, {"messageId": "982", "fix": "1045", "desc": "984"}, {"messageId": "979", "fix": "1046", "desc": "981"}, {"messageId": "982", "fix": "1047", "desc": "984"}, {"messageId": "979", "fix": "1048", "desc": "981"}, {"messageId": "982", "fix": "1049", "desc": "984"}, {"messageId": "979", "fix": "1050", "desc": "981"}, {"messageId": "982", "fix": "1051", "desc": "984"}, {"messageId": "979", "fix": "1052", "desc": "981"}, {"messageId": "982", "fix": "1053", "desc": "984"}, {"messageId": "979", "fix": "1054", "desc": "981"}, {"messageId": "982", "fix": "1055", "desc": "984"}, {"messageId": "979", "fix": "1056", "desc": "981"}, {"messageId": "982", "fix": "1057", "desc": "984"}, {"messageId": "979", "fix": "1058", "desc": "981"}, {"messageId": "982", "fix": "1059", "desc": "984"}, {"messageId": "979", "fix": "1060", "desc": "981"}, {"messageId": "982", "fix": "1061", "desc": "984"}, {"messageId": "979", "fix": "1062", "desc": "981"}, {"messageId": "982", "fix": "1063", "desc": "984"}, {"messageId": "979", "fix": "1064", "desc": "981"}, {"messageId": "982", "fix": "1065", "desc": "984"}, {"messageId": "999", "data": "1066", "fix": "1067", "desc": "1002"}, {"messageId": "999", "data": "1068", "fix": "1069", "desc": "1005"}, {"messageId": "999", "data": "1070", "fix": "1071", "desc": "1008"}, {"messageId": "999", "data": "1072", "fix": "1073", "desc": "1011"}, {"messageId": "999", "data": "1074", "fix": "1075", "desc": "1002"}, {"messageId": "999", "data": "1076", "fix": "1077", "desc": "1005"}, {"messageId": "999", "data": "1078", "fix": "1079", "desc": "1008"}, {"messageId": "999", "data": "1080", "fix": "1081", "desc": "1011"}, {"messageId": "979", "fix": "1082", "desc": "981"}, {"messageId": "982", "fix": "1083", "desc": "984"}, {"messageId": "979", "fix": "1084", "desc": "981"}, {"messageId": "982", "fix": "1085", "desc": "984"}, {"desc": "1086", "fix": "1087"}, {"desc": "1088", "fix": "1089"}, {"messageId": "979", "fix": "1090", "desc": "981"}, {"messageId": "982", "fix": "1091", "desc": "984"}, {"messageId": "979", "fix": "1092", "desc": "981"}, {"messageId": "982", "fix": "1093", "desc": "984"}, {"messageId": "979", "fix": "1094", "desc": "981"}, {"messageId": "982", "fix": "1095", "desc": "984"}, {"messageId": "979", "fix": "1096", "desc": "981"}, {"messageId": "982", "fix": "1097", "desc": "984"}, {"messageId": "979", "fix": "1098", "desc": "981"}, {"messageId": "982", "fix": "1099", "desc": "984"}, {"messageId": "979", "fix": "1100", "desc": "981"}, {"messageId": "982", "fix": "1101", "desc": "984"}, {"messageId": "979", "fix": "1102", "desc": "981"}, {"messageId": "982", "fix": "1103", "desc": "984"}, {"messageId": "979", "fix": "1104", "desc": "981"}, {"messageId": "982", "fix": "1105", "desc": "984"}, {"messageId": "979", "fix": "1106", "desc": "981"}, {"messageId": "982", "fix": "1107", "desc": "984"}, {"messageId": "979", "fix": "1108", "desc": "981"}, {"messageId": "982", "fix": "1109", "desc": "984"}, {"messageId": "979", "fix": "1110", "desc": "981"}, {"messageId": "982", "fix": "1111", "desc": "984"}, {"messageId": "979", "fix": "1112", "desc": "981"}, {"messageId": "982", "fix": "1113", "desc": "984"}, {"desc": "1114", "fix": "1115"}, {"messageId": "979", "fix": "1116", "desc": "981"}, {"messageId": "982", "fix": "1117", "desc": "984"}, {"messageId": "979", "fix": "1118", "desc": "981"}, {"messageId": "982", "fix": "1119", "desc": "984"}, {"messageId": "979", "fix": "1120", "desc": "981"}, {"messageId": "982", "fix": "1121", "desc": "984"}, {"messageId": "979", "fix": "1122", "desc": "981"}, {"messageId": "982", "fix": "1123", "desc": "984"}, {"messageId": "979", "fix": "1124", "desc": "981"}, {"messageId": "982", "fix": "1125", "desc": "984"}, {"messageId": "979", "fix": "1126", "desc": "981"}, {"messageId": "982", "fix": "1127", "desc": "984"}, {"messageId": "979", "fix": "1128", "desc": "981"}, {"messageId": "982", "fix": "1129", "desc": "984"}, {"messageId": "979", "fix": "1130", "desc": "981"}, {"messageId": "982", "fix": "1131", "desc": "984"}, {"messageId": "979", "fix": "1132", "desc": "981"}, {"messageId": "982", "fix": "1133", "desc": "984"}, {"messageId": "979", "fix": "1134", "desc": "981"}, {"messageId": "982", "fix": "1135", "desc": "984"}, {"messageId": "979", "fix": "1136", "desc": "981"}, {"messageId": "982", "fix": "1137", "desc": "984"}, {"messageId": "979", "fix": "1138", "desc": "981"}, {"messageId": "982", "fix": "1139", "desc": "984"}, {"messageId": "979", "fix": "1140", "desc": "981"}, {"messageId": "982", "fix": "1141", "desc": "984"}, {"messageId": "979", "fix": "1142", "desc": "981"}, {"messageId": "982", "fix": "1143", "desc": "984"}, {"messageId": "979", "fix": "1144", "desc": "981"}, {"messageId": "982", "fix": "1145", "desc": "984"}, {"messageId": "979", "fix": "1146", "desc": "981"}, {"messageId": "982", "fix": "1147", "desc": "984"}, {"messageId": "979", "fix": "1148", "desc": "981"}, {"messageId": "982", "fix": "1149", "desc": "984"}, {"messageId": "979", "fix": "1150", "desc": "981"}, {"messageId": "982", "fix": "1151", "desc": "984"}, {"messageId": "979", "fix": "1152", "desc": "981"}, {"messageId": "982", "fix": "1153", "desc": "984"}, {"messageId": "979", "fix": "1154", "desc": "981"}, {"messageId": "982", "fix": "1155", "desc": "984"}, {"messageId": "979", "fix": "1156", "desc": "981"}, {"messageId": "982", "fix": "1157", "desc": "984"}, {"messageId": "979", "fix": "1158", "desc": "981"}, {"messageId": "982", "fix": "1159", "desc": "984"}, {"messageId": "979", "fix": "1160", "desc": "981"}, {"messageId": "982", "fix": "1161", "desc": "984"}, {"messageId": "979", "fix": "1162", "desc": "981"}, {"messageId": "982", "fix": "1163", "desc": "984"}, {"messageId": "979", "fix": "1164", "desc": "981"}, {"messageId": "982", "fix": "1165", "desc": "984"}, "suggestUnknown", {"range": "1166", "text": "1167"}, "Use `unknown` instead, this will force you to explicitly, and safely assert the type is correct.", "suggestNever", {"range": "1168", "text": "1169"}, "Use `never` instead, this is useful when instantiating generic type parameters that you don't need to know the type of.", {"range": "1170", "text": "1167"}, {"range": "1171", "text": "1169"}, {"range": "1172", "text": "1167"}, {"range": "1173", "text": "1169"}, {"range": "1174", "text": "1167"}, {"range": "1175", "text": "1169"}, {"range": "1176", "text": "1167"}, {"range": "1177", "text": "1169"}, {"range": "1178", "text": "1167"}, {"range": "1179", "text": "1169"}, {"range": "1180", "text": "1167"}, {"range": "1181", "text": "1169"}, {"range": "1182", "text": "1167"}, {"range": "1183", "text": "1169"}, "replaceWithAlt", {"alt": "1184"}, {"range": "1185", "text": "1186"}, "Replace with `&quot;`.", {"alt": "1187"}, {"range": "1188", "text": "1189"}, "Replace with `&ldquo;`.", {"alt": "1190"}, {"range": "1191", "text": "1192"}, "Replace with `&#34;`.", {"alt": "1193"}, {"range": "1194", "text": "1195"}, "Replace with `&rdquo;`.", {"alt": "1184"}, {"range": "1196", "text": "1197"}, {"alt": "1187"}, {"range": "1198", "text": "1199"}, {"alt": "1190"}, {"range": "1200", "text": "1201"}, {"alt": "1193"}, {"range": "1202", "text": "1203"}, {"range": "1204", "text": "1167"}, {"range": "1205", "text": "1169"}, {"range": "1206", "text": "1167"}, {"range": "1207", "text": "1169"}, {"range": "1208", "text": "1167"}, {"range": "1209", "text": "1169"}, {"range": "1210", "text": "1167"}, {"range": "1211", "text": "1169"}, {"range": "1212", "text": "1167"}, {"range": "1213", "text": "1169"}, {"range": "1214", "text": "1167"}, {"range": "1215", "text": "1169"}, {"range": "1216", "text": "1167"}, {"range": "1217", "text": "1169"}, {"range": "1218", "text": "1167"}, {"range": "1219", "text": "1169"}, "Update the dependencies array to be: [removeNotification]", {"range": "1220", "text": "1221"}, {"range": "1222", "text": "1167"}, {"range": "1223", "text": "1169"}, {"range": "1224", "text": "1167"}, {"range": "1225", "text": "1169"}, {"range": "1226", "text": "1167"}, {"range": "1227", "text": "1169"}, {"range": "1228", "text": "1167"}, {"range": "1229", "text": "1169"}, {"range": "1230", "text": "1167"}, {"range": "1231", "text": "1169"}, {"range": "1232", "text": "1167"}, {"range": "1233", "text": "1169"}, {"range": "1234", "text": "1167"}, {"range": "1235", "text": "1169"}, {"range": "1236", "text": "1167"}, {"range": "1237", "text": "1169"}, {"range": "1238", "text": "1167"}, {"range": "1239", "text": "1169"}, {"range": "1240", "text": "1167"}, {"range": "1241", "text": "1169"}, {"range": "1242", "text": "1167"}, {"range": "1243", "text": "1169"}, {"range": "1244", "text": "1167"}, {"range": "1245", "text": "1169"}, {"range": "1246", "text": "1167"}, {"range": "1247", "text": "1169"}, {"range": "1248", "text": "1167"}, {"range": "1249", "text": "1169"}, {"alt": "1184"}, {"range": "1250", "text": "1184"}, {"alt": "1187"}, {"range": "1251", "text": "1187"}, {"alt": "1190"}, {"range": "1252", "text": "1190"}, {"alt": "1193"}, {"range": "1253", "text": "1193"}, {"alt": "1184"}, {"range": "1254", "text": "1184"}, {"alt": "1187"}, {"range": "1255", "text": "1187"}, {"alt": "1190"}, {"range": "1256", "text": "1190"}, {"alt": "1193"}, {"range": "1257", "text": "1193"}, {"range": "1258", "text": "1167"}, {"range": "1259", "text": "1169"}, {"range": "1260", "text": "1167"}, {"range": "1261", "text": "1169"}, "Update the dependencies array to be: [query, articles, searchArticles]", {"range": "1262", "text": "1263"}, "Update the dependencies array to be: [preferences.accessibility, preferences.fontSize, preferences.privacy.analytics, preferences.theme, recordPageLoadTime]", {"range": "1264", "text": "1265"}, {"range": "1266", "text": "1167"}, {"range": "1267", "text": "1169"}, {"range": "1268", "text": "1167"}, {"range": "1269", "text": "1169"}, {"range": "1270", "text": "1167"}, {"range": "1271", "text": "1169"}, {"range": "1272", "text": "1167"}, {"range": "1273", "text": "1169"}, {"range": "1274", "text": "1167"}, {"range": "1275", "text": "1169"}, {"range": "1276", "text": "1167"}, {"range": "1277", "text": "1169"}, {"range": "1278", "text": "1167"}, {"range": "1279", "text": "1169"}, {"range": "1280", "text": "1167"}, {"range": "1281", "text": "1169"}, {"range": "1282", "text": "1167"}, {"range": "1283", "text": "1169"}, {"range": "1284", "text": "1167"}, {"range": "1285", "text": "1169"}, {"range": "1286", "text": "1167"}, {"range": "1287", "text": "1169"}, {"range": "1288", "text": "1167"}, {"range": "1289", "text": "1169"}, "Update the dependencies array to be: [duration, handleClose]", {"range": "1290", "text": "1291"}, {"range": "1292", "text": "1167"}, {"range": "1293", "text": "1169"}, {"range": "1294", "text": "1167"}, {"range": "1295", "text": "1169"}, {"range": "1296", "text": "1167"}, {"range": "1297", "text": "1169"}, {"range": "1298", "text": "1167"}, {"range": "1299", "text": "1169"}, {"range": "1300", "text": "1167"}, {"range": "1301", "text": "1169"}, {"range": "1302", "text": "1167"}, {"range": "1303", "text": "1169"}, {"range": "1304", "text": "1167"}, {"range": "1305", "text": "1169"}, {"range": "1306", "text": "1167"}, {"range": "1307", "text": "1169"}, {"range": "1308", "text": "1167"}, {"range": "1309", "text": "1169"}, {"range": "1310", "text": "1167"}, {"range": "1311", "text": "1169"}, {"range": "1312", "text": "1167"}, {"range": "1313", "text": "1169"}, {"range": "1314", "text": "1167"}, {"range": "1315", "text": "1169"}, {"range": "1316", "text": "1167"}, {"range": "1317", "text": "1169"}, {"range": "1318", "text": "1167"}, {"range": "1319", "text": "1169"}, {"range": "1320", "text": "1167"}, {"range": "1321", "text": "1169"}, {"range": "1322", "text": "1167"}, {"range": "1323", "text": "1169"}, {"range": "1324", "text": "1167"}, {"range": "1325", "text": "1169"}, {"range": "1326", "text": "1167"}, {"range": "1327", "text": "1169"}, {"range": "1328", "text": "1167"}, {"range": "1329", "text": "1169"}, {"range": "1330", "text": "1167"}, {"range": "1331", "text": "1169"}, {"range": "1332", "text": "1167"}, {"range": "1333", "text": "1169"}, {"range": "1334", "text": "1167"}, {"range": "1335", "text": "1169"}, {"range": "1336", "text": "1167"}, {"range": "1337", "text": "1169"}, {"range": "1338", "text": "1167"}, {"range": "1339", "text": "1169"}, {"range": "1340", "text": "1167"}, {"range": "1341", "text": "1169"}, [882, 885], "unknown", [882, 885], "never", [1121, 1124], [1121, 1124], [4779, 4782], [4779, 4782], [4840, 4843], [4840, 4843], [5046, 5049], [5046, 5049], [5111, 5114], [5111, 5114], [20643, 20646], [20643, 20646], [21447, 21450], [21447, 21450], "&quot;", [28512, 28540], "\n                          &quot;", "&ldquo;", [28512, 28540], "\n                          &ldquo;", "&#34;", [28512, 28540], "\n                          &#34;", "&rdquo;", [28512, 28540], "\n                          &rdquo;", [28558, 28584], "&quot;\n                        ", [28558, 28584], "&ldquo;\n                        ", [28558, 28584], "&#34;\n                        ", [28558, 28584], "&rdquo;\n                        ", [707, 710], [707, 710], [1369, 1372], [1369, 1372], [1967, 1970], [1967, 1970], [3289, 3292], [3289, 3292], [947, 950], [947, 950], [1691, 1694], [1691, 1694], [18731, 18734], [18731, 18734], [2503, 2506], [2503, 2506], [1474, 1476], "[removeNotification]", [3367, 3370], [3367, 3370], [846, 849], [846, 849], [5165, 5168], [5165, 5168], [14352, 14355], [14352, 14355], [442, 445], [442, 445], [485, 488], [485, 488], [500, 503], [500, 503], [520, 523], [520, 523], [1470, 1473], [1470, 1473], [3191, 3194], [3191, 3194], [8489, 8492], [8489, 8492], [8499, 8502], [8499, 8502], [2444, 2447], [2444, 2447], [1522, 1525], [1522, 1525], [14914, 14915], [14914, 14915], [14914, 14915], [14914, 14915], [14932, 14933], [14932, 14933], [14932, 14933], [14932, 14933], [572, 575], [572, 575], [678, 681], [678, 681], [3277, 3294], "[query, articles, searchArticles]", [1113, 1115], "[preferences.accessibility, preferences.fontSize, preferences.privacy.analytics, preferences.theme, recordPageLoadTime]", [5393, 5396], [5393, 5396], [5458, 5461], [5458, 5461], [490, 493], [490, 493], [852, 855], [852, 855], [1040, 1043], [1040, 1043], [1275, 1278], [1275, 1278], [2195, 2198], [2195, 2198], [3043, 3046], [3043, 3046], [3910, 3913], [3910, 3913], [3974, 3977], [3974, 3977], [3826, 3829], [3826, 3829], [3836, 3839], [3836, 3839], [1352, 1362], "[duration, handleClose]", [171, 174], [171, 174], [432, 435], [432, 435], [699, 702], [699, 702], [1166, 1169], [1166, 1169], [1319, 1322], [1319, 1322], [1327, 1330], [1327, 1330], [1746, 1749], [1746, 1749], [1754, 1757], [1754, 1757], [3285, 3288], [3285, 3288], [3604, 3607], [3604, 3607], [6873, 6876], [6873, 6876], [7106, 7109], [7106, 7109], [7140, 7143], [7140, 7143], [7332, 7335], [7332, 7335], [7366, 7369], [7366, 7369], [7560, 7563], [7560, 7563], [7753, 7756], [7753, 7756], [7787, 7790], [7787, 7790], [658, 661], [658, 661], [4084, 4087], [4084, 4087], [5935, 5938], [5935, 5938], [4331, 4334], [4331, 4334], [917, 920], [917, 920], [1686, 1689], [1686, 1689], [317, 320], [317, 320]]