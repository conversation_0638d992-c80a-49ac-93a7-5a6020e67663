(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7947],{1478:function(e,s,n){Promise.resolve().then(n.bind(n,1609))},1609:function(e,s,n){"use strict";n.r(s),n.d(s,{default:function(){return t}});var a=n(7437),i=n(2265),l=n(5388);function t(){var e,s;let{currentSession:n,currentQuestion:t,currentQuestionIndex:d,totalQuestions:r,result:o,startAssessment:c,answerQuestion:u,goToNextQuestion:m,completeAssessment:p}=(0,l.o)(),[h,x]=(0,i.useState)({}),v=()=>{c("zh")},b=e=>{t&&(x(s=>({...s,[t.id]:e})),u({questionId:t.id,value:e,timestamp:new Date().toISOString()}))};return(0,a.jsxs)("div",{className:"p-8 max-w-4xl mx-auto",children:[(0,a.jsx)("h1",{className:"text-2xl font-bold mb-6",children:"症状评估测试页面"}),(0,a.jsxs)("div",{className:"mb-6 p-4 bg-gray-100 rounded",children:[(0,a.jsx)("h2",{className:"text-lg font-semibold mb-2",children:"状态信息"}),(0,a.jsxs)("p",{children:["当前会话: ",n?"已开始":"未开始"]}),(0,a.jsxs)("p",{children:["当前题目: ",d+1," / ",r]}),(0,a.jsxs)("p",{children:["当前问题ID: ",(null==t?void 0:t.id)||"无"]}),(0,a.jsxs)("p",{children:["是否有结果: ",o?"是":"否"]})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[!n&&(0,a.jsx)("button",{onClick:v,className:"bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:"开始评估"}),n&&!o&&(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("button",{onClick:()=>{if(!n){v();return}[{questionId:"age_range",value:"18-25"},{questionId:"cycle_regularity",value:"mostly_regular"},{questionId:"pain_severity",value:7},{questionId:"pain_duration",value:"one_day"},{questionId:"pain_location",value:["lower_abdomen","lower_back"]},{questionId:"pain_impact",value:"moderate_impact"},{questionId:"accompanying_symptoms",value:["headache","fatigue"]},{questionId:"exercise_frequency",value:"weekly"},{questionId:"stress_level",value:6},{questionId:"sleep_quality",value:"fair"},{questionId:"previous_treatment",value:["otc_painkillers","heat_therapy"]},{questionId:"medical_conditions",value:["none"]}].forEach(e=>{u({...e,timestamp:new Date().toISOString()})}),setTimeout(()=>{p()},1e3)},className:"bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700",children:"快速完成评估（测试用）"}),t&&(0,a.jsxs)("div",{className:"border p-4 rounded",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:t.title}),"single"===t.type&&t.options&&(0,a.jsx)("div",{className:"space-y-2",children:t.options.map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"radio",name:t.id,value:e.value,checked:h[t.id]===e.value,onChange:e=>b(e.target.value),className:"mr-2"}),e.label]},e.value))}),"scale"===t.type&&(0,a.jsxs)("div",{children:[(0,a.jsx)("input",{type:"range",min:(null===(e=t.validation)||void 0===e?void 0:e.min)||1,max:(null===(s=t.validation)||void 0===s?void 0:s.max)||10,value:h[t.id]||1,onChange:e=>b(parseInt(e.target.value)),className:"w-full"}),(0,a.jsxs)("p",{children:["当前值: ",h[t.id]||1]})]}),(0,a.jsx)("button",{onClick:()=>{d>=r-1?p():m()},className:"mt-4 bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700",children:d>=r-1?"完成评估":"下一题"})]})]}),o&&(0,a.jsxs)("div",{className:"border p-4 rounded bg-green-50",children:[(0,a.jsx)("h3",{className:"font-semibold mb-2",children:"评估结果"}),(0,a.jsxs)("p",{children:["评分: ",o.score," / ",o.maxScore," (",Math.round(o.percentage),"%)"]}),(0,a.jsxs)("p",{children:["严重程度: ",o.severity]}),(0,a.jsxs)("p",{children:["类型: ",o.type]}),(0,a.jsxs)("p",{children:["消息: ",o.message]}),(0,a.jsxs)("p",{children:["建议数量: ",o.recommendations.length]})]})]})]})}}},function(e){e.O(0,[1863,5388,2971,2117,1744],function(){return e(e.s=1478)}),_N_E=e.O()}]);