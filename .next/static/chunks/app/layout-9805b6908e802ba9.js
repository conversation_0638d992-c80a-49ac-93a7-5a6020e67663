(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[3185],{9322:function(e,t,r){Promise.resolve().then(r.bind(r,8348)),Promise.resolve().then(r.t.bind(r,1868,23)),Promise.resolve().then(r.t.bind(r,7960,23))},8348:function(e,t,r){"use strict";r.d(t,{AppProvider:function(){return L}});var s=r(7437),n=r(2265),a=r(3639),o=r(7168),i=r(9637),l=r(7260),c=r(3011),d=r(6885),u=r(4391);let m={theme:"system",language:"zh",fontSize:"medium",animations:!0,notifications:{browser:!0,email:!1,sms:!1},privacy:{analytics:!0,cookies:!0,dataSharing:!1},accessibility:{highContrast:!1,reducedMotion:!1,screenReader:!1}},h={preferences:m,ui:{sidebarOpen:!1,loading:!1,error:null,modal:{isOpen:!1,type:null,data:null},toast:[]},data:{lastSync:null,version:"1.0.0",buildTime:new Date().toISOString()},performance:{pageLoadTime:0,apiResponseTimes:{},errorCount:0}},p=()=>"".concat(Date.now(),"-").concat(Math.random().toString(36).substr(2,9)),x=(0,c.U)()((0,d.mW)((0,d.tJ)((0,u.n)((e,t)=>({...h,updatePreferences:t=>{e(e=>{Object.assign(e.preferences,t)})},resetPreferences:()=>{e(e=>{e.preferences=m})},setSidebarOpen:t=>{e(e=>{e.ui.sidebarOpen=t})},toggleSidebar:()=>{e(e=>{e.ui.sidebarOpen=!e.ui.sidebarOpen})},setLoading:t=>{e(e=>{e.ui.loading=t})},setError:t=>{e(e=>{e.ui.error=t})},openModal:function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null;e(e=>{e.ui.modal={isOpen:!0,type:t,data:r}})},closeModal:()=>{e(e=>{e.ui.modal={isOpen:!1,type:null,data:null}})},addToast:r=>{let s=p();return e(e=>{e.ui.toast.push({...r,id:s})}),0!==r.duration&&setTimeout(()=>{t().removeToast(s)},r.duration||5e3),s},removeToast:t=>{e(e=>{e.ui.toast=e.ui.toast.filter(e=>e.id!==t)})},clearToasts:()=>{e(e=>{e.ui.toast=[]})},updateLastSync:()=>{e(e=>{e.data.lastSync=new Date().toISOString()})},recordPageLoadTime:t=>{e(e=>{e.performance.pageLoadTime=t})},recordApiResponseTime:(t,r)=>{e(e=>{e.performance.apiResponseTimes[t]=r})},incrementErrorCount:()=>{e(e=>{e.performance.errorCount+=1})},resetPerformanceMetrics:()=>{e(e=>{e.performance={pageLoadTime:0,apiResponseTimes:{},errorCount:0}})}})),{name:"periodhub-app-store",storage:(0,d.FL)(()=>localStorage),partialize:e=>({preferences:e.preferences,data:{lastSync:e.data.lastSync,version:e.data.version}})}),{name:"PeriodHub App Store"})),g=()=>x(e=>e.ui.toast);class f extends n.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e,errorId:"error_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9))}}componentDidCatch(e,t){this.setState({errorInfo:t}),x.getState().incrementErrorCount(),this.props.onError&&this.props.onError(e,t),this.reportError(e,t)}render(){if(this.state.hasError){if(this.props.fallback)return this.props.fallback;let{error:e,errorInfo:t,errorId:r}=this.state,{level:n="component",showDetails:c=!1}=this.props;return(0,s.jsx)("div",{className:"min-h-[400px] flex items-center justify-center p-6",children:(0,s.jsx)("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg border border-red-200",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,s.jsx)("div",{className:"flex-shrink-0",children:(0,s.jsx)(a.Z,{className:"w-8 h-8 text-red-500"})}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"critical"===n?"严重错误":"出现错误"}),(0,s.jsx)("p",{className:"text-sm text-gray-600",children:"critical"===n?"应用遇到了严重问题":"这个组件遇到了问题"})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsx)("p",{className:"text-sm text-gray-700 mb-2",children:"我们很抱歉给您带来不便。错误已被记录，我们会尽快修复。"}),r&&(0,s.jsxs)("p",{className:"text-xs text-gray-500 font-mono bg-gray-50 p-2 rounded",children:["错误ID: ",r]}),(c||!1)&&e&&(0,s.jsxs)("details",{className:"mt-3",children:[(0,s.jsx)("summary",{className:"text-sm text-gray-600 cursor-pointer hover:text-gray-800",children:"查看技术详情"}),(0,s.jsxs)("div",{className:"mt-2 p-3 bg-gray-50 rounded text-xs font-mono",children:[(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("strong",{children:"错误信息:"}),(0,s.jsx)("div",{className:"text-red-600",children:e.message})]}),e.stack&&(0,s.jsxs)("div",{className:"mb-2",children:[(0,s.jsx)("strong",{children:"堆栈跟踪:"}),(0,s.jsx)("pre",{className:"whitespace-pre-wrap text-gray-600 max-h-32 overflow-y-auto",children:e.stack})]}),(null==t?void 0:t.componentStack)&&(0,s.jsxs)("div",{children:[(0,s.jsx)("strong",{children:"组件堆栈:"}),(0,s.jsx)("pre",{className:"whitespace-pre-wrap text-gray-600 max-h-32 overflow-y-auto",children:t.componentStack})]})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[this.retryCount<this.maxRetries?(0,s.jsxs)("button",{onClick:this.handleRetry,className:"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[(0,s.jsx)(o.Z,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:["重试 (",this.maxRetries-this.retryCount," 次剩余)"]})]}):(0,s.jsxs)("button",{onClick:()=>window.location.reload(),className:"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[(0,s.jsx)(o.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"刷新页面"})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("button",{onClick:this.handleGoHome,className:"flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors",children:[(0,s.jsx)(i.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"返回首页"})]}),(0,s.jsxs)("button",{onClick:this.handleReportBug,className:"flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors",children:[(0,s.jsx)(l.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"报告问题"})]})]})]})]})})})}return this.props.children}constructor(e){super(e),this.retryCount=0,this.maxRetries=3,this.reportError=async(e,t)=>{try{e.message,e.stack,t.componentStack,new Date().toISOString(),window.location.href,navigator.userAgent,this.state.errorId,this.props.level}catch(e){}},this.handleRetry=()=>{this.retryCount<this.maxRetries?(this.retryCount++,this.setState({hasError:!1,error:null,errorInfo:null,errorId:null})):window.location.reload()},this.handleGoHome=()=>{window.location.href="/"},this.handleReportBug=()=>{let{error:e,errorInfo:t,errorId:r}=this.state;null==e||e.message,null==e||e.stack,null==t||t.componentStack,window.location.href,new Date().toISOString();let s=encodeURIComponent("Bug Report: ".concat((null==e?void 0:e.message)||"Unknown Error")),n=encodeURIComponent("\nError ID: ".concat(r,"\nURL: ").concat(window.location.href,"\nTime: ").concat(new Date().toISOString(),"\n\nError Details:\n").concat(null==e?void 0:e.message,"\n\nStack Trace:\n").concat(null==e?void 0:e.stack,"\n\nComponent Stack:\n").concat(null==t?void 0:t.componentStack,"\n\nPlease describe what you were doing when this error occurred:\n[Your description here]\n    "));window.open("mailto:<EMAIL>?subject=".concat(s,"&body=").concat(n))},this.state={hasError:!1,error:null,errorInfo:null,errorId:null}}}var b=r(4887),v=r(5302),w=r(2252),y=r(3245),j=r(2489);let N=e=>{let{id:t,type:r,message:o,title:i,duration:l=5e3,action:c,closable:d=!0,onClose:u}=e,[m,h]=(0,n.useState)(!1),[p,x]=(0,n.useState)(!1);(0,n.useEffect)(()=>{let e=setTimeout(()=>h(!0),10);return()=>clearTimeout(e)},[]),(0,n.useEffect)(()=>{if(l>0){let e=setTimeout(()=>{g()},l);return()=>clearTimeout(e)}},[l]);let g=()=>{x(!0),setTimeout(()=>{u(t)},300)},f=()=>{switch(r){case"success":return"text-green-800";case"error":return"text-red-800";case"warning":return"text-yellow-800";case"info":return"text-blue-800";default:return"text-gray-800"}};return(0,s.jsxs)("div",{className:"\n        transform transition-all duration-300 ease-in-out\n        ".concat(m&&!p?"translate-x-0 opacity-100":"translate-x-full opacity-0","\n        max-w-sm w-full ").concat((()=>{switch(r){case"success":return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"warning":return"bg-yellow-50 border-yellow-200";case"info":return"bg-blue-50 border-blue-200";default:return"bg-gray-50 border-gray-200"}})()," border rounded-lg shadow-lg p-4 mb-3\n      "),children:[(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 mt-0.5",children:(()=>{switch(r){case"success":return(0,s.jsx)(v.Z,{className:"w-5 h-5 text-green-500"});case"error":return(0,s.jsx)(w.Z,{className:"w-5 h-5 text-red-500"});case"warning":return(0,s.jsx)(a.Z,{className:"w-5 h-5 text-yellow-500"});case"info":return(0,s.jsx)(y.Z,{className:"w-5 h-5 text-blue-500"});default:return(0,s.jsx)(y.Z,{className:"w-5 h-5 text-gray-500"})}})()}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[i&&(0,s.jsx)("h4",{className:"text-sm font-semibold ".concat(f()," mb-1"),children:i}),(0,s.jsx)("p",{className:"text-sm ".concat(f()),children:o}),c&&(0,s.jsx)("button",{onClick:c.onClick,className:"\n                mt-2 text-xs font-medium underline hover:no-underline\n                ".concat("success"===r?"text-green-700 hover:text-green-800":"","\n                ").concat("error"===r?"text-red-700 hover:text-red-800":"","\n                ").concat("warning"===r?"text-yellow-700 hover:text-yellow-800":"","\n                ").concat("info"===r?"text-blue-700 hover:text-blue-800":"","\n              "),children:c.label})]}),d&&(0,s.jsx)("button",{onClick:g,className:"\n              flex-shrink-0 p-1 rounded-md hover:bg-white/50 transition-colors\n              ".concat(f(),"\n            "),children:(0,s.jsx)(j.Z,{className:"w-4 h-4"})})]}),l>0&&(0,s.jsx)("div",{className:"mt-3 w-full bg-white/30 rounded-full h-1",children:(0,s.jsx)("div",{className:"\n              h-1 rounded-full transition-all ease-linear\n              ".concat("success"===r?"bg-green-500":"","\n              ").concat("error"===r?"bg-red-500":"","\n              ").concat("warning"===r?"bg-yellow-500":"","\n              ").concat("info"===r?"bg-blue-500":"","\n            "),style:{width:"100%",animation:"shrink ".concat(l,"ms linear")}})})]})},k=()=>{let[e,t]=(0,n.useState)(!1),r=g(),a=x(e=>e.removeToast);if((0,n.useEffect)(()=>{t(!0)},[]),!e)return null;let o=(0,s.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:r.map(e=>(0,s.jsx)(N,{id:e.id,type:e.type,message:e.message,duration:e.duration,onClose:a},e.id))});return(0,b.createPortal)(o,document.body)};if("undefined"!=typeof document){let e=document.createElement("style");e.textContent="\n  @keyframes shrink {\n    from {\n      width: 100%;\n    }\n    to {\n      width: 0%;\n    }\n  }\n",document.head.appendChild(e)}let E=e=>{let{isOpen:t,onClose:r,type:o="default",title:i,content:l,children:c,size:d="md",closable:u=!0,maskClosable:m=!0,showFooter:h=!1,confirmText:p="确认",cancelText:x="取消",onConfirm:g,onCancel:f,className:v="",zIndex:w=1e3}=e,[N,k]=(0,n.useState)(!1),[E,T]=(0,n.useState)(!1);(0,n.useEffect)(()=>{if(t)k(!0),T(!0),document.body.style.overflow="hidden";else{T(!1);let e=setTimeout(()=>{k(!1),document.body.style.overflow=""},200);return()=>clearTimeout(e)}return()=>{document.body.style.overflow=""}},[t]);let S=(0,n.useCallback)(e=>{e.target===e.currentTarget&&m&&r()},[m,r]),C=(0,n.useCallback)(async()=>{if(g)try{await g(),r()}catch(e){}else r()},[g,r]),L=(0,n.useCallback)(()=>{f&&f(),r()},[f,r]);if(!N)return null;let R=(0,s.jsxs)("div",{className:"fixed inset-0 flex items-center justify-center p-4",style:{zIndex:w},onClick:S,children:[(0,s.jsx)("div",{className:"\n          absolute inset-0 bg-black transition-opacity duration-200\n          ".concat(E?"opacity-50":"opacity-0","\n        ")}),(0,s.jsxs)("div",{className:"\n          relative bg-white rounded-lg shadow-xl w-full ".concat((()=>{switch(d){case"sm":return"max-w-sm";case"md":default:return"max-w-md";case"lg":return"max-w-lg";case"xl":return"max-w-xl";case"full":return"max-w-full mx-4"}})(),"\n          transform transition-all duration-200\n          ").concat(E?"scale-100 opacity-100":"scale-95 opacity-0","\n          ").concat(v,"\n        "),children:[(i||u)&&(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(o){case"confirm":return(0,s.jsx)(a.Z,{className:"w-6 h-6 text-yellow-500"});case"alert":return(0,s.jsx)(y.Z,{className:"w-6 h-6 text-blue-500"});default:return null}})(),i&&(0,s.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:i})]}),u&&(0,s.jsx)("button",{onClick:r,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",children:(0,s.jsx)(j.Z,{className:"w-5 h-5"})})]}),(0,s.jsx)("div",{className:"p-6",children:l||c}),h&&(0,s.jsxs)("div",{className:"flex justify-end space-x-3 p-6 border-t border-gray-200",children:[(0,s.jsx)("button",{onClick:L,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors",children:x}),(0,s.jsx)("button",{onClick:C,className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors",children:p})]})]})]});return(0,b.createPortal)(R,document.body)},T=()=>{let e=x(e=>e.ui.modal),t=x(e=>e.closeModal);return(0,s.jsx)(E,{isOpen:e.isOpen,onClose:t,...e.data})};class S{initializeObservers(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{for(let t of e.getEntries())"navigation"===t.entryType&&this.recordPageLoad(t)});e.observe({entryTypes:["navigation"]}),this.observers.push(e);let t=new PerformanceObserver(e=>{for(let t of e.getEntries())"resource"===t.entryType&&this.recordResourceLoad(t)});t.observe({entryTypes:["resource"]}),this.observers.push(t);let r=new PerformanceObserver(e=>{for(let t of e.getEntries())"event"===t.entryType&&this.recordInteraction(t)});try{r.observe({entryTypes:["event"]}),this.observers.push(r)}catch(e){}}}setupErrorHandling(){window.addEventListener("error",e=>{var t;this.recordError({type:"javascript",message:e.message,stack:null===(t=e.error)||void 0===t?void 0:t.stack,timestamp:Date.now(),route:window.location.pathname,userAgent:navigator.userAgent})}),window.addEventListener("unhandledrejection",e=>{var t,r;this.recordError({type:"javascript",message:(null===(t=e.reason)||void 0===t?void 0:t.message)||"Unhandled Promise Rejection",stack:null===(r=e.reason)||void 0===r?void 0:r.stack,timestamp:Date.now(),route:window.location.pathname,userAgent:navigator.userAgent})})}recordPageLoad(e){let t={startTime:e.startTime,endTime:e.loadEventEnd,duration:e.loadEventEnd-e.startTime,route:window.location.pathname};this.addMetric({pageLoad:t}),x.getState().recordPageLoadTime(t.duration)}recordResourceLoad(e){if(e.name.includes("/api/")){let t={endpoint:e.name,method:"GET",startTime:e.startTime,endTime:e.responseEnd,duration:e.responseEnd-e.startTime,status:200,size:e.transferSize};this.addMetric({network:t})}}recordInteraction(e){var t;let r={type:e.name,element:(null===(t=e.target)||void 0===t?void 0:t.tagName)||"unknown",timestamp:e.startTime,duration:e.duration};this.addMetric({interaction:r})}recordError(e){this.addMetric({error:e}),x.getState().incrementErrorCount()}addMetric(e){this.isEnabled&&(this.metrics.push(e),this.metrics.length>1e3&&(this.metrics=this.metrics.slice(-500)))}recordApiCall(e,t,r,s,n,a){let o={endpoint:e,method:t,startTime:r,endTime:s,duration:s-r,status:n,size:a};this.addMetric({network:o}),x.getState().recordApiResponseTime(e,o.duration)}recordCustomInteraction(e,t,r){let s={type:e,element:t,timestamp:Date.now(),duration:r};this.addMetric({interaction:s})}getPerformanceReport(){let e=Date.now()-864e5,t=this.metrics.filter(t=>{var r,s,n,a;return((null===(r=t.pageLoad)||void 0===r?void 0:r.startTime)||(null===(s=t.network)||void 0===s?void 0:s.startTime)||(null===(n=t.interaction)||void 0===n?void 0:n.timestamp)||(null===(a=t.error)||void 0===a?void 0:a.timestamp)||0)>e});return{totalMetrics:t.length,pageLoads:t.filter(e=>e.pageLoad).length,apiCalls:t.filter(e=>e.network).length,interactions:t.filter(e=>e.interaction).length,errors:t.filter(e=>e.error).length,averagePageLoadTime:this.calculateAveragePageLoadTime(t),averageApiResponseTime:this.calculateAverageApiResponseTime(t),errorRate:this.calculateErrorRate(t)}}calculateAveragePageLoadTime(e){let t=e.filter(e=>e.pageLoad);return 0===t.length?0:t.reduce((e,t)=>{var r;return e+((null===(r=t.pageLoad)||void 0===r?void 0:r.duration)||0)},0)/t.length}calculateAverageApiResponseTime(e){let t=e.filter(e=>e.network);return 0===t.length?0:t.reduce((e,t)=>{var r;return e+((null===(r=t.network)||void 0===r?void 0:r.duration)||0)},0)/t.length}calculateErrorRate(e){let t=e.filter(e=>e.error).length,r=e.length;return r>0?t/r*100:0}setEnabled(e){this.isEnabled=e}cleanup(){this.observers.forEach(e=>e.disconnect()),this.observers=[],this.metrics=[]}constructor(){this.metrics=[],this.observers=[],this.isEnabled=!0,this.initializeObservers(),this.setupErrorHandling()}}let C=new S,L=e=>{let{children:t}=e,r=x(e=>e.preferences),a=x(e=>e.recordPageLoadTime);return(0,n.useEffect)(()=>(a(performance.now()),R(r.theme),P(r.fontSize),O(r.accessibility),r.privacy.analytics&&C.setEnabled(!0),()=>{C.cleanup()}),[]),(0,n.useEffect)(()=>{R(r.theme)},[r.theme]),(0,n.useEffect)(()=>{P(r.fontSize)},[r.fontSize]),(0,n.useEffect)(()=>{O(r.accessibility)},[r.accessibility]),(0,n.useEffect)(()=>{C.setEnabled(r.privacy.analytics)},[r.privacy.analytics]),(0,s.jsx)(f,{level:"critical",children:(0,s.jsxs)("div",{className:"app-container",children:[t,(0,s.jsx)(k,{}),(0,s.jsx)(T,{})]})})};function R(e){let t=document.documentElement;"system"===e&&(e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),t.classList.remove("light","dark"),t.classList.add(e);let r=document.querySelector('meta[name="theme-color"]');r&&r.setAttribute("content","dark"===e?"#1f2937":"#ffffff")}function P(e){let t=document.documentElement;switch(t.classList.remove("text-sm","text-base","text-lg"),e){case"small":t.classList.add("text-sm");break;case"large":t.classList.add("text-lg");break;default:t.classList.add("text-base")}}function O(e){let t=document.documentElement;e.highContrast?t.classList.add("high-contrast"):t.classList.remove("high-contrast"),e.reducedMotion?t.classList.add("reduce-motion"):t.classList.remove("reduce-motion"),e.screenReader?t.classList.add("screen-reader-optimized"):t.classList.remove("screen-reader-optimized")}},7960:function(){}},function(e){e.O(0,[3888,3587,425,2971,2117,1744],function(){return e(e.s=9322)}),_N_E=e.O()}]);