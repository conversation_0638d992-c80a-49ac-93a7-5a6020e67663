(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5522],{9910:function(e,t,s){Promise.resolve().then(s.bind(s,52))},52:function(e,t,s){"use strict";s.r(t),s.d(t,{default:function(){return r}});var l=s(7437),a=s(2265),n=s(2586);function r(){let e=(0,n.useLocale)(),[t,s]=(0,a.useState)([]),[r,i]=(0,a.useState)(!1),[c,d]=(0,a.useState)({date:new Date().toISOString().split("T")[0],intensity:5,menstrualStatus:"",symptoms:[],treatments:[],effectiveness:5,notes:""});(0,a.useEffect)(()=>{let e=localStorage.getItem("painEntries");e&&s(JSON.parse(e))},[]);let o=(e,t)=>{d(s=>({...s,[e]:t}))},x=e=>{let t=c.symptoms||[];o("symptoms",t.includes(e)?t.filter(t=>t!==e):[...t,e])},m=()=>{if(t.length<2)return"stable";let e=t.slice(-3),s=t.slice(-6,-3),l=e.reduce((e,t)=>e+t.intensity,0)/e.length,a=s.length>0?s.reduce((e,t)=>e+t.intensity,0)/s.length:l;return l>a+.5?"increasing":l<a-.5?"decreasing":"stable"};return(0,l.jsxs)("div",{className:"space-y-8",children:[(0,l.jsx)("header",{className:"container-custom",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,l.jsx)("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-800 mb-6",children:"zh"===e?"疼痛追踪器":"Pain Tracker"}),(0,l.jsx)("p",{className:"text-neutral-600 mb-8",children:"zh"===e?"记录您的疼痛模式，帮助识别触发因素和有效的治疗方法":"Track your pain patterns to help identify triggers and effective treatments"})]})}),(0,l.jsx)("section",{className:"container-custom",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 text-center",children:[(0,l.jsx)("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:t.length}),(0,l.jsx)("p",{className:"text-neutral-600",children:"zh"===e?"总记录数":"Total Entries"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 text-center",children:[(0,l.jsx)("div",{className:"text-3xl font-bold text-red-600 mb-2",children:0===t.length?0:(t.reduce((e,t)=>e+t.intensity,0)/t.length).toFixed(1)}),(0,l.jsx)("p",{className:"text-neutral-600",children:"zh"===e?"平均疼痛强度":"Average Pain Intensity"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 text-center",children:[(0,l.jsx)("div",{className:"text-3xl font-bold mb-2 ".concat("increasing"===m()?"text-red-600":"decreasing"===m()?"text-green-600":"text-yellow-600"),children:"increasing"===m()?"↗":"decreasing"===m()?"↘":"→"}),(0,l.jsx)("p",{className:"text-neutral-600",children:"zh"===e?"最近趋势":"Recent Trend"})]})]}),(0,l.jsx)("div",{className:"text-center mb-8",children:(0,l.jsx)("button",{onClick:()=>i(!0),className:"btn-primary",children:"zh"===e?"添加新记录":"Add New Entry"})})]})}),r&&(0,l.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:(0,l.jsx)("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-neutral-800",children:"zh"===e?"添加疼痛记录":"Add Pain Entry"}),(0,l.jsx)("button",{onClick:()=>i(!1),className:"text-neutral-500 hover:text-neutral-700",children:"✕"})]}),(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"zh"===e?"日期":"Date"}),(0,l.jsx)("input",{type:"date",value:c.date,onChange:e=>o("date",e.target.value),className:"w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-purple-500"})]}),(0,l.jsxs)("div",{children:[(0,l.jsxs)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:["zh"===e?"疼痛强度":"Pain Intensity"," (",c.intensity,"/10)"]}),(0,l.jsx)("input",{type:"range",min:"0",max:"10",value:c.intensity,onChange:e=>o("intensity",parseInt(e.target.value)),className:"w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"zh"===e?"月经状态":"Menstrual Status"}),(0,l.jsxs)("select",{value:c.menstrualStatus,onChange:e=>o("menstrualStatus",e.target.value),className:"w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-purple-500",children:[(0,l.jsx)("option",{value:"",children:"zh"===e?"请选择":"Please select"}),(0,l.jsx)("option",{value:"before",children:"zh"===e?"月经前":"Before period"}),(0,l.jsx)("option",{value:"during",children:"zh"===e?"月经期间":"During period"}),(0,l.jsx)("option",{value:"after",children:"zh"===e?"月经后":"After period"}),(0,l.jsx)("option",{value:"none",children:"zh"===e?"非月经期":"Not related to period"})]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"zh"===e?"伴随症状":"Associated Symptoms"}),(0,l.jsx)("div",{className:"grid grid-cols-2 gap-2",children:[{value:"nausea",label:"zh"===e?"恶心":"Nausea"},{value:"headache",label:"zh"===e?"头痛":"Headache"},{value:"fatigue",label:"zh"===e?"疲劳":"Fatigue"},{value:"bloating",label:"zh"===e?"腹胀":"Bloating"},{value:"mood_changes",label:"zh"===e?"情绪变化":"Mood changes"},{value:"back_pain",label:"zh"===e?"背痛":"Back pain"}].map(e=>{var t;return(0,l.jsxs)("label",{className:"flex items-center",children:[(0,l.jsx)("input",{type:"checkbox",checked:(null===(t=c.symptoms)||void 0===t?void 0:t.includes(e.value))||!1,onChange:()=>x(e.value),className:"w-4 h-4 text-purple-600 focus:ring-purple-500"}),(0,l.jsx)("span",{className:"ml-2 text-sm text-neutral-700",children:e.label})]},e.value)})})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"zh"===e?"备注":"Notes"}),(0,l.jsx)("textarea",{value:c.notes,onChange:e=>o("notes",e.target.value),rows:3,className:"w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-purple-500",placeholder:"zh"===e?"记录任何额外信息...":"Record any additional information..."})]}),(0,l.jsxs)("div",{className:"flex justify-end space-x-4",children:[(0,l.jsx)("button",{onClick:()=>i(!1),className:"px-6 py-2 border border-neutral-300 rounded-lg text-neutral-600 hover:bg-neutral-50",children:"zh"===e?"取消":"Cancel"}),(0,l.jsx)("button",{onClick:()=>{let e=[...t,{...c,id:Date.now().toString()}];s(e),localStorage.setItem("painEntries",JSON.stringify(e)),i(!1),d({date:new Date().toISOString().split("T")[0],intensity:5,menstrualStatus:"",symptoms:[],treatments:[],effectiveness:5,notes:""})},className:"btn-primary",children:"zh"===e?"保存":"Save"})]})]})]})})}),(0,l.jsx)("section",{className:"container-custom",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,l.jsx)("h2",{className:"text-2xl font-bold text-neutral-800 mb-6",children:"zh"===e?"最近记录":"Recent Entries"}),0===t.length?(0,l.jsx)("div",{className:"text-center py-12",children:(0,l.jsx)("p",{className:"text-neutral-600",children:"zh"===e?"还没有记录，开始添加您的第一条记录吧！":"No entries yet. Start by adding your first entry!"})}):(0,l.jsx)("div",{className:"space-y-4",children:t.slice(-5).reverse().map(t=>(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,l.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"font-semibold text-neutral-800",children:t.date}),(0,l.jsx)("p",{className:"text-sm text-neutral-600",children:t.menstrualStatus})]}),(0,l.jsxs)("div",{className:"text-right",children:[(0,l.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:[t.intensity,"/10"]}),(0,l.jsx)("p",{className:"text-sm text-neutral-600",children:"zh"===e?"疼痛强度":"Pain Intensity"})]})]}),t.symptoms.length>0&&(0,l.jsxs)("div",{className:"mb-2",children:[(0,l.jsx)("span",{className:"text-sm font-medium text-neutral-700",children:"zh"===e?"症状：":"Symptoms: "}),(0,l.jsx)("span",{className:"text-sm text-neutral-600",children:t.symptoms.join(", ")})]}),t.notes&&(0,l.jsx)("p",{className:"text-sm text-neutral-600 italic",children:t.notes})]},t.id))})]})})]})}}},function(e){e.O(0,[2586,2971,2117,1744],function(){return e(e.s=9910)}),_N_E=e.O()}]);