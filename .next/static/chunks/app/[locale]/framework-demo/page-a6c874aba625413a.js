(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[7484],{3204:function(e,s,t){Promise.resolve().then(t.bind(t,5691))},5691:function(e,s,t){"use strict";t.r(s),t.d(s,{default:function(){return b}});var l=t(7437),a=t(2265),r=t(2586),c=t(9763);let i=(0,c.Z)("Rocket",[["path",{d:"M4.5 16.5c-1.5 1.26-2 5-2 5s3.74-.5 5-2c.71-.84.7-2.13-.09-2.91a2.18 2.18 0 0 0-2.91-.09z",key:"m3kijz"}],["path",{d:"m12 15-3-3a22 22 0 0 1 2-3.95A12.88 12.88 0 0 1 22 2c0 2.72-.78 7.5-6 11a22.35 22.35 0 0 1-4 2z",key:"1fmvmk"}],["path",{d:"M9 12H4s.55-3.03 2-4c1.62-1.08 5 0 5 0",key:"1f8sc4"}],["path",{d:"M12 15v5s3.03-.55 4-2c1.08-1.62 0-5 0-5",key:"qeys4"}]]),d=(0,c.Z)("Bell",[["path",{d:"M6 8a6 6 0 0 1 12 0c0 7 3 9 3 9H3s3-2 3-9",key:"1qo2s2"}],["path",{d:"M10.3 21a1.94 1.94 0 0 0 3.4 0",key:"qgo35s"}]]),n=(0,c.Z)("Monitor",[["rect",{width:"20",height:"14",x:"2",y:"3",rx:"2",key:"48i651"}],["line",{x1:"8",x2:"16",y1:"21",y2:"21",key:"1svkeh"}],["line",{x1:"12",x2:"12",y1:"17",y2:"21",key:"vw1qmm"}]]);var x=t(6221),o=t(3247);let m=(0,c.Z)("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),h=(0,c.Z)("Database",[["ellipse",{cx:"12",cy:"5",rx:"9",ry:"3",key:"msslwz"}],["path",{d:"M3 5V19A9 3 0 0 0 21 19V5",key:"1wlel7"}],["path",{d:"M3 12A9 3 0 0 0 21 12",key:"mv7ke4"}]]),p=(0,c.Z)("Shield",[["path",{d:"M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10",key:"1irkt0"}]]),u=(0,c.Z)("Cpu",[["rect",{x:"4",y:"4",width:"16",height:"16",rx:"2",key:"1vbyd7"}],["rect",{x:"9",y:"9",width:"6",height:"6",key:"o3kz5p"}],["path",{d:"M15 2v2",key:"13l42r"}],["path",{d:"M15 20v2",key:"15mkzm"}],["path",{d:"M2 15h2",key:"1gxd5l"}],["path",{d:"M2 9h2",key:"1bbxkp"}],["path",{d:"M20 15h2",key:"19e6y8"}],["path",{d:"M20 9h2",key:"19tzq7"}],["path",{d:"M9 2v2",key:"165o2o"}],["path",{d:"M9 20v2",key:"i2bqo8"}]]);function b(){(0,r.useTranslations)();let[e]=(0,a.useState)([{label:"1月",value:65},{label:"2月",value:78},{label:"3月",value:90},{label:"4月",value:81},{label:"5月",value:95},{label:"6月",value:88}]),s=e=>{alert("演示功能: ".concat(e))};return(0,l.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 to-purple-50 py-12",children:(0,l.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,l.jsxs)("div",{className:"text-center mb-12",children:[(0,l.jsx)("div",{className:"flex justify-center mb-4",children:(0,l.jsx)("div",{className:"p-3 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full",children:(0,l.jsx)(i,{className:"w-8 h-8 text-white"})})}),(0,l.jsx)("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"\uD83D\uDE80 Period Hub Health 框架革命"}),(0,l.jsx)("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"体验我们全新的高级框架系统，包含状态管理、性能监控、缓存系统、高级组件等强大功能"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12",children:[(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,l.jsx)(d,{className:"w-6 h-6 text-blue-600"}),(0,l.jsx)("h3",{className:"text-xl font-semibold",children:"Toast通知系统"})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"高级通知系统，支持多种类型、自动关闭、动画效果"}),(0,l.jsx)("button",{onClick:()=>s("Toast通知"),className:"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors",children:"显示随机通知"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,l.jsx)(n,{className:"w-6 h-6 text-purple-600"}),(0,l.jsx)("h3",{className:"text-xl font-semibold",children:"模态框系统"})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"灵活的模态框系统，支持确认对话框、自定义内容、动画效果"}),(0,l.jsx)("button",{onClick:()=>s("模态框"),className:"bg-purple-600 text-white px-4 py-2 rounded-lg hover:bg-purple-700 transition-colors",children:"打开确认对话框"})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,l.jsx)(x.Z,{className:"w-6 h-6 text-green-600"}),(0,l.jsx)("h3",{className:"text-xl font-semibold",children:"数据可视化"})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"内置图表组件，支持线图、柱图、饼图等多种类型"}),(0,l.jsx)("div",{className:"mt-4 p-4 bg-gray-50 rounded-lg",children:(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"图表演示区域"})})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6",children:[(0,l.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[(0,l.jsx)(o.Z,{className:"w-6 h-6 text-yellow-600"}),(0,l.jsx)("h3",{className:"text-xl font-semibold",children:"搜索系统"})]}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"高级搜索功能，支持防抖、过滤、排序、历史记录"}),(0,l.jsx)("input",{type:"text",placeholder:"搜索演示...",className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-12",children:[(0,l.jsx)("div",{className:"bg-blue-50 text-blue-600 border-blue-200 border rounded-lg p-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium opacity-80",children:"缓存命中率"}),(0,l.jsx)("p",{className:"text-2xl font-bold mt-1",children:"95%"}),(0,l.jsxs)("div",{className:"flex items-center space-x-1 mt-2",children:[(0,l.jsx)(m,{className:"w-4 h-4 text-green-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"+5.2%"})]})]}),(0,l.jsx)(h,{className:"w-6 h-6 opacity-80"})]})}),(0,l.jsx)("div",{className:"bg-green-50 text-green-600 border-green-200 border rounded-lg p-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium opacity-80",children:"页面加载时间"}),(0,l.jsx)("p",{className:"text-2xl font-bold mt-1",children:"1.2s"}),(0,l.jsxs)("div",{className:"flex items-center space-x-1 mt-2",children:[(0,l.jsx)(m,{className:"w-4 h-4 text-green-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"-12.3%"})]})]}),(0,l.jsx)(m,{className:"w-6 h-6 opacity-80"})]})}),(0,l.jsx)("div",{className:"bg-red-50 text-red-600 border-red-200 border rounded-lg p-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium opacity-80",children:"错误计数"}),(0,l.jsx)("p",{className:"text-2xl font-bold mt-1",children:"0"}),(0,l.jsxs)("div",{className:"flex items-center space-x-1 mt-2",children:[(0,l.jsx)(p,{className:"w-4 h-4 text-gray-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"0%"})]})]}),(0,l.jsx)(p,{className:"w-6 h-6 opacity-80"})]})}),(0,l.jsx)("div",{className:"bg-purple-50 text-purple-600 border-purple-200 border rounded-lg p-6",children:(0,l.jsxs)("div",{className:"flex items-center justify-between",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm font-medium opacity-80",children:"API调用"}),(0,l.jsx)("p",{className:"text-2xl font-bold mt-1",children:"5"}),(0,l.jsxs)("div",{className:"flex items-center space-x-1 mt-2",children:[(0,l.jsx)(m,{className:"w-4 h-4 text-green-500"}),(0,l.jsx)("span",{className:"text-sm font-medium",children:"+8.7%"})]})]}),(0,l.jsx)(u,{className:"w-6 h-6 opacity-80"})]})})]}),(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,l.jsx)("h3",{className:"text-2xl font-semibold mb-6",children:"\uD83D\uDE80 框架革命成果展示"}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-4",children:[(0,l.jsx)("button",{onClick:()=>s("缓存系统"),className:"bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 transition-colors",children:"测试缓存系统"}),(0,l.jsx)("button",{onClick:()=>s("性能监控"),className:"bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 transition-colors",children:"测试性能监控"}),(0,l.jsx)("button",{onClick:()=>s("状态管理"),className:"bg-purple-600 text-white px-4 py-3 rounded-lg hover:bg-purple-700 transition-colors",children:"查看状态管理"})]}),(0,l.jsxs)("div",{className:"mt-8 p-6 bg-gradient-to-r from-green-50 to-blue-50 rounded-lg",children:[(0,l.jsx)("h4",{className:"text-lg font-semibold text-gray-800 mb-2",children:"✅ 框架革命完成！"}),(0,l.jsx)("p",{className:"text-gray-600",children:"我们成功实现了企业级的React框架，包含状态管理、性能监控、缓存系统、高级组件等功能。 现在可以继续优化和扩展更多功能！"})]})]})]})})}},9763:function(e,s,t){"use strict";t.d(s,{Z:function(){return c}});var l=t(2265),a={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let r=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),c=(e,s)=>{let t=(0,l.forwardRef)((t,c)=>{let{color:i="currentColor",size:d=24,strokeWidth:n=2,absoluteStrokeWidth:x,className:o="",children:m,...h}=t;return(0,l.createElement)("svg",{ref:c,...a,width:d,height:d,stroke:i,strokeWidth:x?24*Number(n)/Number(d):n,className:["lucide","lucide-".concat(r(e)),o].join(" "),...h},[...s.map(e=>{let[s,t]=e;return(0,l.createElement)(s,t)}),...Array.isArray(m)?m:[m]])});return t.displayName="".concat(e),t}},6221:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});let l=(0,t(9763).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},3247:function(e,s,t){"use strict";t.d(s,{Z:function(){return l}});let l=(0,t(9763).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])}},function(e){e.O(0,[2586,2971,2117,1744],function(){return e(e.s=3204)}),_N_E=e.O()}]);