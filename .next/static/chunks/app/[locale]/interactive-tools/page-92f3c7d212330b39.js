(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8999],{2072:function(e,s,t){Promise.resolve().then(t.bind(t,2099)),Promise.resolve().then(t.t.bind(t,2972,23))},2099:function(e,s,t){"use strict";t.d(s,{default:function(){return r}});var l=t(7437),n=t(2265);function r(e){let{locale:s}=e,[t,r]=(0,n.useState)(!1),[i,a]=(0,n.useState)(0),[c,d]=(0,n.useState)(0),[x,o]=(0,n.useState)(!1),u=[{name:"吸气",nameEn:"Inhale",duration:4,color:"bg-blue-600"},{name:"屏息",nameEn:"Hold",duration:7,color:"bg-purple-600"},{name:"呼气",nameEn:"Exhale",duration:8,color:"bg-pink-600"}];(0,n.useEffect)(()=>{let e;return t&&c>0?e=setInterval(()=>{d(c-1)},1e3):t&&0===c&&(i<u.length-1?setTimeout(()=>{a(i+1),d(u[i+1].duration)},500):(r(!1),o(!0),a(0))),()=>clearInterval(e)},[t,c,i,u]);let m=()=>{r(!0),o(!1),a(0),d(u[0].duration)},h=()=>u[i];return(0,l.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 border border-blue-100",children:[(0,l.jsxs)("div",{className:"text-center mb-6",children:[(0,l.jsx)("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,l.jsx)("span",{className:"text-2xl",children:"\uD83E\uDEC1"})}),(0,l.jsx)("h3",{className:"text-2xl font-bold text-blue-800 mb-2",children:"en"===s?"4-7-8 Breathing Exercise":"4-7-8 深呼吸练习"}),(0,l.jsx)("p",{className:"text-blue-600 text-sm",children:"en"===s?"Natural pain relief through nervous system regulation":"通过调节神经系统自然缓解疼痛"})]}),(0,l.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6",children:[(0,l.jsx)("h4",{className:"font-semibold text-blue-800 mb-2",children:"en"===s?"How to practice:":"练习方法："}),(0,l.jsxs)("div",{className:"grid grid-cols-3 gap-3 text-center text-sm",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,l.jsx)("span",{className:"text-lg font-bold text-blue-600",children:"4"})}),(0,l.jsxs)("p",{className:"text-blue-700",children:["en"===s?"Inhale":"吸气"," 4","en"===s?"s":"秒"]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,l.jsx)("span",{className:"text-lg font-bold text-purple-600",children:"7"})}),(0,l.jsxs)("p",{className:"text-purple-700",children:["en"===s?"Hold":"屏息"," 7","en"===s?"s":"秒"]})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-2",children:(0,l.jsx)("span",{className:"text-lg font-bold text-pink-600",children:"8"})}),(0,l.jsxs)("p",{className:"text-pink-700",children:["en"===s?"Exhale":"呼气"," 8","en"===s?"s":"秒"]})]})]})]}),(0,l.jsx)("div",{className:"text-center mb-6",children:t?(0,l.jsxs)("div",{className:"space-y-4",children:[(0,l.jsx)("div",{className:"w-32 h-32 rounded-full flex items-center justify-center mx-auto transition-all duration-1000 ".concat(h().color),children:(0,l.jsxs)("div",{className:"text-white text-center",children:[(0,l.jsx)("div",{className:"text-3xl font-bold",children:c}),(0,l.jsx)("div",{className:"text-sm",children:"zh"===s?h().name:h().nameEn})]})}),(0,l.jsxs)("p",{className:"text-gray-600",children:["en"===s?"Current:":"正在进行："," ","zh"===s?h().name:h().nameEn]})]}):(0,l.jsx)("div",{className:"w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto",children:(0,l.jsx)("span",{className:"text-4xl text-gray-400",children:"\uD83E\uDEC1"})})}),(0,l.jsxs)("div",{className:"text-center space-y-3",children:[!t&&!x&&(0,l.jsx)("button",{onClick:m,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors w-full",children:"en"===s?"\uD83E\uDEC1 Start Guided Practice":"\uD83E\uDEC1 开始引导练习"}),x&&(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsx)("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:(0,l.jsx)("p",{className:"text-green-700 font-medium",children:"en"===s?"✅ One cycle completed!":"✅ 一轮练习完成！"})}),(0,l.jsx)("button",{onClick:m,className:"bg-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors w-full",children:"en"===s?"Practice Again":"再次练习"})]}),t&&(0,l.jsx)("button",{onClick:()=>{r(!1),o(!1),a(0),d(0)},className:"bg-gray-500 text-white px-6 py-2 rounded-lg font-medium hover:bg-gray-600 transition-colors",children:"en"===s?"Stop Practice":"停止练习"})]}),(0,l.jsxs)("div",{className:"mt-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4",children:[(0,l.jsx)("h5",{className:"font-semibold text-gray-800 mb-2",children:"en"===s?"Scientific Benefits:":"科学效果："}),(0,l.jsxs)("div",{className:"grid grid-cols-3 gap-3 text-center text-xs",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-lg font-bold text-blue-600",children:"-40%"}),(0,l.jsx)("div",{className:"text-gray-600",children:"en"===s?"Pain Perception":"疼痛感知"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-lg font-bold text-purple-600",children:"-35%"}),(0,l.jsx)("div",{className:"text-gray-600",children:"en"===s?"Muscle Tension":"肌肉紧张"})]}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-lg font-bold text-pink-600",children:"+60%"}),(0,l.jsx)("div",{className:"text-gray-600",children:"en"===s?"Relaxation":"放松感受"})]})]})]}),(0,l.jsx)("div",{className:"mt-4 text-xs text-gray-600",children:(0,l.jsx)("p",{children:"en"===s?"\uD83D\uDCA1 Tip: Find a comfortable sitting or lying position, relax all muscles. Beginners should do 3-4 cycles.":"\uD83D\uDCA1 建议：找一个舒适的坐位或躺位，放松全身肌肉。初学者建议进行3-4个循环。"})})]})}}},function(e){e.O(0,[2972,2971,2117,1744],function(){return e(e.s=2072)}),_N_E=e.O()}]);