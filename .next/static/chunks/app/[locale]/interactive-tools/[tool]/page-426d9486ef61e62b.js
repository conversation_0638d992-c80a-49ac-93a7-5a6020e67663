(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[157],{9417:function(e,t,i){Promise.resolve().then(i.bind(i,679)),Promise.resolve().then(i.bind(i,7065)),Promise.resolve().then(i.bind(i,639)),Promise.resolve().then(i.bind(i,3856)),Promise.resolve().then(i.t.bind(i,2972,23)),Promise.resolve().then(i.bind(i,1523)),Promise.resolve().then(i.bind(i,49))},679:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return F}});var a=i(7437),s=i(2265),n=i(8278),o=i(2369),r=i(1723),l=i(8997),c=i(9547),d=i(4401),m=i(2252),u=i(3276),p=i(5302),h=i(3774),g=i(8553),x=i(5846),y=i(4794),b=i(1769),f=i(8736),v=i(6858),w=i(2718),j=i(5805),N=i(8867),D=i(2660);let k={zh:[{id:"energy_level",type:"single",category:"basic",weight:2,title:"您平时的精力状态如何？",description:"选择最符合您日常状态的选项",validation:{required:!0},options:[{value:"energetic",label:"精力充沛，很少感到疲劳",weight:3,constitutionType:"balanced"},{value:"moderate",label:"精力一般，偶尔感到疲劳",weight:2,constitutionType:"qi_deficiency"},{value:"tired",label:"经常感到疲劳，精力不足",weight:3,constitutionType:"qi_deficiency"},{value:"exhausted",label:"总是感到疲惫不堪",weight:4,constitutionType:"yang_deficiency"}]},{id:"cold_tolerance",type:"single",category:"temperature",weight:2,title:"您对寒冷的耐受性如何？",description:"比较您与同龄人的耐寒能力",validation:{required:!0},options:[{value:"very_tolerant",label:"很耐寒，手脚总是温暖",weight:3,constitutionType:"balanced"},{value:"normal",label:"一般，与大多数人差不多",weight:2,constitutionType:"balanced"},{value:"sensitive",label:"比较怕冷，手脚容易凉",weight:3,constitutionType:"yang_deficiency"},{value:"very_sensitive",label:"非常怕冷，即使夏天也手脚冰凉",weight:4,constitutionType:"yang_deficiency"}]},{id:"digestive_health",type:"single",category:"digestion",weight:2,title:"您的消化功能如何？",description:"选择最符合您消化状况的描述",validation:{required:!0},options:[{value:"excellent",label:"消化很好，食欲正常，很少腹胀",weight:3,constitutionType:"balanced"},{value:"good",label:"消化一般，偶尔腹胀或消化不良",weight:2,constitutionType:"qi_deficiency"},{value:"poor",label:"经常腹胀、消化不良，食欲不振",weight:3,constitutionType:"phlegm_dampness"},{value:"very_poor",label:"消化很差，经常腹泻或便秘",weight:4,constitutionType:"phlegm_dampness"}]},{id:"sleep_quality",type:"single",category:"sleep",weight:2,title:"您的睡眠质量如何？",description:"评估您的整体睡眠状况",validation:{required:!0},options:[{value:"excellent",label:"睡眠很好，容易入睡，睡得深沉",weight:3,constitutionType:"balanced"},{value:"light_sleep",label:"睡眠较浅，容易醒，多梦",weight:3,constitutionType:"yin_deficiency"},{value:"insomnia",label:"经常失眠，难以入睡",weight:4,constitutionType:"qi_stagnation"},{value:"drowsy",label:"总是感到困倦，睡不够",weight:3,constitutionType:"phlegm_dampness"}]},{id:"emotional_state",type:"single",category:"emotion",weight:2,title:"您的情绪状态通常如何？",description:"选择最符合您情绪特点的描述",validation:{required:!0},options:[{value:"stable",label:"情绪稳定，心情愉快",weight:3,constitutionType:"balanced"},{value:"anxious",label:"容易焦虑，心情烦躁",weight:3,constitutionType:"qi_stagnation"},{value:"depressed",label:"经常感到抑郁，情绪低落",weight:4,constitutionType:"qi_stagnation"},{value:"irritable",label:"容易发脾气，情绪波动大",weight:3,constitutionType:"damp_heat"}]},{id:"menstrual_pattern",type:"single",category:"menstruation",weight:3,title:"您的月经特点是？",description:"选择最符合您月经情况的描述",validation:{required:!0},options:[{value:"regular_normal",label:"周期规律，量适中，颜色正常",weight:3,constitutionType:"balanced"},{value:"light_delayed",label:"量少，周期延后，颜色淡",weight:3,constitutionType:"qi_deficiency"},{value:"heavy_early",label:"量多，周期提前，颜色深红",weight:3,constitutionType:"damp_heat"},{value:"clots_dark",label:"有血块，颜色暗红或紫黑",weight:4,constitutionType:"blood_stasis"},{value:"irregular",label:"周期不规律，时多时少",weight:3,constitutionType:"qi_stagnation"}]},{id:"body_type",type:"single",category:"physical",weight:2,title:"您的体型特点是？",description:"选择最符合您体型的描述",validation:{required:!0},options:[{value:"normal",label:"体型匀称，不胖不瘦",weight:3,constitutionType:"balanced"},{value:"thin",label:"偏瘦，不容易长胖",weight:3,constitutionType:"yin_deficiency"},{value:"overweight",label:"偏胖，容易水肿",weight:3,constitutionType:"phlegm_dampness"},{value:"muscular",label:"体格健壮，肌肉结实",weight:2,constitutionType:"balanced"}]},{id:"skin_condition",type:"single",category:"appearance",weight:1,title:"您的皮肤状态如何？",description:"选择最符合您皮肤特点的描述",validation:{required:!0},options:[{value:"healthy",label:"皮肤润泽，有光泽，很少长痘",weight:3,constitutionType:"balanced"},{value:"dry",label:"皮肤干燥，缺乏光泽",weight:3,constitutionType:"yin_deficiency"},{value:"oily_acne",label:"皮肤油腻，容易长痘",weight:3,constitutionType:"damp_heat"},{value:"dull",label:"皮肤暗沉，色斑较多",weight:3,constitutionType:"blood_stasis"}]},{id:"menstrual_pain_severity",type:"scale",category:"menstrual",weight:3,title:"您经期疼痛的程度如何？",description:"请在滑块上选择您的疼痛程度（0=无痛，10=剧烈疼痛）",validation:{required:!0,min:0,max:10},options:Array.from({length:11},(e,t)=>({value:t,label:0===t?"无痛":t<=3?"轻微":t<=6?"中等":t<=8?"严重":"剧烈",weight:t<=2?3:t<=4?2:t<=7?3:4,constitutionType:t<=2?"balanced":t<=4?"qi_deficiency":t<=7?"blood_stasis":"qi_stagnation"}))},{id:"pain_nature",type:"single",category:"menstrual",weight:2,title:"您的经期疼痛性质主要是？",description:"选择最符合您疼痛感受的描述",validation:{required:!0},options:[{value:"cramping",label:"绞痛，一阵一阵的收缩感",weight:3,constitutionType:"qi_stagnation"},{value:"dull_ache",label:"钝痛，持续的隐隐作痛",weight:3,constitutionType:"qi_deficiency"},{value:"sharp_pain",label:"刺痛，像针扎一样",weight:4,constitutionType:"blood_stasis"},{value:"cold_pain",label:"冷痛，遇冷加重，喜温喜按",weight:3,constitutionType:"yang_deficiency"}]},{id:"menstrual_symptoms",type:"multiple",category:"menstrual",weight:2,title:"您在经期还有哪些伴随症状？",description:"可以选择多个症状",validation:{required:!1},options:[{value:"bloating",label:"腹胀",weight:2,constitutionType:"phlegm_dampness"},{value:"nausea",label:"恶心呕吐",weight:3,constitutionType:"damp_heat"},{value:"headache",label:"头痛",weight:2,constitutionType:"qi_stagnation"},{value:"mood_swings",label:"情绪波动大",weight:3,constitutionType:"qi_stagnation"},{value:"fatigue",label:"极度疲劳",weight:2,constitutionType:"qi_deficiency"},{value:"back_pain",label:"腰痛",weight:2,constitutionType:"yang_deficiency"},{value:"breast_tenderness",label:"乳房胀痛",weight:2,constitutionType:"qi_stagnation"},{value:"none",label:"以上都没有",weight:1,constitutionType:"balanced"}]}],en:[{id:"energy_level",type:"single",category:"basic",weight:2,title:"How is your usual energy level?",description:"Choose the option that best describes your daily state",validation:{required:!0},options:[{value:"energetic",label:"Energetic, rarely feel tired",weight:3,constitutionType:"balanced"},{value:"moderate",label:"Moderate energy, occasionally feel tired",weight:2,constitutionType:"qi_deficiency"},{value:"tired",label:"Often feel tired, lack of energy",weight:3,constitutionType:"qi_deficiency"},{value:"exhausted",label:"Always feel exhausted",weight:4,constitutionType:"yang_deficiency"}]},{id:"cold_tolerance",type:"single",category:"temperature",weight:2,title:"How is your tolerance to cold?",description:"Compare your cold tolerance with people of your age",validation:{required:!0},options:[{value:"very_tolerant",label:"Very cold-tolerant, hands and feet always warm",weight:3,constitutionType:"balanced"},{value:"normal",label:"Normal, similar to most people",weight:2,constitutionType:"balanced"},{value:"sensitive",label:"Quite sensitive to cold, hands and feet easily get cold",weight:3,constitutionType:"yang_deficiency"},{value:"very_sensitive",label:"Very sensitive to cold, hands and feet cold even in summer",weight:4,constitutionType:"yang_deficiency"}]},{id:"digestive_health",type:"single",category:"digestion",weight:2,title:"How is your digestive function?",description:"Choose the description that best fits your digestive condition",validation:{required:!0},options:[{value:"excellent",label:"Excellent digestion, normal appetite, rarely bloated",weight:3,constitutionType:"balanced"},{value:"good",label:"Fair digestion, occasional bloating or indigestion",weight:2,constitutionType:"qi_deficiency"},{value:"poor",label:"Often bloated, indigestion, poor appetite",weight:3,constitutionType:"phlegm_dampness"},{value:"very_poor",label:"Very poor digestion, often diarrhea or constipation",weight:4,constitutionType:"phlegm_dampness"}]},{id:"sleep_quality",type:"single",category:"sleep",weight:2,title:"How is your sleep quality?",description:"Assess your overall sleep condition",validation:{required:!0},options:[{value:"excellent",label:"Excellent sleep, fall asleep easily, sleep deeply",weight:3,constitutionType:"balanced"},{value:"light_sleep",label:"Light sleep, wake up easily, many dreams",weight:3,constitutionType:"yin_deficiency"},{value:"insomnia",label:"Often insomnia, difficult to fall asleep",weight:4,constitutionType:"qi_stagnation"},{value:"drowsy",label:"Always feel drowsy, never get enough sleep",weight:3,constitutionType:"phlegm_dampness"}]},{id:"emotional_state",type:"single",category:"emotion",weight:2,title:"How is your emotional state usually?",description:"Choose the description that best fits your emotional characteristics",validation:{required:!0},options:[{value:"stable",label:"Emotionally stable, happy mood",weight:3,constitutionType:"balanced"},{value:"anxious",label:"Easily anxious, irritable mood",weight:3,constitutionType:"qi_stagnation"},{value:"depressed",label:"Often feel depressed, low mood",weight:4,constitutionType:"qi_stagnation"},{value:"irritable",label:"Easily lose temper, large mood swings",weight:3,constitutionType:"damp_heat"}]},{id:"menstrual_pattern",type:"single",category:"menstruation",weight:3,title:"What are your menstrual characteristics?",description:"Choose the description that best fits your menstrual condition",validation:{required:!0},options:[{value:"regular_normal",label:"Regular cycle, moderate flow, normal color",weight:3,constitutionType:"balanced"},{value:"light_delayed",label:"Light flow, delayed cycle, pale color",weight:3,constitutionType:"qi_deficiency"},{value:"heavy_early",label:"Heavy flow, early cycle, dark red color",weight:3,constitutionType:"damp_heat"},{value:"clots_dark",label:"Blood clots, dark red or purple-black color",weight:4,constitutionType:"blood_stasis"},{value:"irregular",label:"Irregular cycle, variable flow",weight:3,constitutionType:"qi_stagnation"}]},{id:"body_type",type:"single",category:"physical",weight:2,title:"What are your body type characteristics?",description:"Choose the description that best fits your body type",validation:{required:!0},options:[{value:"normal",label:"Well-proportioned, neither fat nor thin",weight:3,constitutionType:"balanced"},{value:"thin",label:"Lean, not easy to gain weight",weight:3,constitutionType:"yin_deficiency"},{value:"overweight",label:"Overweight, prone to edema",weight:3,constitutionType:"phlegm_dampness"},{value:"muscular",label:"Strong build, firm muscles",weight:2,constitutionType:"balanced"}]},{id:"skin_condition",type:"single",category:"appearance",weight:1,title:"How is your skin condition?",description:"Choose the description that best fits your skin characteristics",validation:{required:!0},options:[{value:"healthy",label:"Moist skin, glossy, rarely get acne",weight:3,constitutionType:"balanced"},{value:"dry",label:"Dry skin, lack of luster",weight:3,constitutionType:"yin_deficiency"},{value:"oily_acne",label:"Oily skin, prone to acne",weight:3,constitutionType:"damp_heat"},{value:"dull",label:"Dull skin, many dark spots",weight:3,constitutionType:"blood_stasis"}]},{id:"menstrual_pain_severity",type:"scale",category:"menstrual",weight:3,title:"How severe is your menstrual pain?",description:"Please select your pain level on the slider (0=No pain, 10=Severe pain)",validation:{required:!0,min:0,max:10},options:Array.from({length:11},(e,t)=>({value:t,label:0===t?"No pain":t<=3?"Mild":t<=6?"Moderate":t<=8?"Severe":"Extreme",weight:t<=2?3:t<=4?2:t<=7?3:4,constitutionType:t<=2?"balanced":t<=4?"qi_deficiency":t<=7?"blood_stasis":"qi_stagnation"}))},{id:"pain_nature",type:"single",category:"menstrual",weight:2,title:"What is the nature of your menstrual pain?",description:"Choose the description that best matches your pain sensation",validation:{required:!0},options:[{value:"cramping",label:"Cramping, wave-like contractions",weight:3,constitutionType:"qi_stagnation"},{value:"dull_ache",label:"Dull ache, continuous mild pain",weight:3,constitutionType:"qi_deficiency"},{value:"sharp_pain",label:"Sharp pain, like needle pricks",weight:4,constitutionType:"blood_stasis"},{value:"cold_pain",label:"Cold pain, worsens with cold, improves with warmth",weight:3,constitutionType:"yang_deficiency"}]},{id:"menstrual_symptoms",type:"multiple",category:"menstrual",weight:2,title:"What other symptoms do you experience during menstruation?",description:"You can select multiple symptoms",validation:{required:!1},options:[{value:"bloating",label:"Bloating",weight:2,constitutionType:"phlegm_dampness"},{value:"nausea",label:"Nausea and vomiting",weight:3,constitutionType:"damp_heat"},{value:"headache",label:"Headache",weight:2,constitutionType:"qi_stagnation"},{value:"mood_swings",label:"Severe mood swings",weight:3,constitutionType:"qi_stagnation"},{value:"fatigue",label:"Extreme fatigue",weight:2,constitutionType:"qi_deficiency"},{value:"back_pain",label:"Back pain",weight:2,constitutionType:"yang_deficiency"},{value:"breast_tenderness",label:"Breast tenderness",weight:2,constitutionType:"qi_stagnation"},{value:"none",label:"None of the above",weight:1,constitutionType:"balanced"}]}]},C={zh:{balanced:{acupoints:{primaryPoints:[{name:"足三里",location:"膝盖下3寸，胫骨外侧1横指",function:"调理脾胃，增强体质",method:"顺时针按揉3-5分钟"},{name:"关元",location:"肚脐下3寸",function:"培元固本，调理气血",method:"温和按压2-3分钟"}],supportingPoints:[{name:"百会",location:"头顶正中",function:"提神醒脑，调节情绪",method:"轻柔按压1-2分钟"}],massageTechnique:"温和按摩，以酸胀感为度",frequency:"每日1-2次",duration:"每次10-15分钟"},diet:{beneficial:["五谷杂粮","新鲜蔬果","优质蛋白","适量坚果"],avoid:["过度油腻","过度辛辣","过度生冷"],principles:["饮食均衡","定时定量","细嚼慢咽"],sampleMeals:["小米粥配青菜","蒸蛋羹","清炖鸡汤","时令水果"]},lifestyle:{exercise:["太极拳","八段锦","慢跑","瑜伽"],sleep:["规律作息","晚上11点前入睡","保证7-8小时睡眠"],emotional:["保持心情愉快","适度社交","培养兴趣爱好"],seasonal:["春季养肝","夏季养心","秋季养肺","冬季养肾"]},moxibustion:{points:["足三里","关元"],timing:"每周2-3次",duration:"每穴15-20分钟",frequency:"保健为主",precautions:["注意防烫","孕期禁用","饭后1小时进行"]}},qi_deficiency:{acupoints:{primaryPoints:[{name:"气海",location:"肚脐下1.5寸",function:"补气益气，增强体力",method:"顺时针按揉5-8分钟"},{name:"足三里",location:"膝盖下3寸，胫骨外侧1横指",function:"健脾益气，增强消化",method:"重点按揉5-10分钟"},{name:"脾俞",location:"第11胸椎棘突下旁开1.5寸",function:"健脾益气，助消化",method:"按压配合艾灸效果更佳"}],supportingPoints:[{name:"百会",location:"头顶正中",function:"升阳举陷，提神益气",method:"轻柔提拉按压"},{name:"太冲",location:"足背第1、2跖骨间",function:"疏肝理气，调和气血",method:"按压2-3分钟"}],massageTechnique:"温和持续按压，避免过度用力",frequency:"每日2次，早晚各一次",duration:"每次15-20分钟"},diet:{beneficial:["黄芪","党参","山药","大枣","桂圆","小米","南瓜","胡萝卜"],avoid:["生冷食物","过度油腻","难消化食物","过量生萝卜"],principles:["温补脾胃","少食多餐","细嚼慢咽","避免过饱"],sampleMeals:["黄芪炖鸡汤","山药小米粥","红枣桂圆茶","蒸蛋羹"]},lifestyle:{exercise:["八段锦","太极拳","散步","轻度瑜伽"],sleep:["早睡早起","午休30分钟","避免熬夜"],emotional:["保持乐观","避免过度思虑","适度放松"],seasonal:["春夏养阳","秋冬进补","避免过度劳累"]},moxibustion:{points:["气海","关元","足三里","脾俞"],timing:"每日或隔日",duration:"每穴20-30分钟",frequency:"连续调理2-3个月",precautions:["温度适中","避免烫伤","经期减量"]}},yang_deficiency:{acupoints:{primaryPoints:[{name:"命门",location:"第2腰椎棘突下",function:"温补肾阳，强腰健肾",method:"温热按压配合艾灸"},{name:"肾俞",location:"第2腰椎棘突下旁开1.5寸",function:"补肾壮阳，强腰膝",method:"双手同时按压"},{name:"关元",location:"肚脐下3寸",function:"温补下焦，固本培元",method:"顺时针按揉配合艾灸"}],supportingPoints:[{name:"涌泉",location:"足底前1/3凹陷处",function:"温补肾阳，引火归元",method:"睡前按摩至发热"}],massageTechnique:"温热按摩，配合艾灸效果更佳",frequency:"每日2次",duration:"每次20-30分钟"},diet:{beneficial:["羊肉","韭菜","生姜","肉桂","核桃","栗子","黑豆","枸杞"],avoid:["生冷食物","寒性水果","冰饮","苦寒药物"],principles:["温补阳气","忌食生冷","适当进补","温热为主"],sampleMeals:["当归生姜羊肉汤","韭菜炒蛋","核桃粥","枸杞茶"]},lifestyle:{exercise:["慢跑","太极拳","八段锦","适度力量训练"],sleep:["保暖睡眠","避免夜间受凉","充足睡眠"],emotional:["保持积极心态","避免过度忧虑"],seasonal:["春夏养阳","秋冬重点保暖","避免贪凉"]},moxibustion:{points:["命门","肾俞","关元","足三里"],timing:"每日艾灸",duration:"每穴30-40分钟",frequency:"长期调理",precautions:["注意保暖","避免受风","经期谨慎使用"]}},yin_deficiency:{acupoints:{primaryPoints:[{name:"太溪",location:"内踝后方，跟腱前凹陷处",function:"滋阴补肾，清虚热",method:"轻柔按揉3-5分钟"},{name:"三阴交",location:"内踝上3寸，胫骨内侧缘后方",function:"滋阴养血，调经止痛",method:"按压至酸胀感"},{name:"肾俞",location:"第2腰椎棘突下旁开1.5寸",function:"补肾滋阴，强腰膝",method:"轻柔按压，避免过重"}],supportingPoints:[{name:"神门",location:"腕横纹尺侧端，尺侧腕屈肌腱桡侧凹陷处",function:"宁心安神，改善睡眠",method:"睡前按压2-3分钟"}],massageTechnique:"轻柔按摩，避免过度刺激",frequency:"每日1-2次",duration:"每次10-15分钟"},diet:{beneficial:["银耳","百合","枸杞","黑芝麻","蜂蜜","梨","葡萄","鸭肉"],avoid:["辛辣食物","煎炸食品","温燥食物","过量咖啡"],principles:["滋阴润燥","清淡饮食","多饮水","少食辛辣"],sampleMeals:["银耳莲子汤","百合粥","蜂蜜柠檬水","清蒸鱼"]},lifestyle:{exercise:["瑜伽","太极拳","游泳","散步"],sleep:["规律作息","创造安静睡眠环境","睡前放松"],emotional:["保持心境平和","学会释放压力","冥想练习"],seasonal:["秋冬滋阴","避免过度出汗","注意补水"]},moxibustion:{points:["太溪","三阴交"],timing:"隔日进行",duration:"每穴15-20分钟",frequency:"温和调理",precautions:["温度不宜过高","时间不宜过长","注意补水"]}},phlegm_dampness:{acupoints:{primaryPoints:[{name:"丰隆",location:"外踝上8寸，胫骨前缘外侧1.5寸",function:"化痰除湿，健脾和胃",method:"重点按揉5-8分钟"},{name:"阴陵泉",location:"胫骨内侧髁下方凹陷处",function:"健脾利湿，消肿",method:"按压至酸胀感明显"},{name:"中脘",location:"肚脐上4寸",function:"健脾和胃，化湿消痰",method:"顺时针按揉"}],supportingPoints:[{name:"天枢",location:"肚脐旁开2寸",function:"调理肠胃，消除腹胀",method:"双侧同时按揉"}],massageTechnique:"稍重按压，以促进气血运行",frequency:"每日2-3次",duration:"每次15-20分钟"},diet:{beneficial:["薏米","冬瓜","白萝卜","陈皮","山楂","荷叶","绿豆"],avoid:["甜腻食物","油炸食品","肥肉","奶制品过量"],principles:["清淡饮食","少油少盐","控制甜食","多食化湿食物"],sampleMeals:["薏米红豆汤","冬瓜汤","山楂茶","清蒸蔬菜"]},lifestyle:{exercise:["快走","慢跑","游泳","有氧运动"],sleep:["避免午睡过长","保持规律作息"],emotional:["保持积极心态","避免过度思虑"],seasonal:["春夏祛湿","秋冬温补","避免潮湿环境"]},moxibustion:{points:["丰隆","阴陵泉","中脘"],timing:"每日或隔日",duration:"每穴20-25分钟",frequency:"坚持调理",precautions:["配合运动","控制饮食","保持环境干燥"]}},damp_heat:{acupoints:{primaryPoints:[{name:"曲池",location:"肘横纹外侧端，屈肘时肘横纹头",function:"清热解毒，祛湿热",method:"按压至酸胀感"},{name:"阴陵泉",location:"胫骨内侧髁下方凹陷处",function:"清热利湿，健脾",method:"重点按揉"},{name:"大椎",location:"第7颈椎棘突下",function:"清热解表，调节免疫",method:"轻柔按压"}],supportingPoints:[{name:"合谷",location:"手背第1、2掌骨间",function:"清热解毒，调理面部",method:"按压2-3分钟"}],massageTechnique:"适中力度，以清热为主",frequency:"每日1-2次",duration:"每次10-15分钟"},diet:{beneficial:["绿豆","苦瓜","黄瓜","西瓜","薏米","茯苓","莲子心"],avoid:["辛辣食物","油炸食品","烧烤","酒类","甜腻食物"],principles:["清热利湿","清淡饮食","多饮水","少食肥甘"],sampleMeals:["绿豆汤","苦瓜炒蛋","薏米粥","莲子心茶"]},lifestyle:{exercise:["游泳","瑜伽","太极拳","避免剧烈运动"],sleep:["保持凉爽睡眠环境","规律作息"],emotional:["保持心境平和","避免急躁情绪"],seasonal:["夏季重点清热","避免暴晒","保持环境通风"]},moxibustion:{points:["阴陵泉"],timing:"谨慎使用",duration:"时间较短",frequency:"以按摩为主",precautions:["避免过热","以清热为主","可用刮痧代替"]}},blood_stasis:{acupoints:{primaryPoints:[{name:"血海",location:"髌骨内上缘上2寸",function:"活血化瘀，调经止痛",method:"按揉至局部发热"},{name:"三阴交",location:"内踝上3寸，胫骨内侧缘后方",function:"活血调经，化瘀止痛",method:"重点按压"},{name:"膈俞",location:"第7胸椎棘突下旁开1.5寸",function:"活血化瘀，宽胸理气",method:"按压配合艾灸"}],supportingPoints:[{name:"太冲",location:"足背第1、2跖骨间",function:"疏肝理气，活血化瘀",method:"按压至酸胀感"}],massageTechnique:"适度用力，以活血为主",frequency:"每日2次",duration:"每次15-20分钟"},diet:{beneficial:["山楂","红花","当归","川芎","红糖","黑木耳","洋葱"],avoid:["生冷食物","油腻食物","过咸食物"],principles:["活血化瘀","温通经络","适当温补"],sampleMeals:["山楂茶","当归炖鸡","黑木耳炒菜","红糖姜茶"]},lifestyle:{exercise:["慢跑","太极拳","瑜伽","适度有氧运动"],sleep:["保持规律作息","避免熬夜"],emotional:["保持心情愉快","避免情绪郁结"],seasonal:["春季疏肝","注意保暖","避免受寒"]},moxibustion:{points:["血海","三阴交","膈俞"],timing:"每日或隔日",duration:"每穴20-25分钟",frequency:"经期前后重点调理",precautions:["经期谨慎使用","注意温度","配合运动"]}},qi_stagnation:{acupoints:{primaryPoints:[{name:"太冲",location:"足背第1、2跖骨间",function:"疏肝解郁，调畅气机",method:"按压至酸胀感明显"},{name:"期门",location:"第6肋间隙，乳头直下",function:"疏肝理气，宽胸解郁",method:"轻柔按揉"},{name:"神门",location:"腕横纹尺侧端",function:"宁心安神，调节情绪",method:"睡前重点按压"}],supportingPoints:[{name:"印堂",location:"两眉头连线中点",function:"宁心安神，开窍醒脑",method:"轻柔按压"}],massageTechnique:"轻柔舒缓，以疏通为主",frequency:"每日2-3次",duration:"每次10-15分钟"},diet:{beneficial:["玫瑰花","柠檬","橙子","佛手","香橼","薄荷","茉莉花"],avoid:["过于油腻","难消化食物","过量咖啡因"],principles:["疏肝理气","清淡饮食","适量芳香类食物"],sampleMeals:["玫瑰花茶","柠檬蜂蜜水","薄荷茶","清淡蔬菜"]},lifestyle:{exercise:["瑜伽","太极拳","散步","深呼吸练习"],sleep:["规律作息","睡前放松","创造安静环境"],emotional:["学会释放压力","培养兴趣爱好","适当社交"],seasonal:["春季重点疏肝","保持心情愉快","避免情绪波动"]},moxibustion:{points:["太冲","神门"],timing:"情绪不佳时",duration:"每穴15-20分钟",frequency:"按需调理",precautions:["温和施灸","配合情绪调节","避免过度刺激"]}},special_diathesis:{acupoints:{primaryPoints:[{name:"风池",location:"枕骨下，胸锁乳突肌与斜方肌间凹陷处",function:"祛风解表，增强抵抗力",method:"轻柔按压"},{name:"足三里",location:"膝盖下3寸，胫骨外侧1横指",function:"调理脾胃，增强体质",method:"温和按揉"}],supportingPoints:[{name:"迎香",location:"鼻翼外缘中点旁",function:"通鼻窍，防过敏",method:"轻柔按揉"}],massageTechnique:"温和按摩，避免过度刺激",frequency:"每日1次",duration:"每次10分钟"},diet:{beneficial:["益生菌食品","新鲜蔬果","优质蛋白","抗过敏食物"],avoid:["已知过敏原","添加剂多的食品","刺激性食物"],principles:["避免过敏原","增强免疫力","营养均衡"],sampleMeals:["酸奶","新鲜水果","清淡蔬菜","白肉类"]},lifestyle:{exercise:["适度运动","避免过敏环境","增强体质"],sleep:["保持充足睡眠","避免过敏原"],emotional:["保持积极心态","学会应对过敏"],seasonal:["根据季节调整","预防过敏发作"]},moxibustion:{points:["足三里"],timing:"谨慎使用",duration:"时间较短",frequency:"个体化调理",precautions:["避免过敏反应","个体化方案","医生指导下进行"]}}},en:{balanced:{acupoints:{primaryPoints:[{name:"Zusanli (ST36)",location:"3 cun below the knee, 1 finger-width lateral to the tibia",function:"Regulate spleen and stomach, strengthen constitution",method:"Massage clockwise for 3-5 minutes"},{name:"Guanyuan (CV4)",location:"3 cun below the navel",function:"Strengthen vitality, regulate qi and blood",method:"Gentle pressure for 2-3 minutes"}],supportingPoints:[{name:"Baihui (GV20)",location:"Top center of the head",function:"Refresh mind, regulate emotions",method:"Gentle pressure for 1-2 minutes"}],massageTechnique:"Gentle massage until feeling soreness",frequency:"1-2 times daily",duration:"10-15 minutes each session"},diet:{beneficial:["Whole grains","Fresh vegetables and fruits","Quality protein","Moderate nuts"],avoid:["Excessive greasy food","Excessive spicy food","Excessive cold food"],principles:["Balanced diet","Regular meals","Chew slowly"],sampleMeals:["Millet porridge with vegetables","Steamed egg custard","Clear chicken soup","Seasonal fruits"]},lifestyle:{exercise:["Tai Chi","Qigong","Jogging","Yoga"],sleep:["Regular schedule","Sleep before 11 PM","Ensure 7-8 hours of sleep"],emotional:["Maintain happy mood","Moderate socializing","Cultivate hobbies"],seasonal:["Nourish liver in spring","Nourish heart in summer","Nourish lungs in autumn","Nourish kidneys in winter"]},moxibustion:{points:["Zusanli","Guanyuan"],timing:"2-3 times per week",duration:"15-20 minutes per point",frequency:"Mainly for health maintenance",precautions:["Prevent burns","Avoid during pregnancy","Perform 1 hour after meals"]}},qi_deficiency:{acupoints:{primaryPoints:[{name:"Qihai (CV6)",location:"1.5 cun below the navel",function:"Tonify qi, enhance physical strength",method:"Massage clockwise for 5-8 minutes"},{name:"Zusanli (ST36)",location:"3 cun below the knee, 1 finger-width lateral to the tibia",function:"Strengthen spleen and qi, enhance digestion",method:"Focus massage for 5-10 minutes"},{name:"Pishu (BL20)",location:"1.5 cun lateral to the 11th thoracic vertebra",function:"Strengthen spleen and qi, aid digestion",method:"Pressure combined with moxibustion works better"}],supportingPoints:[{name:"Baihui (GV20)",location:"Top center of the head",function:"Lift yang qi, refresh and tonify qi",method:"Gentle lifting pressure"},{name:"Taichong (LR3)",location:"Between 1st and 2nd metatarsals on foot dorsum",function:"Soothe liver qi, harmonize qi and blood",method:"Press for 2-3 minutes"}],massageTechnique:"Gentle sustained pressure, avoid excessive force",frequency:"2 times daily, morning and evening",duration:"15-20 minutes each session"},diet:{beneficial:["Astragalus","Codonopsis","Chinese yam","Red dates","Longan","Millet","Pumpkin","Carrot"],avoid:["Cold raw foods","Excessive greasy food","Hard-to-digest foods","Excessive raw radish"],principles:["Warm and tonify spleen-stomach","Small frequent meals","Chew slowly","Avoid overeating"],sampleMeals:["Astragalus chicken soup","Chinese yam millet porridge","Red date longan tea","Steamed egg custard"]},lifestyle:{exercise:["Qigong","Tai Chi","Walking","Light yoga"],sleep:["Early to bed and early to rise","30-minute afternoon nap","Avoid staying up late"],emotional:["Stay optimistic","Avoid overthinking","Moderate relaxation"],seasonal:["Nourish yang in spring-summer","Tonify in autumn-winter","Avoid overexertion"]},moxibustion:{points:["Qihai","Guanyuan","Zusanli","Pishu"],timing:"Daily or every other day",duration:"20-30 minutes per point",frequency:"Continuous treatment for 2-3 months",precautions:["Moderate temperature","Avoid burns","Reduce during menstruation"]}},yang_deficiency:{acupoints:{primaryPoints:[{name:"Mingmen (GV4)",location:"Below the 2nd lumbar vertebra",function:"Warm and tonify kidney yang, strengthen lower back and kidneys",method:"Warm pressure combined with moxibustion"},{name:"Shenshu (BL23)",location:"1.5 cun lateral to the 2nd lumbar vertebra",function:"Tonify kidneys and strengthen yang, strengthen lower back and knees",method:"Press with both hands simultaneously"},{name:"Guanyuan (CV4)",location:"3 cun below the navel",function:"Warm and tonify lower jiao, strengthen foundation",method:"Clockwise massage combined with moxibustion"}],supportingPoints:[{name:"Yongquan (KD1)",location:"Depression in the front 1/3 of the sole",function:"Warm and tonify kidney yang, guide fire back to source",method:"Massage before sleep until warm"}],massageTechnique:"Warm massage, better effect when combined with moxibustion",frequency:"2 times daily",duration:"20-30 minutes each session"},diet:{beneficial:["Mutton","Chinese chives","Ginger","Cinnamon","Walnuts","Chestnuts","Black beans","Goji berries"],avoid:["Cold raw foods","Cold-natured fruits","Ice drinks","Bitter cold medicines"],principles:["Warm and tonify yang qi","Avoid cold foods","Appropriate tonification","Focus on warm foods"],sampleMeals:["Angelica ginger mutton soup","Stir-fried eggs with chives","Walnut porridge","Goji berry tea"]},lifestyle:{exercise:["Jogging","Tai Chi","Qigong","Moderate strength training"],sleep:["Keep warm while sleeping","Avoid catching cold at night","Adequate sleep"],emotional:["Maintain positive attitude","Avoid excessive worry"],seasonal:["Nourish yang in spring-summer","Focus on keeping warm in autumn-winter","Avoid seeking coolness"]},moxibustion:{points:["Mingmen","Shenshu","Guanyuan","Zusanli"],timing:"Daily moxibustion",duration:"30-40 minutes per point",frequency:"Long-term treatment",precautions:["Keep warm","Avoid wind exposure","Use cautiously during menstruation"]}},yin_deficiency:{acupoints:{primaryPoints:[{name:"Taixi (KD3)",location:"Depression behind the medial malleolus, in front of the Achilles tendon",function:"Nourish yin and tonify kidneys, clear deficiency heat",method:"Gentle massage for 3-5 minutes"},{name:"Sanyinjiao (SP6)",location:"3 cun above the medial malleolus, behind the medial border of the tibia",function:"Nourish yin and blood, regulate menstruation and relieve pain",method:"Press until feeling soreness"},{name:"Shenshu (BL23)",location:"1.5 cun lateral to the 2nd lumbar vertebra",function:"Tonify kidneys and nourish yin, strengthen lower back and knees",method:"Gentle pressure, avoid excessive force"}],supportingPoints:[{name:"Shenmen (HE7)",location:"Ulnar end of the wrist crease, in the depression on the radial side of the flexor carpi ulnaris tendon",function:"Calm the mind and spirit, improve sleep",method:"Press for 2-3 minutes before sleep"}],massageTechnique:"Gentle massage, avoid excessive stimulation",frequency:"1-2 times daily",duration:"10-15 minutes each session"},diet:{beneficial:["White fungus","Lily bulb","Goji berries","Black sesame","Honey","Pears","Grapes","Duck meat"],avoid:["Spicy foods","Fried foods","Warm-dry foods","Excessive coffee"],principles:["Nourish yin and moisten dryness","Light diet","Drink plenty of water","Reduce spicy foods"],sampleMeals:["White fungus lotus seed soup","Lily porridge","Honey lemon water","Steamed fish"]},lifestyle:{exercise:["Yoga","Tai Chi","Swimming","Walking"],sleep:["Regular schedule","Create quiet sleep environment","Relax before sleep"],emotional:["Maintain peaceful mind","Learn to release stress","Meditation practice"],seasonal:["Nourish yin in autumn-winter","Avoid excessive sweating","Pay attention to hydration"]},moxibustion:{points:["Taixi","Sanyinjiao"],timing:"Every other day",duration:"15-20 minutes per point",frequency:"Gentle treatment",precautions:["Temperature should not be too high","Duration should not be too long","Pay attention to hydration"]}},phlegm_dampness:{acupoints:{primaryPoints:[{name:"Fenglong (ST40)",location:"8 cun above the lateral malleolus, 1.5 cun lateral to the anterior border of the tibia",function:"Transform phlegm and eliminate dampness, strengthen spleen and harmonize stomach",method:"Focus massage for 5-8 minutes"},{name:"Yinlingquan (SP9)",location:"Depression below the medial condyle of the tibia",function:"Strengthen spleen and drain dampness, reduce swelling",method:"Press until obvious soreness"},{name:"Zhongwan (CV12)",location:"4 cun above the navel",function:"Strengthen spleen and harmonize stomach, transform dampness and eliminate phlegm",method:"Clockwise massage"}],supportingPoints:[{name:"Tianshu (ST25)",location:"2 cun lateral to the navel",function:"Regulate intestines and stomach, eliminate abdominal distension",method:"Massage both sides simultaneously"}],massageTechnique:"Slightly stronger pressure to promote qi and blood circulation",frequency:"2-3 times daily",duration:"15-20 minutes each session"},diet:{beneficial:["Job's tears","Winter melon","White radish","Tangerine peel","Hawthorn","Lotus leaf","Mung beans"],avoid:["Sweet greasy foods","Fried foods","Fatty meat","Excessive dairy products"],principles:["Light diet","Low oil and salt","Control sweets","Eat more dampness-transforming foods"],sampleMeals:["Job's tears and red bean soup","Winter melon soup","Hawthorn tea","Steamed vegetables"]},lifestyle:{exercise:["Brisk walking","Jogging","Swimming","Aerobic exercise"],sleep:["Avoid excessive afternoon naps","Maintain regular schedule"],emotional:["Maintain positive attitude","Avoid overthinking"],seasonal:["Eliminate dampness in spring-summer","Warm tonification in autumn-winter","Avoid humid environments"]},moxibustion:{points:["Fenglong","Yinlingquan","Zhongwan"],timing:"Daily or every other day",duration:"20-25 minutes per point",frequency:"Persist in treatment",precautions:["Combine with exercise","Control diet","Keep environment dry"]}},damp_heat:{acupoints:{primaryPoints:[{name:"Quchi (LI11)",location:"Lateral end of the elbow crease when elbow is flexed",function:"Clear heat and detoxify, eliminate damp-heat",method:"Press until feeling soreness"},{name:"Yinlingquan (SP9)",location:"Depression below the medial condyle of the tibia",function:"Clear heat and drain dampness, strengthen spleen",method:"Focus massage"},{name:"Dazhui (GV14)",location:"Below the 7th cervical vertebra",function:"Clear heat and release exterior, regulate immunity",method:"Gentle pressure"}],supportingPoints:[{name:"Hegu (LI4)",location:"Between the 1st and 2nd metacarpals on the back of hand",function:"Clear heat and detoxify, regulate facial area",method:"Press for 2-3 minutes"}],massageTechnique:"Moderate force, focus on clearing heat",frequency:"1-2 times daily",duration:"10-15 minutes each session"},diet:{beneficial:["Mung beans","Bitter melon","Cucumber","Watermelon","Job's tears","Poria","Lotus seed heart"],avoid:["Spicy foods","Fried foods","Barbecue","Alcohol","Sweet greasy foods"],principles:["Clear heat and drain dampness","Light diet","Drink plenty of water","Reduce rich foods"],sampleMeals:["Mung bean soup","Stir-fried bitter melon with eggs","Job's tears porridge","Lotus seed heart tea"]},lifestyle:{exercise:["Swimming","Yoga","Tai Chi","Avoid vigorous exercise"],sleep:["Keep cool sleep environment","Regular schedule"],emotional:["Maintain peaceful mind","Avoid irritable emotions"],seasonal:["Focus on clearing heat in summer","Avoid sun exposure","Keep environment ventilated"]},moxibustion:{points:["Yinlingquan"],timing:"Use cautiously",duration:"Shorter duration",frequency:"Focus on massage",precautions:["Avoid overheating","Focus on clearing heat","Can use scraping instead"]}},blood_stasis:{acupoints:{primaryPoints:[{name:"Xuehai (SP10)",location:"2 cun above the medial superior border of the patella",function:"Invigorate blood and resolve stasis, regulate menstruation and relieve pain",method:"Massage until local warmth"},{name:"Sanyinjiao (SP6)",location:"3 cun above the medial malleolus, behind the medial border of the tibia",function:"Invigorate blood and regulate menstruation, resolve stasis and relieve pain",method:"Focus pressure"},{name:"Geshu (BL17)",location:"1.5 cun lateral to the 7th thoracic vertebra",function:"Invigorate blood and resolve stasis, expand chest and regulate qi",method:"Pressure combined with moxibustion"}],supportingPoints:[{name:"Taichong (LR3)",location:"Between the 1st and 2nd metatarsals on foot dorsum",function:"Soothe liver and regulate qi, invigorate blood and resolve stasis",method:"Press until feeling soreness"}],massageTechnique:"Moderate force, focus on invigorating blood",frequency:"2 times daily",duration:"15-20 minutes each session"},diet:{beneficial:["Hawthorn","Safflower","Angelica","Chuanxiong","Brown sugar","Black fungus","Onions"],avoid:["Cold raw foods","Greasy foods","Excessively salty foods"],principles:["Invigorate blood and resolve stasis","Warm and unblock meridians","Appropriate warm tonification"],sampleMeals:["Hawthorn tea","Angelica stewed chicken","Stir-fried black fungus","Brown sugar ginger tea"]},lifestyle:{exercise:["Jogging","Tai Chi","Yoga","Moderate aerobic exercise"],sleep:["Maintain regular schedule","Avoid staying up late"],emotional:["Keep happy mood","Avoid emotional stagnation"],seasonal:["Soothe liver in spring","Pay attention to keeping warm","Avoid catching cold"]},moxibustion:{points:["Xuehai","Sanyinjiao","Geshu"],timing:"Daily or every other day",duration:"20-25 minutes per point",frequency:"Focus treatment before and after menstruation",precautions:["Use cautiously during menstruation","Pay attention to temperature","Combine with exercise"]}},qi_stagnation:{acupoints:{primaryPoints:[{name:"Taichong (LR3)",location:"Between the 1st and 2nd metatarsals on foot dorsum",function:"Soothe liver and relieve depression, regulate qi movement",method:"Press until obvious soreness"},{name:"Qimen (LR14)",location:"6th intercostal space, directly below the nipple",function:"Soothe liver and regulate qi, expand chest and relieve depression",method:"Gentle massage"},{name:"Shenmen (HE7)",location:"Ulnar end of the wrist crease",function:"Calm the mind and spirit, regulate emotions",method:"Focus pressure before sleep"}],supportingPoints:[{name:"Yintang (EX-HN3)",location:"Midpoint between the eyebrows",function:"Calm the mind and spirit, open orifices and awaken brain",method:"Gentle pressure"}],massageTechnique:"Gentle and soothing, focus on unblocking",frequency:"2-3 times daily",duration:"10-15 minutes each session"},diet:{beneficial:["Rose flowers","Lemon","Orange","Buddha's hand","Citron","Mint","Jasmine flowers"],avoid:["Excessively greasy food","Hard-to-digest foods","Excessive caffeine"],principles:["Soothe liver and regulate qi","Light diet","Moderate aromatic foods"],sampleMeals:["Rose flower tea","Lemon honey water","Mint tea","Light vegetables"]},lifestyle:{exercise:["Yoga","Tai Chi","Walking","Deep breathing exercises"],sleep:["Regular schedule","Relax before sleep","Create quiet environment"],emotional:["Learn to release stress","Cultivate hobbies","Appropriate socializing"],seasonal:["Focus on soothing liver in spring","Keep happy mood","Avoid emotional fluctuations"]},moxibustion:{points:["Taichong","Shenmen"],timing:"When emotions are poor",duration:"15-20 minutes per point",frequency:"Treatment as needed",precautions:["Gentle moxibustion","Combine with emotional regulation","Avoid excessive stimulation"]}},special_diathesis:{acupoints:{primaryPoints:[{name:"Fengchi (GB20)",location:"Depression between the sternocleidomastoid and trapezius muscles below the occiput",function:"Dispel wind and release exterior, enhance resistance",method:"Gentle pressure"},{name:"Zusanli (ST36)",location:"3 cun below the knee, 1 finger-width lateral to the tibia",function:"Regulate spleen and stomach, strengthen constitution",method:"Gentle massage"}],supportingPoints:[{name:"Yingxiang (LI20)",location:"Beside the midpoint of the lateral border of the nostril",function:"Unblock nasal orifices, prevent allergies",method:"Gentle massage"}],massageTechnique:"Gentle massage, avoid excessive stimulation",frequency:"1 time daily",duration:"10 minutes each session"},diet:{beneficial:["Probiotic foods","Fresh vegetables and fruits","Quality protein","Anti-allergic foods"],avoid:["Known allergens","Foods with many additives","Irritating foods"],principles:["Avoid allergens","Enhance immunity","Balanced nutrition"],sampleMeals:["Yogurt","Fresh fruits","Light vegetables","White meat"]},lifestyle:{exercise:["Moderate exercise","Avoid allergic environments","Strengthen constitution"],sleep:["Maintain adequate sleep","Avoid allergens"],emotional:["Maintain positive attitude","Learn to cope with allergies"],seasonal:["Adjust according to seasons","Prevent allergic episodes"]},moxibustion:{points:["Zusanli"],timing:"Use cautiously",duration:"Shorter duration",frequency:"Individualized treatment",precautions:["Avoid allergic reactions","Individualized plan","Under medical guidance"]}}}};var S=i(2475),_=i(9048),T=i(1082);let q={zh:{balanced:{name:"平和质",description:"体质平和，身心健康，是最理想的体质状态。",characteristics:["精力充沛，不易疲劳","睡眠良好，情绪稳定","消化功能正常","对环境适应能力强"],commonSymptoms:["很少生病","恢复能力强","抵抗力好"],menstrualFeatures:["月经周期规律（28-30天）","经量适中","颜色正常红色","痛经轻微或无痛经"]},qi_deficiency:{name:"气虚质",description:"元气不足，以疲乏、气短、自汗等气虚表现为主要特征。",characteristics:["容易疲劳，精神不振","说话声音低，不爱说话","容易出汗，活动后更明显","抵抗力差，容易感冒"],commonSymptoms:["气短懒言","容易疲劳","自汗","食欲不振"],menstrualFeatures:["月经量少，颜色淡","周期可能延后","经期疲劳加重","可能有轻度痛经"]},yang_deficiency:{name:"阳虚质",description:"阳气不足，以畏寒怕冷、手足不温等虚寒表现为主要特征。",characteristics:["畏寒怕冷，手足不温","喜热饮食，不耐寒邪","精神不振，睡眠偏多","大便溏薄，小便清长"],commonSymptoms:["畏寒肢冷","精神萎靡","腰膝酸软","性功能减退"],menstrualFeatures:["月经量少，颜色淡","周期延后","经期腹痛喜温喜按","经前或经期腰酸明显"]},yin_deficiency:{name:"阴虚质",description:"阴液亏少，以口燥咽干、手足心热等虚热表现为主要特征。",characteristics:["手足心热，口咽干燥","喜冷饮，不耐暑热","大便干燥，小便短赤","睡眠差，性情急躁"],commonSymptoms:["五心烦热","口干咽燥","盗汗","失眠多梦"],menstrualFeatures:["月经量少或正常","周期可能提前","经色鲜红","经前烦躁，失眠"]},phlegm_dampness:{name:"痰湿质",description:"痰湿凝聚，以形体肥胖、腹部肥满、口黏苔腻等痰湿表现为主要特征。",characteristics:["形体肥胖，腹部肥满松软","面部皮肤油脂较多","容易困倦，身重不爽","口黏腻或甜，喜食肥甘甜腻"],commonSymptoms:["身重困倦","胸闷痰多","口黏腻","大便正常或不实"],menstrualFeatures:["月经量多或正常","经色淡红","质地粘稠","经前胸闷、水肿"]},damp_heat:{name:"湿热质",description:"湿热内蕴，以面垢油腻、口苦、苔黄腻等湿热表现为主要特征。",characteristics:["面垢油腻，易生痤疮","口苦口干，身重困倦","大便黏滞不畅或燥结","小便短黄，男易阴囊潮湿"],commonSymptoms:["面部油腻","口苦口干","身重困倦","大便黏滞"],menstrualFeatures:["月经量多，颜色深红","周期可能提前","经前烦躁易怒","痛经较重，喜冷恶热"]},blood_stasis:{name:"血瘀质",description:"血行不畅，以肤色晦黯、舌质紫黯等血瘀表现为主要特征。",characteristics:["肤色晦黯，色素沉着","容易出现瘀斑","口唇黯淡，舌下络脉紫黯","性情急躁，健忘"],commonSymptoms:["肤色晦黯","易生色斑","疼痛如针刺","健忘"],menstrualFeatures:["月经有血块","经色暗红或紫黑","痛经明显，拒按","经前乳房胀痛"]},qi_stagnation:{name:"气郁质",description:"气机郁滞，以神情抑郁、忧虑脆弱等气郁表现为主要特征。",characteristics:["神情抑郁，情感脆弱","烦闷不乐，容易紧张","多愁善感，忧虑不安","对精神刺激适应能力较差"],commonSymptoms:["情绪抑郁","胸胁胀满","善太息","咽中如有异物"],menstrualFeatures:["月经不规律","经前情绪波动大","乳房胀痛明显","痛经程度与情绪相关"]},special_diathesis:{name:"特禀质",description:"先天失常，以生理缺陷、过敏反应等为主要特征。",characteristics:["先天禀赋不足","容易过敏","适应能力差","遗传性疾病家族史"],commonSymptoms:["过敏性疾病","遗传性疾病","胎传性疾病"],menstrualFeatures:["月经异常多样","可能伴随过敏症状","对环境变化敏感"]}},en:{balanced:{name:"Balanced Constitution",description:"A harmonious constitution with balanced body and mind, representing the ideal health state.",characteristics:["Energetic and not easily fatigued","Good sleep and stable emotions","Normal digestive function","Strong adaptability to environment"],commonSymptoms:["Rarely gets sick","Strong recovery ability","Good resistance"],menstrualFeatures:["Regular menstrual cycle (28-30 days)","Moderate flow","Normal red color","Mild or no menstrual pain"]},qi_deficiency:{name:"Qi Deficiency Constitution",description:"Insufficient vital energy, characterized by fatigue, shortness of breath, and spontaneous sweating.",characteristics:["Easily fatigued and low spirits","Low voice, reluctant to speak","Prone to sweating, especially after activity","Poor resistance, easily catches cold"],commonSymptoms:["Shortness of breath and reluctance to speak","Easy fatigue","Spontaneous sweating","Poor appetite"],menstrualFeatures:["Scanty menstruation with pale color","Cycle may be delayed","Increased fatigue during menstruation","May have mild menstrual pain"]},yang_deficiency:{name:"Yang Deficiency Constitution",description:"Insufficient yang qi, characterized by aversion to cold, cold limbs, and other cold manifestations.",characteristics:["Aversion to cold, cold hands and feet","Prefers warm food and drinks, intolerant to cold","Low spirits, tends to sleep more","Loose stools, clear and long urine"],commonSymptoms:["Aversion to cold and cold limbs","Mental fatigue","Sore and weak lower back and knees","Decreased sexual function"],menstrualFeatures:["Scanty menstruation with pale color","Delayed cycle","Abdominal pain during menstruation, relieved by warmth and pressure","Obvious lower back pain before or during menstruation"]},yin_deficiency:{name:"Yin Deficiency Constitution",description:"Insufficient yin fluid, characterized by dry mouth and throat, hot palms and soles.",characteristics:["Hot palms and soles, dry mouth and throat","Prefers cold drinks, intolerant to heat","Dry stools, short and yellow urine","Poor sleep, irritable temperament"],commonSymptoms:["Five-center heat (palms, soles, chest)","Dry mouth and throat","Night sweats","Insomnia and vivid dreams"],menstrualFeatures:["Scanty or normal menstruation","Cycle may be advanced","Bright red menstrual color","Irritability and insomnia before menstruation"]},phlegm_dampness:{name:"Phlegm-Dampness Constitution",description:"Accumulation of phlegm and dampness, characterized by obesity, abdominal fullness, and sticky mouth.",characteristics:["Obese body with soft and full abdomen","Oily facial skin","Easily drowsy, heavy body feeling","Sticky or sweet mouth, prefers fatty and sweet foods"],commonSymptoms:["Heavy body and drowsiness","Chest tightness and phlegm","Sticky mouth","Normal or loose stools"],menstrualFeatures:["Heavy or normal menstruation","Light red color","Thick consistency","Chest tightness and edema before menstruation"]},damp_heat:{name:"Damp-Heat Constitution",description:"Internal accumulation of damp-heat, characterized by oily face, bitter mouth, and yellow greasy tongue coating.",characteristics:["Oily face, prone to acne","Bitter and dry mouth, heavy body feeling","Sticky or dry stools","Short and yellow urine, men prone to scrotal dampness"],commonSymptoms:["Oily face","Bitter and dry mouth","Heavy body and drowsiness","Sticky stools"],menstrualFeatures:["Heavy menstruation with dark red color","Cycle may be advanced","Irritability before menstruation","Severe menstrual pain, prefers cold and dislikes heat"]},blood_stasis:{name:"Blood Stasis Constitution",description:"Poor blood circulation, characterized by dull complexion and purple tongue.",characteristics:["Dull complexion with pigmentation","Prone to bruising","Dark lips, purple sublingual vessels","Irritable temperament, forgetful"],commonSymptoms:["Dull complexion","Prone to dark spots","Needle-like pain","Forgetfulness"],menstrualFeatures:["Menstruation with blood clots","Dark red or purple-black color","Obvious menstrual pain, refuses pressure","Breast distension before menstruation"]},qi_stagnation:{name:"Qi Stagnation Constitution",description:"Stagnant qi movement, characterized by depression, anxiety, and emotional fragility.",characteristics:["Depressed mood, emotionally fragile","Restless and easily tense","Sentimental and anxious","Poor adaptability to mental stimulation"],commonSymptoms:["Emotional depression","Chest and hypochondriac distension","Frequent sighing","Feeling of foreign body in throat"],menstrualFeatures:["Irregular menstruation","Large emotional fluctuations before menstruation","Obvious breast distension","Menstrual pain related to emotions"]},special_diathesis:{name:"Special Constitution",description:"Congenital abnormalities, characterized by physiological defects and allergic reactions.",characteristics:["Congenital insufficiency","Prone to allergies","Poor adaptability","Family history of hereditary diseases"],commonSymptoms:["Allergic diseases","Hereditary diseases","Congenital diseases"],menstrualFeatures:["Various menstrual abnormalities","May be accompanied by allergic symptoms","Sensitive to environmental changes"]}}},E={zh:{balanced:[{name:"三阴交",description:"调理气血，缓解轻微经期不适"},{name:"血海",description:"活血调经，维持经期平衡"}],qi_deficiency:[{name:"气海",description:"补益元气，缓解疲劳型痛经"},{name:"足三里",description:"健脾益气，改善体质虚弱"},{name:"关元",description:"温补肾阳，增强体质"}],yang_deficiency:[{name:"关元",description:"温阳散寒，缓解冷痛"},{name:"神阙",description:"温中散寒，改善宫寒症状"},{name:"肾俞",description:"补肾壮阳，温暖下焦"}],yin_deficiency:[{name:"太溪",description:"滋阴补肾，缓解燥热症状"},{name:"三阴交",description:"滋阴养血，调理月经"},{name:"照海",description:"滋肾阴，清虚热"}],phlegm_dampness:[{name:"丰隆",description:"化痰除湿，缓解腹胀"},{name:"阴陵泉",description:"健脾利湿，消除水肿"},{name:"中脘",description:"健脾和胃，化湿消胀"}],damp_heat:[{name:"阴陵泉",description:"清热利湿，缓解湿热症状"},{name:"曲池",description:"清热解毒，凉血止痛"},{name:"太冲",description:"疏肝清热，调理情绪"}],blood_stasis:[{name:"血海",description:"活血化瘀，缓解刺痛"},{name:"膈俞",description:"活血化瘀，通络止痛"},{name:"次髎",description:"活血通络，缓解盆腔瘀血"}],qi_stagnation:[{name:"太冲",description:"疏肝理气，缓解绞痛"},{name:"期门",description:"疏肝解郁，调理情绪"},{name:"行间",description:"疏肝泄热，缓解烦躁"}],special_diathesis:[{name:"百会",description:"调节神经，缓解过敏症状"},{name:"风池",description:"疏风解表，调节免疫"},{name:"合谷",description:"调气止痛，增强抵抗力"}]},en:{balanced:[{name:"Sanyinjiao (SP6)",description:"Regulates qi and blood, relieves mild menstrual discomfort"},{name:"Xuehai (SP10)",description:"Activates blood circulation, maintains menstrual balance"}],qi_deficiency:[{name:"Qihai (CV6)",description:"Tonifies primordial qi, relieves fatigue-type dysmenorrhea"},{name:"Zusanli (ST36)",description:"Strengthens spleen and qi, improves weak constitution"},{name:"Guanyuan (CV4)",description:"Warms and tonifies kidney yang, strengthens constitution"}],yang_deficiency:[{name:"Guanyuan (CV4)",description:"Warms yang and disperses cold, relieves cold pain"},{name:"Shenque (CV8)",description:"Warms the center and disperses cold, improves uterine cold"},{name:"Shenshu (BL23)",description:"Tonifies kidney and strengthens yang, warms lower jiao"}],yin_deficiency:[{name:"Taixi (KI3)",description:"Nourishes yin and tonifies kidney, relieves heat symptoms"},{name:"Sanyinjiao (SP6)",description:"Nourishes yin and blood, regulates menstruation"},{name:"Zhaohai (KI6)",description:"Nourishes kidney yin, clears deficiency heat"}],phlegm_dampness:[{name:"Fenglong (ST40)",description:"Transforms phlegm and eliminates dampness, relieves bloating"},{name:"Yinlingquan (SP9)",description:"Strengthens spleen and drains dampness, reduces edema"},{name:"Zhongwan (CV12)",description:"Strengthens spleen and stomach, transforms dampness"}],damp_heat:[{name:"Yinlingquan (SP9)",description:"Clears heat and drains dampness, relieves damp-heat symptoms"},{name:"Quchi (LI11)",description:"Clears heat and detoxifies, cools blood and stops pain"},{name:"Taichong (LR3)",description:"Soothes liver and clears heat, regulates emotions"}],blood_stasis:[{name:"Xuehai (SP10)",description:"Activates blood and resolves stasis, relieves stabbing pain"},{name:"Geshu (BL17)",description:"Activates blood and resolves stasis, unblocks meridians"},{name:"Ciliao (BL32)",description:"Activates blood and unblocks meridians, relieves pelvic stasis"}],qi_stagnation:[{name:"Taichong (LR3)",description:"Soothes liver and regulates qi, relieves cramping pain"},{name:"Qimen (LR14)",description:"Soothes liver and relieves depression, regulates emotions"},{name:"Xingjian (LR2)",description:"Soothes liver and drains heat, relieves irritability"}],special_diathesis:[{name:"Baihui (GV20)",description:"Regulates nervous system, relieves allergic symptoms"},{name:"Fengchi (GB20)",description:"Expels wind and releases exterior, regulates immunity"},{name:"Hegu (LI4)",description:"Regulates qi and stops pain, strengthens resistance"}]}},A={zh:{balanced:["保持规律的作息时间","适量运动，如散步、瑜伽","经期注意保暖，避免受凉","保持心情愉快，避免过度紧张"],qi_deficiency:["充足睡眠，避免熬夜","选择温和的运动，避免剧烈活动","经期多休息，减少体力消耗","注意营养补充，多吃补气食物"],yang_deficiency:["注意保暖，特别是腹部和腰部","避免生冷食物，多喝温开水","适当进行温和的有氧运动","经期可用热水袋敷腹部"],yin_deficiency:["避免熬夜，保证充足睡眠","减少辛辣刺激性食物","多吃滋阴润燥的食物","保持情绪稳定，避免急躁"],phlegm_dampness:["控制体重，避免过度肥胖","减少甜腻食物的摄入","增加有氧运动，促进代谢","保持环境干燥，避免潮湿"],damp_heat:["饮食清淡，避免油腻食物","多吃清热利湿的食物","保持心情舒畅，避免急躁","注意个人卫生，保持清洁"],blood_stasis:["适当运动，促进血液循环","避免久坐不动","经期可进行轻柔按摩","保持情绪稳定，避免生气"],qi_stagnation:["学会情绪管理，保持心情舒畅","适当进行舒缓运动，如瑜伽","避免压力过大，学会放松","可以听音乐、冥想来缓解压力"],special_diathesis:["避免接触过敏原","增强体质，提高免疫力","注意环境卫生，减少刺激","必要时寻求专业医疗建议"]},en:{balanced:["Maintain regular sleep schedule","Moderate exercise like walking and yoga","Keep warm during menstruation, avoid cold","Stay positive and avoid excessive stress"],qi_deficiency:["Get adequate sleep, avoid staying up late","Choose gentle exercises, avoid intense activities","Rest more during menstruation, reduce physical exertion","Focus on nutrition, eat qi-tonifying foods"],yang_deficiency:["Keep warm, especially abdomen and lower back","Avoid cold foods, drink warm water","Engage in gentle aerobic exercises","Use heating pad on abdomen during menstruation"],yin_deficiency:["Avoid staying up late, ensure adequate sleep","Reduce spicy and irritating foods","Eat yin-nourishing and moistening foods","Maintain emotional stability, avoid irritability"],phlegm_dampness:["Control weight, avoid excessive obesity","Reduce intake of sweet and greasy foods","Increase aerobic exercise to boost metabolism","Keep environment dry, avoid humidity"],damp_heat:["Eat light diet, avoid greasy foods","Eat heat-clearing and dampness-draining foods","Stay calm and avoid irritability","Maintain personal hygiene and cleanliness"],blood_stasis:["Exercise appropriately to promote blood circulation","Avoid prolonged sitting","Gentle massage during menstruation","Maintain emotional stability, avoid anger"],qi_stagnation:["Learn emotional management, stay cheerful","Engage in soothing exercises like yoga","Avoid excessive stress, learn to relax","Listen to music or meditate to relieve stress"],special_diathesis:["Avoid contact with allergens","Strengthen constitution and boost immunity","Pay attention to environmental hygiene, reduce irritation","Seek professional medical advice when necessary"]}},P=(e,t,i)=>{var a;let s={zh:[{title:"痛经的自然与物理疗法综合指南：15种科学验证的缓解方法",description:"详细介绍热敷、按摩、瑜伽等自然疗法，以及穴位按摩的具体操作方法，帮助您自然缓解痛经。",category:"自然疗法",link:"/zh/articles/natural-physical-therapy-comprehensive-guide"},{title:"痛经药物治疗专业指南：NSAIDs安全用药与剂量计算",description:"专业的痛经药物治疗指南，包括布洛芬、萘普生等NSAIDs的安全用药方法和剂量计算。",category:"药物治疗",link:"/zh/articles/nsaid-menstrual-pain-professional-guide"}],en:[{title:"Comprehensive Guide to Natural and Physical Therapies for Menstrual Pain",description:"Detailed introduction to natural therapies such as heat therapy, massage, yoga, and specific acupoint massage techniques to naturally relieve menstrual pain.",category:"Natural Therapy",link:"/en/articles/natural-physical-therapy-comprehensive-guide"},{title:"Professional Guide to Menstrual Pain Medication: Safe Use of NSAIDs and Dosage Calculation",description:"Professional guide to menstrual pain medication, including safe use and dosage calculation of NSAIDs like ibuprofen and naproxen.",category:"Medical Treatment",link:"/en/articles/nsaid-menstrual-pain-professional-guide"}]},n=[...s[i]||s.zh],o=null===(a=({zh:{qi_deficiency:[{title:"气虚体质痛经调理：补气养血的中医方案",description:"针对气虚体质的痛经特点，提供补气养血的中医调理方案，包括食疗、穴位按摩等。",category:"体质调理",link:"/zh/interactive-tools/constitution-test"}],yang_deficiency:[{title:"阳虚体质痛经调理：温阳散寒的调理方法",description:"专门针对阳虚体质的痛经调理，重点介绍温阳散寒的方法和注意事项。",category:"体质调理",link:"/zh/interactive-tools/constitution-test"}],blood_stasis:[{title:"血瘀体质痛经调理：活血化瘀的有效方法",description:"针对血瘀体质的痛经特点，提供活血化瘀的调理方案和生活指导。",category:"体质调理",link:"/zh/interactive-tools/constitution-test"}]},en:{qi_deficiency:[{title:"Qi Deficiency Constitution Menstrual Pain Management: TCM Solutions for Qi and Blood Tonification",description:"Targeted TCM solutions for qi deficiency constitution menstrual pain, including dietary therapy and acupoint massage.",category:"Constitution Care",link:"/en/interactive-tools/constitution-test"}],yang_deficiency:[{title:"Yang Deficiency Constitution Menstrual Pain Management: Warming Yang and Dispersing Cold",description:"Specialized care for yang deficiency constitution menstrual pain, focusing on warming yang and dispersing cold methods.",category:"Constitution Care",link:"/en/interactive-tools/constitution-test"}],blood_stasis:[{title:"Blood Stasis Constitution Menstrual Pain Management: Effective Blood Circulation Methods",description:"Targeted solutions for blood stasis constitution menstrual pain, providing blood circulation and stasis resolution guidance.",category:"Constitution Care",link:"/en/interactive-tools/constitution-test"}]}})[i])||void 0===a?void 0:a[e];return o&&n.push(...o),n.slice(0,3)},M={zh:[{scenario:"与伴侣沟通",templates:[{title:"温和告知",content:"亲爱的，我今天经期疼痛比较严重，可能需要多休息一下。如果我看起来不太舒服，请不要担心，这是正常的生理反应。",tone:"intimate"},{title:"寻求理解",content:"我现在有些痛经，可能情绪会有些波动，不是因为你做错了什么。能给我一些时间和空间吗？",tone:"intimate"},{title:"请求帮助",content:"我现在肚子很痛，能帮我准备一杯热水吗？或者陪我安静地待一会儿就好。",tone:"intimate"}]},{scenario:"与朋友沟通",templates:[{title:"约会改期",content:"不好意思，我今天身体不太舒服（经期疼痛），可能没办法保持最佳状态。我们能改到下次吗？",tone:"casual"},{title:"聚会参与",content:"我会参加聚会，但可能需要早点回家休息。如果我看起来有点疲惫，请理解一下～",tone:"casual"},{title:"寻求支持",content:"姐妹，我现在痛经痛得厉害，你有什么好的缓解方法吗？或者就是想找人聊聊。",tone:"casual"}]},{scenario:"与同事/领导沟通",templates:[{title:"请假申请",content:"您好，我今天身体不适，可能需要请假半天/一天。我会尽快处理紧急工作，其他事务明天补上。",tone:"formal"},{title:"工作调整",content:"不好意思，我今天身体有些不适，可能工作效率会受影响。如果有紧急事务，请优先安排。",tone:"formal"},{title:"会议参与",content:"我可能需要在会议中途短暂离开一下，不是对会议内容不感兴趣，而是身体原因。",tone:"formal"}]}],en:[{scenario:"Communicating with Partner",templates:[{title:"Gentle Notification",content:"Honey, I'm experiencing severe menstrual cramps today and might need some extra rest. If I seem uncomfortable, please don't worry - it's a normal physiological response.",tone:"intimate"},{title:"Seeking Understanding",content:"I'm having period pain right now and my emotions might be a bit up and down. It's not because you did anything wrong. Could you give me some time and space?",tone:"intimate"},{title:"Asking for Help",content:"I'm having really bad cramps right now. Could you help me get some hot water? Or just stay with me quietly for a while.",tone:"intimate"}]},{scenario:"Communicating with Friends",templates:[{title:"Rescheduling Dates",content:"Sorry, I'm not feeling well today (period pain) and might not be at my best. Could we reschedule for another time?",tone:"casual"},{title:"Party Participation",content:"I'll join the party, but I might need to head home early to rest. Please understand if I seem a bit tired~",tone:"casual"},{title:"Seeking Support",content:"Girl, I'm having terrible period cramps right now. Do you have any good relief methods? Or I just want someone to talk to.",tone:"casual"}]},{scenario:"Communicating with Colleagues/Boss",templates:[{title:"Leave Request",content:"Hello, I'm not feeling well today and may need to take half a day/full day off. I'll handle urgent work as soon as possible and catch up on other tasks tomorrow.",tone:"formal"},{title:"Work Adjustment",content:"Sorry, I'm feeling a bit unwell today and my work efficiency might be affected. Please prioritize urgent matters if any.",tone:"formal"},{title:"Meeting Participation",content:"I might need to step out briefly during the meeting. It's not because I'm not interested in the content, but due to health reasons.",tone:"formal"}]}]},B={zh:{balanced:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"小包纸巾",reason:"日常清洁需要",priority:"medium"}]},{category:"舒缓用品",items:[{name:"暖宝宝",reason:"温热缓解轻微不适",priority:"medium"},{name:"保温杯",reason:"随时补充温水",priority:"medium"},{name:"薄荷糖",reason:"提神醒脑，缓解疲劳",priority:"low"}]}],qi_deficiency:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"能量小零食",reason:"及时补充体力",priority:"high"}]},{category:"补气用品",items:[{name:"红枣茶包",reason:"补气养血，缓解疲劳",priority:"high"},{name:"暖宝宝",reason:"温暖身体，提升阳气",priority:"high"},{name:"小毯子",reason:"保暖休息，避免受凉",priority:"medium"}]},{category:"应急药品",items:[{name:"维生素B群",reason:"支持神经系统，缓解疲劳",priority:"medium"},{name:"葡萄糖片",reason:"快速补充能量",priority:"low"}]}],yang_deficiency:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"保温杯",reason:"随时饮用热水",priority:"high"}]},{category:"温阳用品",items:[{name:"暖宝宝",reason:"持续温暖，驱散寒气",priority:"high"},{name:"暖宫贴",reason:"专门温暖腹部",priority:"high"},{name:"生姜茶包",reason:"温中散寒，暖胃驱寒",priority:"high"}]},{category:"保暖用品",items:[{name:"薄外套",reason:"随时增添衣物保暖",priority:"medium"},{name:"暖手宝",reason:"温暖手部，促进循环",priority:"medium"},{name:"保暖袜",reason:"足部保暖，防止寒从脚起",priority:"low"}]}],yin_deficiency:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"保湿喷雾",reason:"缓解干燥，滋润肌肤",priority:"medium"}]},{category:"滋阴用品",items:[{name:"蜂蜜柠檬茶",reason:"滋阴润燥，缓解内热",priority:"high"},{name:"润喉糖",reason:"滋润咽喉，缓解干燥",priority:"medium"},{name:"保湿面膜",reason:"滋润肌肤，缓解干燥",priority:"low"}]},{category:"镇静用品",items:[{name:"薰衣草精油",reason:"舒缓情绪，帮助放松",priority:"medium"},{name:"眼罩",reason:"遮光休息，缓解疲劳",priority:"low"}]}],phlegm_dampness:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"干爽粉",reason:"保持身体干爽",priority:"medium"}]},{category:"化湿用品",items:[{name:"陈皮茶包",reason:"健脾化湿，消除胀气",priority:"high"},{name:"薄荷茶",reason:"清香化湿，提神醒脑",priority:"medium"},{name:"除湿贴",reason:"局部除湿，保持干爽",priority:"low"}]},{category:"消胀用品",items:[{name:"消化酶片",reason:"帮助消化，减少胀气",priority:"medium"},{name:"按摩球",reason:"促进循环，消除水肿",priority:"low"}]}],damp_heat:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"抗菌洗手液",reason:"清洁杀菌，预防感染",priority:"medium"}]},{category:"清热用品",items:[{name:"菊花茶包",reason:"清热解毒，降火消炎",priority:"high"},{name:"绿茶包",reason:"清热利湿，抗氧化",priority:"medium"},{name:"清凉贴",reason:"局部降温，缓解热感",priority:"low"}]},{category:"清洁用品",items:[{name:"私处清洁湿巾",reason:"专用清洁，预防炎症",priority:"medium"},{name:"漱口水",reason:"口腔清洁，去除异味",priority:"low"}]}],blood_stasis:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"止痛药",reason:"缓解刺痛，改善循环",priority:"high"}]},{category:"活血用品",items:[{name:"红花茶包",reason:"活血化瘀，缓解疼痛",priority:"high"},{name:"暖宝宝",reason:"温热促循环，缓解瘀滞",priority:"high"},{name:"按摩膏",reason:"局部按摩，促进血液循环",priority:"medium"}]},{category:"舒缓用品",items:[{name:"热敷袋",reason:"深度热敷，缓解深层疼痛",priority:"medium"},{name:"按摩球",reason:"穴位按摩，疏通经络",priority:"low"}]}],qi_stagnation:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"止痛药",reason:"缓解绞痛，舒缓情绪",priority:"high"}]},{category:"疏肝用品",items:[{name:"玫瑰花茶包",reason:"疏肝解郁，调节情绪",priority:"high"},{name:"柠檬精油",reason:"芳香疏肝，提升心情",priority:"medium"},{name:"暖宝宝",reason:"温暖腹部，缓解痉挛",priority:"high"}]},{category:"情绪调节",items:[{name:"舒缓音乐",reason:"放松心情，缓解压力",priority:"medium"},{name:"减压玩具",reason:"转移注意力，释放压力",priority:"low"}]}],special_diathesis:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"抗过敏药",reason:"预防过敏反应",priority:"high"}]},{category:"防护用品",items:[{name:"口罩",reason:"过滤空气，减少过敏原",priority:"high"},{name:"免洗洗手液",reason:"随时清洁，减少接触",priority:"medium"},{name:"防过敏贴",reason:"皮肤保护，预防接触性过敏",priority:"medium"}]},{category:"应急药品",items:[{name:"抗组胺药",reason:"快速缓解过敏症状",priority:"high"},{name:"肾上腺素笔",reason:"严重过敏时的救命药物",priority:"medium"},{name:"舒缓喷雾",reason:"缓解皮肤过敏不适",priority:"low"}]}]},en:{balanced:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Small tissue packs",reason:"Daily cleaning needs",priority:"medium"}]},{category:"Comfort Items",items:[{name:"Heat pads",reason:"Warm relief for mild discomfort",priority:"medium"},{name:"Thermos bottle",reason:"Stay hydrated with warm water",priority:"medium"},{name:"Mint candies",reason:"Refresh and relieve fatigue",priority:"low"}]}],qi_deficiency:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Energy snacks",reason:"Timely energy replenishment",priority:"high"}]},{category:"Qi-Tonifying Items",items:[{name:"Jujube tea bags",reason:"Tonify qi and blood, relieve fatigue",priority:"high"},{name:"Heat pads",reason:"Warm body, boost yang qi",priority:"high"},{name:"Small blanket",reason:"Keep warm and rest, avoid catching cold",priority:"medium"}]},{category:"Emergency Supplements",items:[{name:"Vitamin B complex",reason:"Support nervous system, relieve fatigue",priority:"medium"},{name:"Glucose tablets",reason:"Quick energy boost",priority:"low"}]}],yang_deficiency:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Thermos bottle",reason:"Drink hot water anytime",priority:"high"}]},{category:"Yang-Warming Items",items:[{name:"Heat pads",reason:"Continuous warmth, dispel cold",priority:"high"},{name:"Abdominal heat patches",reason:"Specifically warm abdomen",priority:"high"},{name:"Ginger tea bags",reason:"Warm center, dispel cold from stomach",priority:"high"}]},{category:"Warming Items",items:[{name:"Light jacket",reason:"Add layers for warmth anytime",priority:"medium"},{name:"Hand warmers",reason:"Warm hands, promote circulation",priority:"medium"},{name:"Warm socks",reason:"Keep feet warm, prevent cold from feet",priority:"low"}]}],yin_deficiency:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Moisturizing spray",reason:"Relieve dryness, moisturize skin",priority:"medium"}]},{category:"Yin-Nourishing Items",items:[{name:"Honey lemon tea",reason:"Nourish yin, moisten dryness, relieve internal heat",priority:"high"},{name:"Throat lozenges",reason:"Moisten throat, relieve dryness",priority:"medium"},{name:"Moisturizing face mask",reason:"Moisturize skin, relieve dryness",priority:"low"}]},{category:"Calming Items",items:[{name:"Lavender essential oil",reason:"Soothe emotions, help relaxation",priority:"medium"},{name:"Eye mask",reason:"Block light for rest, relieve fatigue",priority:"low"}]}],phlegm_dampness:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Drying powder",reason:"Keep body dry",priority:"medium"}]},{category:"Dampness-Resolving Items",items:[{name:"Tangerine peel tea bags",reason:"Strengthen spleen, resolve dampness, eliminate bloating",priority:"high"},{name:"Mint tea",reason:"Fragrant dampness resolution, refresh mind",priority:"medium"},{name:"Moisture-absorbing patches",reason:"Local moisture removal, stay dry",priority:"low"}]},{category:"Anti-Bloating Items",items:[{name:"Digestive enzyme tablets",reason:"Aid digestion, reduce bloating",priority:"medium"},{name:"Massage ball",reason:"Promote circulation, eliminate edema",priority:"low"}]}],damp_heat:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Antibacterial hand sanitizer",reason:"Clean and sterilize, prevent infection",priority:"medium"}]},{category:"Heat-Clearing Items",items:[{name:"Chrysanthemum tea bags",reason:"Clear heat, detoxify, reduce inflammation",priority:"high"},{name:"Green tea bags",reason:"Clear heat, drain dampness, antioxidant",priority:"medium"},{name:"Cooling patches",reason:"Local cooling, relieve heat sensation",priority:"low"}]},{category:"Cleansing Items",items:[{name:"Intimate cleansing wipes",reason:"Specialized cleaning, prevent inflammation",priority:"medium"},{name:"Mouthwash",reason:"Oral hygiene, remove odors",priority:"low"}]}],blood_stasis:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Pain relievers",reason:"Relieve stabbing pain, improve circulation",priority:"high"}]},{category:"Blood-Activating Items",items:[{name:"Safflower tea bags",reason:"Activate blood, resolve stasis, relieve pain",priority:"high"},{name:"Heat pads",reason:"Warm heat promotes circulation, relieves stasis",priority:"high"},{name:"Massage balm",reason:"Local massage, promote blood circulation",priority:"medium"}]},{category:"Soothing Items",items:[{name:"Hot compress bags",reason:"Deep heat therapy, relieve deep pain",priority:"medium"},{name:"Massage ball",reason:"Acupoint massage, unblock meridians",priority:"low"}]}],qi_stagnation:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Pain relievers",reason:"Relieve cramping pain, soothe emotions",priority:"high"}]},{category:"Liver-Soothing Items",items:[{name:"Rose tea bags",reason:"Soothe liver, relieve depression, regulate emotions",priority:"high"},{name:"Lemon essential oil",reason:"Aromatic liver soothing, uplift mood",priority:"medium"},{name:"Heat pads",reason:"Warm abdomen, relieve spasms",priority:"high"}]},{category:"Mood Regulation",items:[{name:"Soothing music",reason:"Relax mood, relieve stress",priority:"medium"},{name:"Stress relief toys",reason:"Divert attention, release stress",priority:"low"}]}],special_diathesis:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Anti-allergy medication",reason:"Prevent allergic reactions",priority:"high"}]},{category:"Protective Items",items:[{name:"Face masks",reason:"Filter air, reduce allergens",priority:"high"},{name:"Hand sanitizer",reason:"Clean anytime, reduce contact",priority:"medium"},{name:"Anti-allergy patches",reason:"Skin protection, prevent contact allergies",priority:"medium"}]},{category:"Emergency Medications",items:[{name:"Antihistamines",reason:"Quickly relieve allergy symptoms",priority:"high"},{name:"Epinephrine pen",reason:"Life-saving medication for severe allergies",priority:"medium"},{name:"Soothing spray",reason:"Relieve skin allergy discomfort",priority:"low"}]}]}},L={zh:{balanced:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["保持良好坐姿，每小时起身活动5分钟","办公桌常备温水杯，保持充足水分","适当调节空调温度，避免过冷","工作间隙可做简单的颈肩放松操"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["选择舒适的鞋子，减少足部疲劳","公共交通上可听轻音乐放松心情","避免长时间低头看手机","提前准备好保暖外套"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["选择舒适宽松的衣物","聚会时适量饮食，避免过饱","主动选择温热的饮品","必要时可提前告知亲近朋友"]}],qi_deficiency:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["工作强度适中，避免过度劳累","午休时间尽量小憩15-20分钟","常备红枣茶或桂圆茶补气","重要会议前可按压足三里穴提神"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["避免早高峰拥挤，可适当错峰出行","通勤包里备好小零食补充能量","选择有座位的交通方式","疲劳时可按压合谷穴缓解"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["聚会时间不宜过长，适时休息","选择营养丰富、易消化的食物","避免过于激烈的娱乐活动","可以坐着参与，减少站立时间"]}],yang_deficiency:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["办公室常备小毯子或暖宝宝","选择温热的午餐，避免生冷食物","座位尽量远离空调出风口","工作间隙可做暖身小运动"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["出门前检查保暖措施是否充足","随身携带保温杯装热水","避免在寒冷环境中久待","可在包里放暖手宝"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["选择温暖的聚会场所","避免冰镇饮料和生冷食物","可以带一件薄外套备用","聚会后注意保暖回家"]}],yin_deficiency:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["保持办公环境适度湿润","多喝温开水，少喝咖啡","避免长时间对着电脑屏幕","中午可以闭目养神片刻"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["避免在烈日下长时间等车","可以听舒缓音乐平静心情","通勤时间可做深呼吸练习","保持心情平和，避免急躁"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["避免过于嘈杂的聚会环境","选择清淡的食物，少吃辛辣","聚会时间适中，不宜过晚","保持情绪稳定，避免过度兴奋"]}],phlegm_dampness:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["保持办公环境通风干燥","午餐选择清淡少油的食物","工作间隙可做简单伸展运动","避免久坐，定时起身活动"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["选择透气性好的衣物","避免在潮湿环境中久留","可以做一些简单的活动筋骨","保持心情愉快，避免沉闷"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["避免过量饮食，特别是甜腻食物","选择有氧活动类型的聚会","多与朋友交流，保持活跃","聚会后可以散步消食"]}],damp_heat:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["保持办公环境清洁干爽","多喝绿茶或菊花茶清热","避免辛辣刺激的外卖食物","工作压力大时可做放松练习"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["选择吸汗透气的衣物","避免在闷热环境中久待","保持心情平静，避免烦躁","可以听清淡的音乐舒缓情绪"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["选择清爽的聚会环境","避免油腻、辛辣、酒精类食物","聚会时间不宜过长","保持情绪稳定，避免激动"]}],blood_stasis:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["避免长时间保持同一姿势","定时做颈肩和腰部活动","工作间隙可按摩手部穴位","保持心情舒畅，避免郁闷"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["在车上可做简单的踝关节运动","避免紧身衣物限制血液循环","可以听欢快的音乐调节心情","到站后可以快走几分钟"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["选择活跃一些的聚会活动","避免久坐不动的聚会形式","多与朋友交流，保持心情愉快","可以参与一些轻松的运动"]}],qi_stagnation:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["工作压力大时及时调节情绪","可以在办公室放一些绿植","午休时可以到户外走走","与同事保持良好的沟通"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["通勤时可以听喜欢的音乐","避免在拥挤时段出行","可以做深呼吸缓解压力","保持积极乐观的心态"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["多参与轻松愉快的聚会","与朋友分享心情，释放压力","选择开阔明亮的聚会场所","避免过于严肃的话题"]}],special_diathesis:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["注意办公环境的过敏原","保持办公用品的清洁","避免使用刺激性的清洁用品","工作压力大时注意调节"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["避免接触可能的过敏原","在空气质量差时戴口罩","选择相对清洁的交通工具","随身携带必要的应急药物"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["提前了解聚会环境和食物","避免接触已知的过敏原","必要时提前告知朋友注意事项","随身携带抗过敏药物"]}]},en:{balanced:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Maintain good posture, stand and move for 5 minutes every hour","Keep a water bottle at your desk for adequate hydration","Adjust air conditioning temperature appropriately, avoid overcooling","Do simple neck and shoulder relaxation exercises during breaks"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Choose comfortable shoes to reduce foot fatigue","Listen to light music on public transport to relax","Avoid looking down at phone for extended periods","Prepare warm outerwear in advance"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Choose comfortable and loose-fitting clothing","Eat moderately at gatherings, avoid overeating","Actively choose warm beverages","Inform close friends in advance if necessary"]}],qi_deficiency:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Moderate work intensity, avoid overexertion","Take 15-20 minute naps during lunch break","Keep jujube tea or longan tea for qi tonification","Press Zusanli acupoint before important meetings for energy"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Avoid rush hour crowds, consider off-peak travel","Pack small snacks in commute bag for energy","Choose transportation with seating when possible","Press Hegu acupoint when feeling fatigued"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Keep gathering time moderate, rest when needed","Choose nutritious, easily digestible foods","Avoid overly vigorous entertainment activities","Participate while seated, reduce standing time"]}],yang_deficiency:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Keep small blankets or heating pads in office","Choose warm lunch, avoid cold foods","Sit away from air conditioning vents","Do warming exercises during work breaks"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Check warmth measures before leaving home","Carry thermos with hot water","Avoid prolonged stays in cold environments","Keep hand warmers in bag"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Choose warm gathering venues","Avoid iced drinks and cold foods","Bring a light jacket as backup","Stay warm when heading home after gatherings"]}],yin_deficiency:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Maintain moderate humidity in office environment","Drink more warm water, less coffee","Avoid prolonged computer screen exposure","Close eyes and rest briefly at noon"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Avoid waiting in direct sunlight for extended periods","Listen to soothing music to calm mood","Practice deep breathing during commute","Stay calm and avoid irritability"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Avoid overly noisy gathering environments","Choose light foods, eat less spicy food","Keep gathering time moderate, not too late","Maintain emotional stability, avoid overexcitement"]}],phlegm_dampness:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Keep office environment ventilated and dry","Choose light, low-oil foods for lunch","Do simple stretching exercises during breaks","Avoid prolonged sitting, stand and move regularly"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Choose breathable clothing","Avoid prolonged stays in humid environments","Do simple joint movements","Stay cheerful, avoid feeling stuffy"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Avoid overeating, especially sweet and greasy foods","Choose aerobic activity-type gatherings","Communicate actively with friends, stay active","Take a walk after gatherings to aid digestion"]}],damp_heat:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Keep office environment clean and dry","Drink more green tea or chrysanthemum tea for heat clearing","Avoid spicy and irritating takeout foods","Do relaxation exercises when work stress is high"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Choose sweat-wicking, breathable clothing","Avoid prolonged stays in stuffy environments","Stay calm, avoid irritability","Listen to light music to soothe emotions"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Choose refreshing gathering environments","Avoid greasy, spicy, and alcoholic foods","Keep gathering time moderate","Maintain emotional stability, avoid excitement"]}],blood_stasis:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Avoid maintaining same posture for long periods","Do regular neck, shoulder, and waist movements","Massage hand acupoints during work breaks","Stay cheerful, avoid feeling depressed"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Do simple ankle exercises while on transport","Avoid tight clothing that restricts blood circulation","Listen to upbeat music to regulate mood","Walk briskly for a few minutes after getting off"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Choose more active gathering activities","Avoid sedentary gathering formats","Communicate more with friends, stay happy","Participate in some light exercises"]}],qi_stagnation:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Regulate emotions promptly when work stress is high","Place some green plants in the office","Go outdoors for a walk during lunch break","Maintain good communication with colleagues"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Listen to favorite music during commute","Avoid traveling during crowded times","Do deep breathing to relieve stress","Maintain positive and optimistic attitude"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Participate more in relaxed and pleasant gatherings","Share feelings with friends, release stress","Choose open and bright gathering venues","Avoid overly serious topics"]}],special_diathesis:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Pay attention to allergens in office environment","Keep office supplies clean","Avoid using irritating cleaning products","Pay attention to regulation when work stress is high"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Avoid contact with potential allergens","Wear mask when air quality is poor","Choose relatively clean transportation","Carry necessary emergency medications"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Learn about gathering environment and food in advance","Avoid contact with known allergens","Inform friends of precautions if necessary","Carry anti-allergy medications"]}]}};function F(e){var t,i,F,I,z,Z,R,G,W,H,O;let{locale:K}=e,{t:Y}=(0,n.A0)("constitutionTest"),[V,U]=(0,s.useState)({}),{currentSession:Q,currentQuestion:J,currentQuestionIndex:X,totalQuestions:$,progress:ee,isComplete:et,result:ei,isLoading:ea,error:es,startTest:en,answerQuestion:eo,goToPreviousQuestion:er,goToNextQuestion:el,completeTest:ec,resetTest:ed}=function(){let[e,t]=(0,s.useState)(null),[i,a]=(0,s.useState)(0),[n,o]=(0,s.useState)(null),[r,l]=(0,s.useState)(!1),[c,d]=(0,s.useState)(null),m=e?k[e.locale]||k.zh:[],u=m[i]||null,p=i>=m.length,h=m.length>0?Math.min(i/m.length*100,100):0,g=(0,s.useCallback)(e=>{t({id:"constitution_test_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),answers:[],startedAt:new Date().toISOString(),locale:e}),a(0),o(null),d(null)},[]),x=(0,s.useCallback)(i=>{e&&t(e=>{if(!e)return e;let t=e.answers.findIndex(e=>e.questionId===i.questionId),a=[...e.answers];return t>=0?a[t]=i:a.push(i),{...e,answers:a}})},[e]),y=(0,s.useCallback)(e=>{e>=0&&e<m.length&&a(e)},[m.length]),b=(0,s.useCallback)(()=>{i>0&&a(e=>e-1)},[i]),f=(0,s.useCallback)(()=>{i<m.length-1&&a(e=>e+1)},[i,m.length]),v=(0,s.useCallback)(e=>{let t={balanced:0,qi_deficiency:0,yang_deficiency:0,yin_deficiency:0,phlegm_dampness:0,damp_heat:0,blood_stasis:0,qi_stagnation:0,special_diathesis:0};return e.forEach(e=>{let i=m.find(t=>t.id===e.questionId);i&&e.selectedValues.forEach(e=>{let a=i.options.find(t=>t.value===e);a&&(t[a.constitutionType]+=a.weight*i.weight)})}),t},[m]),w=(0,s.useCallback)(()=>{if(!e||e.answers.length!==m.length)return d((null==e?void 0:e.locale)==="en"?"Test incomplete, please answer all questions":"测试未完成，请回答所有问题"),null;l(!0);try{var i;let a=v(e.answers),s=Object.entries(a).sort((e,t)=>{let[,i]=e,[,a]=t;return a-i}),n=s[0][0],r=s[1][1]>0?s[1][0]:void 0,l=Object.values(a).reduce((e,t)=>e+t,0),c=l>0?Math.round(a[n]/l*100):0,m={primaryType:n,secondaryType:r,scores:a,confidence:c,recommendations:(null===(i=C[e.locale])||void 0===i?void 0:i[n])||C.zh[n],sessionId:e.id,completedAt:new Date().toISOString()};return t(e=>e?{...e,completedAt:m.completedAt}:null),o(m),d(null),m}catch(t){return d((null==e?void 0:e.locale)==="en"?"Error calculating results, please try again":"计算结果时出错，请重试"),null}finally{l(!1)}},[e,m.length,v]),j=(0,s.useCallback)(()=>{t(null),a(0),o(null),d(null),l(!1)},[]);return{currentSession:e,currentQuestionIndex:i,currentQuestion:u,isComplete:p,progress:h,totalQuestions:m.length,startTest:g,answerQuestion:x,goToQuestion:y,goToPreviousQuestion:b,goToNextQuestion:f,completeTest:w,resetTest:j,result:n,isLoading:r,error:c}}(),{notifications:em,removeNotification:eu,addSuccessNotification:ep,addErrorNotification:eh}=(0,S.z)(),eg=(e,t)=>{let i=String(t);U(t=>({...t,[e]:i})),eo({questionId:e,selectedValues:[i],timestamp:new Date().toISOString()})},ex=(e,t)=>{let i;let a=Array.isArray(V[e])?V[e]:V[e]?[V[e]]:[],s="none"===t;if(a.includes("none"),s)i=a.includes("none")?[]:["none"];else{let e=a.filter(e=>"none"!==e);i=e.includes(t)?e.filter(e=>e!==t):[...e,t]}U(t=>({...t,[e]:i})),eo({questionId:e,selectedValues:i,timestamp:new Date().toISOString()})},ey=()=>J?V[J.id]:void 0,eb=e=>e.some(e=>"menstrual_pain_severity"===e.questionId&&e.selectedValues.some(e=>"no_pain"!==e));if(!Q)return(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)(_.Z,{notifications:em,onRemove:eu}),(0,a.jsxs)("div",{className:"text-center mb-8 animate-fade-in",children:[(0,a.jsx)("div",{className:"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center shadow-lg",children:(0,a.jsx)(o.Z,{className:"w-12 h-12 text-purple-600"})}),(0,a.jsx)("h1",{className:"text-4xl font-bold bg-gradient-to-r from-purple-600 to-purple-800 bg-clip-text text-transparent mb-4",children:Y("title")}),(0,a.jsx)("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed",children:Y("subtitle")})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-purple-100",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(r.Z,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:Y("features.quick.title")}),(0,a.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:Y("features.quick.description")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-purple-100",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-pink-100 to-pink-200 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(l.Z,{className:"w-6 h-6 text-pink-600"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:Y("features.professional.title")}),(0,a.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:Y("features.professional.description")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-purple-100",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(c.Z,{className:"w-6 h-6 text-green-600"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:Y("features.personalized.title")}),(0,a.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:Y("features.personalized.description")})]}),(0,a.jsxs)("div",{className:"text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-purple-100",children:[(0,a.jsx)("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(d.Z,{className:"w-6 h-6 text-purple-600"})}),(0,a.jsx)("h3",{className:"font-semibold text-gray-800 mb-2",children:Y("features.practical.title")}),(0,a.jsx)("p",{className:"text-sm text-gray-600 leading-relaxed",children:Y("features.practical.description")})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 border-l-4 border-purple-500 p-6 mb-8 rounded-r-lg shadow-sm",children:[(0,a.jsxs)("h3",{className:"font-semibold text-purple-800 mb-3 flex items-center",children:[(0,a.jsx)(m.Z,{className:"w-5 h-5 mr-2"}),Y("instructions.title")]}),(0,a.jsxs)("ul",{className:"text-purple-700 space-y-2",children:[(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"}),Y("instructions.item1")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"}),Y("instructions.item2")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"}),Y("instructions.item3")]}),(0,a.jsxs)("li",{className:"flex items-start",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"}),Y("instructions.item4")]})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("button",{onClick:()=>{en(K),U({})},className:"inline-flex items-center justify-center bg-gradient-to-r from-purple-600 to-purple-700 text-white text-lg px-10 py-4 rounded-xl font-semibold hover:from-purple-700 hover:to-purple-800 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl shadow-lg",children:[(0,a.jsx)(u.Z,{className:"w-6 h-6 mr-3 flex-shrink-0"}),(0,a.jsx)("span",{children:Y("navigation.startTest")})]})})]});if(ei){let e=(null===(I=q[K])||void 0===I?void 0:I[ei.primaryType])||q.zh[ei.primaryType];return(0,a.jsxs)("div",{className:"max-w-6xl mx-auto",children:[(0,a.jsx)(_.Z,{notifications:em,onRemove:eu}),(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center",children:(0,a.jsx)(p.Z,{className:"w-10 h-10 text-green-600"})}),(0,a.jsx)("h1",{className:"text-3xl font-bold text-neutral-800 mb-2",children:Y("result.title")}),(0,a.jsx)("p",{className:"text-lg text-neutral-600",children:Y("result.subtitle")})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-blue-50 p-8 rounded-xl mb-8",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-green-700 mb-2",children:e.name}),(0,a.jsx)("p",{className:"text-lg text-neutral-700 mb-4",children:e.description}),(0,a.jsxs)("div",{className:"inline-flex items-center bg-white px-4 py-2 rounded-full",children:[(0,a.jsx)("span",{className:"text-sm text-neutral-600 mr-2",children:Y("result.match")}),(0,a.jsxs)("span",{className:"font-semibold text-green-600",children:[ei.confidence,"%"]})]})]}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold text-neutral-800 mb-3 flex items-center",children:[(0,a.jsx)(o.Z,{className:"w-5 h-5 mr-2 text-blue-600"}),Y("result.constitutionFeatures")]}),(0,a.jsx)("ul",{className:"space-y-1",children:e.characteristics.map((e,t)=>(0,a.jsxs)("li",{className:"text-sm text-neutral-700",children:["• ",e]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold text-neutral-800 mb-3 flex items-center",children:[(0,a.jsx)(m.Z,{className:"w-5 h-5 mr-2 text-orange-600"}),Y("result.commonSymptoms")]}),(0,a.jsx)("ul",{className:"space-y-1",children:e.commonSymptoms.map((e,t)=>(0,a.jsxs)("li",{className:"text-sm text-neutral-700",children:["• ",e]},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("h3",{className:"font-semibold text-neutral-800 mb-3 flex items-center",children:[(0,a.jsx)(l.Z,{className:"w-5 h-5 mr-2 text-red-600"}),Y("result.menstrualFeatures")]}),(0,a.jsx)("ul",{className:"space-y-1",children:e.menstrualFeatures.map((e,t)=>(0,a.jsxs)("li",{className:"text-sm text-neutral-700",children:["• ",e]},t))})]})]})]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 mb-8",children:[(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-neutral-800 mb-4 flex items-center",children:[(0,a.jsx)(h.Z,{className:"w-6 h-6 mr-2 text-green-600"}),Y("recommendations.acupoints.title")]}),(0,a.jsxs)("div",{className:"mb-6",children:[(0,a.jsx)("h4",{className:"font-medium text-neutral-700 mb-3",children:Y("recommendations.acupoints.primaryAcupoints")}),(0,a.jsx)("div",{className:"space-y-3",children:ei.recommendations.acupoints.primaryPoints.map((e,t)=>(0,a.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg",children:[(0,a.jsx)("h5",{className:"font-medium text-green-800",children:e.name}),(0,a.jsxs)("p",{className:"text-sm text-green-700 mb-1",children:[Y("recommendations.acupoints.location"),e.location]}),(0,a.jsxs)("p",{className:"text-sm text-green-700 mb-1",children:[Y("recommendations.acupoints.function"),e.function]}),(0,a.jsxs)("p",{className:"text-sm text-green-600",children:[Y("recommendations.acupoints.method"),e.method]})]},t))})]}),(0,a.jsxs)("div",{className:"bg-neutral-50 p-4 rounded-lg",children:[(0,a.jsx)("h4",{className:"font-medium text-neutral-700 mb-2",children:Y("recommendations.acupoints.guidelines")}),(0,a.jsxs)("p",{className:"text-sm text-neutral-600 mb-1",children:[(0,a.jsx)("strong",{children:Y("recommendations.acupoints.technique")}),ei.recommendations.acupoints.massageTechnique]}),(0,a.jsxs)("p",{className:"text-sm text-neutral-600 mb-1",children:[(0,a.jsx)("strong",{children:Y("recommendations.acupoints.frequency")}),ei.recommendations.acupoints.frequency]}),(0,a.jsxs)("p",{className:"text-sm text-neutral-600",children:[(0,a.jsx)("strong",{children:Y("recommendations.acupoints.duration")}),ei.recommendations.acupoints.duration]})]})]}),(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("h3",{className:"text-xl font-semibold text-neutral-800 mb-4 flex items-center",children:[(0,a.jsx)(g.Z,{className:"w-6 h-6 mr-2 text-orange-600"}),Y("recommendations.dietary.title")]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-green-700 mb-2",children:Y("recommendations.dietary.beneficialFoods")}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:ei.recommendations.diet.beneficial.map((e,t)=>(0,a.jsx)("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm",children:e},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-red-700 mb-2",children:Y("recommendations.dietary.foodsToAvoid")}),(0,a.jsx)("div",{className:"flex flex-wrap gap-2",children:ei.recommendations.diet.avoid.map((e,t)=>(0,a.jsx)("span",{className:"bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm",children:e},t))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h4",{className:"font-medium text-neutral-700 mb-2",children:Y("recommendations.dietary.dietaryPrinciples")}),(0,a.jsx)("ul",{className:"space-y-1",children:ei.recommendations.diet.principles.map((e,t)=>(0,a.jsxs)("li",{className:"text-sm text-neutral-600",children:["• ",e]},t))})]})]})]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-50 p-8 rounded-xl mb-8",children:[(0,a.jsxs)("h3",{className:"text-2xl font-semibold text-emerald-800 mb-6 flex items-center",children:[(0,a.jsx)(h.Z,{className:"w-7 h-7 mr-3 text-green-600"}),Y("recommendations.lifestyle.title")]}),(0,a.jsx)("p",{className:"text-emerald-700 mb-6",children:Y("recommendations.lifestyle.description")}),(0,a.jsx)("div",{className:"grid lg:grid-cols-3 gap-6",children:(null===(Z=L[K])||void 0===Z?void 0:null===(z=Z[ei.primaryType])||void 0===z?void 0:z.map((e,t)=>(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-green-200",children:[(0,a.jsxs)("h4",{className:"font-semibold text-emerald-800 mb-4 flex items-center",children:[(0,a.jsx)("span",{className:"text-2xl mr-3",children:e.icon}),e.scenario]}),(0,a.jsx)("ul",{className:"space-y-3",children:e.tips.map((e,t)=>(0,a.jsxs)("li",{className:"text-sm text-emerald-700 flex items-start",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-emerald-400 rounded-full mt-2 mr-3 flex-shrink-0"}),e]},t))})]},t)))||[]}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-green-100 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-green-800",children:[(0,a.jsx)("strong",{children:Y("recommendations.lifestyle.reminder")}),Y("recommendations.lifestyle.reminderText")]})})]}),eb((null==Q?void 0:Q.answers)||[])&&(0,a.jsxs)("div",{className:"bg-gradient-to-br from-pink-50 to-purple-50 p-8 rounded-xl mb-8",children:[(0,a.jsxs)("h3",{className:"text-2xl font-semibold text-purple-800 mb-6 flex items-center",children:[(0,a.jsx)(l.Z,{className:"w-7 h-7 mr-3 text-pink-600"}),Y("recommendations.menstrualPain.title")]}),(0,a.jsxs)("div",{className:"grid lg:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,a.jsxs)("h4",{className:"font-semibold text-purple-700 mb-4 flex items-center",children:[(0,a.jsx)(h.Z,{className:"w-5 h-5 mr-2"}),Y("recommendations.menstrualPain.acupointTherapy")]}),(H=ei.primaryType,(E[K]||E.zh)[H]||[]).map((e,t)=>(0,a.jsxs)("div",{className:"mb-3 p-3 bg-purple-50 rounded-lg",children:[(0,a.jsx)("h5",{className:"font-medium text-purple-800",children:e.name}),(0,a.jsx)("p",{className:"text-sm text-purple-700",children:e.description})]},t))]}),(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,a.jsxs)("h4",{className:"font-semibold text-purple-700 mb-4 flex items-center",children:[(0,a.jsx)(x.Z,{className:"w-5 h-5 mr-2"}),Y("recommendations.menstrualPain.lifestyleAdjustments")]}),(0,a.jsx)("ul",{className:"space-y-2",children:(O=ei.primaryType,(A[K]||A.zh)[O]||[]).map((e,t)=>(0,a.jsxs)("li",{className:"text-sm text-purple-700 flex items-start",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"}),e]},t))})]})]})]}),eb((null==Q?void 0:Q.answers)||[])&&(0,a.jsxs)("div",{className:"bg-gradient-to-br from-orange-50 to-amber-50 p-8 rounded-xl mb-8",children:[(0,a.jsxs)("h3",{className:"text-2xl font-semibold text-orange-800 mb-6 flex items-center",children:[(0,a.jsx)(y.Z,{className:"w-7 h-7 mr-3 text-orange-600"}),Y("emergencyKit.title")]}),(0,a.jsx)("p",{className:"text-orange-700 mb-6",children:Y("emergencyKit.description")}),(0,a.jsx)("div",{className:"space-y-6",children:(null===(G=B[K])||void 0===G?void 0:null===(R=G[ei.primaryType])||void 0===R?void 0:R.map((e,t)=>(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-orange-200",children:[(0,a.jsxs)("h4",{className:"font-semibold text-orange-800 mb-4 flex items-center",children:[(0,a.jsx)("span",{className:"w-3 h-3 bg-orange-500 rounded-full mr-3"}),e.category]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.items.map((e,t)=>(0,a.jsxs)("div",{className:"p-4 rounded-lg border-2 ".concat("high"===e.priority?"border-red-200 bg-red-50":"medium"===e.priority?"border-yellow-200 bg-yellow-50":"border-green-200 bg-green-50"),children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h5",{className:"font-medium text-orange-800",children:e.name}),(0,a.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("high"===e.priority?"bg-red-100 text-red-700":"medium"===e.priority?"bg-yellow-100 text-yellow-700":"bg-green-100 text-green-700"),children:"high"===e.priority?Y("emergencyKit.priority.high"):"medium"===e.priority?Y("emergencyKit.priority.medium"):Y("emergencyKit.priority.low")})]}),(0,a.jsx)("p",{className:"text-sm text-orange-600",children:e.reason})]},t))})]},t)))||[]}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-orange-100 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-orange-800",children:[(0,a.jsx)("strong",{children:Y("emergencyKit.packingTips")}),Y("emergencyKit.packingAdvice")]})})]}),(0,a.jsxs)("div",{className:"bg-white p-8 rounded-xl shadow-sm mb-8",children:[(0,a.jsxs)("h3",{className:"text-2xl font-semibold text-neutral-800 mb-6 flex items-center",children:[(0,a.jsx)(b.Z,{className:"w-7 h-7 mr-3 text-blue-600"}),Y("articles.title")]}),(0,a.jsx)("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:P(ei.primaryType,(null==Q?void 0:Q.answers)||[],K).map((e,t)=>(0,a.jsxs)("div",{className:"border border-neutral-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,a.jsxs)("div",{className:"flex items-center mb-3",children:[(0,a.jsx)("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:(0,a.jsx)(f.Z,{className:"w-5 h-5 text-blue-600"})}),(0,a.jsx)("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:e.category})]}),(0,a.jsx)("h4",{className:"font-semibold text-neutral-800 mb-2 line-clamp-2",children:e.title}),(0,a.jsx)("p",{className:"text-sm text-neutral-600 mb-3 line-clamp-3",children:e.description}),(0,a.jsxs)("a",{href:e.link,className:"inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium",children:[Y("articles.readMore"),(0,a.jsx)(v.Z,{className:"w-4 h-4 ml-1"})]})]},t))})]}),eb((null==Q?void 0:Q.answers)||[])&&(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-xl mb-8",children:[(0,a.jsxs)("h3",{className:"text-2xl font-semibold text-indigo-800 mb-6 flex items-center",children:[(0,a.jsx)(w.Z,{className:"w-7 h-7 mr-3 text-blue-600"}),Y("communication.title")]}),(0,a.jsx)("p",{className:"text-indigo-700 mb-6",children:Y("communication.description")}),(0,a.jsx)("div",{className:"grid lg:grid-cols-3 gap-6",children:(null===(W=M[K])||void 0===W?void 0:W.map((e,t)=>(0,a.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,a.jsxs)("h4",{className:"font-semibold text-indigo-800 mb-4 flex items-center",children:[(0,a.jsx)(j.Z,{className:"w-5 h-5 mr-2"}),e.scenario]}),(0,a.jsx)("div",{className:"space-y-4",children:e.templates.map((e,t)=>(0,a.jsxs)("div",{className:"border border-indigo-200 rounded-lg p-4",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-2",children:[(0,a.jsx)("h5",{className:"font-medium text-indigo-700",children:e.title}),(0,a.jsx)("span",{className:"text-xs px-2 py-1 rounded-full ".concat("intimate"===e.tone?"bg-pink-100 text-pink-700":"casual"===e.tone?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"),children:"intimate"===e.tone?Y("communication.styles.intimate"):"casual"===e.tone?Y("communication.styles.casual"):Y("communication.styles.formal")})]}),(0,a.jsxs)("p",{className:"text-sm text-indigo-600 mb-3 leading-relaxed",children:['"',e.content,'"']}),(0,a.jsxs)("button",{onClick:()=>{navigator.clipboard.writeText(e.content)},className:"flex items-center text-xs text-indigo-600 hover:text-indigo-800 transition-colors",children:[(0,a.jsx)(N.Z,{className:"w-3 h-3 mr-1"}),Y("communication.copyText")]})]},t))})]},t)))||[]}),(0,a.jsx)("div",{className:"mt-6 p-4 bg-blue-100 rounded-lg",children:(0,a.jsxs)("p",{className:"text-sm text-blue-800",children:[(0,a.jsx)("strong",{children:Y("communication.usageTips")}),Y("communication.usageAdvice")]})})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("button",{onClick:ed,className:"btn-secondary",children:Y("navigation.retakeTest")})})]})}return(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsx)(_.Z,{notifications:em,onRemove:eu}),ea&&(0,a.jsx)(T.Z,{}),(0,a.jsxs)("div",{className:"mb-8 bg-white p-6 rounded-xl shadow-lg border border-purple-100",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,a.jsx)("span",{className:"text-sm font-medium text-gray-700 bg-purple-50 px-3 py-1 rounded-full",children:"zh"===K?"第 ".concat(X+1," 题，共 ").concat($," 题"):"Question ".concat(X+1," of ").concat($)}),(0,a.jsxs)("span",{className:"text-sm font-medium text-purple-600 bg-purple-50 px-3 py-1 rounded-full",children:[Math.round(ee),"% ","zh"===K?"完成":"Complete"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-3 overflow-hidden",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-500 to-purple-600 h-3 rounded-full transition-all duration-500 ease-out shadow-sm",style:{width:"".concat(ee,"%")}})})]}),J&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg border border-purple-100 p-8 animate-fade-in",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-800 mb-3 leading-tight",children:J.title}),J.description&&(0,a.jsx)("p",{className:"text-gray-600 text-lg leading-relaxed",children:J.description})]}),(0,a.jsx)("div",{className:"mb-8",children:"scale"===J.type?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"px-4 pain-scale-container",children:[(0,a.jsx)("input",{type:"range",min:(null===(t=J.validation)||void 0===t?void 0:t.min)||0,max:(null===(i=J.validation)||void 0===i?void 0:i.max)||10,value:V[J.id]||0,onChange:e=>eg(J.id,e.target.value),className:"w-full pain-scale cursor-pointer"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-neutral-600 mt-2",children:[(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:Y("painScale.levels.none")}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:Y("painScale.levels.mild")}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:Y("painScale.levels.moderate")}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:Y("painScale.levels.severe")}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:Y("painScale.levels.extreme")})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsxs)("div",{className:"inline-flex items-center bg-gradient-to-r from-purple-100 via-purple-50 to-pink-100 px-8 py-4 rounded-2xl shadow-lg border border-purple-200",children:[(0,a.jsx)(l.Z,{className:"w-6 h-6 text-purple-600 mr-3"}),(0,a.jsxs)("span",{className:"text-xl font-bold text-purple-800",children:[Y("painScale.title"),(0,a.jsx)("span",{className:"text-3xl font-extrabold text-purple-600 mx-2",children:V[J.id]||0}),(0,a.jsxs)("span",{className:"text-base font-medium text-purple-700 ml-2",children:["(",null===(F=J.options.find(e=>e.value==(V[J.id]||0)))||void 0===F?void 0:F.label,")"]})]})]})}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl overflow-hidden border border-purple-200 shadow-sm",children:[(0,a.jsxs)("h4",{className:"font-semibold text-purple-800 mb-4 flex items-center",children:[(0,a.jsx)(b.Z,{className:"w-5 h-5 mr-2"}),Y("painScale.reference")]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm text-purple-700",children:[(0,a.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"0-2:"})," ",Y("painScale.descriptions.0-2")]})]}),(0,a.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"3-4:"})," ",Y("painScale.descriptions.3-4")]})]}),(0,a.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-orange-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"5-7:"})," ",Y("painScale.descriptions.5-7")]})]}),(0,a.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"8-10:"})," ",Y("painScale.descriptions.8-10")]})]})]})]})]}):"multiple"===J.type?(0,a.jsx)("div",{className:"space-y-4",children:J.options.map(e=>{let t=(Array.isArray(V[J.id])?V[J.id]:V[J.id]?[V[J.id]]:[]).includes(String(e.value));return(0,a.jsxs)("label",{className:"block p-5 border-2 rounded-xl cursor-pointer transition-all duration-300 transform hover:-translate-y-1 ".concat(t?"border-purple-500 bg-gradient-to-r from-purple-50 to-purple-100 shadow-lg":"border-gray-200 hover:border-purple-300 hover:shadow-md bg-white"),children:[(0,a.jsx)("input",{type:"checkbox",checked:t,onChange:()=>ex(J.id,String(e.value)),className:"sr-only"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-5 h-5 rounded border-2 mr-4 flex items-center justify-center ".concat(t?"border-purple-500 bg-purple-500":"border-gray-300"),children:t&&(0,a.jsx)(p.Z,{className:"w-3 h-3 text-white"})}),(0,a.jsx)("span",{className:"text-gray-800 font-medium",children:e.label})]})]},e.value)})}):(0,a.jsx)("div",{className:"space-y-4",children:J.options.map(e=>(0,a.jsxs)("label",{className:"block p-5 border-2 rounded-xl cursor-pointer transition-all duration-300 transform hover:-translate-y-1 ".concat(V[J.id]===e.value?"border-purple-500 bg-gradient-to-r from-purple-50 to-purple-100 shadow-lg":"border-gray-200 hover:border-purple-300 hover:shadow-md bg-white"),children:[(0,a.jsx)("input",{type:"radio",name:J.id,value:e.value,checked:V[J.id]===e.value,onChange:()=>eg(J.id,e.value),className:"sr-only"}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("div",{className:"w-5 h-5 rounded-full border-2 mr-4 flex items-center justify-center ".concat(V[J.id]===e.value?"border-purple-500 bg-purple-500":"border-gray-300"),children:V[J.id]===e.value&&(0,a.jsx)("div",{className:"w-2 h-2 bg-white rounded-full"})}),(0,a.jsx)("span",{className:"text-gray-800 font-medium",children:e.label})]})]},e.value))})}),(0,a.jsxs)("div",{className:"flex justify-between items-center pt-6",children:[(0,a.jsxs)("button",{onClick:()=>{er()},disabled:0===X,className:"flex items-center px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-gray-100",children:[(0,a.jsx)(D.Z,{className:"w-5 h-5 mr-2"}),Y("navigation.previous")]}),(0,a.jsxs)("button",{onClick:()=>{X>=$-1?ec()?ep(Y("messages.testComplete"),Y("messages.testCompleteDesc")):eh(Y("messages.testFailed"),Y("messages.testFailedDesc")):el()},disabled:!(()=>{if(!J)return!1;let e=ey();return"multiple"===J.type||null!=e&&""!==e})(),className:"flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl font-semibold hover:from-purple-700 hover:to-purple-800 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:transform-none disabled:hover:shadow-none",children:[X>=$-1?Y("navigation.completeTest"):Y("navigation.next"),(0,a.jsx)(v.Z,{className:"w-5 h-5 ml-2"})]})]})]}),es&&(0,a.jsx)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-red-700",children:es})})]})}},7065:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return D}});var a=i(7437),s=i(2265),n=i(2586),o=i(6221),r=i(9397),l=i(7586),c=i(2735),d=i(8594);let m=e=>{let[t,i]=(0,s.useState)([]),[a,n]=(0,s.useState)({totalEntries:0,averagePain:0,maxPain:0,minPain:0,mostCommonSymptoms:[],mostEffectiveRemedies:[],painFrequency:{},trendDirection:"stable"}),[o,r]=(0,s.useState)(!0),[l,c]=(0,s.useState)(null),m=(0,d.I6)(e||"anonymous","pain_records");(0,s.useEffect)(()=>{(async()=>{try{r(!0),c(null);let e=(0,d.mO)(m);e&&Array.isArray(e)&&(i(e),n((0,d.i5)(e)))}catch(e){c("Failed to load data. Please refresh the page.")}finally{r(!1)}})()},[m]),(0,s.useEffect)(()=>{o||!(t.length>=0)||(0,d.RY)(m,t)||c("Failed to save data. Please try again.")},[t,m,o]),(0,s.useEffect)(()=>{n((0,d.i5)(t))},[t]);let u=(0,s.useCallback)(async e=>{try{c(null);let a=(0,d.Yp)(e);if(a.length>0)return{success:!1,errors:a};if(t.find(t=>t.date===e.date)){if(!0!==e.overwrite)return{success:!1,errors:[{field:"date",message:"An entry for this date already exists. Do you want to overwrite it?",code:"DUPLICATE_DATE"}]};i(t=>t.filter(t=>t.date!==e.date))}let s=new Date().toISOString(),n={id:"pain_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),...e,location:e.location||[],symptoms:e.symptoms||[],remedies:e.remedies||[],createdAt:s,updatedAt:s};return i(e=>[...e,n].sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())),{success:!0}}catch(e){return c("Failed to add entry. Please try again."),{success:!1,errors:[{field:"general",message:"Failed to add entry",code:"ADD_ERROR"}]}}},[t]),p=(0,s.useCallback)(async(e,a)=>{try{c(null);let s=t.find(t=>t.id===e);if(!s)return{success:!1,errors:[{field:"id",message:"Entry not found",code:"NOT_FOUND"}]};let n={...s,...a},o=(0,d.Yp)(n);if(o.length>0)return{success:!1,errors:o};if(a.date&&a.date!==s.date&&t.find(t=>t.id!==e&&t.date===a.date))return{success:!1,errors:[{field:"date",message:"An entry for this date already exists",code:"DUPLICATE_DATE"}]};let r={...s,...a,updatedAt:new Date().toISOString()};return i(t=>t.map(t=>t.id===e?r:t).sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())),{success:!0}}catch(e){return c("Failed to update entry. Please try again."),{success:!1,errors:[{field:"general",message:"Failed to update entry",code:"UPDATE_ERROR"}]}}},[t]),h=(0,s.useCallback)(async e=>{try{if(c(null),!t.some(t=>t.id===e))return c("Entry not found"),!1;return i(t=>t.filter(t=>t.id!==e)),!0}catch(e){return c("Failed to delete entry. Please try again."),!1}},[t]),g=(0,s.useCallback)(async()=>{try{if(c(null),i([]),!(0,d.B$)(m))return c("Failed to clear data. Please try again."),!1;return!0}catch(e){return c("Failed to clear data. Please try again."),!1}},[m]),x=(0,s.useCallback)(e=>t.find(t=>t.id===e),[t]),y=(0,s.useCallback)((e,i)=>t.filter(t=>{let a=new Date(t.date),s=new Date(e),n=new Date(i);return a>=s&&a<=n}),[t]),b=(0,s.useCallback)(e=>{try{if("json"===e){let e=JSON.stringify(t,null,2),i=new Blob([e],{type:"application/json"}),a=URL.createObjectURL(i),s=document.createElement("a");s.href=a,s.download="pain-tracker-data-".concat(new Date().toISOString().split("T")[0],".json"),document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(a)}else"csv"===e&&b("json")}catch(e){c("Failed to export data. Please try again.")}},[t]);return{entries:t,statistics:a,isLoading:o,error:l,addEntry:u,updateEntry:p,deleteEntry:h,clearAllEntries:g,getEntry:x,getEntriesInRange:y,exportData:b,refreshData:(0,s.useCallback)(()=>{let e=(0,d.mO)(m);e&&Array.isArray(e)&&i(e)},[m]),setError:c}};var u=i(2475),p=i(1047),h=i(4401),g=i(1723),x=i(3774),y=i(8997),b=i(6595),f=i(8736),v=i(6032),w=i(1082),j=e=>{var t;let{initialData:i,onSubmit:o,onCancel:r,isLoading:l=!1,locale:c}=e,m=(0,n.useTranslations)("painTracker"),[u,j]=(0,s.useState)({date:(null==i?void 0:i.date)||(0,d.mn)(new Date),painLevel:(null==i?void 0:i.painLevel)||1,duration:(null==i?void 0:i.duration)||void 0,location:(null==i?void 0:i.location)||[],menstrualStatus:(null==i?void 0:i.menstrualStatus)||"other",symptoms:(null==i?void 0:i.symptoms)||[],remedies:(null==i?void 0:i.remedies)||[],effectiveness:(null==i?void 0:i.effectiveness)||void 0,notes:(null==i?void 0:i.notes)||""}),[N,D]=(0,s.useState)({}),[k,C]=(0,s.useState)(!1),[S,_]=(0,s.useState)({}),T=v.DI[c]||v.DI.en,q=v.C7[c]||v.C7.en,E=v.Ju[c]||v.Ju.en,A=v.Yw[c]||v.Yw.en,P=v.E2[c]||v.E2.en,M=v.bp[c]||v.bp.en,B=v.RZ[c]||v.RZ.en,L=(e,t)=>{j(i=>({...i,[e]:t})),_(t=>({...t,[e]:!0})),N[e]&&D(t=>({...t,[e]:""}))},F=(e,t)=>{let i=u[e];L(e,i.includes(t)?i.filter(e=>e!==t):[...i,t])},I=()=>{let e={};if(u.date){let t=new Date(u.date),i=new Date;i.setHours(23,59,59,999),t>i&&(e.date=B.futureDate)}else e.date=B.required;return(u.painLevel<1||u.painLevel>10)&&(e.painLevel=B.painLevelRange),void 0!==u.duration&&(u.duration<0||u.duration>1440)&&(e.duration=B.durationRange),void 0!==u.effectiveness&&(u.effectiveness<1||u.effectiveness>5)&&(e.effectiveness=B.effectivenessRange),u.notes&&u.notes.length>500&&(e.notes=B.notesLength),D(e),0===Object.keys(e).length},z=async function(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1];if(e.preventDefault(),I())try{let e=t?{...u,overwrite:!0}:u,a=await o(e);if(a.success)j({date:(0,d.mn)(new Date),painLevel:1,duration:void 0,location:[],menstrualStatus:"other",symptoms:[],remedies:[],effectiveness:void 0,notes:""}),D({}),C(!1);else if(a.errors){var i;let e={};a.errors.forEach(t=>{e[t.field]=t.message}),D(e),(null===(i=a.errors[0])||void 0===i?void 0:i.code)==="DUPLICATE_DATE"&&C(!0)}}catch(e){D({general:B.storageError})}},Z=P.find(e=>e.value===u.painLevel);return(0,a.jsxs)("form",{onSubmit:z,className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(p.Z,{className:"w-4 h-4 mr-2"}),m("form.date")]}),(0,a.jsx)("input",{type:"date",value:u.date,onChange:e=>L("date",e.target.value),className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 ".concat(N.date?"border-red-500":"border-gray-300"),max:(0,d.mn)(new Date)}),N.date&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:N.date}),k&&(0,a.jsx)("div",{className:"mt-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(0,a.jsx)("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:(0,a.jsx)("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-yellow-800",children:"zh"===c?"该日期已有记录":"Entry exists for this date"}),(0,a.jsx)("p",{className:"mt-1 text-sm text-yellow-700",children:"zh"===c?"该日期已经有疼痛记录了。您想要覆盖现有记录吗？":"There is already a pain entry for this date. Do you want to overwrite the existing entry?"}),(0,a.jsxs)("div",{className:"mt-3 flex space-x-3",children:[(0,a.jsx)("button",{type:"button",onClick:e=>{z(e,!0)},className:"bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700 transition-colors",children:"zh"===c?"覆盖":"Overwrite"}),(0,a.jsx)("button",{type:"button",onClick:()=>{C(!1),D({})},className:"bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-400 transition-colors",children:"zh"===c?"取消":"Cancel"})]})]})]})})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(h.Z,{className:"w-4 h-4 mr-2"}),m("form.painLevel")," (",u.painLevel,"/10)"]}),(0,a.jsxs)("div",{className:"space-y-4 pain-scale-container",children:[(0,a.jsx)("input",{type:"range",min:"1",max:"10",value:u.painLevel,onChange:e=>L("painLevel",parseInt(e.target.value)),className:"w-full pain-scale cursor-pointer"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-neutral-600 mt-2",children:[(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:"1"}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:"3"}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:"5"}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:"7"}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:"10"})]}),Z&&(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"inline-flex items-center bg-gradient-to-r from-pink-100 via-pink-50 to-purple-100 px-6 py-3 rounded-xl shadow-lg border border-pink-200",children:(0,a.jsxs)("span",{className:"text-lg font-bold text-pink-800",children:["疼痛程度：",(0,a.jsx)("span",{className:"text-2xl font-extrabold text-pink-600 mx-2",children:u.painLevel}),(0,a.jsxs)("span",{className:"text-base font-medium text-pink-700",children:["(",Z.label,")"]})]})}),(0,a.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:Z.description})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(g.Z,{className:"w-4 h-4 mr-2"}),m("form.duration")," (",m("form.optional"),")"]}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("input",{type:"number",min:"0",max:"1440",value:u.duration||"",onChange:e=>L("duration",e.target.value?parseInt(e.target.value):void 0),className:"flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 ".concat(N.duration?"border-red-500":"border-gray-300"),placeholder:"0"}),(0,a.jsx)("span",{className:"text-sm text-gray-500",children:m("form.minutes")})]}),N.duration&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:N.duration})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-3",children:[(0,a.jsx)(x.Z,{className:"w-4 h-4 mr-2"}),m("form.location")]}),(0,a.jsx)("div",{className:"grid grid-cols-2 gap-2",children:T.map(e=>(0,a.jsxs)("button",{type:"button",onClick:()=>F("location",e.value),className:"p-3 text-left border rounded-lg transition-colors ".concat(u.location.includes(e.value)?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 hover:border-gray-400"),children:[(0,a.jsx)("span",{className:"text-lg mr-2",children:e.icon}),(0,a.jsx)("span",{className:"text-sm",children:e.label})]},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-3",children:[(0,a.jsx)(y.Z,{className:"w-4 h-4 mr-2"}),m("form.menstrualStatus")]}),(0,a.jsx)("div",{className:"grid grid-cols-1 gap-2",children:A.map(e=>(0,a.jsxs)("button",{type:"button",onClick:()=>L("menstrualStatus",e.value),className:"p-3 text-left border rounded-lg transition-colors ".concat(u.menstrualStatus===e.value?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 hover:border-gray-400"),children:[(0,a.jsx)("span",{className:"text-lg mr-2",children:e.icon}),(0,a.jsx)("span",{className:"text-sm",children:e.label})]},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-3",children:[(0,a.jsx)(h.Z,{className:"w-4 h-4 mr-2"}),m("form.symptoms")," (",m("form.optional"),")"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-48 overflow-y-auto",children:q.map(e=>(0,a.jsxs)("button",{type:"button",onClick:()=>F("symptoms",e.value),className:"p-3 sm:p-2 text-left border rounded-lg transition-colors text-sm mobile-touch-target ".concat(u.symptoms.includes(e.value)?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 hover:border-gray-400 active:bg-gray-50"),children:[(0,a.jsx)("span",{className:"text-base mr-2",children:e.icon}),(0,a.jsx)("span",{children:e.label})]},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-3",children:[(0,a.jsx)(y.Z,{className:"w-4 h-4 mr-2"}),m("form.remedies")," (",m("form.optional"),")"]}),(0,a.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-48 overflow-y-auto",children:E.map(e=>(0,a.jsxs)("button",{type:"button",onClick:()=>F("remedies",e.value),className:"p-3 sm:p-2 text-left border rounded-lg transition-colors text-sm mobile-touch-target ".concat(u.remedies.includes(e.value)?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 hover:border-gray-400 active:bg-gray-50"),children:[(0,a.jsx)("span",{className:"text-base mr-2",children:e.icon}),(0,a.jsx)("span",{children:e.label})]},e.value))})]}),u.remedies.length>0&&(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-3",children:[(0,a.jsx)(b.Z,{className:"w-4 h-4 mr-2"}),m("form.effectiveness")," (",m("form.optional"),")"]}),(0,a.jsx)("div",{className:"grid grid-cols-5 gap-2",children:M.map(e=>(0,a.jsxs)("button",{type:"button",onClick:()=>L("effectiveness",e.value),className:"p-3 text-center border rounded-lg transition-colors ".concat(u.effectiveness===e.value?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 hover:border-gray-400"),children:[(0,a.jsx)("div",{className:"text-lg mb-1",children:e.icon}),(0,a.jsx)("div",{className:"text-xs",children:e.label})]},e.value))}),N.effectiveness&&(0,a.jsx)("p",{className:"mt-1 text-sm text-red-600",children:N.effectiveness})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[(0,a.jsx)(f.Z,{className:"w-4 h-4 mr-2"}),m("form.notes")," (",m("form.optional"),")"]}),(0,a.jsx)("textarea",{value:u.notes,onChange:e=>L("notes",e.target.value),rows:3,maxLength:500,className:"w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 resize-none ".concat(N.notes?"border-red-500":"border-gray-300"),placeholder:m("form.notesPlaceholder")}),(0,a.jsxs)("div",{className:"flex justify-between mt-1",children:[N.notes&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:N.notes}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 ml-auto",children:[(null===(t=u.notes)||void 0===t?void 0:t.length)||0,"/500"]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 sm:space-x-3 pt-4",children:[(0,a.jsx)("button",{type:"submit",disabled:l,className:"flex-1 bg-gradient-to-r from-pink-600 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-pink-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors mobile-touch-target order-1",children:l?(0,a.jsxs)("div",{className:"flex items-center justify-center",children:[(0,a.jsx)(w.Z,{size:"sm",color:"white",className:"mr-2"}),m("form.saving")]}):m("form.save")}),r&&(0,a.jsx)("button",{type:"button",onClick:r,className:"w-full sm:w-auto px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors mobile-touch-target order-2",children:m("form.cancel")})]}),N.general&&(0,a.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsx)("p",{className:"text-sm text-red-600",children:N.general})})]})},N=i(9048);function D(e){let{locale:t}=e,i=(0,n.useTranslations)("painTracker"),[d,p]=(0,s.useState)("overview"),[h,g]=(0,s.useState)(!1),[x,y]=(0,s.useState)(null),{entries:b,statistics:f,isLoading:v,error:D,addEntry:k,updateEntry:C,deleteEntry:S,setError:_}=m(),{notifications:T,removeNotification:q,addSuccessNotification:E,addErrorNotification:A}=(0,u.z)(),P=async e=>{g(!0);try{let t=await k(e);if(t.success)return E(i("messages.saveSuccess"),i("form.save")),p("entries"),{success:!0};return t}catch(e){return A(i("messages.saveError"),i("messages.validationError")),{success:!1}}finally{g(!1)}},M=async e=>{if(!x)return{success:!1};g(!0);try{let t=await C(x.id,e);if(t.success)return E(i("messages.updateSuccess"),i("form.save")),y(null),p("entries"),{success:!0};return t}catch(e){return A(i("messages.updateError"),i("messages.validationError")),{success:!1}}finally{g(!1)}},B=async e=>{if(window.confirm(i("messages.confirmDelete")))try{await S(e)?E(i("messages.deleteSuccess"),i("entries.delete")):A(i("messages.deleteError"),i("messages.validationError"))}catch(e){A(i("messages.deleteError"),i("messages.validationError"))}},L=e=>{y({id:e.id,data:e}),p("add")},F=[{id:"overview",label:i("navigation.overview"),icon:o.Z},{id:"add",label:i("navigation.addEntry"),icon:r.Z},{id:"entries",label:i("navigation.viewEntries"),icon:l.Z},{id:"statistics",label:i("navigation.statistics"),icon:o.Z},{id:"export",label:i("navigation.export"),icon:c.Z}];return(0,a.jsxs)("div",{className:"bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 rounded-xl p-8",children:[(0,a.jsxs)(w.f,{isLoading:v,message:"Loading...",children:[(0,a.jsx)("div",{className:"mb-8",children:(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm p-1",children:(0,a.jsx)("nav",{className:"flex space-x-1",children:F.map(e=>{let t=e.icon;return(0,a.jsxs)("button",{onClick:()=>p(e.id),className:"flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ".concat(d===e.id?"bg-gradient-to-r from-pink-600 to-purple-600 text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"),children:[(0,a.jsx)(t,{className:"w-4 h-4 mr-2"}),e.label]},e.id)})})})}),(0,a.jsxs)("div",{children:["overview"===d&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:i("statistics.overview")}),0===b.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(o.Z,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:i("entries.noEntries")}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:i("entries.noEntriesDescription")}),(0,a.jsx)("button",{onClick:()=>p("add"),className:"bg-gradient-to-r from-pink-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-pink-700 hover:to-purple-700 transition-colors",children:i("entries.addFirst")})]}):(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-pink-100 to-purple-100 p-6 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:i("statistics.totalEntries")}),(0,a.jsx)("p",{className:"text-3xl font-bold text-gray-900",children:f.totalEntries})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-100 to-indigo-100 p-6 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:i("statistics.averagePain")}),(0,a.jsxs)("p",{className:"text-3xl font-bold text-gray-900",children:[f.averagePain,"/10"]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-100 to-teal-100 p-6 rounded-lg",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:i("statistics.trendDirection")}),(0,a.jsx)("p",{className:"text-lg font-semibold text-gray-900",children:i("statistics.".concat(f.trendDirection))})]})]})]}),"add"===d&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:x?i("form.editTitle"):i("form.title")}),(0,a.jsx)(j,{onSubmit:x?M:P,onCancel:x?()=>{y(null),p("entries")}:()=>p("overview"),isLoading:h,locale:t,initialData:null==x?void 0:x.data})]}),"entries"===d&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:i("entries.title")}),0===b.length?(0,a.jsxs)("div",{className:"text-center py-12",children:[(0,a.jsx)("div",{className:"text-gray-400 mb-4",children:(0,a.jsx)(l.Z,{className:"w-16 h-16 mx-auto"})}),(0,a.jsx)("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:i("entries.noEntries")}),(0,a.jsx)("p",{className:"text-gray-600 mb-6",children:i("entries.noEntriesDescription")}),(0,a.jsx)("button",{onClick:()=>p("add"),className:"bg-gradient-to-r from-pink-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-pink-700 hover:to-purple-700 transition-colors",children:i("entries.addFirst")})]}):(0,a.jsx)("div",{className:"space-y-4",children:b.map(e=>(0,a.jsx)("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,a.jsxs)("div",{className:"flex justify-between items-start",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"font-medium text-gray-900",children:new Date(e.date).toLocaleDateString(t)}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("entries.painIntensity"),": ",e.painLevel,"/10"]}),e.duration&&(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[i("entries.duration"),": ",e.duration," ",i("entries.minutes")]})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)("button",{onClick:()=>L(e),className:"text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors",children:i("entries.edit")}),(0,a.jsx)("button",{onClick:()=>B(e.id),className:"text-red-600 hover:text-red-800 text-sm font-medium transition-colors",children:i("entries.delete")})]})]})},e.id))})]}),"statistics"===d&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:i("statistics.title")}),(0,a.jsx)("p",{className:"text-gray-600",children:i("statistics.inDevelopment")})]}),"export"===d&&(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[(0,a.jsx)("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:i("export.title")}),(0,a.jsx)("p",{className:"text-gray-600",children:i("export.inDevelopment")})]})]}),D&&(0,a.jsx)("div",{className:"mt-4",children:(0,a.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[(0,a.jsx)("p",{className:"text-red-600",children:D}),(0,a.jsx)("button",{onClick:()=>_(null),className:"mt-2 text-sm text-red-600 hover:text-red-800",children:i("messages.close")})]})})]}),(0,a.jsx)(N.Z,{notifications:T,onRemove:q})]})}},639:function(e,t,i){"use strict";i.r(t);var a=i(7437),s=i(2265),n=i(8278);t.default=e=>{let{locale:t="zh"}=e,[i,o]=(0,s.useState)(""),[r,l]=(0,s.useState)(""),[c,d]=(0,s.useState)([]),[m,u]=(0,s.useState)(null),[p,h]=(0,s.useState)(!1),{t:g}=(0,n.A0)("periodPainAssessment"),x=(e,t)=>{t?d([...c,e]):d(c.filter(t=>t!==e))};return(0,a.jsxs)("div",{className:"max-w-4xl mx-auto p-4 sm:p-6 bg-white rounded-lg shadow-lg mobile-safe-area",children:[(0,a.jsxs)("div",{className:"text-center mb-6 sm:mb-8",children:[(0,a.jsx)("h2",{className:"text-2xl sm:text-3xl font-bold text-primary mb-3 sm:mb-4",children:g("title")}),(0,a.jsx)("p",{className:"text-gray-600 text-sm sm:text-base px-2",children:g("subtitle")})]}),p?(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"p-6 rounded-lg border-l-4 ".concat((null==m?void 0:m.severity)==="high"?"border-red-500 bg-red-50":(null==m?void 0:m.severity)==="medium"?"border-yellow-500 bg-yellow-50":"border-green-500 bg-green-50"),children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-3 ".concat((null==m?void 0:m.severity)==="high"?"text-red-700":(null==m?void 0:m.severity)==="medium"?"text-yellow-700":"text-green-700"),children:g("results.title")}),(0,a.jsx)("p",{className:"text-gray-700 leading-relaxed",children:null==m?void 0:m.advice})]}),(null==m?void 0:m.needConsult)&&(0,a.jsx)("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:(0,a.jsx)("p",{className:"text-blue-700 font-medium text-sm",children:g("results.consultAdvice")})}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center gap-3 sm:gap-4",children:[(0,a.jsx)("button",{onClick:()=>{o(""),l(""),d([]),u(null),h(!1)},className:"btn-outline px-4 sm:px-6 py-3 font-semibold mobile-touch-target order-2 sm:order-1",children:g("actions.reset")}),(0,a.jsx)("a",{href:"/".concat(t,"/teen-health"),className:"btn-secondary px-4 sm:px-6 py-3 font-semibold text-center mobile-touch-target order-1 sm:order-2",children:g("actions.moreInfo")})]})]}):(0,a.jsxs)("div",{className:"space-y-6 sm:space-y-8",children:[(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl font-semibold mb-3 sm:mb-4",children:g("questions.intensity.title")}),(0,a.jsx)("div",{className:"space-y-2 sm:space-y-3",children:[{value:"mild",label:g("questions.intensity.options.mild")},{value:"moderate",label:g("questions.intensity.options.moderate")},{value:"severe",label:g("questions.intensity.options.severe")}].map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer mobile-touch-target p-2 sm:p-1 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)("input",{type:"radio",name:"intensity",value:e.value,checked:i===e.value,onChange:e=>o(e.target.value),className:"w-5 h-5 sm:w-4 sm:h-4 text-primary"}),(0,a.jsx)("span",{className:"text-gray-700 text-sm sm:text-base",children:e.label})]},e.value))})]}),(0,a.jsxs)("div",{className:"form-group",children:[(0,a.jsx)("h3",{className:"text-lg sm:text-xl font-semibold mb-3 sm:mb-4",children:g("questions.onset.title")}),(0,a.jsx)("div",{className:"space-y-2 sm:space-y-3",children:[{value:"early",label:g("questions.onset.options.recent")},{value:"late",label:g("questions.onset.options.later")}].map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer mobile-touch-target p-2 sm:p-1 rounded-lg hover:bg-gray-50 transition-colors",children:[(0,a.jsx)("input",{type:"radio",name:"onset",value:e.value,checked:r===e.value,onChange:e=>l(e.target.value),className:"w-5 h-5 sm:w-4 sm:h-4 text-primary"}),(0,a.jsx)("span",{className:"text-gray-700 text-sm sm:text-base",children:e.label})]},e.value))})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h3",{className:"text-xl font-semibold mb-4",children:g("questions.symptoms.title")}),(0,a.jsx)("div",{className:"space-y-3",children:[{value:"fever",label:g("questions.symptoms.options.fever")},{value:"severe_vomiting",label:g("questions.symptoms.options.vomiting")},{value:"fainting",label:g("questions.symptoms.options.dizziness")},{value:"abnormal_bleeding",label:g("questions.symptoms.options.bleeding")},{value:"non_period_pain",label:g("questions.symptoms.options.nonMenstrual")}].map(e=>(0,a.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[(0,a.jsx)("input",{type:"checkbox",value:e.value,checked:c.includes(e.value),onChange:t=>x(e.value,t.target.checked),className:"w-4 h-4 text-primary"}),(0,a.jsx)("span",{className:"text-gray-700",children:e.label})]},e.value))})]}),(0,a.jsx)("div",{className:"text-center pt-4",children:(0,a.jsx)("button",{onClick:()=>{if(!i||!r){alert(g("results.validationMessage"));return}let e="",t=!1,a="low";c.length>0?(e=g("results.assessments.severe_symptoms"),t=!0,a="high"):"severe"===i?(e="late"===r?g("results.assessments.severe_late"):g("results.assessments.severe_early"),t=!0,a="high"):"moderate"===i&&"late"===r?(e=g("results.assessments.moderate_late"),t=!0,a="medium"):(e=g("results.assessments.normal"),t=!1,a="low"),u({advice:e,needConsult:t,severity:a}),h(!0)},className:"btn-primary text-base sm:text-lg font-bold w-full sm:w-auto sm:min-w-[200px] mobile-touch-target",children:g("actions.assess")})})]}),(0,a.jsx)("div",{className:"mt-8 text-center text-sm text-gray-500",children:(0,a.jsx)("p",{children:"⚠️ 本工具仅供参考，不能替代专业医疗诊断。如有疑虑，请咨询医生。"})})]})}},3856:function(e,t,i){"use strict";i.r(t),i.d(t,{default:function(){return b}});var a=i(7437),s=i(2265),n=i(4972),o=i(8997),r=i(5302),l=i(4401),c=i(3276),d=i(2660),m=i(6858),u=i(3639),p=i(5388),h=i(2475),g=i(9048),x=i(2586);function y(e,t){var i;let a={zh:{title:"标题",description:"描述",submit:"提交",cancel:"取消",save:"保存",delete:"删除",edit:"编辑",add:"添加",loading:"加载中...",error:"错误",success:"成功",warning:"警告",info:"信息",close:"关闭",open:"打开",start:"开始",stop:"停止",next:"下一步",previous:"上一步",finish:"完成",retry:"重试"},en:{title:"Title",description:"Description",submit:"Submit",cancel:"Cancel",save:"Save",delete:"Delete",edit:"Edit",add:"Add",loading:"Loading...",error:"Error",success:"Success",warning:"Warning",info:"Information",close:"Close",open:"Open",start:"Start",stop:"Stop",next:"Next",previous:"Previous",finish:"Finish",retry:"Retry"}},s=a[t]||a.en,n=(null===(i=e.split(".").pop())||void 0===i?void 0:i.toLowerCase())||"";return s[n]?s[n]:n.replace(/([A-Z])/g," $1").replace(/[_-]/g," ").replace(/\b\w/g,e=>e.toUpperCase()).trim()||("zh"===t?"未知":"Unknown")}function b(e){var t,i,b,f,v;let{locale:w}=e,{t:j}=function(e){let t=(0,x.useTranslations)(e),i=(0,x.useLocale)();return{t:(a,s,n)=>{try{let o=t(a,s),r=e?"".concat(e,".").concat(a):a;if(o===r||o===a||o.includes(r)){if(n)return n;return y(a,i)}return o}catch(e){return n||y(a,i)}},locale:i,isZh:"zh"===i,isEn:"en"===i}}("painTracker.assessment"),[N,D]=(0,s.useState)({}),{currentSession:k,currentQuestion:C,currentQuestionIndex:S,totalQuestions:_,progress:T,isComplete:q,result:E,isLoading:A,error:P,startAssessment:M,answerQuestion:B,goToPreviousQuestion:L,goToNextQuestion:F,completeAssessment:I,resetAssessment:z}=(0,p.o)();(0,s.useEffect)(()=>{},[E]);let{notifications:Z,removeNotification:R,addSuccessNotification:G,addErrorNotification:W}=(0,h.z)(),H=e=>{C&&(D(t=>({...t,[C.id]:e})),B({questionId:C.id,value:e,timestamp:new Date().toISOString()}))},O=()=>{S>=_-1?I(j)?(G(j("messages.assessmentComplete"),j("messages.assessmentCompleteDesc")),setTimeout(()=>{},100)):W(j("messages.assessmentFailed"),j("messages.assessmentFailedDesc")):F()};return k?E?(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8 max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-green-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(r.Z,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:j("result.title",{},"en"===w?"Assessment Results":"评估结果")})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-100 to-purple-100 p-6 rounded-lg text-center",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:j("result.yourScore",{},"en"===w?"Your Score":"您的得分")}),(0,a.jsxs)("p",{className:"text-3xl font-bold text-gray-900",children:[E.score,"/",E.maxScore]}),(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:[Math.round(E.percentage),"%"]})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-green-100 to-teal-100 p-6 rounded-lg text-center",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:j("result.severity")}),(0,a.jsx)("p",{className:"text-xl font-bold text-gray-900",children:j("severity.".concat(E.severity))})]}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-yellow-100 to-orange-100 p-6 rounded-lg text-center",children:[(0,a.jsx)("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:j("result.type")}),(0,a.jsx)("p",{className:"text-xl font-bold text-gray-900",children:j("severity.".concat(E.type))})]})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:j("result.summary")}),(0,a.jsx)("div",{className:"bg-gray-50 rounded-lg p-6",children:(0,a.jsx)("p",{className:"text-gray-700",children:E.message})})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsx)("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:j("result.recommendations")}),(0,a.jsx)("div",{className:"space-y-4",children:E.recommendations.map(e=>(0,a.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6",children:[(0,a.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[(0,a.jsx)("h4",{className:"text-lg font-semibold text-gray-900",children:e.title}),(0,a.jsx)("span",{className:"px-3 py-1 rounded-full text-xs font-medium ".concat("high"===e.priority?"bg-red-100 text-red-800":"medium"===e.priority?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"),children:j("priority.".concat(e.priority))})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-3",children:e.description}),(0,a.jsxs)("p",{className:"text-sm text-gray-500 mb-3",children:[(0,a.jsx)("strong",{children:j("result.timeframe")})," ",e.timeframe]}),e.actionSteps&&(0,a.jsxs)("div",{children:[(0,a.jsx)("h5",{className:"font-medium text-gray-900 mb-2",children:j("result.actionSteps")}),(0,a.jsx)("ul",{className:"list-disc list-inside text-sm text-gray-600 space-y-1",children:(Array.isArray(e.actionSteps)?e.actionSteps:"string"==typeof e.actionSteps?[e.actionSteps]:[]).map((e,t)=>(0,a.jsx)("li",{children:e},t))})]})]},e.id))})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[(0,a.jsx)("button",{onClick:z,className:"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors",children:j("result.retakeAssessment")}),(0,a.jsx)("button",{onClick:()=>G(j("messages.resultsSaved"),j("messages.resultsSavedDesc")),className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-colors",children:j("result.saveResults")})]})]}),(0,a.jsx)(g.Z,{notifications:Z,onRemove:R})]}):(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-4 sm:p-6 lg:p-8 mobile-safe-area",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-4 sm:p-6 lg:p-8 max-w-3xl mx-auto",children:[(0,a.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,a.jsx)("span",{className:"text-xs sm:text-sm font-medium text-gray-600",children:j("progress.questionOf",{current:Math.min(S+1,_),total:_})}),(0,a.jsxs)("span",{className:"text-xs sm:text-sm font-medium text-gray-600",children:[Math.round(Math.min(T,100)),"%"]})]}),(0,a.jsx)("div",{className:"w-full bg-gray-200 rounded-full h-2 sm:h-3",children:(0,a.jsx)("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 h-2 sm:h-3 rounded-full transition-all duration-300",style:{width:"".concat(T,"%")}})})]}),C&&(0,a.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,a.jsx)("h2",{className:"text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-3 sm:mb-4 leading-tight",children:C.title}),C.description&&(0,a.jsx)("p",{className:"text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 leading-relaxed",children:C.description}),(0,a.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:["single"===C.type&&C.options&&(0,a.jsx)("div",{className:"space-y-2 sm:space-y-3",children:C.options.map(e=>(0,a.jsxs)("label",{className:"flex items-center p-3 sm:p-4 border rounded-lg cursor-pointer transition-colors mobile-touch-target ".concat(N[C.id]===e.value?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-gray-400 active:bg-gray-50"),children:[(0,a.jsx)("input",{type:"radio",name:C.id,value:e.value,checked:N[C.id]===e.value,onChange:e=>H(e.target.value),className:"sr-only"}),(0,a.jsx)("div",{className:"w-5 h-5 sm:w-4 sm:h-4 rounded-full border-2 mr-3 flex-shrink-0 ".concat(N[C.id]===e.value?"border-blue-500 bg-blue-500":"border-gray-300"),children:N[C.id]===e.value&&(0,a.jsx)("div",{className:"w-2.5 h-2.5 sm:w-2 sm:h-2 bg-white rounded-full mx-auto mt-0.5"})}),e.icon&&(0,a.jsx)("span",{className:"text-lg sm:text-base mr-2 sm:mr-3 flex-shrink-0",children:e.icon}),(0,a.jsx)("span",{className:"text-sm sm:text-base text-gray-900 leading-relaxed",children:e.label})]},e.value))}),"multiple"===C.type&&C.options&&(0,a.jsx)("div",{className:"space-y-2 sm:space-y-3",children:C.options.map(e=>{let t=Array.isArray(N[C.id])&&N[C.id].includes(e.value);return(0,a.jsxs)("label",{className:"flex items-center p-3 sm:p-4 border rounded-lg cursor-pointer transition-colors mobile-touch-target ".concat(t?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-gray-400 active:bg-gray-50"),children:[(0,a.jsx)("input",{type:"checkbox",checked:t,onChange:t=>{let i;let a=N[C.id]||[],s="none"===e.value||"no_treatment"===e.value;if(a.includes("none")||a.includes("no_treatment"),s)i=t.target.checked?[String(e.value)]:[];else{let s=a.filter(e=>"none"!==e&&"no_treatment"!==e);i=t.target.checked?[...s,e.value]:s.filter(t=>t!==e.value)}H(i)},className:"sr-only"}),(0,a.jsx)("div",{className:"w-5 h-5 sm:w-4 sm:h-4 rounded border-2 mr-3 flex items-center justify-center flex-shrink-0 ".concat(t?"border-blue-500 bg-blue-500":"border-gray-300"),children:t&&(0,a.jsx)(r.Z,{className:"w-3.5 h-3.5 sm:w-3 sm:h-3 text-white"})}),e.icon&&(0,a.jsx)("span",{className:"text-lg sm:text-base mr-2 sm:mr-3 flex-shrink-0",children:e.icon}),(0,a.jsx)("span",{className:"text-sm sm:text-base text-gray-900 leading-relaxed",children:e.label})]},e.value)})}),"scale"===C.type&&(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{className:"px-4 pain-scale-container",children:[(0,a.jsx)("input",{type:"range",min:(null===(t=C.validation)||void 0===t?void 0:t.min)||1,max:(null===(i=C.validation)||void 0===i?void 0:i.max)||10,value:N[C.id]||(null===(b=C.validation)||void 0===b?void 0:b.min)||1,onChange:e=>H(parseInt(e.target.value)),className:"w-full pain-scale cursor-pointer"}),(0,a.jsxs)("div",{className:"flex justify-between text-sm text-neutral-600 mt-2",children:[(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:j("painScale.levels.none",{},"en"===w?"None":"无痛")}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:j("painScale.levels.mild",{},"en"===w?"Mild":"轻微")}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:j("painScale.levels.moderate",{},"en"===w?"Moderate":"中等")}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:j("painScale.levels.severe",{},"en"===w?"Severe":"严重")}),(0,a.jsx)("span",{className:"text-xs sm:text-sm",children:j("painScale.levels.extreme",{},"en"===w?"Extreme":"极重")})]})]}),(0,a.jsx)("div",{className:"text-center",children:(0,a.jsx)("div",{className:"inline-flex items-center bg-gradient-to-r from-blue-100 via-blue-50 to-purple-100 px-8 py-4 rounded-2xl shadow-lg border border-blue-200",children:(0,a.jsxs)("span",{className:"text-xl font-bold text-blue-800",children:[j("painScale.title",{},"en"===w?"Pain Level: ":"疼痛程度："),(0,a.jsx)("span",{className:"text-3xl font-extrabold text-blue-600 mx-2",children:N[C.id]||(null===(f=C.validation)||void 0===f?void 0:f.min)||1}),(0,a.jsxs)("span",{className:"text-base font-medium text-blue-700 ml-2",children:["(",(()=>{var e;let t=N[C.id]||(null===(e=C.validation)||void 0===e?void 0:e.min)||1;return t<=2?j("painScale.levels.none",{},"en"===w?"None":"无痛"):t<=4?j("painScale.levels.mild",{},"en"===w?"Mild":"轻微"):t<=6?j("painScale.levels.moderate",{},"en"===w?"Moderate":"中等"):t<=8?j("painScale.levels.severe",{},"en"===w?"Severe":"严重"):j("painScale.levels.extreme",{},"en"===w?"Extreme":"极重")})(),")"]})]})})}),(0,a.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl overflow-hidden border border-blue-200 shadow-sm",children:[(0,a.jsxs)("h4",{className:"font-semibold text-blue-800 mb-4 flex items-center",children:[(0,a.jsx)("span",{children:"\uD83D\uDCD6"}),(0,a.jsx)("span",{className:"ml-2",children:j("painScale.reference",{},"en"===w?"Pain Level Reference":"疼痛程度参考")})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm text-blue-700",children:[(0,a.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"0-2:"})," ",j("painScale.descriptions.0-2",{},"en"===w?"No pain or very mild discomfort":"无痛或极轻微不适")]})]}),(0,a.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"3-4:"})," ",j("painScale.descriptions.3-4",{},"en"===w?"Mild pain, does not affect daily activities":"轻微疼痛，不影响日常活动")]})]}),(0,a.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-orange-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"5-7:"})," ",j("painScale.descriptions.5-7",{},"en"===w?"Moderate pain, affects some activities":"中等疼痛，影响部分活动")]})]}),(0,a.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[(0,a.jsx)("span",{className:"w-2 h-2 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,a.jsxs)("span",{children:[(0,a.jsx)("strong",{children:"8-10:"})," ",j("painScale.descriptions.8-10",{},"en"===w?"Severe pain, seriously affects life":"严重疼痛，严重影响生活")]})]})]})]})]})]})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-3 sm:gap-0 mt-6 sm:mt-8",children:[(0,a.jsxs)("button",{onClick:()=>{L()},disabled:0===S,className:"flex items-center justify-center sm:justify-start px-4 py-3 sm:py-2 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed mobile-touch-target order-2 sm:order-1",children:[(0,a.jsx)(d.Z,{className:"w-4 h-4 mr-2"}),(0,a.jsx)("span",{className:"text-sm sm:text-base",children:j("navigation.previous")})]}),(0,a.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-3 order-1 sm:order-2",children:[!(null==C?void 0:null===(v=C.validation)||void 0===v?void 0:v.required)&&(0,a.jsx)("button",{onClick:O,className:"px-4 sm:px-6 py-3 sm:py-2 text-gray-600 hover:text-gray-900 mobile-touch-target text-sm sm:text-base",children:j("navigation.skip")}),(0,a.jsx)("button",{onClick:O,disabled:!(()=>{var e;if(!C)return!1;let t=N[C.id];return null===(e=C.validation)||void 0===e||!e.required||("multiple"===C.type?Array.isArray(t)&&t.length>0:null!=t&&""!==t)})(),className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 sm:py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center mobile-touch-target text-sm sm:text-base",children:S>=_-1?(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(r.Z,{className:"w-4 h-4 mr-2"}),j("navigation.finish")]}):(0,a.jsxs)(a.Fragment,{children:[j("navigation.next"),(0,a.jsx)(m.Z,{className:"w-4 h-4 ml-2"})]})})]})]}),P&&(0,a.jsx)("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)(u.Z,{className:"w-5 h-5 text-red-500 mr-2"}),(0,a.jsx)("p",{className:"text-red-600",children:P})]})})]}),(0,a.jsx)(g.Z,{notifications:Z,onRemove:R})]}):(0,a.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8 max-w-2xl mx-auto",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[(0,a.jsx)("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4",children:(0,a.jsx)(n.Z,{className:"w-8 h-8 text-white"})}),(0,a.jsx)("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:j("title",{},"en"===w?"Symptom Assessment Tool":"症状评估工具")}),(0,a.jsx)("p",{className:"text-lg text-gray-600 mb-6",children:j("subtitle",{},"en"===w?"Professional symptom analysis tool to help you understand your health condition":"专业的症状分析工具，帮助您了解自己的健康状况")})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 mb-8",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:j("start.title",{},"en"===w?"Start Assessment":"开始评估")}),(0,a.jsx)("p",{className:"text-blue-800 mb-4",children:j("start.description",{},"en"===w?"This assessment tool will help you understand the severity of your symptoms and provide personalized recommendations":"这个评估工具将帮助您了解症状的严重程度并提供个性化建议")}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:(()=>{try{return[j("start.feature1",{},"en"===w?"12 Professional Questions":"12个专业问题"),j("start.feature2",{},"en"===w?"Personalized Recommendations":"个性化建议"),j("start.feature3",{},"en"===w?"Scientific Assessment":"科学评估"),j("start.feature4",{},"en"===w?"Instant Results":"即时结果")].map((e,t)=>{let i=[o.Z,n.Z,r.Z,l.Z][t]||o.Z;return(0,a.jsxs)("div",{className:"flex items-center space-x-3",children:[(0,a.jsx)(i,{className:"w-5 h-5 text-blue-600"}),(0,a.jsx)("span",{className:"text-blue-800",children:e})]},t)})}catch(e){return null}})()})]}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsxs)("button",{onClick:()=>{M(w)},className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-colors inline-flex items-center space-x-2",children:[(0,a.jsx)(c.Z,{className:"w-5 h-5"}),(0,a.jsx)("span",{children:j("start.startButton",{},"en"===w?"Start Assessment":"开始评估")})]}),(0,a.jsx)("p",{className:"text-sm text-gray-500 mt-4",children:j("start.disclaimer",{},"en"===w?"This tool is for reference only and cannot replace professional medical advice":"此工具仅供参考，不能替代专业医疗建议")})]})]}),(0,a.jsx)(g.Z,{notifications:Z,onRemove:R})]})}},1082:function(e,t,i){"use strict";i.d(t,{f:function(){return n}});var a=i(7437);i(2265);let s=e=>{let{size:t="md",color:i="primary",className:s=""}=e;return(0,a.jsx)("div",{className:"inline-flex items-center justify-center ".concat(s),children:(0,a.jsxs)("svg",{className:"animate-spin ".concat((()=>{switch(t){case"sm":return"w-4 h-4";case"md":default:return"w-6 h-6";case"lg":return"w-8 h-8"}})()," ").concat((()=>{switch(i){case"primary":default:return"text-pink-600";case"secondary":return"text-purple-600";case"white":return"text-white"}})()),xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,a.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,a.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})},n=e=>{let{isLoading:t,message:i="Loading...",children:n}=e;return(0,a.jsxs)("div",{className:"relative",children:[n,t&&(0,a.jsx)("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)(s,{size:"lg"}),(0,a.jsx)("p",{className:"mt-2 text-sm text-gray-600",children:i})]})})]})};t.Z=s},9048:function(e,t,i){"use strict";var a=i(7437);i(2265);var s=i(5302),n=i(2252),o=i(3639),r=i(3245),l=i(2489),c=i(6032);let d=e=>{let{notification:t,onRemove:i}=e;return(0,a.jsx)("div",{className:"\n        ".concat((()=>{switch(t.type){case"success":return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"warning":return"bg-yellow-50 border-yellow-200";case"info":return"bg-blue-50 border-blue-200";default:return"bg-gray-50 border-gray-200"}})(),"\n        border rounded-lg p-4 shadow-lg\n        transform transition-all duration-").concat(c.zn.normal,"\n        animate-slide-in-right\n      "),role:"alert",children:(0,a.jsxs)("div",{className:"flex items-start",children:[(0,a.jsx)("div",{className:"flex-shrink-0",children:(()=>{switch(t.type){case"success":return(0,a.jsx)(s.Z,{className:"w-5 h-5 text-green-500"});case"error":return(0,a.jsx)(n.Z,{className:"w-5 h-5 text-red-500"});case"warning":return(0,a.jsx)(o.Z,{className:"w-5 h-5 text-yellow-500"});case"info":return(0,a.jsx)(r.Z,{className:"w-5 h-5 text-blue-500"});default:return(0,a.jsx)(r.Z,{className:"w-5 h-5 text-gray-500"})}})()}),(0,a.jsxs)("div",{className:"ml-3 flex-1",children:[(0,a.jsx)("h3",{className:"text-sm font-medium ".concat((()=>{switch(t.type){case"success":return"text-green-800";case"error":return"text-red-800";case"warning":return"text-yellow-800";case"info":return"text-blue-800";default:return"text-gray-800"}})()),children:t.title}),(0,a.jsx)("p",{className:"mt-1 text-sm ".concat((()=>{switch(t.type){case"success":return"text-green-700";case"error":return"text-red-700";case"warning":return"text-yellow-700";case"info":return"text-blue-700";default:return"text-gray-700"}})()),children:t.message}),t.actions&&t.actions.length>0&&(0,a.jsx)("div",{className:"mt-3 flex space-x-2",children:t.actions.map((e,t)=>(0,a.jsx)("button",{onClick:e.action,className:"\n                    text-xs font-medium px-3 py-1 rounded-md\n                    ".concat("primary"===e.style?"bg-pink-600 text-white hover:bg-pink-700":"bg-gray-200 text-gray-800 hover:bg-gray-300","\n                    transition-colors duration-").concat(c.zn.fast,"\n                  "),children:e.label},t))})]}),(0,a.jsx)("div",{className:"ml-4 flex-shrink-0",children:(0,a.jsx)("button",{onClick:()=>i(t.id),className:"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 rounded-md","aria-label":"Close notification",children:(0,a.jsx)(l.Z,{className:"w-4 h-4"})})})]})})};t.Z=e=>{let{notifications:t,onRemove:i}=e;return 0===t.length?null:(0,a.jsx)("div",{className:"fixed top-4 right-4 z-50 space-y-2 max-w-sm",children:t.map(e=>(0,a.jsx)(d,{notification:e,onRemove:i},e.id))})}},6032:function(e,t,i){"use strict";i.d(t,{C7:function(){return s},DI:function(){return a},E2:function(){return r},Ju:function(){return n},RZ:function(){return d},Yw:function(){return o},bp:function(){return l},zn:function(){return c}});let a={en:[{value:"lower-abdomen",label:"Lower Abdomen",icon:"\uD83E\uDD30"},{value:"lower-back",label:"Lower Back",icon:"\uD83D\uDD19"},{value:"upper-back",label:"Upper Back",icon:"⬆️"},{value:"thighs",label:"Inner Thighs",icon:"\uD83E\uDDB5"},{value:"head",label:"Head",icon:"\uD83E\uDDE0"},{value:"chest",label:"Chest/Breasts",icon:"\uD83D\uDC97"},{value:"pelvis",label:"Pelvic Area",icon:"\uD83D\uDD3B"},{value:"joints",label:"Joints",icon:"\uD83E\uDDB4"}],zh:[{value:"lower-abdomen",label:"下腹部",icon:"\uD83E\uDD30"},{value:"lower-back",label:"下背部",icon:"\uD83D\uDD19"},{value:"upper-back",label:"上背部",icon:"⬆️"},{value:"thighs",label:"大腿内侧",icon:"\uD83E\uDDB5"},{value:"head",label:"头部",icon:"\uD83E\uDDE0"},{value:"chest",label:"胸部/乳房",icon:"\uD83D\uDC97"},{value:"pelvis",label:"盆腔区域",icon:"\uD83D\uDD3B"},{value:"joints",label:"关节",icon:"\uD83E\uDDB4"}]},s={en:[{value:"cramps",label:"Abdominal Cramps",icon:"\uD83D\uDE23"},{value:"headache",label:"Headache",icon:"\uD83E\uDD15"},{value:"bloating",label:"Bloating",icon:"\uD83C\uDF88"},{value:"backache",label:"Back Pain",icon:"\uD83D\uDD19"},{value:"fatigue",label:"Fatigue",icon:"\uD83D\uDE34"},{value:"nausea",label:"Nausea",icon:"\uD83E\uDD22"},{value:"mood-swings",label:"Mood Swings",icon:"\uD83D\uDE24"},{value:"breast-tenderness",label:"Breast Tenderness",icon:"\uD83D\uDC97"},{value:"diarrhea",label:"Diarrhea",icon:"\uD83D\uDCA9"},{value:"constipation",label:"Constipation",icon:"\uD83D\uDEAB"},{value:"dizziness",label:"Dizziness",icon:"\uD83D\uDCAB"},{value:"hot-flashes",label:"Hot Flashes",icon:"\uD83D\uDD25"},{value:"cold-sweats",label:"Cold Sweats",icon:"\uD83E\uDD76"},{value:"insomnia",label:"Sleep Problems",icon:"\uD83C\uDF19"},{value:"anxiety",label:"Anxiety",icon:"\uD83D\uDE30"},{value:"depression",label:"Low Mood",icon:"\uD83D\uDE22"}],zh:[{value:"cramps",label:"腹部痉挛",icon:"\uD83D\uDE23"},{value:"headache",label:"头痛",icon:"\uD83E\uDD15"},{value:"bloating",label:"腹胀",icon:"\uD83C\uDF88"},{value:"backache",label:"背痛",icon:"\uD83D\uDD19"},{value:"fatigue",label:"疲劳",icon:"\uD83D\uDE34"},{value:"nausea",label:"恶心",icon:"\uD83E\uDD22"},{value:"mood-swings",label:"情绪波动",icon:"\uD83D\uDE24"},{value:"breast-tenderness",label:"乳房胀痛",icon:"\uD83D\uDC97"},{value:"diarrhea",label:"腹泻",icon:"\uD83D\uDCA9"},{value:"constipation",label:"便秘",icon:"\uD83D\uDEAB"},{value:"dizziness",label:"头晕",icon:"\uD83D\uDCAB"},{value:"hot-flashes",label:"潮热",icon:"\uD83D\uDD25"},{value:"cold-sweats",label:"冷汗",icon:"\uD83E\uDD76"},{value:"insomnia",label:"睡眠问题",icon:"\uD83C\uDF19"},{value:"anxiety",label:"焦虑",icon:"\uD83D\uDE30"},{value:"depression",label:"情绪低落",icon:"\uD83D\uDE22"}]},n={en:[{value:"heat-therapy",label:"Heat Therapy",icon:"\uD83D\uDD25"},{value:"cold-therapy",label:"Cold Therapy",icon:"\uD83E\uDDCA"},{value:"massage",label:"Massage",icon:"\uD83D\uDC86"},{value:"exercise",label:"Light Exercise",icon:"\uD83D\uDEB6"},{value:"yoga",label:"Yoga/Stretching",icon:"\uD83E\uDDD8"},{value:"meditation",label:"Meditation",icon:"\uD83D\uDD6F️"},{value:"breathing",label:"Breathing Exercises",icon:"\uD83D\uDCA8"},{value:"bath",label:"Warm Bath",icon:"\uD83D\uDEC1"},{value:"rest",label:"Rest/Sleep",icon:"\uD83D\uDE34"},{value:"hydration",label:"Increased Hydration",icon:"\uD83D\uDCA7"},{value:"diet-change",label:"Dietary Changes",icon:"\uD83E\uDD57"},{value:"herbal-tea",label:"Herbal Tea",icon:"\uD83C\uDF75"},{value:"supplements",label:"Supplements",icon:"\uD83D\uDC8A"},{value:"medication",label:"Pain Medication",icon:"\uD83D\uDC89"},{value:"acupuncture",label:"Acupuncture",icon:"\uD83D\uDCCD"},{value:"aromatherapy",label:"Aromatherapy",icon:"\uD83C\uDF38"}],zh:[{value:"heat-therapy",label:"热敷疗法",icon:"\uD83D\uDD25"},{value:"cold-therapy",label:"冷敷疗法",icon:"\uD83E\uDDCA"},{value:"massage",label:"按摩",icon:"\uD83D\uDC86"},{value:"exercise",label:"轻度运动",icon:"\uD83D\uDEB6"},{value:"yoga",label:"瑜伽/拉伸",icon:"\uD83E\uDDD8"},{value:"meditation",label:"冥想",icon:"\uD83D\uDD6F️"},{value:"breathing",label:"呼吸练习",icon:"\uD83D\uDCA8"},{value:"bath",label:"温水浴",icon:"\uD83D\uDEC1"},{value:"rest",label:"休息/睡眠",icon:"\uD83D\uDE34"},{value:"hydration",label:"增加水分摄入",icon:"\uD83D\uDCA7"},{value:"diet-change",label:"饮食调整",icon:"\uD83E\uDD57"},{value:"herbal-tea",label:"草药茶",icon:"\uD83C\uDF75"},{value:"supplements",label:"营养补充剂",icon:"\uD83D\uDC8A"},{value:"medication",label:"止痛药物",icon:"\uD83D\uDC89"},{value:"acupuncture",label:"针灸",icon:"\uD83D\uDCCD"},{value:"aromatherapy",label:"芳香疗法",icon:"\uD83C\uDF38"}]},o={en:[{value:"period",label:"During Period",icon:"\uD83D\uDD34"},{value:"pre",label:"Pre-menstrual (1-7 days before)",icon:"\uD83D\uDFE1"},{value:"post",label:"Post-menstrual (1-7 days after)",icon:"\uD83D\uDFE2"},{value:"ovulation",label:"Around Ovulation",icon:"\uD83E\uDD5A"},{value:"other",label:"Other Time",icon:"⚪"}],zh:[{value:"period",label:"月经期",icon:"\uD83D\uDD34"},{value:"pre",label:"经前期（前1-7天）",icon:"\uD83D\uDFE1"},{value:"post",label:"经后期（后1-7天）",icon:"\uD83D\uDFE2"},{value:"ovulation",label:"排卵期",icon:"\uD83E\uDD5A"},{value:"other",label:"其他时期",icon:"⚪"}]},r={en:[{value:1,label:"Very Mild",description:"Barely noticeable"},{value:2,label:"Mild",description:"Noticeable but not bothersome"},{value:3,label:"Mild+",description:"Slightly bothersome"},{value:4,label:"Moderate",description:"Bothersome but manageable"},{value:5,label:"Moderate+",description:"Quite bothersome"},{value:6,label:"Strong",description:"Interferes with activities"},{value:7,label:"Strong+",description:"Difficult to ignore"},{value:8,label:"Severe",description:"Dominates thoughts"},{value:9,label:"Very Severe",description:"Unable to function"},{value:10,label:"Unbearable",description:"Worst pain imaginable"}],zh:[{value:1,label:"非常轻微",description:"几乎感觉不到"},{value:2,label:"轻微",description:"能感觉到但不困扰"},{value:3,label:"轻微+",description:"稍有困扰"},{value:4,label:"中等",description:"困扰但可管理"},{value:5,label:"中等+",description:"相当困扰"},{value:6,label:"强烈",description:"影响日常活动"},{value:7,label:"强烈+",description:"难以忽视"},{value:8,label:"严重",description:"占据思维"},{value:9,label:"非常严重",description:"无法正常功能"},{value:10,label:"无法忍受",description:"能想象的最严重疼痛"}]},l={en:[{value:1,label:"Not Helpful",icon:"❌"},{value:2,label:"Slightly Helpful",icon:"\uD83D\uDFE1"},{value:3,label:"Moderately Helpful",icon:"\uD83D\uDFE0"},{value:4,label:"Very Helpful",icon:"\uD83D\uDFE2"},{value:5,label:"Extremely Helpful",icon:"✅"}],zh:[{value:1,label:"无效",icon:"❌"},{value:2,label:"稍有帮助",icon:"\uD83D\uDFE1"},{value:3,label:"中等帮助",icon:"\uD83D\uDFE0"},{value:4,label:"很有帮助",icon:"\uD83D\uDFE2"},{value:5,label:"极其有效",icon:"✅"}]},c={fast:150,normal:300,slow:500};new Date().toISOString().split("T")[0];let d={en:{required:"This field is required",invalidDate:"Please enter a valid date",futureDate:"Date cannot be in the future",painLevelRange:"Pain level must be between 1 and 10",durationRange:"Duration must be between 0 and 1440 minutes",effectivenessRange:"Effectiveness must be between 1 and 5",notesLength:"Notes cannot exceed 500 characters",storageError:"Failed to save data. Please try again.",loadError:"Failed to load data. Please refresh the page.",exportError:"Failed to export data. Please try again.",networkError:"Network error. Please check your connection."},zh:{required:"此字段为必填项",invalidDate:"请输入有效日期",futureDate:"日期不能是未来时间",painLevelRange:"疼痛等级必须在1-10之间",durationRange:"持续时间必须在0-1440分钟之间",effectivenessRange:"有效性必须在1-5之间",notesLength:"备注不能超过500个字符",storageError:"保存数据失败，请重试",loadError:"加载数据失败，请刷新页面",exportError:"导出数据失败，请重试",networkError:"网络错误，请检查连接"}}},8278:function(e,t,i){"use strict";i.d(t,{A0:function(){return s}});var a=i(2586);let s=e=>{let t=(0,a.useTranslations)("interactiveTools"),i=(0,a.useLocale)();return{t:e?i=>t("".concat(e,".").concat(i)):t,locale:i,isZh:"zh"===i,isEn:"en"===i}}},2475:function(e,t,i){"use strict";i.d(t,{z:function(){return s}});var a=i(2265);let s=()=>{let[e,t]=(0,a.useState)([]),i=(0,a.useCallback)(e=>{var i;let a="notification_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),n={...e,id:a,duration:null!==(i=e.duration)&&void 0!==i?i:5e3};return t(e=>[...e,n]),n.duration&&n.duration>0&&setTimeout(()=>{s(a)},n.duration),a},[]),s=(0,a.useCallback)(e=>{t(t=>t.filter(t=>t.id!==e))},[]),n=(0,a.useCallback)(()=>{t([])},[]),o=(0,a.useCallback)((e,t,a)=>i({type:"success",title:e,message:t,duration:a}),[i]),r=(0,a.useCallback)((e,t,a)=>i({type:"error",title:e,message:t,duration:null!=a?a:8e3}),[i]),l=(0,a.useCallback)((e,t,a)=>i({type:"warning",title:e,message:t,duration:a}),[i]),c=(0,a.useCallback)((e,t,a)=>i({type:"info",title:e,message:t,duration:a}),[i]);return{notifications:e,addNotification:i,removeNotification:s,clearAllNotifications:n,addSuccessNotification:o,addErrorNotification:r,addWarningNotification:l,addInfoNotification:c}}}},function(e){e.O(0,[2972,2586,3418,1863,5388,2971,2117,1744],function(){return e(e.s=9417)}),_N_E=e.O()}]);