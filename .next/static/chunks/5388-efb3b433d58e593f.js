"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5388],{5388:function(e,t,n){n.d(t,{o:function(){return r}});var a=n(2265),s=n(1863),i=n(8594);let r=e=>{let[t,n]=(0,a.useState)(null),[r,l]=(0,a.useState)(0),[o,m]=(0,a.useState)(null),[c,d]=(0,a.useState)(!1),[u,g]=(0,a.useState)(null),p=(0,i.I6)(e||"anonymous","assessment_session");(0,a.useEffect)(()=>{let e=(0,i.mO)(p);e&&!e.completedAt&&(n(e),l(e.answers.length))},[p]),(0,a.useEffect)(()=>{t&&(0,i.RY)(p,t)},[t,p]);let f=t?s.n[t.locale]||s.n.en:[],h=f[r]||null,y=r>=f.length,v=f.length>0?Math.min(r/f.length*100,100):0,S=(0,a.useCallback)(e=>{n({id:"assessment_".concat(Date.now(),"_").concat(Math.random().toString(36).substr(2,9)),answers:[],startedAt:new Date().toISOString(),locale:e}),l(0),m(null),g(null)},[]),w=(0,a.useCallback)(e=>{t&&n(t=>{if(!t)return t;let n=[...t.answers.filter(t=>t.questionId!==e.questionId),e];return{...t,answers:n}})},[t]),b=(0,a.useCallback)(e=>{e>=0&&e<=f.length&&l(e)},[f.length]),M=(0,a.useCallback)(()=>{r>0&&l(e=>e-1)},[r]),A=(0,a.useCallback)(()=>{r<f.length&&l(e=>e+1)},[r,f.length]),E=(0,a.useCallback)((e,t)=>{let n=0,a=0;return t.forEach(t=>{let s=e.find(e=>e.questionId===t.id);if(s){if(a+=10*t.weight,"scale"===t.type)n+=s.value*t.weight;else if("single"===t.type){var i;let e=null===(i=t.options)||void 0===i?void 0:i.find(e=>e.value===s.value);e&&void 0!==e.weight&&(n+=e.weight*t.weight)}else"multiple"===t.type&&s.value.forEach(e=>{var a;let s=null===(a=t.options)||void 0===a?void 0:a.find(t=>t.value===e);s&&void 0!==s.weight&&(n+=s.weight*t.weight)})}}),Math.min(n,a)},[]),I=(0,a.useCallback)((e,t,n,a,s)=>{var i,r,l,o,m,c,d,u;let g=[],p="en"===s;if(t>=80){let e=a?a("recommendations.emergencyMedical.title"):p?"Seek Immediate Medical Care":"建议立即就医",t=a?a("recommendations.emergencyMedical.description"):p?"Your symptoms may require professional medical evaluation and treatment":"您的症状可能需要专业医疗评估和治疗",n=a?a("recommendations.emergencyMedical.timeframe"):p?"Immediately":"立即",s=a?a("recommendations.emergencyMedical.actionSteps"):p?["Contact your gynecologist","If pain is severe, consider emergency care","Keep a detailed symptom diary"]:["联系您的妇科医生","如果疼痛剧烈，考虑急诊就医","记录详细的症状日志"],i=e&&!e.includes("recommendations.")?e:p?"Seek Immediate Medical Care":"建议立即就医",r=t&&!t.includes("recommendations.")?t:p?"Your symptoms may require professional medical evaluation and treatment":"您的症状可能需要专业医疗评估和治疗",l=n&&!n.includes("recommendations.")?n:p?"Immediately":"立即",o=!Array.isArray(s)||!(s.length>0)||(null===(c=s[0])||void 0===c?void 0:null===(m=c.includes)||void 0===m?void 0:m.call(c,"recommendations."))?p?["Contact your gynecologist","If pain is severe, consider emergency care","Keep a detailed symptom diary"]:["联系您的妇科医生","如果疼痛剧烈，考虑急诊就医","记录详细的症状日志"]:s;g.push({id:"emergency_medical",category:"medical",title:i,description:r,priority:"high",timeframe:l,actionSteps:Array.isArray(o)?o:[o]})}if(t>=40){let e=a?a("recommendations.painManagement.title"):p?"Pain Management Strategies":"疼痛管理策略",t=a?a("recommendations.painManagement.description"):p?"Multiple methods can help relieve menstrual pain":"多种方法可以帮助缓解经期疼痛",n=a?a("recommendations.painManagement.timeframe"):p?"Immediately available":"立即可用",s=a?a("recommendations.painManagement.actionSteps"):p?["Use heating pads or hot water bottles","Try light exercise like walking","Consider over-the-counter pain relievers (follow instructions)"]:["使用热敷垫或热水袋","尝试轻度运动如散步","考虑非处方止痛药（按说明使用）"],i=e&&!e.includes("recommendations.")?e:p?"Pain Management Strategies":"疼痛管理策略",r=t&&!t.includes("recommendations.")?t:p?"Multiple methods can help relieve menstrual pain":"多种方法可以帮助缓解经期疼痛",l=n&&!n.includes("recommendations.")?n:p?"Immediately available":"立即可用",o=!Array.isArray(s)||!(s.length>0)||(null===(u=s[0])||void 0===u?void 0:null===(d=u.includes)||void 0===d?void 0:d.call(u,"recommendations."))?p?["Use heating pads or hot water bottles","Try light exercise like walking","Consider over-the-counter pain relievers (follow instructions)"]:["使用热敷垫或热水袋","尝试轻度运动如散步","考虑非处方止痛药（按说明使用）"]:s;g.push({id:"pain_management",category:"immediate",title:i,description:r,priority:"high",timeframe:l,actionSteps:Array.isArray(o)?o:[o]})}let f=a?a("recommendations.lifestyleChanges.title"):p?"Lifestyle Adjustments":"生活方式调整",h=a?a("recommendations.lifestyleChanges.description"):p?"Long-term lifestyle changes can significantly improve symptoms":"长期的生活方式改变可以显著改善症状",y=a?a("recommendations.lifestyleChanges.timeframe"):p?"2-3 months to see effects":"2-3个月见效",v=a?a("recommendations.lifestyleChanges.actionSteps"):p?["Maintain regular exercise habits","Ensure adequate sleep","Learn stress management techniques","Maintain a balanced diet"]:["保持规律的运动习惯","确保充足的睡眠","学习压力管理技巧","保持均衡饮食"],S=f&&!f.includes("recommendations.")?f:p?"Lifestyle Adjustments":"生活方式调整",w=h&&!h.includes("recommendations.")?h:p?"Long-term lifestyle changes can significantly improve symptoms":"长期的生活方式改变可以显著改善症状",b=y&&!y.includes("recommendations.")?y:p?"2-3 months to see effects":"2-3个月见效",M=!Array.isArray(v)||!(v.length>0)||(null===(r=v[0])||void 0===r?void 0:null===(i=r.includes)||void 0===i?void 0:i.call(r,"recommendations."))?p?["Maintain regular exercise habits","Ensure adequate sleep","Learn stress management techniques","Maintain a balanced diet"]:["保持规律的运动习惯","确保充足的睡眠","学习压力管理技巧","保持均衡饮食"]:v;g.push({id:"lifestyle_changes",category:"lifestyle",title:S,description:w,priority:"medium",timeframe:b,actionSteps:Array.isArray(M)?M:[M]});let A=a?a("recommendations.selfcarePractices.title"):p?"Self-Care Practices":"自我护理实践",E=a?a("recommendations.selfcarePractices.description"):p?"Daily self-care can help you better manage symptoms":"日常的自我护理可以帮助您更好地管理症状",I=a?a("recommendations.selfcarePractices.timeframe"):p?"Ongoing":"持续进行",k=a?a("recommendations.selfcarePractices.actionSteps"):p?["Practice deep breathing and meditation","Use pain tracker to record symptoms","Build a support network","Learn relaxation techniques"]:["练习深呼吸和冥想","使用疼痛追踪器记录症状","建立支持网络","学习放松技巧"],C=A&&!A.includes("recommendations.")?A:p?"Self-Care Practices":"自我护理实践",_=E&&!E.includes("recommendations.")?E:p?"Daily self-care can help you better manage symptoms":"日常的自我护理可以帮助您更好地管理症状",D=I&&!I.includes("recommendations.")?I:p?"Ongoing":"持续进行",O=!Array.isArray(k)||!(k.length>0)||(null===(o=k[0])||void 0===o?void 0:null===(l=o.includes)||void 0===l?void 0:l.call(o,"recommendations."))?p?["Practice deep breathing and meditation","Use pain tracker to record symptoms","Build a support network","Learn relaxation techniques"]:["练习深呼吸和冥想","使用疼痛追踪器记录症状","建立支持网络","学习放松技巧"]:k;return g.push({id:"selfcare_practices",category:"selfcare",title:C,description:_,priority:"medium",timeframe:D,actionSteps:Array.isArray(O)?O:[O]}),g},[]),k=(0,a.useCallback)(e=>{if(!t)return null;d(!0);try{let a,s,i,r;let l=E(t.answers,f),o=f.reduce((e,t)=>e+10*t.weight,0),c=o>0?l/o*100:0,d="en"===t.locale;c>=80?(a="emergency",s="emergency",i=e?e("resultMessages.emergency"):d?"Your symptoms are quite severe. We recommend consulting a healthcare professional as soon as possible.":"您的症状较为严重，建议尽快咨询医疗专业人士。",r=e?e("resultMessages.emergencySummary"):d?"Assessment indicates you may need professional medical attention.":"评估显示您可能需要专业医疗关注。"):c>=60?(a="severe",s="severe",i=e?e("resultMessages.severe"):d?"Your symptoms are quite serious. We recommend adopting comprehensive management strategies.":"您的症状比较严重，建议采取综合管理策略。",r=e?e("resultMessages.severeSummary"):d?"Your symptoms require active management and possible medical intervention.":"您的症状需要积极的管理和可能的医疗干预。"):c>=40?(a="moderate",s="moderate",i=e?e("resultMessages.moderate"):d?"You have moderate symptoms that can be managed through various methods.":"您有中等程度的症状，可以通过多种方法进行管理。",r=e?e("resultMessages.moderateSummary"):d?"Your symptoms are manageable with recommended relief strategies.":"您的症状是可以管理的，建议采用多种缓解策略。"):(a="mild",s="mild",i=e?e("resultMessages.mild"):d?"Your symptoms are relatively mild and can be well managed through simple self-care.":"您的症状相对较轻，通过简单的自我护理就能很好地管理。",r=e?e("resultMessages.mildSummary"):d?"Your symptoms are mild and can be improved through lifestyle adjustments.":"您的症状较轻，可以通过生活方式调整来改善。");let u=I(l,c,t.answers,e,t.locale),g={sessionId:t.id,type:s,severity:a,score:l,maxScore:o,percentage:c,recommendations:u,emergency:c>=80,message:i,summary:r,relatedArticles:["/articles/menstrual-pain-management","/articles/lifestyle-tips-for-period-health","/articles/when-to-see-a-doctor"],nextSteps:e?[e("result.nextSteps.trackSymptoms")||(d?"Use pain tracker to record symptoms":"使用疼痛追踪器记录症状"),e("result.nextSteps.tryRecommendations")||(d?"Try recommended relief methods":"尝试推荐的缓解方法"),e("result.nextSteps.consultDoctor")||(d?"Consult a doctor if symptoms persist or worsen":"如果症状持续或恶化，请咨询医生")]:d?["Use pain tracker to record symptoms","Try recommended relief methods","Consult a doctor if symptoms persist or worsen"]:["使用疼痛追踪器记录症状","尝试推荐的缓解方法","如果症状持续或恶化，请咨询医生"],createdAt:new Date().toISOString()},p={...t,result:g,completedAt:new Date().toISOString()};return n(p),m(g),g}catch(a){let n=(null==t?void 0:t.locale)==="en";return g(e?e("messages.assessmentFailed"):n?"An error occurred while completing the assessment. Please try again.":"评估完成时出现错误，请重试。"),null}finally{d(!1)}},[t,E,I,f]),C=(0,a.useCallback)(()=>{n(null),l(0),m(null),g(null),localStorage.removeItem(p)},[p]);return{currentSession:t,currentQuestionIndex:r,currentQuestion:h,isComplete:y,progress:v,totalQuestions:f.length,startAssessment:S,answerQuestion:w,goToQuestion:b,goToPreviousQuestion:M,goToNextQuestion:A,completeAssessment:k,resetAssessment:C,result:o,isLoading:c,error:u}}},8594:function(e,t,n){n.d(t,{B$:function(){return u},I6:function(){return m},RY:function(){return c},Yp:function(){return i},i5:function(){return r},mO:function(){return d},mn:function(){return a}});let a=e=>{let t="string"==typeof e?new Date(e):e,n=t.getFullYear(),a=String(t.getMonth()+1).padStart(2,"0"),s=String(t.getDate()).padStart(2,"0");return"".concat(n,"-").concat(a,"-").concat(s)},s=e=>!isNaN(new Date(e).getTime())&&!!e.match(/^\d{4}-\d{2}-\d{2}$/),i=e=>{let t=[];return e.date?s(e.date)||t.push({field:"date",message:"Invalid date format",code:"INVALID_FORMAT"}):t.push({field:"date",message:"Date is required",code:"REQUIRED"}),void 0===e.painLevel||null===e.painLevel?t.push({field:"painLevel",message:"Pain level is required",code:"REQUIRED"}):(e.painLevel<1||e.painLevel>10)&&t.push({field:"painLevel",message:"Pain level must be between 1 and 10",code:"OUT_OF_RANGE"}),void 0!==e.duration&&(e.duration<0||e.duration>1440)&&t.push({field:"duration",message:"Duration must be between 0 and 1440 minutes",code:"OUT_OF_RANGE"}),void 0!==e.effectiveness&&(e.effectiveness<1||e.effectiveness>5)&&t.push({field:"effectiveness",message:"Effectiveness must be between 1 and 5",code:"OUT_OF_RANGE"}),t},r=e=>{if(0===e.length)return{totalEntries:0,averagePain:0,maxPain:0,minPain:0,mostCommonSymptoms:[],mostEffectiveRemedies:[],painFrequency:{},trendDirection:"stable"};let t=e.map(e=>e.painLevel),n=t.reduce((e,t)=>e+t,0)/t.length,a={};e.forEach(e=>{e.symptoms.forEach(e=>{a[e]=(a[e]||0)+1})});let s=Object.entries(a).sort((e,t)=>{let[,n]=e,[,a]=t;return a-n}).slice(0,5).map(e=>{let[t]=e;return t}),i={};e.forEach(e=>{e.effectiveness&&e.remedies.length>0&&e.remedies.forEach(t=>{i[t]||(i[t]={total:0,count:0}),i[t].total+=e.effectiveness,i[t].count+=1})});let r=Object.entries(i).map(e=>{let[t,n]=e;return{remedy:t,avgEffectiveness:n.total/n.count}}).sort((e,t)=>t.avgEffectiveness-e.avgEffectiveness).slice(0,5).map(e=>e.remedy),m={};t.forEach(e=>{let t=l(e);m[t]=(m[t]||0)+1});let c=o(e);return{totalEntries:e.length,averagePain:Math.round(10*n)/10,maxPain:Math.max(...t),minPain:Math.min(...t),mostCommonSymptoms:s,mostEffectiveRemedies:r,painFrequency:m,trendDirection:c}},l=e=>e<=3?"Mild (1-3)":e<=6?"Moderate (4-6)":"Severe (7-10)",o=e=>{if(e.length<4)return"stable";let t=[...e].sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()),n=t.slice(0,Math.floor(t.length/2)),a=t.slice(Math.floor(t.length/2)),s=n.reduce((e,t)=>e+t.painLevel,0)/n.length,i=a.reduce((e,t)=>e+t.painLevel,0)/a.length-s;return i>.5?"worsening":i<-.5?"improving":"stable"},m=(e,t)=>"periodhub_".concat(t,"_").concat(e||"anonymous"),c=(e,t)=>{try{let n={data:t,version:"1.0.0",timestamp:new Date().toISOString()};return localStorage.setItem(e,JSON.stringify(n)),!0}catch(e){return!1}},d=e=>{try{let t=localStorage.getItem(e);if(!t)return null;return JSON.parse(t).data}catch(e){return null}},u=e=>{try{return localStorage.removeItem(e),!0}catch(e){return!1}}}}]);