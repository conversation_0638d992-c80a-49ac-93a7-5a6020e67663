exports.id=2810,exports.ids=[2810],exports.modules={7435:(e,t,r)=>{Promise.resolve().then(r.bind(r,51817))},18256:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,12994,23)),Promise.resolve().then(r.t.bind(r,96114,23)),Promise.resolve().then(r.t.bind(r,9727,23)),Promise.resolve().then(r.t.bind(r,79671,23)),Promise.resolve().then(r.t.bind(r,41868,23)),Promise.resolve().then(r.t.bind(r,84759,23))},51817:(e,t,r)=>{"use strict";r.d(t,{AppProvider:()=>L});var s=r(10326),a=r(17577),n=r(37202),o=r(21405),i=r(95920),l=r(94244),c=r(68408),d=r(85251),m=r(62750);let h={theme:"system",language:"zh",fontSize:"medium",animations:!0,notifications:{browser:!0,email:!1,sms:!1},privacy:{analytics:!0,cookies:!0,dataSharing:!1},accessibility:{highContrast:!1,reducedMotion:!1,screenReader:!1}},u={preferences:h,ui:{sidebarOpen:!1,loading:!1,error:null,modal:{isOpen:!1,type:null,data:null},toast:[]},data:{lastSync:null,version:"1.0.0",buildTime:new Date().toISOString()},performance:{pageLoadTime:0,apiResponseTimes:{},errorCount:0}},p=()=>`${Date.now()}-${Math.random().toString(36).substr(2,9)}`,x=(0,c.U)()((0,d.mW)((0,d.tJ)((0,m.n)((e,t)=>({...u,updatePreferences:t=>{e(e=>{Object.assign(e.preferences,t)})},resetPreferences:()=>{e(e=>{e.preferences=h})},setSidebarOpen:t=>{e(e=>{e.ui.sidebarOpen=t})},toggleSidebar:()=>{e(e=>{e.ui.sidebarOpen=!e.ui.sidebarOpen})},setLoading:t=>{e(e=>{e.ui.loading=t})},setError:t=>{e(e=>{e.ui.error=t})},openModal:(t,r=null)=>{e(e=>{e.ui.modal={isOpen:!0,type:t,data:r}})},closeModal:()=>{e(e=>{e.ui.modal={isOpen:!1,type:null,data:null}})},addToast:r=>{let s=p();return e(e=>{e.ui.toast.push({...r,id:s})}),0!==r.duration&&setTimeout(()=>{t().removeToast(s)},r.duration||5e3),s},removeToast:t=>{e(e=>{e.ui.toast=e.ui.toast.filter(e=>e.id!==t)})},clearToasts:()=>{e(e=>{e.ui.toast=[]})},updateLastSync:()=>{e(e=>{e.data.lastSync=new Date().toISOString()})},recordPageLoadTime:t=>{e(e=>{e.performance.pageLoadTime=t})},recordApiResponseTime:(t,r)=>{e(e=>{e.performance.apiResponseTimes[t]=r})},incrementErrorCount:()=>{e(e=>{e.performance.errorCount+=1})},resetPerformanceMetrics:()=>{e(e=>{e.performance={pageLoadTime:0,apiResponseTimes:{},errorCount:0}})}})),{name:"periodhub-app-store",storage:(0,d.FL)(()=>localStorage),partialize:e=>({preferences:e.preferences,data:{lastSync:e.data.lastSync,version:e.data.version}})}),{name:"PeriodHub App Store"})),g=()=>x(e=>e.ui.toast);class f extends a.Component{constructor(e){super(e),this.retryCount=0,this.maxRetries=3,this.reportError=async(e,t)=>{try{e.message,e.stack,t.componentStack,new Date().toISOString(),window.location.href,navigator.userAgent,this.state.errorId,this.props.level}catch(e){}},this.handleRetry=()=>{this.retryCount<this.maxRetries?(this.retryCount++,this.setState({hasError:!1,error:null,errorInfo:null,errorId:null})):window.location.reload()},this.handleGoHome=()=>{window.location.href="/"},this.handleReportBug=()=>{let{error:e,errorInfo:t,errorId:r}=this.state;e?.message,e?.stack,t?.componentStack,window.location.href,new Date().toISOString();let s=encodeURIComponent(`Bug Report: ${e?.message||"Unknown Error"}`),a=encodeURIComponent(`
Error ID: ${r}
URL: ${window.location.href}
Time: ${new Date().toISOString()}

Error Details:
${e?.message}

Stack Trace:
${e?.stack}

Component Stack:
${t?.componentStack}

Please describe what you were doing when this error occurred:
[Your description here]
    `);window.open(`mailto:<EMAIL>?subject=${s}&body=${a}`)},this.state={hasError:!1,error:null,errorInfo:null,errorId:null}}static getDerivedStateFromError(e){return{hasError:!0,error:e,errorId:`error_${Date.now()}_${Math.random().toString(36).substr(2,9)}`}}componentDidCatch(e,t){this.setState({errorInfo:t}),x.getState().incrementErrorCount(),this.props.onError&&this.props.onError(e,t),this.reportError(e,t)}render(){if(this.state.hasError){if(this.props.fallback)return this.props.fallback;let{error:e,errorInfo:t,errorId:r}=this.state,{level:a="component",showDetails:c=!1}=this.props;return s.jsx("div",{className:"min-h-[400px] flex items-center justify-center p-6",children:s.jsx("div",{className:"max-w-md w-full bg-white rounded-lg shadow-lg border border-red-200",children:(0,s.jsxs)("div",{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3 mb-4",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx(n.Z,{className:"w-8 h-8 text-red-500"})}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:"critical"===a?"严重错误":"出现错误"}),s.jsx("p",{className:"text-sm text-gray-600",children:"critical"===a?"应用遇到了严重问题":"这个组件遇到了问题"})]})]}),(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("p",{className:"text-sm text-gray-700 mb-2",children:"我们很抱歉给您带来不便。错误已被记录，我们会尽快修复。"}),r&&(0,s.jsxs)("p",{className:"text-xs text-gray-500 font-mono bg-gray-50 p-2 rounded",children:["错误ID: ",r]}),(c||!1)&&e&&(0,s.jsxs)("details",{className:"mt-3",children:[s.jsx("summary",{className:"text-sm text-gray-600 cursor-pointer hover:text-gray-800",children:"查看技术详情"}),(0,s.jsxs)("div",{className:"mt-2 p-3 bg-gray-50 rounded text-xs font-mono",children:[(0,s.jsxs)("div",{className:"mb-2",children:[s.jsx("strong",{children:"错误信息:"}),s.jsx("div",{className:"text-red-600",children:e.message})]}),e.stack&&(0,s.jsxs)("div",{className:"mb-2",children:[s.jsx("strong",{children:"堆栈跟踪:"}),s.jsx("pre",{className:"whitespace-pre-wrap text-gray-600 max-h-32 overflow-y-auto",children:e.stack})]}),t?.componentStack&&(0,s.jsxs)("div",{children:[s.jsx("strong",{children:"组件堆栈:"}),s.jsx("pre",{className:"whitespace-pre-wrap text-gray-600 max-h-32 overflow-y-auto",children:t.componentStack})]})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[this.retryCount<this.maxRetries?(0,s.jsxs)("button",{onClick:this.handleRetry,className:"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[s.jsx(o.Z,{className:"w-4 h-4"}),(0,s.jsxs)("span",{children:["重试 (",this.maxRetries-this.retryCount," 次剩余)"]})]}):(0,s.jsxs)("button",{onClick:()=>window.location.reload(),className:"w-full flex items-center justify-center space-x-2 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:[s.jsx(o.Z,{className:"w-4 h-4"}),s.jsx("span",{children:"刷新页面"})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[(0,s.jsxs)("button",{onClick:this.handleGoHome,className:"flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors",children:[s.jsx(i.Z,{className:"w-4 h-4"}),s.jsx("span",{children:"返回首页"})]}),(0,s.jsxs)("button",{onClick:this.handleReportBug,className:"flex-1 flex items-center justify-center space-x-2 px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 transition-colors",children:[s.jsx(l.Z,{className:"w-4 h-4"}),s.jsx("span",{children:"报告问题"})]})]})]})]})})})}return this.props.children}}var b=r(60962),w=r(54659),v=r(87888),y=r(18019),j=r(94019);let k=({id:e,type:t,message:r,title:o,duration:i=5e3,action:l,closable:c=!0,onClose:d})=>{let[m,h]=(0,a.useState)(!1),[u,p]=(0,a.useState)(!1);(0,a.useEffect)(()=>{let e=setTimeout(()=>h(!0),10);return()=>clearTimeout(e)},[]),(0,a.useEffect)(()=>{if(i>0){let e=setTimeout(()=>{x()},i);return()=>clearTimeout(e)}},[i]);let x=()=>{p(!0),setTimeout(()=>{d(e)},300)},g=()=>{switch(t){case"success":return"text-green-800";case"error":return"text-red-800";case"warning":return"text-yellow-800";case"info":return"text-blue-800";default:return"text-gray-800"}};return(0,s.jsxs)("div",{className:`
        transform transition-all duration-300 ease-in-out
        ${m&&!u?"translate-x-0 opacity-100":"translate-x-full opacity-0"}
        max-w-sm w-full ${(()=>{switch(t){case"success":return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"warning":return"bg-yellow-50 border-yellow-200";case"info":return"bg-blue-50 border-blue-200";default:return"bg-gray-50 border-gray-200"}})()} border rounded-lg shadow-lg p-4 mb-3
      `,children:[(0,s.jsxs)("div",{className:"flex items-start space-x-3",children:[s.jsx("div",{className:"flex-shrink-0 mt-0.5",children:(()=>{switch(t){case"success":return s.jsx(w.Z,{className:"w-5 h-5 text-green-500"});case"error":return s.jsx(v.Z,{className:"w-5 h-5 text-red-500"});case"warning":return s.jsx(n.Z,{className:"w-5 h-5 text-yellow-500"});case"info":return s.jsx(y.Z,{className:"w-5 h-5 text-blue-500"});default:return s.jsx(y.Z,{className:"w-5 h-5 text-gray-500"})}})()}),(0,s.jsxs)("div",{className:"flex-1 min-w-0",children:[o&&s.jsx("h4",{className:`text-sm font-semibold ${g()} mb-1`,children:o}),s.jsx("p",{className:`text-sm ${g()}`,children:r}),l&&s.jsx("button",{onClick:l.onClick,className:`
                mt-2 text-xs font-medium underline hover:no-underline
                ${"success"===t?"text-green-700 hover:text-green-800":""}
                ${"error"===t?"text-red-700 hover:text-red-800":""}
                ${"warning"===t?"text-yellow-700 hover:text-yellow-800":""}
                ${"info"===t?"text-blue-700 hover:text-blue-800":""}
              `,children:l.label})]}),c&&s.jsx("button",{onClick:x,className:`
              flex-shrink-0 p-1 rounded-md hover:bg-white/50 transition-colors
              ${g()}
            `,children:s.jsx(j.Z,{className:"w-4 h-4"})})]}),i>0&&s.jsx("div",{className:"mt-3 w-full bg-white/30 rounded-full h-1",children:s.jsx("div",{className:`
              h-1 rounded-full transition-all ease-linear
              ${"success"===t?"bg-green-500":""}
              ${"error"===t?"bg-red-500":""}
              ${"warning"===t?"bg-yellow-500":""}
              ${"info"===t?"bg-blue-500":""}
            `,style:{width:"100%",animation:`shrink ${i}ms linear`}})})]})},N=()=>{let[e,t]=(0,a.useState)(!1),r=g(),n=x(e=>e.removeToast);if((0,a.useEffect)(()=>{t(!0)},[]),!e)return null;let o=s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2",children:r.map(e=>s.jsx(k,{id:e.id,type:e.type,message:e.message,duration:e.duration,onClose:n},e.id))});return(0,b.createPortal)(o,document.body)},E=`
  @keyframes shrink {
    from {
      width: 100%;
    }
    to {
      width: 0%;
    }
  }
`;if("undefined"!=typeof document){let e=document.createElement("style");e.textContent=E,document.head.appendChild(e)}let T=({isOpen:e,onClose:t,type:r="default",title:o,content:i,children:l,size:c="md",closable:d=!0,maskClosable:m=!0,showFooter:h=!1,confirmText:u="确认",cancelText:p="取消",onConfirm:x,onCancel:g,className:f="",zIndex:w=1e3})=>{let[v,k]=(0,a.useState)(!1),[N,E]=(0,a.useState)(!1);(0,a.useEffect)(()=>{if(e)k(!0),E(!0),document.body.style.overflow="hidden";else{E(!1);let e=setTimeout(()=>{k(!1),document.body.style.overflow=""},200);return()=>clearTimeout(e)}return()=>{document.body.style.overflow=""}},[e]);let T=(0,a.useCallback)(e=>{e.target===e.currentTarget&&m&&t()},[m,t]),S=(0,a.useCallback)(async()=>{if(x)try{await x(),t()}catch(e){}else t()},[x,t]),P=(0,a.useCallback)(()=>{g&&g(),t()},[g,t]);if(!v)return null;let C=(0,s.jsxs)("div",{className:"fixed inset-0 flex items-center justify-center p-4",style:{zIndex:w},onClick:T,children:[s.jsx("div",{className:`
          absolute inset-0 bg-black transition-opacity duration-200
          ${N?"opacity-50":"opacity-0"}
        `}),(0,s.jsxs)("div",{className:`
          relative bg-white rounded-lg shadow-xl w-full ${(()=>{switch(c){case"sm":return"max-w-sm";case"md":default:return"max-w-md";case"lg":return"max-w-lg";case"xl":return"max-w-xl";case"full":return"max-w-full mx-4"}})()}
          transform transition-all duration-200
          ${N?"scale-100 opacity-100":"scale-95 opacity-0"}
          ${f}
        `,children:[(o||d)&&(0,s.jsxs)("div",{className:"flex items-center justify-between p-6 border-b border-gray-200",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[(()=>{switch(r){case"confirm":return s.jsx(n.Z,{className:"w-6 h-6 text-yellow-500"});case"alert":return s.jsx(y.Z,{className:"w-6 h-6 text-blue-500"});default:return null}})(),o&&s.jsx("h3",{className:"text-lg font-semibold text-gray-900",children:o})]}),d&&s.jsx("button",{onClick:t,className:"p-1 text-gray-400 hover:text-gray-600 transition-colors",children:s.jsx(j.Z,{className:"w-5 h-5"})})]}),s.jsx("div",{className:"p-6",children:i||l}),h&&(0,s.jsxs)("div",{className:"flex justify-end space-x-3 p-6 border-t border-gray-200",children:[s.jsx("button",{onClick:P,className:"px-4 py-2 text-sm font-medium text-gray-700 bg-gray-100 border border-gray-300 rounded-md hover:bg-gray-200 transition-colors",children:p}),s.jsx("button",{onClick:S,className:"px-4 py-2 text-sm font-medium text-white bg-blue-600 border border-transparent rounded-md hover:bg-blue-700 transition-colors",children:u})]})]})]});return(0,b.createPortal)(C,document.body)},S=()=>{let e=x(e=>e.ui.modal),t=x(e=>e.closeModal);return s.jsx(T,{isOpen:e.isOpen,onClose:t,...e.data})};class P{constructor(){this.metrics=[],this.observers=[],this.isEnabled=!0}initializeObservers(){if("PerformanceObserver"in window){let e=new PerformanceObserver(e=>{for(let t of e.getEntries())"navigation"===t.entryType&&this.recordPageLoad(t)});e.observe({entryTypes:["navigation"]}),this.observers.push(e);let t=new PerformanceObserver(e=>{for(let t of e.getEntries())"resource"===t.entryType&&this.recordResourceLoad(t)});t.observe({entryTypes:["resource"]}),this.observers.push(t);let r=new PerformanceObserver(e=>{for(let t of e.getEntries())"event"===t.entryType&&this.recordInteraction(t)});try{r.observe({entryTypes:["event"]}),this.observers.push(r)}catch(e){}}}setupErrorHandling(){window.addEventListener("error",e=>{this.recordError({type:"javascript",message:e.message,stack:e.error?.stack,timestamp:Date.now(),route:window.location.pathname,userAgent:navigator.userAgent})}),window.addEventListener("unhandledrejection",e=>{this.recordError({type:"javascript",message:e.reason?.message||"Unhandled Promise Rejection",stack:e.reason?.stack,timestamp:Date.now(),route:window.location.pathname,userAgent:navigator.userAgent})})}recordPageLoad(e){let t={startTime:e.startTime,endTime:e.loadEventEnd,duration:e.loadEventEnd-e.startTime,route:window.location.pathname};this.addMetric({pageLoad:t}),x.getState().recordPageLoadTime(t.duration)}recordResourceLoad(e){if(e.name.includes("/api/")){let t={endpoint:e.name,method:"GET",startTime:e.startTime,endTime:e.responseEnd,duration:e.responseEnd-e.startTime,status:200,size:e.transferSize};this.addMetric({network:t})}}recordInteraction(e){let t={type:e.name,element:e.target?.tagName||"unknown",timestamp:e.startTime,duration:e.duration};this.addMetric({interaction:t})}recordError(e){this.addMetric({error:e}),x.getState().incrementErrorCount()}addMetric(e){this.isEnabled&&(this.metrics.push(e),this.metrics.length>1e3&&(this.metrics=this.metrics.slice(-500)))}recordApiCall(e,t,r,s,a,n){let o={endpoint:e,method:t,startTime:r,endTime:s,duration:s-r,status:a,size:n};this.addMetric({network:o}),x.getState().recordApiResponseTime(e,o.duration)}recordCustomInteraction(e,t,r){let s={type:e,element:t,timestamp:Date.now(),duration:r};this.addMetric({interaction:s})}getPerformanceReport(){let e=Date.now()-864e5,t=this.metrics.filter(t=>(t.pageLoad?.startTime||t.network?.startTime||t.interaction?.timestamp||t.error?.timestamp||0)>e);return{totalMetrics:t.length,pageLoads:t.filter(e=>e.pageLoad).length,apiCalls:t.filter(e=>e.network).length,interactions:t.filter(e=>e.interaction).length,errors:t.filter(e=>e.error).length,averagePageLoadTime:this.calculateAveragePageLoadTime(t),averageApiResponseTime:this.calculateAverageApiResponseTime(t),errorRate:this.calculateErrorRate(t)}}calculateAveragePageLoadTime(e){let t=e.filter(e=>e.pageLoad);return 0===t.length?0:t.reduce((e,t)=>e+(t.pageLoad?.duration||0),0)/t.length}calculateAverageApiResponseTime(e){let t=e.filter(e=>e.network);return 0===t.length?0:t.reduce((e,t)=>e+(t.network?.duration||0),0)/t.length}calculateErrorRate(e){let t=e.filter(e=>e.error).length,r=e.length;return r>0?t/r*100:0}setEnabled(e){this.isEnabled=e}cleanup(){this.observers.forEach(e=>e.disconnect()),this.observers=[],this.metrics=[]}}let C=new P,L=({children:e})=>{let t=x(e=>e.preferences),r=x(e=>e.recordPageLoadTime);return(0,a.useEffect)(()=>(r(performance.now()),$(t.theme),R(t.fontSize),A(t.accessibility),t.privacy.analytics&&C.setEnabled(!0),()=>{C.cleanup()}),[]),(0,a.useEffect)(()=>{$(t.theme)},[t.theme]),(0,a.useEffect)(()=>{R(t.fontSize)},[t.fontSize]),(0,a.useEffect)(()=>{A(t.accessibility)},[t.accessibility]),(0,a.useEffect)(()=>{C.setEnabled(t.privacy.analytics)},[t.privacy.analytics]),s.jsx(f,{level:"critical",children:(0,s.jsxs)("div",{className:"app-container",children:[e,s.jsx(N,{}),s.jsx(S,{})]})})};function $(e){let t=document.documentElement;"system"===e&&(e=window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light"),t.classList.remove("light","dark"),t.classList.add(e);let r=document.querySelector('meta[name="theme-color"]');r&&r.setAttribute("content","dark"===e?"#1f2937":"#ffffff")}function R(e){let t=document.documentElement;switch(t.classList.remove("text-sm","text-base","text-lg"),e){case"small":t.classList.add("text-sm");break;case"large":t.classList.add("text-lg");break;default:t.classList.add("text-base")}}function A(e){let t=document.documentElement;e.highContrast?t.classList.add("high-contrast"):t.classList.remove("high-contrast"),e.reducedMotion?t.classList.add("reduce-motion"):t.classList.remove("reduce-motion"),e.screenReader?t.classList.add("screen-reader-optimized"):t.classList.remove("screen-reader-optimized")}},21149:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d,metadata:()=>c});var s=r(19510),a=r(62694),n=r.n(a),o=r(71615);r(67272);var i=r(68570);let l=(0,i.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#AppProvider`);(0,i.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#PageProvider`),(0,i.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#ComponentProvider`),(0,i.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#usePagePerformance`),(0,i.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/advanced/AppProvider.tsx#useUserTracking`);let c={title:{template:"%s | periodhub.health",default:"periodhub.health - Your Guide to Menstrual Wellness"},description:"Your compassionate guide to navigating menstrual pain with effective solutions, supportive resources, and a path to better menstrual health.",keywords:["period pain relief","menstrual cramps","natural remedies","period management","women's health","dysmenorrhea treatment","periodhub.health","menstrual health","articles","therapies"],authors:[{name:"periodhub.health team"}],creator:"periodhub.health",publisher:"periodhub.health",formatDetection:{email:!1,address:!1,telephone:!1},metadataBase:new URL(process.env.NEXT_PUBLIC_BASE_URL||"https://periodhub.health"),robots:{index:!0,follow:!0,googleBot:{index:!0,follow:!0,"max-video-preview":-1,"max-image-preview":"large","max-snippet":-1}}};function d({children:e}){let t=((0,o.headers)().get("x-pathname")||"").startsWith("/zh")?"zh":"en";return(0,s.jsxs)("html",{lang:t,className:`scroll-smooth ${n().variable}`,children:[(0,s.jsxs)("head",{children:[s.jsx("meta",{name:"theme-color",content:"#ffffff"}),s.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1, maximum-scale=5"}),s.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),s.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),s.jsx("link",{rel:"dns-prefetch",href:"https://v3.fal.media"}),s.jsx("link",{rel:"preconnect",href:"https://unpkg.com"}),s.jsx("link",{rel:"icon",href:"/favicon.ico",sizes:"any"}),s.jsx("link",{rel:"icon",href:"/icon.svg",type:"image/svg+xml"}),s.jsx("link",{rel:"apple-touch-icon",href:"/apple-touch-icon.png"}),s.jsx("link",{rel:"manifest",href:"/manifest.json"})]}),(0,s.jsxs)("body",{className:"antialiased bg-neutral-50 text-neutral-900 flex flex-col min-h-screen",children:[s.jsx(l,{children:e}),s.jsx("div",{className:"sr-only",children:"This website provides information about menstrual health for educational purposes only. The content is not intended to be a substitute for professional medical advice, diagnosis, or treatment. Always seek the advice of your physician or other qualified health provider with any questions you may have."})]})]})}},67272:()=>{}};