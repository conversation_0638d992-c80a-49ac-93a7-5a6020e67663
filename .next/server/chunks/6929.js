"use strict";exports.id=6929,exports.ids=[6929],exports.modules={68913:(e,t,r)=>{function n(e,t){var r=t&&t.cache?t.cache:u,n=t&&t.serializer?t.serializer:a;return(t&&t.strategy?t.strategy:function(e,t){var r,n,a=1===e.length?o:i;return r=t.cache.create(),n=t.serializer,a.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function o(e,t,r,n){var o=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),i=t.get(o);return void 0===i&&(i=e.call(this,n),t.set(o,i)),i}function i(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return void 0===i&&(i=e.apply(this,n),t.set(o,i)),i}r.r(t),r.d(t,{memoize:()=>n,strategies:()=>l});var a=function(){return JSON.stringify(arguments)},s=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),u={create:function(){return new s}},l={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)}}},72638:(e,t,r)=>{r.r(t),r.d(t,{ErrorCode:()=>u,FormatError:()=>ei,IntlMessageFormat:()=>ef,InvalidValueError:()=>ea,InvalidValueTypeError:()=>es,MissingValueError:()=>eu,PART_TYPE:()=>l,default:()=>ep,formatToParts:()=>ec,isFormatXMLElementFn:()=>el});var n,o,i,a,s,u,l,c=function(e,t){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function h(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}c(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var f=function(){return(f=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function p(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,Object.create;var m=("function"==typeof SuppressedError&&SuppressedError,r(68913));function d(e){return e.type===o.literal}function E(e){return e.type===o.number}function g(e){return e.type===o.date}function b(e){return e.type===o.time}function y(e){return e.type===o.select}function _(e){return e.type===o.plural}function v(e){return e.type===o.tag}function T(e){return!!(e&&"object"==typeof e&&e.type===i.number)}function A(e){return!!(e&&"object"==typeof e&&e.type===i.dateTime)}(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(o||(o={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(i||(i={}));var S=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,I=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,R=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,P=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,H=/^(@+)?(\+|#+)?[rs]?$/g,N=/(\*)(0+)|(#+)(0+)|(0+)/g,B=/^(0+)$/;function O(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(H,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function L(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function M(e){return L(e)||{}}var C={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},w=new RegExp("^".concat(S.source,"*")),F=new RegExp("".concat(S.source,"*$"));function U(e,t){return{start:e,end:t}}var G=!!String.prototype.startsWith&&"_a".startsWith("a",1),D=!!String.fromCodePoint,x=!!Object.fromEntries,j=!!String.prototype.codePointAt,k=!!String.prototype.trimStart,V=!!String.prototype.trimEnd,X=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&9007199254740991>=Math.abs(e)},K=!0;try{var Z=$("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");K=(null===(a=Z.exec("a"))||void 0===a?void 0:a[0])==="a"}catch(e){K=!1}var Y=G?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},W=D?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",o=t.length,i=0;o>i;){if((e=t[i++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},z=x?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],o=n[0],i=n[1];t[o]=i}return t},q=j?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var o=e.charCodeAt(t);return o<55296||o>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?o:(o-55296<<10)+(r-56320)+65536}},Q=k?function(e){return e.trimStart()}:function(e){return e.replace(w,"")},J=V?function(e){return e.trimEnd()}:function(e){return e.replace(F,"")};function $(e,t){return new RegExp(e,t)}if(K){var ee=$("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");s=function(e,t){var r;return ee.lastIndex=t,null!==(r=ee.exec(e)[1])&&void 0!==r?r:""}}else s=function(e,t){for(var r=[];;){var n,o=q(e,t);if(void 0===o||en(o)||(n=o)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(o),t+=o>=65536?2:1}return W.apply(void 0,r)};var et=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var i=[];!this.isEOF();){var a=this.char();if(123===a){var s=this.parseArgument(e,r);if(s.err)return s;i.push(s.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var u=this.clonePosition();this.bump(),i.push({type:o.pound,location:U(u,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek()){if(60===a&&!this.ignoreTag&&er(this.peek()||0)){var s=this.parseTag(e,t);if(s.err)return s;i.push(s.val)}else{var s=this.parseLiteral(e,t);if(s.err)return s;i.push(s.val)}}else{if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,U(this.clonePosition(),this.clonePosition()));break}}return{val:i,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:o.literal,value:"<".concat(i,"/>"),location:U(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,U(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var s=a.val,u=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,U(r,this.clonePosition()));if(this.isEOF()||!er(this.char()))return this.error(n.INVALID_TAG,U(u,this.clonePosition()));var l=this.clonePosition();return i!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,U(l,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:o.tag,value:i,children:s,location:U(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,U(u,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(t);if(i){n+=i;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var s=this.tryParseLeftAngleBracket();if(s){n+=s;continue}break}var u=U(r,this.clonePosition());return{val:{type:o.literal,value:n,location:u},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(er(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r){if(39===this.peek())t.push(39),this.bump();else{this.bump();break}}else t.push(r);this.bump()}return W.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),W(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,U(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,U(r,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(n.MALFORMED_ARGUMENT,U(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,U(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:o.argument,value:i,location:U(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,U(r,this.clonePosition()));return this.parseArgumentOptions(e,t,i,r);default:return this.error(n.MALFORMED_ARGUMENT,U(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=s(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:U(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var s,u=this.clonePosition(),l=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(l){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,U(u,c));case"number":case"date":case"time":this.bumpSpace();var h=null;if(this.bumpIf(",")){this.bumpSpace();var p=this.clonePosition(),m=this.parseSimpleArgStyleIfPossible();if(m.err)return m;var d=J(m.val);if(0===d.length)return this.error(n.EXPECT_ARGUMENT_STYLE,U(this.clonePosition(),this.clonePosition()));h={style:d,styleLocation:U(p,this.clonePosition())}}var E=this.tryParseArgumentClose(a);if(E.err)return E;var g=U(a,this.clonePosition());if(h&&Y(null==h?void 0:h.style,"::",0)){var b,y=Q(h.style.slice(2));if("number"===l){var m=this.parseNumberSkeletonFromString(y,h.styleLocation);if(m.err)return m;return{val:{type:o.number,value:r,location:g,style:m.val},err:null}}if(0===y.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,g);var _=y;this.locale&&(_=function(e,t){for(var r="",n=0;n<e.length;n++){var o=e.charAt(n);if("j"===o){for(var i=0;n+1<e.length&&e.charAt(n+1)===o;)i++,n++;var a=1+(1&i),s=i<2?1:3+(i>>1),u=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(C[t||""]||C[n||""]||C["".concat(n,"-001")]||C["001"])[0]}(t);for(("H"==u||"k"==u)&&(s=0);s-- >0;)r+="a";for(;a-- >0;)r=u+r}else"J"===o?r+="H":r+=o}return r}(y,this.locale));var d={type:i.dateTime,pattern:_,location:h.styleLocation,parsedOptions:this.shouldParseSkeletons?(b={},_.replace(I,function(e){var t=e.length;switch(e[0]){case"G":b.era=4===t?"long":5===t?"narrow":"short";break;case"y":b.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":b.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":b.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":b.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");b.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");b.weekday=["short","long","narrow","short"][t-4];break;case"a":b.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":b.hourCycle="h12",b.hour=["numeric","2-digit"][t-1];break;case"H":b.hourCycle="h23",b.hour=["numeric","2-digit"][t-1];break;case"K":b.hourCycle="h11",b.hour=["numeric","2-digit"][t-1];break;case"k":b.hourCycle="h24",b.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":b.minute=["numeric","2-digit"][t-1];break;case"s":b.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":b.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),b):{}};return{val:{type:"date"===l?o.date:o.time,value:r,location:g,style:d},err:null}}return{val:{type:"number"===l?o.number:"date"===l?o.date:o.time,value:r,location:g,style:null!==(s=null==h?void 0:h.style)&&void 0!==s?s:null},err:null};case"plural":case"selectordinal":case"select":var v=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,U(v,f({},v)));this.bumpSpace();var T=this.parseIdentifierIfPossible(),A=0;if("select"!==l&&"offset"===T.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,U(this.clonePosition(),this.clonePosition()));this.bumpSpace();var m=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(m.err)return m;this.bumpSpace(),T=this.parseIdentifierIfPossible(),A=m.val}var S=this.tryParsePluralOrSelectOptions(e,l,t,T);if(S.err)return S;var E=this.tryParseArgumentClose(a);if(E.err)return E;var R=U(a,this.clonePosition());if("select"===l)return{val:{type:o.select,value:r,options:z(S.val),location:R},err:null};return{val:{type:o.plural,value:r,options:z(S.val),offset:A,pluralType:"plural"===l?"cardinal":"ordinal",location:R},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,U(u,c))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,U(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,U(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(R).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var o=t[n].split("/");if(0===o.length)throw Error("Invalid number skeleton");for(var i=o[0],a=o.slice(1),s=0;s<a.length;s++)if(0===a[s].length)throw Error("Invalid number skeleton");r.push({stem:i,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:i.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=f(f(f({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return f(f({},e),L(t)||{})},{}));continue;case"engineering":t=f(f(f({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return f(f({},e),L(t)||{})},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(N,function(e,r,n,o,i,a){if(r)t.minimumIntegerDigits=n.length;else if(o&&i)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(B.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(P.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(P,function(e,r,n,o,i,a){return"*"===n?t.minimumFractionDigits=r.length:o&&"#"===o[0]?t.maximumFractionDigits=o.length:i&&a?(t.minimumFractionDigits=i.length,t.maximumFractionDigits=i.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var o=n.options[0];"w"===o?t=f(f({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=f(f({},t),O(o)));continue}if(H.test(n.stem)){t=f(f({},t),O(n.stem));continue}var i=L(n.stem);i&&(t=f(f({},t),i));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!B.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=f(f({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,o){for(var i,a=!1,s=[],u=new Set,l=o.value,c=o.location;;){if(0===l.length){var h=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;c=U(h,this.clonePosition()),l=this.message.slice(h.offset,this.offset())}else break}if(u.has(l))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);"other"===l&&(a=!0),this.bumpSpace();var p=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,U(this.clonePosition(),this.clonePosition()));var m=this.parseMessage(e+1,t,r);if(m.err)return m;var d=this.tryParseArgumentClose(p);if(d.err)return d;s.push([l,{value:m.val,location:U(p,this.clonePosition())}]),u.add(l),this.bumpSpace(),l=(i=this.parseIdentifierIfPossible()).value,c=i.location}return 0===s.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,U(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,U(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var o=!1,i=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,i=10*i+(a-48),this.bump();else break}var s=U(n,this.clonePosition());return o?X(i*=r)?{val:i,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=q(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(Y(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&en(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function er(e){return e>=97&&e<=122||e>=65&&e<=90}function en(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function eo(e,t){void 0===t&&(t={});var r=new et(e,t=f({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var o=SyntaxError(n[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,y(t)||_(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else E(t)&&T(t.style)?delete t.style.location:(g(t)||b(t))&&A(t.style)?delete t.style.location:v(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(u||(u={}));var ei=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.code=r,o.originalMessage=n,o}return h(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),ea=function(e){function t(t,r,n,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),u.INVALID_VALUE,o)||this}return h(t,e),t}(ei),es=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),u.INVALID_VALUE,n)||this}return h(t,e),t}(ei),eu=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),u.MISSING_VALUE,r)||this}return h(t,e),t}(ei);function el(e){return"function"==typeof e}function ec(e,t,r,n,i,a,s){if(1===e.length&&d(e[0]))return[{type:l.literal,value:e[0].value}];for(var c=[],h=0;h<e.length;h++){var f=e[h];if(d(f)){c.push({type:l.literal,value:f.value});continue}if(f.type===o.pound){"number"==typeof a&&c.push({type:l.literal,value:r.getNumberFormat(t).format(a)});continue}var p=f.value;if(!(i&&p in i))throw new eu(p,s);var m=i[p];if(f.type===o.argument){m&&"string"!=typeof m&&"number"!=typeof m||(m="string"==typeof m||"number"==typeof m?String(m):""),c.push({type:"string"==typeof m?l.literal:l.object,value:m});continue}if(g(f)){var S="string"==typeof f.style?n.date[f.style]:A(f.style)?f.style.parsedOptions:void 0;c.push({type:l.literal,value:r.getDateTimeFormat(t,S).format(m)});continue}if(b(f)){var S="string"==typeof f.style?n.time[f.style]:A(f.style)?f.style.parsedOptions:n.time.medium;c.push({type:l.literal,value:r.getDateTimeFormat(t,S).format(m)});continue}if(E(f)){var S="string"==typeof f.style?n.number[f.style]:T(f.style)?f.style.parsedOptions:void 0;S&&S.scale&&(m*=S.scale||1),c.push({type:l.literal,value:r.getNumberFormat(t,S).format(m)});continue}if(v(f)){var I=f.children,R=f.value,P=i[R];if(!el(P))throw new es(R,"function",s);var H=P(ec(I,t,r,n,i,a).map(function(e){return e.value}));Array.isArray(H)||(H=[H]),c.push.apply(c,H.map(function(e){return{type:"string"==typeof e?l.literal:l.object,value:e}}))}if(y(f)){var N=f.options[m]||f.options.other;if(!N)throw new ea(f.value,m,Object.keys(f.options),s);c.push.apply(c,ec(N.value,t,r,n,i));continue}if(_(f)){var N=f.options["=".concat(m)];if(!N){if(!Intl.PluralRules)throw new ei('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',u.MISSING_INTL_API,s);var B=r.getPluralRules(t,{type:f.pluralType}).select(m-(f.offset||0));N=f.options[B]||f.options.other}if(!N)throw new ea(f.value,m,Object.keys(f.options),s);c.push.apply(c,ec(N.value,t,r,n,i,m-(f.offset||0)));continue}}return c.length<2?c:c.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===l.literal&&t.type===l.literal?r.value+=t.value:e.push(t),e},[])}function eh(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(l||(l={}));var ef=function(){function e(t,r,n,o){void 0===r&&(r=e.defaultLocale);var i,a,s=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=s.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===l.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return ec(s.ast,s.locales,s.formatters,s.formats,e,void 0,s.message)},this.resolvedOptions=function(){var e;return{locale:(null===(e=s.resolvedLocale)||void 0===e?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(s.locales)[0]}},this.getAst=function(){return s.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var u=o||{},c=(u.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}(u,["formatters"]));this.ast=e.__parse(t,f(f({},c),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(i=e.formats,n?Object.keys(i).reduce(function(e,t){var r,o;return e[t]=(r=i[t],(o=n[t])?f(f(f({},r||{}),o||{}),Object.keys(r).reduce(function(e,t){return e[t]=f(f({},r[t]),o[t]||{}),e},{})):r),e},f({},i)):i),this.formatters=o&&o.formatters||(void 0===(a=this.formatterCache)&&(a={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,m.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,p([void 0],t,!1)))},{cache:eh(a.number),strategy:m.strategies.variadic}),getDateTimeFormat:(0,m.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,p([void 0],t,!1)))},{cache:eh(a.dateTime),strategy:m.strategies.variadic}),getPluralRules:(0,m.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,p([void 0],t,!1)))},{cache:eh(a.pluralRules),strategy:m.strategies.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=eo,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();let ep=ef},941:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},90748:(e,t,r)=>{r.d(t,{Z:()=>n});let n=(0,r(76557).Z)("Menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]])},13170:(e,t,r)=>{function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{default:()=>s});var o=r(17577),i=r.n(o),a=r(30776);function s(e){let{locale:t,...r}=e;if(!t)throw Error("Failed to determine locale in `NextIntlClientProvider`, please provide the `locale` prop explicitly.\n\nSee https://next-intl.dev/docs/configuration#locale");return i().createElement(a.IntlProvider,n({locale:t},r))}},2167:(e,t)=>{function r(){return(r=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}Object.defineProperty(t,"__esModule",{value:!0}),t.extends=r},23844:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(13378),o=r(27916),i=r(40397),a=r(78119);t.useFormatter=n.useFormatter,t.useTranslations=n.useTranslations,t.useLocale=o.default,t.NextIntlClientProvider=i.default,Object.keys(a).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return a[e]}})})},13378:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(78119);function o(e,t){return function(){try{return t(...arguments)}catch(e){throw Error(void 0)}}}let i=o(0,n.useTranslations),a=o(0,n.useFormatter);t.useFormatter=a,t.useTranslations=i,Object.keys(n).forEach(function(e){"default"===e||Object.prototype.hasOwnProperty.call(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:function(){return n[e]}})})},27916:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(35047),o=r(13015),i=r(19237);t.default=function(){let e;let t=n.useParams();try{e=o.useLocale()}catch(r){if("string"!=typeof(null==t?void 0:t[i.LOCALE_SEGMENT_NAME]))throw r;e=t[i.LOCALE_SEGMENT_NAME]}return e}},40397:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(2167),o=r(17577),i=r(11669),a=function(e){return e&&e.__esModule?e:{default:e}}(o);t.default=function(e){let{locale:t,...r}=e;if(!t)throw Error(void 0);return a.default.createElement(i.IntlProvider,n.extends({locale:t},r))}},19237:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.HEADER_LOCALE_NAME="X-NEXT-INTL-LOCALE",t.LOCALE_SEGMENT_NAME="locale"},90434:(e,t,r)=>{r.d(t,{default:()=>o.a});var n=r(79404),o=r.n(n)},35047:(e,t,r)=>{r.r(t);var n=r(77389),o={};for(let e in n)"default"!==e&&(o[e]=()=>n[e]);r.d(t,o)},53416:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"addLocale",{enumerable:!0,get:function(){return n}}),r(23658);let n=function(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return e};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},39683:(e,t,r)=>{function n(e,t,r,n){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return n}}),r(23658),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},79404:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return b}});let n=r(91174),o=r(10326),i=n._(r(17577)),a=r(25619),s=r(60944),u=r(43071),l=r(51348),c=r(53416),h=r(50131),f=r(52413),p=r(49408),m=r(39683),d=r(3486),E=r(57767);function g(e){return"string"==typeof e?e:(0,u.formatUrl)(e)}let b=i.default.forwardRef(function(e,t){let r,n;let{href:u,as:b,children:y,prefetch:_=null,passHref:v,replace:T,shallow:A,scroll:S,locale:I,onClick:R,onMouseEnter:P,onTouchStart:H,legacyBehavior:N=!1,...B}=e;r=y,N&&("string"==typeof r||"number"==typeof r)&&(r=(0,o.jsx)("a",{children:r}));let O=i.default.useContext(h.RouterContext),L=i.default.useContext(f.AppRouterContext),M=null!=O?O:L,C=!O,w=!1!==_,F=null===_?E.PrefetchKind.AUTO:E.PrefetchKind.FULL,{href:U,as:G}=i.default.useMemo(()=>{if(!O){let e=g(u);return{href:e,as:b?g(b):e}}let[e,t]=(0,a.resolveHref)(O,u,!0);return{href:e,as:b?(0,a.resolveHref)(O,b):t||e}},[O,u,b]),D=i.default.useRef(U),x=i.default.useRef(G);N&&(n=i.default.Children.only(r));let j=N?n&&"object"==typeof n&&n.ref:t,[k,V,X]=(0,p.useIntersection)({rootMargin:"200px"}),K=i.default.useCallback(e=>{(x.current!==G||D.current!==U)&&(X(),x.current=G,D.current=U),k(e),j&&("function"==typeof j?j(e):"object"==typeof j&&(j.current=e))},[G,j,U,X,k]);i.default.useEffect(()=>{},[G,U,V,I,w,null==O?void 0:O.locale,M,C,F]);let Z={ref:K,onClick(e){N||"function"!=typeof R||R(e),N&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),M&&!e.defaultPrevented&&function(e,t,r,n,o,a,u,l,c){let{nodeName:h}=e.currentTarget;if("A"===h.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!c&&!(0,s.isLocalURL)(r)))return;e.preventDefault();let f=()=>{let e=null==u||u;"beforePopState"in t?t[o?"replace":"push"](r,n,{shallow:a,locale:l,scroll:e}):t[o?"replace":"push"](n||r,{scroll:e})};c?i.default.startTransition(f):f()}(e,M,U,G,T,A,S,I,C)},onMouseEnter(e){N||"function"!=typeof P||P(e),N&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e)},onTouchStart:function(e){N||"function"!=typeof H||H(e),N&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e)}};if((0,l.isAbsoluteUrl)(G))Z.href=G;else if(!N||v||"a"===n.type&&!("href"in n.props)){let e=void 0!==I?I:null==O?void 0:O.locale,t=(null==O?void 0:O.isLocaleDomain)&&(0,m.getDomainLocale)(G,e,null==O?void 0:O.locales,null==O?void 0:O.domainLocales);Z.href=t||(0,d.addBasePath)((0,c.addLocale)(G,e,null==O?void 0:O.defaultLocale))}return N?i.default.cloneElement(n,Z):(0,o.jsx)("a",{...B,...Z,children:r})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},10956:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{cancelIdleCallback:function(){return n},requestIdleCallback:function(){return r}});let r="undefined"!=typeof self&&self.requestIdleCallback&&self.requestIdleCallback.bind(window)||function(e){let t=Date.now();return self.setTimeout(function(){e({didTimeout:!1,timeRemaining:function(){return Math.max(0,50-(Date.now()-t))}})},1)},n="undefined"!=typeof self&&self.cancelIdleCallback&&self.cancelIdleCallback.bind(window)||function(e){return clearTimeout(e)};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25619:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"resolveHref",{enumerable:!0,get:function(){return h}});let n=r(72149),o=r(43071),i=r(20757),a=r(51348),s=r(23658),u=r(60944),l=r(94903),c=r(81394);function h(e,t,r){let h;let f="string"==typeof t?t:(0,o.formatWithValidation)(t),p=f.match(/^[a-zA-Z]{1,}:\/\//),m=p?f.slice(p[0].length):f;if((m.split("?",1)[0]||"").match(/(\/\/|\\)/)){let e=(0,a.normalizeRepeatedSlashes)(m);f=(p?p[0]:"")+e}if(!(0,u.isLocalURL)(f))return r?[f]:f;try{h=new URL(f.startsWith("#")?e.asPath:e.pathname,"http://n")}catch(e){h=new URL("/","http://n")}try{let e=new URL(f,h);e.pathname=(0,s.normalizePathTrailingSlash)(e.pathname);let t="";if((0,l.isDynamicRoute)(e.pathname)&&e.searchParams&&r){let r=(0,n.searchParamsToUrlQuery)(e.searchParams),{result:a,params:s}=(0,c.interpolateAs)(e.pathname,e.pathname,r);a&&(t=(0,o.formatWithValidation)({pathname:a,hash:e.hash,query:(0,i.omit)(r,s)}))}let a=e.origin===h.origin?e.href.slice(e.origin.length):e.href;return r?[a,t||a]:a}catch(e){return r?[f]:f}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},49408:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return u}});let n=r(17577),o=r(10956),i="function"==typeof IntersectionObserver,a=new Map,s=[];function u(e){let{rootRef:t,rootMargin:r,disabled:u}=e,l=u||!i,[c,h]=(0,n.useState)(!1),f=(0,n.useRef)(null),p=(0,n.useCallback)(e=>{f.current=e},[]);return(0,n.useEffect)(()=>{if(i){if(l||c)return;let e=f.current;if(e&&e.tagName)return function(e,t,r){let{id:n,observer:o,elements:i}=function(e){let t;let r={root:e.root||null,margin:e.rootMargin||""},n=s.find(e=>e.root===r.root&&e.margin===r.margin);if(n&&(t=a.get(n)))return t;let o=new Map;return t={id:r,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),r=e.isIntersecting||e.intersectionRatio>0;t&&r&&t(r)})},e),elements:o},s.push(r),a.set(r,t),t}(r);return i.set(e,t),o.observe(e),function(){if(i.delete(e),o.unobserve(e),0===i.size){o.disconnect(),a.delete(n);let e=s.findIndex(e=>e.root===n.root&&e.margin===n.margin);e>-1&&s.splice(e,1)}}}(e,e=>e&&h(e),{root:null==t?void 0:t.current,rootMargin:r})}else if(!c){let e=(0,o.requestIdleCallback)(()=>h(!0));return()=>(0,o.cancelIdleCallback)(e)}},[l,r,t,c,f.current]),[p,c,(0,n.useCallback)(()=>{h(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},25633:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ACTION_SUFFIX:function(){return u},APP_DIR_ALIAS:function(){return P},CACHE_ONE_YEAR:function(){return _},DOT_NEXT_ALIAS:function(){return I},ESLINT_DEFAULT_DIRS:function(){return K},GSP_NO_RETURNED_VALUE:function(){return D},GSSP_COMPONENT_MEMBER_ERROR:function(){return k},GSSP_NO_RETURNED_VALUE:function(){return x},INSTRUMENTATION_HOOK_FILENAME:function(){return A},MIDDLEWARE_FILENAME:function(){return v},MIDDLEWARE_LOCATION_REGEXP:function(){return T},NEXT_BODY_SUFFIX:function(){return h},NEXT_CACHE_IMPLICIT_TAG_ID:function(){return y},NEXT_CACHE_REVALIDATED_TAGS_HEADER:function(){return m},NEXT_CACHE_REVALIDATE_TAG_TOKEN_HEADER:function(){return d},NEXT_CACHE_SOFT_TAGS_HEADER:function(){return p},NEXT_CACHE_SOFT_TAG_MAX_LENGTH:function(){return b},NEXT_CACHE_TAGS_HEADER:function(){return f},NEXT_CACHE_TAG_MAX_ITEMS:function(){return E},NEXT_CACHE_TAG_MAX_LENGTH:function(){return g},NEXT_DATA_SUFFIX:function(){return l},NEXT_INTERCEPTION_MARKER_PREFIX:function(){return n},NEXT_META_SUFFIX:function(){return c},NEXT_QUERY_PARAM_PREFIX:function(){return r},NON_STANDARD_NODE_ENV:function(){return V},PAGES_DIR_ALIAS:function(){return S},PRERENDER_REVALIDATE_HEADER:function(){return o},PRERENDER_REVALIDATE_ONLY_GENERATED_HEADER:function(){return i},PUBLIC_DIR_MIDDLEWARE_CONFLICT:function(){return M},ROOT_DIR_ALIAS:function(){return R},RSC_ACTION_CLIENT_WRAPPER_ALIAS:function(){return L},RSC_ACTION_ENCRYPTION_ALIAS:function(){return O},RSC_ACTION_PROXY_ALIAS:function(){return B},RSC_ACTION_VALIDATE_ALIAS:function(){return N},RSC_MOD_REF_PROXY_ALIAS:function(){return H},RSC_PREFETCH_SUFFIX:function(){return a},RSC_SUFFIX:function(){return s},SERVER_PROPS_EXPORT_ERROR:function(){return G},SERVER_PROPS_GET_INIT_PROPS_CONFLICT:function(){return w},SERVER_PROPS_SSG_CONFLICT:function(){return F},SERVER_RUNTIME:function(){return Z},SSG_FALLBACK_EXPORT_ERROR:function(){return X},SSG_GET_INITIAL_PROPS_CONFLICT:function(){return C},STATIC_STATUS_PAGE_GET_INITIAL_PROPS_ERROR:function(){return U},UNSTABLE_REVALIDATE_RENAME_ERROR:function(){return j},WEBPACK_LAYERS:function(){return W},WEBPACK_RESOURCE_QUERIES:function(){return z}});let r="nxtP",n="nxtI",o="x-prerender-revalidate",i="x-prerender-revalidate-if-generated",a=".prefetch.rsc",s=".rsc",u=".action",l=".json",c=".meta",h=".body",f="x-next-cache-tags",p="x-next-cache-soft-tags",m="x-next-revalidated-tags",d="x-next-revalidate-tag-token",E=128,g=256,b=1024,y="_N_T_",_=31536e3,v="middleware",T=`(?:src/)?${v}`,A="instrumentation",S="private-next-pages",I="private-dot-next",R="private-next-root-dir",P="private-next-app-dir",H="private-next-rsc-mod-ref-proxy",N="private-next-rsc-action-validate",B="private-next-rsc-server-reference",O="private-next-rsc-action-encryption",L="private-next-rsc-action-client-wrapper",M="You can not have a '_next' folder inside of your public folder. This conflicts with the internal '/_next' route. https://nextjs.org/docs/messages/public-next-folder-conflict",C="You can not use getInitialProps with getStaticProps. To use SSG, please remove your getInitialProps",w="You can not use getInitialProps with getServerSideProps. Please remove getInitialProps.",F="You can not use getStaticProps or getStaticPaths with getServerSideProps. To use SSG, please remove getServerSideProps",U="can not have getInitialProps/getServerSideProps, https://nextjs.org/docs/messages/404-get-initial-props",G="pages with `getServerSideProps` can not be exported. See more info here: https://nextjs.org/docs/messages/gssp-export",D="Your `getStaticProps` function did not return an object. Did you forget to add a `return`?",x="Your `getServerSideProps` function did not return an object. Did you forget to add a `return`?",j="The `unstable_revalidate` property is available for general use.\nPlease use `revalidate` instead.",k="can not be attached to a page's component and must be exported from the page. See more info here: https://nextjs.org/docs/messages/gssp-component-member",V='You are using a non-standard "NODE_ENV" value in your environment. This creates inconsistencies in the project and is strongly advised against. Read more: https://nextjs.org/docs/messages/non-standard-node-env',X="Pages with `fallback` enabled in `getStaticPaths` can not be exported. See more info here: https://nextjs.org/docs/messages/ssg-fallback-true-export",K=["app","pages","components","lib","src"],Z={edge:"edge",experimentalEdge:"experimental-edge",nodejs:"nodejs"},Y={shared:"shared",reactServerComponents:"rsc",serverSideRendering:"ssr",actionBrowser:"action-browser",api:"api",middleware:"middleware",instrument:"instrument",edgeAsset:"edge-asset",appPagesBrowser:"app-pages-browser",appMetadataRoute:"app-metadata-route",appRouteHandler:"app-route-handler"},W={...Y,GROUP:{serverOnly:[Y.reactServerComponents,Y.actionBrowser,Y.appMetadataRoute,Y.appRouteHandler,Y.instrument],clientOnly:[Y.serverSideRendering,Y.appPagesBrowser],nonClientServerTarget:[Y.middleware,Y.api],app:[Y.reactServerComponents,Y.actionBrowser,Y.appMetadataRoute,Y.appRouteHandler,Y.serverSideRendering,Y.appPagesBrowser,Y.shared,Y.instrument]}},z={edgeSSREntry:"__next_edge_ssr_entry__",metadata:"__next_metadata__",metadataRoute:"__next_metadata_route__",metadataImageMeta:"__next_metadata_image_meta__"}},50131:(e,t,r)=>{e.exports=r(81616).vendored.contexts.RouterContext},2451:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"escapeStringRegexp",{enumerable:!0,get:function(){return o}});let r=/[|\\{}()[\]^$+*?.-]/,n=/[|\\{}()[\]^$+*?.-]/g;function o(e){return r.test(e)?e.replace(n,"\\$&"):e}},43071:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return s},urlObjectKeys:function(){return a}});let n=r(58374)._(r(72149)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:r}=e,i=e.protocol||"",a=e.pathname||"",s=e.hash||"",u=e.query||"",l=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?l=t+e.host:r&&(l=t+(~r.indexOf(":")?"["+r+"]":r),e.port&&(l+=":"+e.port)),u&&"object"==typeof u&&(u=String(n.urlQueryToSearchParams(u)));let c=e.search||u&&"?"+u||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==l?(l="//"+(l||""),a&&"/"!==a[0]&&(a="/"+a)):l||(l=""),s&&"#"!==s[0]&&(s="#"+s),c&&"?"!==c[0]&&(c="?"+c),""+i+l+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+s}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function s(e){return i(e)}},94903:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getSortedRoutes:function(){return n.getSortedRoutes},isDynamicRoute:function(){return o.isDynamicRoute}});let n=r(44712),o=r(45541)},81394:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"interpolateAs",{enumerable:!0,get:function(){return i}});let n=r(9966),o=r(37249);function i(e,t,r){let i="",a=(0,o.getRouteRegex)(e),s=a.groups,u=(t!==e?(0,n.getRouteMatcher)(a)(t):"")||r;i=e;let l=Object.keys(s);return l.every(e=>{let t=u[e]||"",{repeat:r,optional:n}=s[e],o="["+(r?"...":"")+e+"]";return n&&(o=(t?"":"/")+"["+o+"]"),r&&!Array.isArray(t)&&(t=[t]),(n||e in u)&&(i=i.replace(o,r?t.map(e=>encodeURIComponent(e)).join("/"):encodeURIComponent(t))||"/")})||(i=""),{params:l,result:i}}},45541:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isDynamicRoute",{enumerable:!0,get:function(){return i}});let n=r(87356),o=/\/\[[^/]+?\](?=\/|$)/;function i(e){return(0,n.isInterceptionRouteAppPath)(e)&&(e=(0,n.extractInterceptionRouteInformation)(e).interceptedRoute),o.test(e)}},60944:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let n=r(51348),o=r(37929);function i(e){if(!(0,n.isAbsoluteUrl)(e))return!0;try{let t=(0,n.getLocationOrigin)(),r=new URL(e,t);return r.origin===t&&(0,o.hasBasePath)(r.pathname)}catch(e){return!1}}},20757:(e,t)=>{function r(e,t){let r={};return Object.keys(e).forEach(n=>{t.includes(n)||(r[n]=e[n])}),r}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"omit",{enumerable:!0,get:function(){return r}})},72149:(e,t)=>{function r(e){let t={};return e.forEach((e,r)=>{void 0===t[r]?t[r]=e:Array.isArray(t[r])?t[r].push(e):t[r]=[t[r],e]}),t}function n(e){return"string"!=typeof e&&("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;return Object.entries(e).forEach(e=>{let[r,o]=e;Array.isArray(o)?o.forEach(e=>t.append(r,n(e))):t.set(r,n(o))}),t}function i(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];return r.forEach(t=>{Array.from(t.keys()).forEach(t=>e.delete(t)),t.forEach((t,r)=>e.append(r,t))}),e}Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return o}})},9966:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getRouteMatcher",{enumerable:!0,get:function(){return o}});let n=r(51348);function o(e){let{re:t,groups:r}=e;return e=>{let o=t.exec(e);if(!o)return!1;let i=e=>{try{return decodeURIComponent(e)}catch(e){throw new n.DecodeError("failed to decode param")}},a={};return Object.keys(r).forEach(e=>{let t=r[e],n=o[t.pos];void 0!==n&&(a[e]=~n.indexOf("/")?n.split("/").map(e=>i(e)):t.repeat?[i(n)]:i(n))}),a}}},37249:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getNamedMiddlewareRegex:function(){return p},getNamedRouteRegex:function(){return f},getRouteRegex:function(){return l},parseParameter:function(){return s}});let n=r(25633),o=r(87356),i=r(2451),a=r(83236);function s(e){let t=e.startsWith("[")&&e.endsWith("]");t&&(e=e.slice(1,-1));let r=e.startsWith("...");return r&&(e=e.slice(3)),{key:e,repeat:r,optional:t}}function u(e){let t=(0,a.removeTrailingSlash)(e).slice(1).split("/"),r={},n=1;return{parameterizedRoute:t.map(e=>{let t=o.INTERCEPTION_ROUTE_MARKERS.find(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(t&&a){let{key:e,optional:o,repeat:u}=s(a[1]);return r[e]={pos:n++,repeat:u,optional:o},"/"+(0,i.escapeStringRegexp)(t)+"([^/]+?)"}if(!a)return"/"+(0,i.escapeStringRegexp)(e);{let{key:e,repeat:t,optional:o}=s(a[1]);return r[e]={pos:n++,repeat:t,optional:o},t?o?"(?:/(.+?))?":"/(.+?)":"/([^/]+?)"}}).join(""),groups:r}}function l(e){let{parameterizedRoute:t,groups:r}=u(e);return{re:RegExp("^"+t+"(?:/)?$"),groups:r}}function c(e){let{interceptionMarker:t,getSafeRouteKey:r,segment:n,routeKeys:o,keyPrefix:a}=e,{key:u,optional:l,repeat:c}=s(n),h=u.replace(/\W/g,"");a&&(h=""+a+h);let f=!1;(0===h.length||h.length>30)&&(f=!0),isNaN(parseInt(h.slice(0,1)))||(f=!0),f&&(h=r()),a?o[h]=""+a+u:o[h]=u;let p=t?(0,i.escapeStringRegexp)(t):"";return c?l?"(?:/"+p+"(?<"+h+">.+?))?":"/"+p+"(?<"+h+">.+?)":"/"+p+"(?<"+h+">[^/]+?)"}function h(e,t){let r;let s=(0,a.removeTrailingSlash)(e).slice(1).split("/"),u=(r=0,()=>{let e="",t=++r;for(;t>0;)e+=String.fromCharCode(97+(t-1)%26),t=Math.floor((t-1)/26);return e}),l={};return{namedParameterizedRoute:s.map(e=>{let r=o.INTERCEPTION_ROUTE_MARKERS.some(t=>e.startsWith(t)),a=e.match(/\[((?:\[.*\])|.+)\]/);if(r&&a){let[r]=e.split(a[0]);return c({getSafeRouteKey:u,interceptionMarker:r,segment:a[1],routeKeys:l,keyPrefix:t?n.NEXT_INTERCEPTION_MARKER_PREFIX:void 0})}return a?c({getSafeRouteKey:u,segment:a[1],routeKeys:l,keyPrefix:t?n.NEXT_QUERY_PARAM_PREFIX:void 0}):"/"+(0,i.escapeStringRegexp)(e)}).join(""),routeKeys:l}}function f(e,t){let r=h(e,t);return{...l(e),namedRegex:"^"+r.namedParameterizedRoute+"(?:/)?$",routeKeys:r.routeKeys}}function p(e,t){let{parameterizedRoute:r}=u(e),{catchAll:n=!0}=t;if("/"===r)return{namedRegex:"^/"+(n?".*":"")+"$"};let{namedParameterizedRoute:o}=h(e,!1);return{namedRegex:"^"+o+(n?"(?:(/.*)?)":"")+"$"}}},44712:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getSortedRoutes",{enumerable:!0,get:function(){return n}});class r{insert(e){this._insert(e.split("/").filter(Boolean),[],!1)}smoosh(){return this._smoosh()}_smoosh(e){void 0===e&&(e="/");let t=[...this.children.keys()].sort();null!==this.slugName&&t.splice(t.indexOf("[]"),1),null!==this.restSlugName&&t.splice(t.indexOf("[...]"),1),null!==this.optionalRestSlugName&&t.splice(t.indexOf("[[...]]"),1);let r=t.map(t=>this.children.get(t)._smoosh(""+e+t+"/")).reduce((e,t)=>[...e,...t],[]);if(null!==this.slugName&&r.push(...this.children.get("[]")._smoosh(e+"["+this.slugName+"]/")),!this.placeholder){let t="/"===e?"/":e.slice(0,-1);if(null!=this.optionalRestSlugName)throw Error('You cannot define a route with the same specificity as a optional catch-all route ("'+t+'" and "'+t+"[[..."+this.optionalRestSlugName+']]").');r.unshift(t)}return null!==this.restSlugName&&r.push(...this.children.get("[...]")._smoosh(e+"[..."+this.restSlugName+"]/")),null!==this.optionalRestSlugName&&r.push(...this.children.get("[[...]]")._smoosh(e+"[[..."+this.optionalRestSlugName+"]]/")),r}_insert(e,t,n){if(0===e.length){this.placeholder=!1;return}if(n)throw Error("Catch-all must be the last part of the URL.");let o=e[0];if(o.startsWith("[")&&o.endsWith("]")){let r=o.slice(1,-1),a=!1;if(r.startsWith("[")&&r.endsWith("]")&&(r=r.slice(1,-1),a=!0),r.startsWith("...")&&(r=r.substring(3),n=!0),r.startsWith("[")||r.endsWith("]"))throw Error("Segment names may not start or end with extra brackets ('"+r+"').");if(r.startsWith("."))throw Error("Segment names may not start with erroneous periods ('"+r+"').");function i(e,r){if(null!==e&&e!==r)throw Error("You cannot use different slug names for the same dynamic path ('"+e+"' !== '"+r+"').");t.forEach(e=>{if(e===r)throw Error('You cannot have the same slug name "'+r+'" repeat within a single dynamic path');if(e.replace(/\W/g,"")===o.replace(/\W/g,""))throw Error('You cannot have the slug names "'+e+'" and "'+r+'" differ only by non-word symbols within a single dynamic path')}),t.push(r)}if(n){if(a){if(null!=this.restSlugName)throw Error('You cannot use both an required and optional catch-all route at the same level ("[...'+this.restSlugName+']" and "'+e[0]+'" ).');i(this.optionalRestSlugName,r),this.optionalRestSlugName=r,o="[[...]]"}else{if(null!=this.optionalRestSlugName)throw Error('You cannot use both an optional and required catch-all route at the same level ("[[...'+this.optionalRestSlugName+']]" and "'+e[0]+'").');i(this.restSlugName,r),this.restSlugName=r,o="[...]"}}else{if(a)throw Error('Optional route parameters are not yet supported ("'+e[0]+'").');i(this.slugName,r),this.slugName=r,o="[]"}}this.children.has(o)||this.children.set(o,new r),this.children.get(o)._insert(e.slice(1),t,n)}constructor(){this.placeholder=!0,this.children=new Map,this.slugName=null,this.restSlugName=null,this.optionalRestSlugName=null}}function n(e){let t=new r;return e.forEach(e=>t.insert(e)),t.smoosh()}},51348:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return g},NormalizeError:function(){return d},PageNotFoundError:function(){return E},SP:function(){return f},ST:function(){return p},WEB_VITALS:function(){return r},execOnce:function(){return n},getDisplayName:function(){return u},getLocationOrigin:function(){return a},getURL:function(){return s},isAbsoluteUrl:function(){return i},isResSent:function(){return l},loadGetInitialProps:function(){return h},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return y}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function n(e){let t,r=!1;return function(){for(var n=arguments.length,o=Array(n),i=0;i<n;i++)o[i]=arguments[i];return r||(r=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:r}=window.location;return e+"//"+t+(r?":"+r:"")}function s(){let{href:e}=window.location,t=a();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function l(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function h(e,t){let r=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await h(t.Component,t.ctx)}:{};let n=await e.getInitialProps(t);if(r&&l(r))return n;if(!n)throw Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+n+'" instead.');return n}let f="undefined"!=typeof performance,p=f&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class d extends Error{}class E extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function y(e){return JSON.stringify({message:e.message,stack:e.stack})}},11669:(e,t,r)=>{e.exports=r(30776)},13015:(e,t,r)=>{e.exports=r(39471)},78119:(e,t,r)=>{e.exports=r(45948)},91695:(e,t,r)=>{let n=r(17577).createContext(void 0);t.IntlContext=n},30776:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(17577),o=r(36118),i=r(91695);r(68913);var a=function(e){return e&&e.__esModule?e:{default:e}}(n);t.IntlProvider=function(e){let{children:t,defaultTranslationValues:r,formats:s,getMessageFallback:u,locale:l,messages:c,now:h,onError:f,timeZone:p}=e,m=n.useMemo(()=>o.createCache(),[l]),d=n.useMemo(()=>o.createIntlFormatters(m),[m]),E=n.useMemo(()=>({...o.initializeConfig({locale:l,defaultTranslationValues:r,formats:s,getMessageFallback:u,messages:c,now:h,onError:f,timeZone:p}),formatters:d,cache:m}),[m,r,s,d,u,l,c,h,f,p]);return a.default.createElement(i.IntlContext.Provider,{value:E},t)}},69422:(e,t,r)=>{var n=r(17577),o=r(91695);function i(){let e=n.useContext(o.IntlContext);if(!e)throw Error(void 0);return e}t.useIntlContext=i,t.useLocale=function(){return i().locale}},39471:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(69422);r(17577),r(91695),t.useLocale=n.useLocale},1153:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(72729),o=r(36118);r(72638),r(17577),r(68913),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t._createCache=o.createCache,t._createIntlFormatters=o.createIntlFormatters,t.initializeConfig=o.initializeConfig,t.createTranslator=function(e){let{_cache:t=o.createCache(),_formatters:r=o.createIntlFormatters(t),getMessageFallback:i=o.defaultGetMessageFallback,messages:a,namespace:s,onError:u=o.defaultOnError,...l}=e;return function(e,t){let{messages:r,namespace:o,...i}=e;return r=r["!"],o=n.resolveNamespace(o,"!"),n.createBaseTranslator({...i,messages:r,namespace:o})}({...l,onError:u,cache:t,formatters:r,getMessageFallback:i,messages:{"!":a},namespace:s?"!.".concat(s):"!"},0)}},72729:(e,t,r)=>{var n=r(72638),o=r(17577),i=r(36118),a=function(e){return e&&e.__esModule?e:{default:e}}(n);function s(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let u=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class l extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),s(this,"code",void 0),s(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function c(e,t){return e?Object.keys(e).reduce((r,n)=>(r[n]={timeZone:t,...e[n]},r),{}):e}function h(e,t,r,n){let o=i.joinPath(n,r);if(!t)throw Error(o);let a=t;return r.split(".").forEach(t=>{let r=a[t];if(null==t||null==r)throw Error(o+" (".concat(e,")"));a=r}),a}let f=365/12*86400,p={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:365/12*86400,months:365/12*86400,quarter:365/12*259200,quarters:365/12*259200,year:31536e3,years:31536e3};t.IntlError=l,t.IntlErrorCode=u,t.createBaseTranslator=function(e){let t=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:i.defaultOnError;try{if(!t)throw Error(void 0);let n=r?h(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new l(u.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function(e){let{cache:t,defaultTranslationValues:r,formats:n,formatters:s,getMessageFallback:f=i.defaultGetMessageFallback,locale:p,messagesOrError:m,namespace:d,onError:E,timeZone:g}=e,b=m instanceof l;function y(e,t,r){let n=new l(t,r);return E(n),f({error:n,key:e,namespace:d})}function _(e,l,E){let _,v;if(b)return f({error:m,key:e,namespace:d});try{_=h(p,m,e,d)}catch(t){return y(e,u.MISSING_MESSAGE,t.message)}if("object"==typeof _){let t;return y(e,Array.isArray(_)?u.INVALID_MESSAGE:u.INSUFFICIENT_PATH,t)}let T=function(e,t){if(t)return;let r=e.replace(/'([{}])/gi,"$1");return/<|{/.test(r)?void 0:r}(_,l);if(T)return T;s.getMessageFormat||(s.getMessageFormat=i.memoFn(function(){return new a.default(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:s,...arguments.length<=3?void 0:arguments[3]})},t.message));try{v=s.getMessageFormat(_,p,function(e,t){let r=t?{...e,dateTime:c(e.dateTime,t)}:e,n=a.default.formats.date,o=t?c(n,t):n,i=a.default.formats.time,s=t?c(i,t):i;return{...r,date:{...o,...r.dateTime},time:{...s,...r.dateTime}}}({...n,...E},g),{formatters:{...s,getDateTimeFormat:(e,t)=>s.getDateTimeFormat(e,{timeZone:g,...t})}})}catch(t){return y(e,u.INVALID_MESSAGE,t.message)}try{let e=v.format(function(e){if(0===Object.keys(e).length)return;let t={};return Object.keys(e).forEach(r=>{let n,i=0,a=e[r];n="function"==typeof a?e=>{let t=a(e);return o.isValidElement(t)?o.cloneElement(t,{key:r+i++}):t}:a,t[r]=n}),t}({...r,...l}));if(null==e)throw Error(void 0);return o.isValidElement(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(t){return y(e,u.FORMATTING_ERROR,t.message)}}function v(e,t,r){let n=_(e,t,r);return"string"!=typeof n?y(e,u.INVALID_MESSAGE,void 0):n}return v.rich=_,v.markup=(e,t,r)=>{let n=_(e,t,r);if("string"!=typeof n){let t=new l(u.FORMATTING_ERROR,void 0);return E(t),f({error:t,key:e,namespace:d})}return n},v.raw=e=>{if(b)return f({error:m,key:e,namespace:d});try{return h(p,m,e,d)}catch(t){return y(e,u.MISSING_MESSAGE,t.message)}},v.has=e=>{if(b)return!1;try{return h(p,m,e,d),!0}catch(e){return!1}},v}({...e,messagesOrError:t})},t.createFormatter=function(e){let{_cache:t=i.createCache(),_formatters:r=i.createIntlFormatters(t),formats:n,locale:o,now:a,onError:s=i.defaultOnError,timeZone:c}=e;function h(e){var t;return null!==(t=e)&&void 0!==t&&t.timeZone||(c?e={...e,timeZone:c}:s(new l(u.ENVIRONMENT_FALLBACK,void 0))),e}function m(e,t,r,n){let o;try{o=function(e,t){let r;if("string"==typeof t){if(!(r=null==e?void 0:e[t])){let e=new l(u.MISSING_FORMAT,void 0);throw s(e),e}}else r=t;return r}(t,e)}catch(e){return n()}try{return r(o)}catch(e){return s(new l(u.FORMATTING_ERROR,e.message)),n()}}function d(e,t){return m(t,null==n?void 0:n.dateTime,t=>(t=h(t),r.getDateTimeFormat(o,t).format(e)),()=>String(e))}function E(){return a||(s(new l(u.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:d,number:function(e,t){return m(t,null==n?void 0:n.number,t=>r.getNumberFormat(o,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let i,a;let s={};t instanceof Date||"number"==typeof t?i=new Date(t):t&&(i=null!=t.now?new Date(t.now):E(),a=t.unit,s.style=t.style,s.numberingSystem=t.numberingSystem),i||(i=E());let u=(new Date(e).getTime()-i.getTime())/1e3;a||(a=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<f?"week":t<31536e3?"month":"year"}(u)),s.numeric="second"===a?"auto":"always";let l=(n=a,Math.round(u/p[n]));return r.getRelativeTimeFormat(o,s).format(l,a)}catch(t){return s(new l(u.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){let i=[],a=new Map,s=0;for(let t of e){let e;"object"==typeof t?(e=String(s),a.set(e,t)):e=String(t),i.push(e),s++}return m(t,null==n?void 0:n.list,e=>{let t=r.getListFormat(o,e).formatToParts(i).map(e=>"literal"===e.type?e.value:a.get(e.value)||e.value);return a.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,i){return m(i,null==n?void 0:n.dateTime,n=>(n=h(n),r.getDateTimeFormat(o,n).formatRange(e,t)),()=>[d(e),d(t)].join(" – "))}}},t.resolveNamespace=function(e,t){return e===t?void 0:e.slice((t+".").length)}},45948:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(72729),o=r(1153),i=r(36118),a=r(30776),s=r(55965),u=r(69422);r(72638),r(17577),r(68913),r(91695),t.IntlError=n.IntlError,t.IntlErrorCode=n.IntlErrorCode,t.createFormatter=n.createFormatter,t.createTranslator=o.createTranslator,t._createCache=i.createCache,t._createIntlFormatters=i.createIntlFormatters,t.initializeConfig=i.initializeConfig,t.IntlProvider=a.IntlProvider,t.useFormatter=s.useFormatter,t.useMessages=s.useMessages,t.useNow=s.useNow,t.useTimeZone=s.useTimeZone,t.useTranslations=s.useTranslations,t.useLocale=u.useLocale},36118:(e,t,r)=>{var n=r(68913);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function i(e){return o(e.namespace,e.key)}function a(e){console.error(e)}function s(e,t){return n.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:n.strategies.variadic})}function u(e,t){return s(function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return new e(...r)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:u(Intl.DateTimeFormat,e.dateTime),getNumberFormat:u(Intl.NumberFormat,e.number),getPluralRules:u(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:u(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:u(Intl.ListFormat,e.list),getDisplayNames:u(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=i,t.defaultOnError=a,t.initializeConfig=function(e){let{getMessageFallback:t,messages:r,onError:n,...o}=e;return{...o,messages:r,onError:n||a,getMessageFallback:t||i}},t.joinPath=o,t.memoFn=s},55965:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0});var n=r(30776),o=r(69422),i=r(17577),a=r(72729);r(36118),r(68913),r(91695),r(72638);let s=!1,u="undefined"==typeof window;t.IntlProvider=n.IntlProvider,t.useLocale=o.useLocale,t.useFormatter=function(){let{formats:e,formatters:t,locale:r,now:n,onError:s,timeZone:u}=o.useIntlContext();return i.useMemo(()=>a.createFormatter({formats:e,locale:r,now:n,onError:s,timeZone:u,_formatters:t}),[e,t,n,r,s,u])},t.useMessages=function(){let e=o.useIntlContext();if(!e.messages)throw Error(void 0);return e.messages},t.useNow=function(e){let t=null==e?void 0:e.updateInterval,{now:r}=o.useIntlContext(),[n,a]=i.useState(r||new Date);return i.useEffect(()=>{if(!t)return;let e=setInterval(()=>{a(new Date)},t);return()=>{clearInterval(e)}},[r,t]),null==t&&r?r:n},t.useTimeZone=function(){return o.useIntlContext().timeZone},t.useTranslations=function(e){return function(e,t,r){let{cache:n,defaultTranslationValues:l,formats:c,formatters:h,getMessageFallback:f,locale:p,onError:m,timeZone:d}=o.useIntlContext(),E=e["!"],g=a.resolveNamespace(t,"!");return d||s||!u||(s=!0,m(new a.IntlError(a.IntlErrorCode.ENVIRONMENT_FALLBACK,void 0))),i.useMemo(()=>a.createBaseTranslator({cache:n,formatters:h,getMessageFallback:f,messages:E,defaultTranslationValues:l,namespace:g,onError:m,formats:c,locale:p,timeZone:d}),[n,h,f,E,l,g,m,c,p,d])}({"!":o.useIntlContext().messages},e?"!.".concat(e):"!",0)}},98995:(e,t,r)=>{function n(e,t){var r=t&&t.cache?t.cache:u,n=t&&t.serializer?t.serializer:a;return(t&&t.strategy?t.strategy:function(e,t){var r,n,a=1===e.length?o:i;return r=t.cache.create(),n=t.serializer,a.bind(this,e,r,n)})(e,{cache:r,serializer:n})}function o(e,t,r,n){var o=null==n||"number"==typeof n||"boolean"==typeof n?n:r(n),i=t.get(o);return void 0===i&&(i=e.call(this,n),t.set(o,i)),i}function i(e,t,r){var n=Array.prototype.slice.call(arguments,3),o=r(n),i=t.get(o);return void 0===i&&(i=e.apply(this,n),t.set(o,i)),i}r.r(t),r.d(t,{memoize:()=>n,strategies:()=>l});var a=function(){return JSON.stringify(arguments)},s=function(){function e(){this.cache=Object.create(null)}return e.prototype.get=function(e){return this.cache[e]},e.prototype.set=function(e,t){this.cache[e]=t},e}(),u={create:function(){return new s}},l={variadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,i.bind(this,e,r,n)},monadic:function(e,t){var r,n;return r=t.cache.create(),n=t.serializer,o.bind(this,e,r,n)}}},25544:(e,t,r)=>{r.r(t),r.d(t,{ErrorCode:()=>u,FormatError:()=>ei,IntlMessageFormat:()=>ef,InvalidValueError:()=>ea,InvalidValueTypeError:()=>es,MissingValueError:()=>eu,PART_TYPE:()=>l,default:()=>ep,formatToParts:()=>ec,isFormatXMLElementFn:()=>el});var n,o,i,a,s,u,l,c=function(e,t){return(c=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)};function h(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}c(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}var f=function(){return(f=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var o in t=arguments[r])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function p(e,t,r){if(r||2==arguments.length)for(var n,o=0,i=t.length;o<i;o++)!n&&o in t||(n||(n=Array.prototype.slice.call(t,0,o)),n[o]=t[o]);return e.concat(n||Array.prototype.slice.call(t))}Object.create,Object.create;var m=("function"==typeof SuppressedError&&SuppressedError,r(98995));function d(e){return e.type===o.literal}function E(e){return e.type===o.number}function g(e){return e.type===o.date}function b(e){return e.type===o.time}function y(e){return e.type===o.select}function _(e){return e.type===o.plural}function v(e){return e.type===o.tag}function T(e){return!!(e&&"object"==typeof e&&e.type===i.number)}function A(e){return!!(e&&"object"==typeof e&&e.type===i.dateTime)}(function(e){e[e.EXPECT_ARGUMENT_CLOSING_BRACE=1]="EXPECT_ARGUMENT_CLOSING_BRACE",e[e.EMPTY_ARGUMENT=2]="EMPTY_ARGUMENT",e[e.MALFORMED_ARGUMENT=3]="MALFORMED_ARGUMENT",e[e.EXPECT_ARGUMENT_TYPE=4]="EXPECT_ARGUMENT_TYPE",e[e.INVALID_ARGUMENT_TYPE=5]="INVALID_ARGUMENT_TYPE",e[e.EXPECT_ARGUMENT_STYLE=6]="EXPECT_ARGUMENT_STYLE",e[e.INVALID_NUMBER_SKELETON=7]="INVALID_NUMBER_SKELETON",e[e.INVALID_DATE_TIME_SKELETON=8]="INVALID_DATE_TIME_SKELETON",e[e.EXPECT_NUMBER_SKELETON=9]="EXPECT_NUMBER_SKELETON",e[e.EXPECT_DATE_TIME_SKELETON=10]="EXPECT_DATE_TIME_SKELETON",e[e.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE=11]="UNCLOSED_QUOTE_IN_ARGUMENT_STYLE",e[e.EXPECT_SELECT_ARGUMENT_OPTIONS=12]="EXPECT_SELECT_ARGUMENT_OPTIONS",e[e.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE=13]="EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE=14]="INVALID_PLURAL_ARGUMENT_OFFSET_VALUE",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR=15]="EXPECT_SELECT_ARGUMENT_SELECTOR",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR=16]="EXPECT_PLURAL_ARGUMENT_SELECTOR",e[e.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT=17]="EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT",e[e.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT=18]="EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT",e[e.INVALID_PLURAL_ARGUMENT_SELECTOR=19]="INVALID_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_PLURAL_ARGUMENT_SELECTOR=20]="DUPLICATE_PLURAL_ARGUMENT_SELECTOR",e[e.DUPLICATE_SELECT_ARGUMENT_SELECTOR=21]="DUPLICATE_SELECT_ARGUMENT_SELECTOR",e[e.MISSING_OTHER_CLAUSE=22]="MISSING_OTHER_CLAUSE",e[e.INVALID_TAG=23]="INVALID_TAG",e[e.INVALID_TAG_NAME=25]="INVALID_TAG_NAME",e[e.UNMATCHED_CLOSING_TAG=26]="UNMATCHED_CLOSING_TAG",e[e.UNCLOSED_TAG=27]="UNCLOSED_TAG"})(n||(n={})),function(e){e[e.literal=0]="literal",e[e.argument=1]="argument",e[e.number=2]="number",e[e.date=3]="date",e[e.time=4]="time",e[e.select=5]="select",e[e.plural=6]="plural",e[e.pound=7]="pound",e[e.tag=8]="tag"}(o||(o={})),function(e){e[e.number=0]="number",e[e.dateTime=1]="dateTime"}(i||(i={}));var S=/[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/,I=/(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g,R=/[\t-\r \x85\u200E\u200F\u2028\u2029]/i,P=/^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g,H=/^(@+)?(\+|#+)?[rs]?$/g,N=/(\*)(0+)|(#+)(0+)|(0+)/g,B=/^(0+)$/;function O(e){var t={};return"r"===e[e.length-1]?t.roundingPriority="morePrecision":"s"===e[e.length-1]&&(t.roundingPriority="lessPrecision"),e.replace(H,function(e,r,n){return"string"!=typeof n?(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length):"+"===n?t.minimumSignificantDigits=r.length:"#"===r[0]?t.maximumSignificantDigits=r.length:(t.minimumSignificantDigits=r.length,t.maximumSignificantDigits=r.length+("string"==typeof n?n.length:0)),""}),t}function L(e){switch(e){case"sign-auto":return{signDisplay:"auto"};case"sign-accounting":case"()":return{currencySign:"accounting"};case"sign-always":case"+!":return{signDisplay:"always"};case"sign-accounting-always":case"()!":return{signDisplay:"always",currencySign:"accounting"};case"sign-except-zero":case"+?":return{signDisplay:"exceptZero"};case"sign-accounting-except-zero":case"()?":return{signDisplay:"exceptZero",currencySign:"accounting"};case"sign-never":case"+_":return{signDisplay:"never"}}}function M(e){return L(e)||{}}var C={"001":["H","h"],419:["h","H","hB","hb"],AC:["H","h","hb","hB"],AD:["H","hB"],AE:["h","hB","hb","H"],AF:["H","hb","hB","h"],AG:["h","hb","H","hB"],AI:["H","h","hb","hB"],AL:["h","H","hB"],AM:["H","hB"],AO:["H","hB"],AR:["h","H","hB","hb"],AS:["h","H"],AT:["H","hB"],AU:["h","hb","H","hB"],AW:["H","hB"],AX:["H"],AZ:["H","hB","h"],BA:["H","hB","h"],BB:["h","hb","H","hB"],BD:["h","hB","H"],BE:["H","hB"],BF:["H","hB"],BG:["H","hB","h"],BH:["h","hB","hb","H"],BI:["H","h"],BJ:["H","hB"],BL:["H","hB"],BM:["h","hb","H","hB"],BN:["hb","hB","h","H"],BO:["h","H","hB","hb"],BQ:["H"],BR:["H","hB"],BS:["h","hb","H","hB"],BT:["h","H"],BW:["H","h","hb","hB"],BY:["H","h"],BZ:["H","h","hb","hB"],CA:["h","hb","H","hB"],CC:["H","h","hb","hB"],CD:["hB","H"],CF:["H","h","hB"],CG:["H","hB"],CH:["H","hB","h"],CI:["H","hB"],CK:["H","h","hb","hB"],CL:["h","H","hB","hb"],CM:["H","h","hB"],CN:["H","hB","hb","h"],CO:["h","H","hB","hb"],CP:["H"],CR:["h","H","hB","hb"],CU:["h","H","hB","hb"],CV:["H","hB"],CW:["H","hB"],CX:["H","h","hb","hB"],CY:["h","H","hb","hB"],CZ:["H"],DE:["H","hB"],DG:["H","h","hb","hB"],DJ:["h","H"],DK:["H"],DM:["h","hb","H","hB"],DO:["h","H","hB","hb"],DZ:["h","hB","hb","H"],EA:["H","h","hB","hb"],EC:["h","H","hB","hb"],EE:["H","hB"],EG:["h","hB","hb","H"],EH:["h","hB","hb","H"],ER:["h","H"],ES:["H","hB","h","hb"],ET:["hB","hb","h","H"],FI:["H"],FJ:["h","hb","H","hB"],FK:["H","h","hb","hB"],FM:["h","hb","H","hB"],FO:["H","h"],FR:["H","hB"],GA:["H","hB"],GB:["H","h","hb","hB"],GD:["h","hb","H","hB"],GE:["H","hB","h"],GF:["H","hB"],GG:["H","h","hb","hB"],GH:["h","H"],GI:["H","h","hb","hB"],GL:["H","h"],GM:["h","hb","H","hB"],GN:["H","hB"],GP:["H","hB"],GQ:["H","hB","h","hb"],GR:["h","H","hb","hB"],GT:["h","H","hB","hb"],GU:["h","hb","H","hB"],GW:["H","hB"],GY:["h","hb","H","hB"],HK:["h","hB","hb","H"],HN:["h","H","hB","hb"],HR:["H","hB"],HU:["H","h"],IC:["H","h","hB","hb"],ID:["H"],IE:["H","h","hb","hB"],IL:["H","hB"],IM:["H","h","hb","hB"],IN:["h","H"],IO:["H","h","hb","hB"],IQ:["h","hB","hb","H"],IR:["hB","H"],IS:["H"],IT:["H","hB"],JE:["H","h","hb","hB"],JM:["h","hb","H","hB"],JO:["h","hB","hb","H"],JP:["H","K","h"],KE:["hB","hb","H","h"],KG:["H","h","hB","hb"],KH:["hB","h","H","hb"],KI:["h","hb","H","hB"],KM:["H","h","hB","hb"],KN:["h","hb","H","hB"],KP:["h","H","hB","hb"],KR:["h","H","hB","hb"],KW:["h","hB","hb","H"],KY:["h","hb","H","hB"],KZ:["H","hB"],LA:["H","hb","hB","h"],LB:["h","hB","hb","H"],LC:["h","hb","H","hB"],LI:["H","hB","h"],LK:["H","h","hB","hb"],LR:["h","hb","H","hB"],LS:["h","H"],LT:["H","h","hb","hB"],LU:["H","h","hB"],LV:["H","hB","hb","h"],LY:["h","hB","hb","H"],MA:["H","h","hB","hb"],MC:["H","hB"],MD:["H","hB"],ME:["H","hB","h"],MF:["H","hB"],MG:["H","h"],MH:["h","hb","H","hB"],MK:["H","h","hb","hB"],ML:["H"],MM:["hB","hb","H","h"],MN:["H","h","hb","hB"],MO:["h","hB","hb","H"],MP:["h","hb","H","hB"],MQ:["H","hB"],MR:["h","hB","hb","H"],MS:["H","h","hb","hB"],MT:["H","h"],MU:["H","h"],MV:["H","h"],MW:["h","hb","H","hB"],MX:["h","H","hB","hb"],MY:["hb","hB","h","H"],MZ:["H","hB"],NA:["h","H","hB","hb"],NC:["H","hB"],NE:["H"],NF:["H","h","hb","hB"],NG:["H","h","hb","hB"],NI:["h","H","hB","hb"],NL:["H","hB"],NO:["H","h"],NP:["H","h","hB"],NR:["H","h","hb","hB"],NU:["H","h","hb","hB"],NZ:["h","hb","H","hB"],OM:["h","hB","hb","H"],PA:["h","H","hB","hb"],PE:["h","H","hB","hb"],PF:["H","h","hB"],PG:["h","H"],PH:["h","hB","hb","H"],PK:["h","hB","H"],PL:["H","h"],PM:["H","hB"],PN:["H","h","hb","hB"],PR:["h","H","hB","hb"],PS:["h","hB","hb","H"],PT:["H","hB"],PW:["h","H"],PY:["h","H","hB","hb"],QA:["h","hB","hb","H"],RE:["H","hB"],RO:["H","hB"],RS:["H","hB","h"],RU:["H"],RW:["H","h"],SA:["h","hB","hb","H"],SB:["h","hb","H","hB"],SC:["H","h","hB"],SD:["h","hB","hb","H"],SE:["H"],SG:["h","hb","H","hB"],SH:["H","h","hb","hB"],SI:["H","hB"],SJ:["H"],SK:["H"],SL:["h","hb","H","hB"],SM:["H","h","hB"],SN:["H","h","hB"],SO:["h","H"],SR:["H","hB"],SS:["h","hb","H","hB"],ST:["H","hB"],SV:["h","H","hB","hb"],SX:["H","h","hb","hB"],SY:["h","hB","hb","H"],SZ:["h","hb","H","hB"],TA:["H","h","hb","hB"],TC:["h","hb","H","hB"],TD:["h","H","hB"],TF:["H","h","hB"],TG:["H","hB"],TH:["H","h"],TJ:["H","h"],TL:["H","hB","hb","h"],TM:["H","h"],TN:["h","hB","hb","H"],TO:["h","H"],TR:["H","hB"],TT:["h","hb","H","hB"],TW:["hB","hb","h","H"],TZ:["hB","hb","H","h"],UA:["H","hB","h"],UG:["hB","hb","H","h"],UM:["h","hb","H","hB"],US:["h","hb","H","hB"],UY:["h","H","hB","hb"],UZ:["H","hB","h"],VA:["H","h","hB"],VC:["h","hb","H","hB"],VE:["h","H","hB","hb"],VG:["h","hb","H","hB"],VI:["h","hb","H","hB"],VN:["H","h"],VU:["h","H"],WF:["H","hB"],WS:["h","H"],XK:["H","hB","h"],YE:["h","hB","hb","H"],YT:["H","hB"],ZA:["H","h","hb","hB"],ZM:["h","hb","H","hB"],ZW:["H","h"],"af-ZA":["H","h","hB","hb"],"ar-001":["h","hB","hb","H"],"ca-ES":["H","h","hB"],"en-001":["h","hb","H","hB"],"en-HK":["h","hb","H","hB"],"en-IL":["H","h","hb","hB"],"en-MY":["h","hb","H","hB"],"es-BR":["H","h","hB","hb"],"es-ES":["H","h","hB","hb"],"es-GQ":["H","h","hB","hb"],"fr-CA":["H","h","hB"],"gl-ES":["H","h","hB"],"gu-IN":["hB","hb","h","H"],"hi-IN":["hB","h","H"],"it-CH":["H","h","hB"],"it-IT":["H","h","hB"],"kn-IN":["hB","h","H"],"ml-IN":["hB","h","H"],"mr-IN":["hB","hb","h","H"],"pa-IN":["hB","hb","h","H"],"ta-IN":["hB","h","hb","H"],"te-IN":["hB","h","H"],"zu-ZA":["H","hB","hb","h"]},w=new RegExp("^".concat(S.source,"*")),F=new RegExp("".concat(S.source,"*$"));function U(e,t){return{start:e,end:t}}var G=!!String.prototype.startsWith&&"_a".startsWith("a",1),D=!!String.fromCodePoint,x=!!Object.fromEntries,j=!!String.prototype.codePointAt,k=!!String.prototype.trimStart,V=!!String.prototype.trimEnd,X=Number.isSafeInteger?Number.isSafeInteger:function(e){return"number"==typeof e&&isFinite(e)&&Math.floor(e)===e&&9007199254740991>=Math.abs(e)},K=!0;try{var Z=$("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");K=(null===(a=Z.exec("a"))||void 0===a?void 0:a[0])==="a"}catch(e){K=!1}var Y=G?function(e,t,r){return e.startsWith(t,r)}:function(e,t,r){return e.slice(r,r+t.length)===t},W=D?String.fromCodePoint:function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];for(var n="",o=t.length,i=0;o>i;){if((e=t[i++])>1114111)throw RangeError(e+" is not a valid code point");n+=e<65536?String.fromCharCode(e):String.fromCharCode(((e-=65536)>>10)+55296,e%1024+56320)}return n},z=x?Object.fromEntries:function(e){for(var t={},r=0;r<e.length;r++){var n=e[r],o=n[0],i=n[1];t[o]=i}return t},q=j?function(e,t){return e.codePointAt(t)}:function(e,t){var r,n=e.length;if(!(t<0)&&!(t>=n)){var o=e.charCodeAt(t);return o<55296||o>56319||t+1===n||(r=e.charCodeAt(t+1))<56320||r>57343?o:(o-55296<<10)+(r-56320)+65536}},Q=k?function(e){return e.trimStart()}:function(e){return e.replace(w,"")},J=V?function(e){return e.trimEnd()}:function(e){return e.replace(F,"")};function $(e,t){return new RegExp(e,t)}if(K){var ee=$("([^\\p{White_Space}\\p{Pattern_Syntax}]*)","yu");s=function(e,t){var r;return ee.lastIndex=t,null!==(r=ee.exec(e)[1])&&void 0!==r?r:""}}else s=function(e,t){for(var r=[];;){var n,o=q(e,t);if(void 0===o||en(o)||(n=o)>=33&&n<=35||36===n||n>=37&&n<=39||40===n||41===n||42===n||43===n||44===n||45===n||n>=46&&n<=47||n>=58&&n<=59||n>=60&&n<=62||n>=63&&n<=64||91===n||92===n||93===n||94===n||96===n||123===n||124===n||125===n||126===n||161===n||n>=162&&n<=165||166===n||167===n||169===n||171===n||172===n||174===n||176===n||177===n||182===n||187===n||191===n||215===n||247===n||n>=8208&&n<=8213||n>=8214&&n<=8215||8216===n||8217===n||8218===n||n>=8219&&n<=8220||8221===n||8222===n||8223===n||n>=8224&&n<=8231||n>=8240&&n<=8248||8249===n||8250===n||n>=8251&&n<=8254||n>=8257&&n<=8259||8260===n||8261===n||8262===n||n>=8263&&n<=8273||8274===n||8275===n||n>=8277&&n<=8286||n>=8592&&n<=8596||n>=8597&&n<=8601||n>=8602&&n<=8603||n>=8604&&n<=8607||8608===n||n>=8609&&n<=8610||8611===n||n>=8612&&n<=8613||8614===n||n>=8615&&n<=8621||8622===n||n>=8623&&n<=8653||n>=8654&&n<=8655||n>=8656&&n<=8657||8658===n||8659===n||8660===n||n>=8661&&n<=8691||n>=8692&&n<=8959||n>=8960&&n<=8967||8968===n||8969===n||8970===n||8971===n||n>=8972&&n<=8991||n>=8992&&n<=8993||n>=8994&&n<=9e3||9001===n||9002===n||n>=9003&&n<=9083||9084===n||n>=9085&&n<=9114||n>=9115&&n<=9139||n>=9140&&n<=9179||n>=9180&&n<=9185||n>=9186&&n<=9254||n>=9255&&n<=9279||n>=9280&&n<=9290||n>=9291&&n<=9311||n>=9472&&n<=9654||9655===n||n>=9656&&n<=9664||9665===n||n>=9666&&n<=9719||n>=9720&&n<=9727||n>=9728&&n<=9838||9839===n||n>=9840&&n<=10087||10088===n||10089===n||10090===n||10091===n||10092===n||10093===n||10094===n||10095===n||10096===n||10097===n||10098===n||10099===n||10100===n||10101===n||n>=10132&&n<=10175||n>=10176&&n<=10180||10181===n||10182===n||n>=10183&&n<=10213||10214===n||10215===n||10216===n||10217===n||10218===n||10219===n||10220===n||10221===n||10222===n||10223===n||n>=10224&&n<=10239||n>=10240&&n<=10495||n>=10496&&n<=10626||10627===n||10628===n||10629===n||10630===n||10631===n||10632===n||10633===n||10634===n||10635===n||10636===n||10637===n||10638===n||10639===n||10640===n||10641===n||10642===n||10643===n||10644===n||10645===n||10646===n||10647===n||10648===n||n>=10649&&n<=10711||10712===n||10713===n||10714===n||10715===n||n>=10716&&n<=10747||10748===n||10749===n||n>=10750&&n<=11007||n>=11008&&n<=11055||n>=11056&&n<=11076||n>=11077&&n<=11078||n>=11079&&n<=11084||n>=11085&&n<=11123||n>=11124&&n<=11125||n>=11126&&n<=11157||11158===n||n>=11159&&n<=11263||n>=11776&&n<=11777||11778===n||11779===n||11780===n||11781===n||n>=11782&&n<=11784||11785===n||11786===n||11787===n||11788===n||11789===n||n>=11790&&n<=11798||11799===n||n>=11800&&n<=11801||11802===n||11803===n||11804===n||11805===n||n>=11806&&n<=11807||11808===n||11809===n||11810===n||11811===n||11812===n||11813===n||11814===n||11815===n||11816===n||11817===n||n>=11818&&n<=11822||11823===n||n>=11824&&n<=11833||n>=11834&&n<=11835||n>=11836&&n<=11839||11840===n||11841===n||11842===n||n>=11843&&n<=11855||n>=11856&&n<=11857||11858===n||n>=11859&&n<=11903||n>=12289&&n<=12291||12296===n||12297===n||12298===n||12299===n||12300===n||12301===n||12302===n||12303===n||12304===n||12305===n||n>=12306&&n<=12307||12308===n||12309===n||12310===n||12311===n||12312===n||12313===n||12314===n||12315===n||12316===n||12317===n||n>=12318&&n<=12319||12320===n||12336===n||64830===n||64831===n||n>=65093&&n<=65094)break;r.push(o),t+=o>=65536?2:1}return W.apply(void 0,r)};var et=function(){function e(e,t){void 0===t&&(t={}),this.message=e,this.position={offset:0,line:1,column:1},this.ignoreTag=!!t.ignoreTag,this.locale=t.locale,this.requiresOtherClause=!!t.requiresOtherClause,this.shouldParseSkeletons=!!t.shouldParseSkeletons}return e.prototype.parse=function(){if(0!==this.offset())throw Error("parser can only be used once");return this.parseMessage(0,"",!1)},e.prototype.parseMessage=function(e,t,r){for(var i=[];!this.isEOF();){var a=this.char();if(123===a){var s=this.parseArgument(e,r);if(s.err)return s;i.push(s.val)}else if(125===a&&e>0)break;else if(35===a&&("plural"===t||"selectordinal"===t)){var u=this.clonePosition();this.bump(),i.push({type:o.pound,location:U(u,this.clonePosition())})}else if(60!==a||this.ignoreTag||47!==this.peek()){if(60===a&&!this.ignoreTag&&er(this.peek()||0)){var s=this.parseTag(e,t);if(s.err)return s;i.push(s.val)}else{var s=this.parseLiteral(e,t);if(s.err)return s;i.push(s.val)}}else{if(!r)return this.error(n.UNMATCHED_CLOSING_TAG,U(this.clonePosition(),this.clonePosition()));break}}return{val:i,err:null}},e.prototype.parseTag=function(e,t){var r=this.clonePosition();this.bump();var i=this.parseTagName();if(this.bumpSpace(),this.bumpIf("/>"))return{val:{type:o.literal,value:"<".concat(i,"/>"),location:U(r,this.clonePosition())},err:null};if(!this.bumpIf(">"))return this.error(n.INVALID_TAG,U(r,this.clonePosition()));var a=this.parseMessage(e+1,t,!0);if(a.err)return a;var s=a.val,u=this.clonePosition();if(!this.bumpIf("</"))return this.error(n.UNCLOSED_TAG,U(r,this.clonePosition()));if(this.isEOF()||!er(this.char()))return this.error(n.INVALID_TAG,U(u,this.clonePosition()));var l=this.clonePosition();return i!==this.parseTagName()?this.error(n.UNMATCHED_CLOSING_TAG,U(l,this.clonePosition())):(this.bumpSpace(),this.bumpIf(">"))?{val:{type:o.tag,value:i,children:s,location:U(r,this.clonePosition())},err:null}:this.error(n.INVALID_TAG,U(u,this.clonePosition()))},e.prototype.parseTagName=function(){var e,t=this.offset();for(this.bump();!this.isEOF()&&(45===(e=this.char())||46===e||e>=48&&e<=57||95===e||e>=97&&e<=122||e>=65&&e<=90||183==e||e>=192&&e<=214||e>=216&&e<=246||e>=248&&e<=893||e>=895&&e<=8191||e>=8204&&e<=8205||e>=8255&&e<=8256||e>=8304&&e<=8591||e>=11264&&e<=12271||e>=12289&&e<=55295||e>=63744&&e<=64975||e>=65008&&e<=65533||e>=65536&&e<=983039);)this.bump();return this.message.slice(t,this.offset())},e.prototype.parseLiteral=function(e,t){for(var r=this.clonePosition(),n="";;){var i=this.tryParseQuote(t);if(i){n+=i;continue}var a=this.tryParseUnquoted(e,t);if(a){n+=a;continue}var s=this.tryParseLeftAngleBracket();if(s){n+=s;continue}break}var u=U(r,this.clonePosition());return{val:{type:o.literal,value:n,location:u},err:null}},e.prototype.tryParseLeftAngleBracket=function(){var e;return this.isEOF()||60!==this.char()||!this.ignoreTag&&(er(e=this.peek()||0)||47===e)?null:(this.bump(),"<")},e.prototype.tryParseQuote=function(e){if(this.isEOF()||39!==this.char())return null;switch(this.peek()){case 39:return this.bump(),this.bump(),"'";case 123:case 60:case 62:case 125:break;case 35:if("plural"===e||"selectordinal"===e)break;return null;default:return null}this.bump();var t=[this.char()];for(this.bump();!this.isEOF();){var r=this.char();if(39===r){if(39===this.peek())t.push(39),this.bump();else{this.bump();break}}else t.push(r);this.bump()}return W.apply(void 0,t)},e.prototype.tryParseUnquoted=function(e,t){if(this.isEOF())return null;var r=this.char();return 60===r||123===r||35===r&&("plural"===t||"selectordinal"===t)||125===r&&e>0?null:(this.bump(),W(r))},e.prototype.parseArgument=function(e,t){var r=this.clonePosition();if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,U(r,this.clonePosition()));if(125===this.char())return this.bump(),this.error(n.EMPTY_ARGUMENT,U(r,this.clonePosition()));var i=this.parseIdentifierIfPossible().value;if(!i)return this.error(n.MALFORMED_ARGUMENT,U(r,this.clonePosition()));if(this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,U(r,this.clonePosition()));switch(this.char()){case 125:return this.bump(),{val:{type:o.argument,value:i,location:U(r,this.clonePosition())},err:null};case 44:if(this.bump(),this.bumpSpace(),this.isEOF())return this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,U(r,this.clonePosition()));return this.parseArgumentOptions(e,t,i,r);default:return this.error(n.MALFORMED_ARGUMENT,U(r,this.clonePosition()))}},e.prototype.parseIdentifierIfPossible=function(){var e=this.clonePosition(),t=this.offset(),r=s(this.message,t),n=t+r.length;return this.bumpTo(n),{value:r,location:U(e,this.clonePosition())}},e.prototype.parseArgumentOptions=function(e,t,r,a){var s,u=this.clonePosition(),l=this.parseIdentifierIfPossible().value,c=this.clonePosition();switch(l){case"":return this.error(n.EXPECT_ARGUMENT_TYPE,U(u,c));case"number":case"date":case"time":this.bumpSpace();var h=null;if(this.bumpIf(",")){this.bumpSpace();var p=this.clonePosition(),m=this.parseSimpleArgStyleIfPossible();if(m.err)return m;var d=J(m.val);if(0===d.length)return this.error(n.EXPECT_ARGUMENT_STYLE,U(this.clonePosition(),this.clonePosition()));h={style:d,styleLocation:U(p,this.clonePosition())}}var E=this.tryParseArgumentClose(a);if(E.err)return E;var g=U(a,this.clonePosition());if(h&&Y(null==h?void 0:h.style,"::",0)){var b,y=Q(h.style.slice(2));if("number"===l){var m=this.parseNumberSkeletonFromString(y,h.styleLocation);if(m.err)return m;return{val:{type:o.number,value:r,location:g,style:m.val},err:null}}if(0===y.length)return this.error(n.EXPECT_DATE_TIME_SKELETON,g);var _=y;this.locale&&(_=function(e,t){for(var r="",n=0;n<e.length;n++){var o=e.charAt(n);if("j"===o){for(var i=0;n+1<e.length&&e.charAt(n+1)===o;)i++,n++;var a=1+(1&i),s=i<2?1:3+(i>>1),u=function(e){var t,r=e.hourCycle;if(void 0===r&&e.hourCycles&&e.hourCycles.length&&(r=e.hourCycles[0]),r)switch(r){case"h24":return"k";case"h23":return"H";case"h12":return"h";case"h11":return"K";default:throw Error("Invalid hourCycle")}var n=e.language;return"root"!==n&&(t=e.maximize().region),(C[t||""]||C[n||""]||C["".concat(n,"-001")]||C["001"])[0]}(t);for(("H"==u||"k"==u)&&(s=0);s-- >0;)r+="a";for(;a-- >0;)r=u+r}else"J"===o?r+="H":r+=o}return r}(y,this.locale));var d={type:i.dateTime,pattern:_,location:h.styleLocation,parsedOptions:this.shouldParseSkeletons?(b={},_.replace(I,function(e){var t=e.length;switch(e[0]){case"G":b.era=4===t?"long":5===t?"narrow":"short";break;case"y":b.year=2===t?"2-digit":"numeric";break;case"Y":case"u":case"U":case"r":throw RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");case"q":case"Q":throw RangeError("`q/Q` (quarter) patterns are not supported");case"M":case"L":b.month=["numeric","2-digit","short","long","narrow"][t-1];break;case"w":case"W":throw RangeError("`w/W` (week) patterns are not supported");case"d":b.day=["numeric","2-digit"][t-1];break;case"D":case"F":case"g":throw RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");case"E":b.weekday=4===t?"long":5===t?"narrow":"short";break;case"e":if(t<4)throw RangeError("`e..eee` (weekday) patterns are not supported");b.weekday=["short","long","narrow","short"][t-4];break;case"c":if(t<4)throw RangeError("`c..ccc` (weekday) patterns are not supported");b.weekday=["short","long","narrow","short"][t-4];break;case"a":b.hour12=!0;break;case"b":case"B":throw RangeError("`b/B` (period) patterns are not supported, use `a` instead");case"h":b.hourCycle="h12",b.hour=["numeric","2-digit"][t-1];break;case"H":b.hourCycle="h23",b.hour=["numeric","2-digit"][t-1];break;case"K":b.hourCycle="h11",b.hour=["numeric","2-digit"][t-1];break;case"k":b.hourCycle="h24",b.hour=["numeric","2-digit"][t-1];break;case"j":case"J":case"C":throw RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");case"m":b.minute=["numeric","2-digit"][t-1];break;case"s":b.second=["numeric","2-digit"][t-1];break;case"S":case"A":throw RangeError("`S/A` (second) patterns are not supported, use `s` instead");case"z":b.timeZoneName=t<4?"short":"long";break;case"Z":case"O":case"v":case"V":case"X":case"x":throw RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead")}return""}),b):{}};return{val:{type:"date"===l?o.date:o.time,value:r,location:g,style:d},err:null}}return{val:{type:"number"===l?o.number:"date"===l?o.date:o.time,value:r,location:g,style:null!==(s=null==h?void 0:h.style)&&void 0!==s?s:null},err:null};case"plural":case"selectordinal":case"select":var v=this.clonePosition();if(this.bumpSpace(),!this.bumpIf(","))return this.error(n.EXPECT_SELECT_ARGUMENT_OPTIONS,U(v,f({},v)));this.bumpSpace();var T=this.parseIdentifierIfPossible(),A=0;if("select"!==l&&"offset"===T.value){if(!this.bumpIf(":"))return this.error(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,U(this.clonePosition(),this.clonePosition()));this.bumpSpace();var m=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE,n.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);if(m.err)return m;this.bumpSpace(),T=this.parseIdentifierIfPossible(),A=m.val}var S=this.tryParsePluralOrSelectOptions(e,l,t,T);if(S.err)return S;var E=this.tryParseArgumentClose(a);if(E.err)return E;var R=U(a,this.clonePosition());if("select"===l)return{val:{type:o.select,value:r,options:z(S.val),location:R},err:null};return{val:{type:o.plural,value:r,options:z(S.val),offset:A,pluralType:"plural"===l?"cardinal":"ordinal",location:R},err:null};default:return this.error(n.INVALID_ARGUMENT_TYPE,U(u,c))}},e.prototype.tryParseArgumentClose=function(e){return this.isEOF()||125!==this.char()?this.error(n.EXPECT_ARGUMENT_CLOSING_BRACE,U(e,this.clonePosition())):(this.bump(),{val:!0,err:null})},e.prototype.parseSimpleArgStyleIfPossible=function(){for(var e=0,t=this.clonePosition();!this.isEOF();)switch(this.char()){case 39:this.bump();var r=this.clonePosition();if(!this.bumpUntil("'"))return this.error(n.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE,U(r,this.clonePosition()));this.bump();break;case 123:e+=1,this.bump();break;case 125:if(!(e>0))return{val:this.message.slice(t.offset,this.offset()),err:null};e-=1;break;default:this.bump()}return{val:this.message.slice(t.offset,this.offset()),err:null}},e.prototype.parseNumberSkeletonFromString=function(e,t){var r=[];try{r=function(e){if(0===e.length)throw Error("Number skeleton cannot be empty");for(var t=e.split(R).filter(function(e){return e.length>0}),r=[],n=0;n<t.length;n++){var o=t[n].split("/");if(0===o.length)throw Error("Invalid number skeleton");for(var i=o[0],a=o.slice(1),s=0;s<a.length;s++)if(0===a[s].length)throw Error("Invalid number skeleton");r.push({stem:i,options:a})}return r}(e)}catch(e){return this.error(n.INVALID_NUMBER_SKELETON,t)}return{val:{type:i.number,tokens:r,location:t,parsedOptions:this.shouldParseSkeletons?function(e){for(var t={},r=0;r<e.length;r++){var n=e[r];switch(n.stem){case"percent":case"%":t.style="percent";continue;case"%x100":t.style="percent",t.scale=100;continue;case"currency":t.style="currency",t.currency=n.options[0];continue;case"group-off":case",_":t.useGrouping=!1;continue;case"precision-integer":case".":t.maximumFractionDigits=0;continue;case"measure-unit":case"unit":t.style="unit",t.unit=n.options[0].replace(/^(.*?)-/,"");continue;case"compact-short":case"K":t.notation="compact",t.compactDisplay="short";continue;case"compact-long":case"KK":t.notation="compact",t.compactDisplay="long";continue;case"scientific":t=f(f(f({},t),{notation:"scientific"}),n.options.reduce(function(e,t){return f(f({},e),L(t)||{})},{}));continue;case"engineering":t=f(f(f({},t),{notation:"engineering"}),n.options.reduce(function(e,t){return f(f({},e),L(t)||{})},{}));continue;case"notation-simple":t.notation="standard";continue;case"unit-width-narrow":t.currencyDisplay="narrowSymbol",t.unitDisplay="narrow";continue;case"unit-width-short":t.currencyDisplay="code",t.unitDisplay="short";continue;case"unit-width-full-name":t.currencyDisplay="name",t.unitDisplay="long";continue;case"unit-width-iso-code":t.currencyDisplay="symbol";continue;case"scale":t.scale=parseFloat(n.options[0]);continue;case"rounding-mode-floor":t.roundingMode="floor";continue;case"rounding-mode-ceiling":t.roundingMode="ceil";continue;case"rounding-mode-down":t.roundingMode="trunc";continue;case"rounding-mode-up":t.roundingMode="expand";continue;case"rounding-mode-half-even":t.roundingMode="halfEven";continue;case"rounding-mode-half-down":t.roundingMode="halfTrunc";continue;case"rounding-mode-half-up":t.roundingMode="halfExpand";continue;case"integer-width":if(n.options.length>1)throw RangeError("integer-width stems only accept a single optional option");n.options[0].replace(N,function(e,r,n,o,i,a){if(r)t.minimumIntegerDigits=n.length;else if(o&&i)throw Error("We currently do not support maximum integer digits");else if(a)throw Error("We currently do not support exact integer digits");return""});continue}if(B.test(n.stem)){t.minimumIntegerDigits=n.stem.length;continue}if(P.test(n.stem)){if(n.options.length>1)throw RangeError("Fraction-precision stems only accept a single optional option");n.stem.replace(P,function(e,r,n,o,i,a){return"*"===n?t.minimumFractionDigits=r.length:o&&"#"===o[0]?t.maximumFractionDigits=o.length:i&&a?(t.minimumFractionDigits=i.length,t.maximumFractionDigits=i.length+a.length):(t.minimumFractionDigits=r.length,t.maximumFractionDigits=r.length),""});var o=n.options[0];"w"===o?t=f(f({},t),{trailingZeroDisplay:"stripIfInteger"}):o&&(t=f(f({},t),O(o)));continue}if(H.test(n.stem)){t=f(f({},t),O(n.stem));continue}var i=L(n.stem);i&&(t=f(f({},t),i));var a=function(e){var t;if("E"===e[0]&&"E"===e[1]?(t={notation:"engineering"},e=e.slice(2)):"E"===e[0]&&(t={notation:"scientific"},e=e.slice(1)),t){var r=e.slice(0,2);if("+!"===r?(t.signDisplay="always",e=e.slice(2)):"+?"===r&&(t.signDisplay="exceptZero",e=e.slice(2)),!B.test(e))throw Error("Malformed concise eng/scientific notation");t.minimumIntegerDigits=e.length}return t}(n.stem);a&&(t=f(f({},t),a))}return t}(r):{}},err:null}},e.prototype.tryParsePluralOrSelectOptions=function(e,t,r,o){for(var i,a=!1,s=[],u=new Set,l=o.value,c=o.location;;){if(0===l.length){var h=this.clonePosition();if("select"!==t&&this.bumpIf("=")){var f=this.tryParseDecimalInteger(n.EXPECT_PLURAL_ARGUMENT_SELECTOR,n.INVALID_PLURAL_ARGUMENT_SELECTOR);if(f.err)return f;c=U(h,this.clonePosition()),l=this.message.slice(h.offset,this.offset())}else break}if(u.has(l))return this.error("select"===t?n.DUPLICATE_SELECT_ARGUMENT_SELECTOR:n.DUPLICATE_PLURAL_ARGUMENT_SELECTOR,c);"other"===l&&(a=!0),this.bumpSpace();var p=this.clonePosition();if(!this.bumpIf("{"))return this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT:n.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT,U(this.clonePosition(),this.clonePosition()));var m=this.parseMessage(e+1,t,r);if(m.err)return m;var d=this.tryParseArgumentClose(p);if(d.err)return d;s.push([l,{value:m.val,location:U(p,this.clonePosition())}]),u.add(l),this.bumpSpace(),l=(i=this.parseIdentifierIfPossible()).value,c=i.location}return 0===s.length?this.error("select"===t?n.EXPECT_SELECT_ARGUMENT_SELECTOR:n.EXPECT_PLURAL_ARGUMENT_SELECTOR,U(this.clonePosition(),this.clonePosition())):this.requiresOtherClause&&!a?this.error(n.MISSING_OTHER_CLAUSE,U(this.clonePosition(),this.clonePosition())):{val:s,err:null}},e.prototype.tryParseDecimalInteger=function(e,t){var r=1,n=this.clonePosition();this.bumpIf("+")||this.bumpIf("-")&&(r=-1);for(var o=!1,i=0;!this.isEOF();){var a=this.char();if(a>=48&&a<=57)o=!0,i=10*i+(a-48),this.bump();else break}var s=U(n,this.clonePosition());return o?X(i*=r)?{val:i,err:null}:this.error(t,s):this.error(e,s)},e.prototype.offset=function(){return this.position.offset},e.prototype.isEOF=function(){return this.offset()===this.message.length},e.prototype.clonePosition=function(){return{offset:this.position.offset,line:this.position.line,column:this.position.column}},e.prototype.char=function(){var e=this.position.offset;if(e>=this.message.length)throw Error("out of bound");var t=q(this.message,e);if(void 0===t)throw Error("Offset ".concat(e," is at invalid UTF-16 code unit boundary"));return t},e.prototype.error=function(e,t){return{val:null,err:{kind:e,message:this.message,location:t}}},e.prototype.bump=function(){if(!this.isEOF()){var e=this.char();10===e?(this.position.line+=1,this.position.column=1,this.position.offset+=1):(this.position.column+=1,this.position.offset+=e<65536?1:2)}},e.prototype.bumpIf=function(e){if(Y(this.message,e,this.offset())){for(var t=0;t<e.length;t++)this.bump();return!0}return!1},e.prototype.bumpUntil=function(e){var t=this.offset(),r=this.message.indexOf(e,t);return r>=0?(this.bumpTo(r),!0):(this.bumpTo(this.message.length),!1)},e.prototype.bumpTo=function(e){if(this.offset()>e)throw Error("targetOffset ".concat(e," must be greater than or equal to the current offset ").concat(this.offset()));for(e=Math.min(e,this.message.length);;){var t=this.offset();if(t===e)break;if(t>e)throw Error("targetOffset ".concat(e," is at invalid UTF-16 code unit boundary"));if(this.bump(),this.isEOF())break}},e.prototype.bumpSpace=function(){for(;!this.isEOF()&&en(this.char());)this.bump()},e.prototype.peek=function(){if(this.isEOF())return null;var e=this.char(),t=this.offset(),r=this.message.charCodeAt(t+(e>=65536?2:1));return null!=r?r:null},e}();function er(e){return e>=97&&e<=122||e>=65&&e<=90}function en(e){return e>=9&&e<=13||32===e||133===e||e>=8206&&e<=8207||8232===e||8233===e}function eo(e,t){void 0===t&&(t={});var r=new et(e,t=f({shouldParseSkeletons:!0,requiresOtherClause:!0},t)).parse();if(r.err){var o=SyntaxError(n[r.err.kind]);throw o.location=r.err.location,o.originalMessage=r.err.message,o}return(null==t?void 0:t.captureLocation)||function e(t){t.forEach(function(t){if(delete t.location,y(t)||_(t))for(var r in t.options)delete t.options[r].location,e(t.options[r].value);else E(t)&&T(t.style)?delete t.style.location:(g(t)||b(t))&&A(t.style)?delete t.style.location:v(t)&&e(t.children)})}(r.val),r.val}!function(e){e.MISSING_VALUE="MISSING_VALUE",e.INVALID_VALUE="INVALID_VALUE",e.MISSING_INTL_API="MISSING_INTL_API"}(u||(u={}));var ei=function(e){function t(t,r,n){var o=e.call(this,t)||this;return o.code=r,o.originalMessage=n,o}return h(t,e),t.prototype.toString=function(){return"[formatjs Error: ".concat(this.code,"] ").concat(this.message)},t}(Error),ea=function(e){function t(t,r,n,o){return e.call(this,'Invalid values for "'.concat(t,'": "').concat(r,'". Options are "').concat(Object.keys(n).join('", "'),'"'),u.INVALID_VALUE,o)||this}return h(t,e),t}(ei),es=function(e){function t(t,r,n){return e.call(this,'Value for "'.concat(t,'" must be of type ').concat(r),u.INVALID_VALUE,n)||this}return h(t,e),t}(ei),eu=function(e){function t(t,r){return e.call(this,'The intl string context variable "'.concat(t,'" was not provided to the string "').concat(r,'"'),u.MISSING_VALUE,r)||this}return h(t,e),t}(ei);function el(e){return"function"==typeof e}function ec(e,t,r,n,i,a,s){if(1===e.length&&d(e[0]))return[{type:l.literal,value:e[0].value}];for(var c=[],h=0;h<e.length;h++){var f=e[h];if(d(f)){c.push({type:l.literal,value:f.value});continue}if(f.type===o.pound){"number"==typeof a&&c.push({type:l.literal,value:r.getNumberFormat(t).format(a)});continue}var p=f.value;if(!(i&&p in i))throw new eu(p,s);var m=i[p];if(f.type===o.argument){m&&"string"!=typeof m&&"number"!=typeof m||(m="string"==typeof m||"number"==typeof m?String(m):""),c.push({type:"string"==typeof m?l.literal:l.object,value:m});continue}if(g(f)){var S="string"==typeof f.style?n.date[f.style]:A(f.style)?f.style.parsedOptions:void 0;c.push({type:l.literal,value:r.getDateTimeFormat(t,S).format(m)});continue}if(b(f)){var S="string"==typeof f.style?n.time[f.style]:A(f.style)?f.style.parsedOptions:n.time.medium;c.push({type:l.literal,value:r.getDateTimeFormat(t,S).format(m)});continue}if(E(f)){var S="string"==typeof f.style?n.number[f.style]:T(f.style)?f.style.parsedOptions:void 0;S&&S.scale&&(m*=S.scale||1),c.push({type:l.literal,value:r.getNumberFormat(t,S).format(m)});continue}if(v(f)){var I=f.children,R=f.value,P=i[R];if(!el(P))throw new es(R,"function",s);var H=P(ec(I,t,r,n,i,a).map(function(e){return e.value}));Array.isArray(H)||(H=[H]),c.push.apply(c,H.map(function(e){return{type:"string"==typeof e?l.literal:l.object,value:e}}))}if(y(f)){var N=f.options[m]||f.options.other;if(!N)throw new ea(f.value,m,Object.keys(f.options),s);c.push.apply(c,ec(N.value,t,r,n,i));continue}if(_(f)){var N=f.options["=".concat(m)];if(!N){if(!Intl.PluralRules)throw new ei('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n',u.MISSING_INTL_API,s);var B=r.getPluralRules(t,{type:f.pluralType}).select(m-(f.offset||0));N=f.options[B]||f.options.other}if(!N)throw new ea(f.value,m,Object.keys(f.options),s);c.push.apply(c,ec(N.value,t,r,n,i,m-(f.offset||0)));continue}}return c.length<2?c:c.reduce(function(e,t){var r=e[e.length-1];return r&&r.type===l.literal&&t.type===l.literal?r.value+=t.value:e.push(t),e},[])}function eh(e){return{create:function(){return{get:function(t){return e[t]},set:function(t,r){e[t]=r}}}}}!function(e){e[e.literal=0]="literal",e[e.object=1]="object"}(l||(l={}));var ef=function(){function e(t,r,n,o){void 0===r&&(r=e.defaultLocale);var i,a,s=this;if(this.formatterCache={number:{},dateTime:{},pluralRules:{}},this.format=function(e){var t=s.formatToParts(e);if(1===t.length)return t[0].value;var r=t.reduce(function(e,t){return e.length&&t.type===l.literal&&"string"==typeof e[e.length-1]?e[e.length-1]+=t.value:e.push(t.value),e},[]);return r.length<=1?r[0]||"":r},this.formatToParts=function(e){return ec(s.ast,s.locales,s.formatters,s.formats,e,void 0,s.message)},this.resolvedOptions=function(){var e;return{locale:(null===(e=s.resolvedLocale)||void 0===e?void 0:e.toString())||Intl.NumberFormat.supportedLocalesOf(s.locales)[0]}},this.getAst=function(){return s.ast},this.locales=r,this.resolvedLocale=e.resolveLocale(r),"string"==typeof t){if(this.message=t,!e.__parse)throw TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");var u=o||{},c=(u.formatters,function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,n=Object.getOwnPropertySymbols(e);o<n.length;o++)0>t.indexOf(n[o])&&Object.prototype.propertyIsEnumerable.call(e,n[o])&&(r[n[o]]=e[n[o]]);return r}(u,["formatters"]));this.ast=e.__parse(t,f(f({},c),{locale:this.resolvedLocale}))}else this.ast=t;if(!Array.isArray(this.ast))throw TypeError("A message must be provided as a String or AST.");this.formats=(i=e.formats,n?Object.keys(i).reduce(function(e,t){var r,o;return e[t]=(r=i[t],(o=n[t])?f(f(f({},r||{}),o||{}),Object.keys(r).reduce(function(e,t){return e[t]=f(f({},r[t]),o[t]||{}),e},{})):r),e},f({},i)):i),this.formatters=o&&o.formatters||(void 0===(a=this.formatterCache)&&(a={number:{},dateTime:{},pluralRules:{}}),{getNumberFormat:(0,m.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.NumberFormat).bind.apply(e,p([void 0],t,!1)))},{cache:eh(a.number),strategy:m.strategies.variadic}),getDateTimeFormat:(0,m.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.DateTimeFormat).bind.apply(e,p([void 0],t,!1)))},{cache:eh(a.dateTime),strategy:m.strategies.variadic}),getPluralRules:(0,m.memoize)(function(){for(var e,t=[],r=0;r<arguments.length;r++)t[r]=arguments[r];return new((e=Intl.PluralRules).bind.apply(e,p([void 0],t,!1)))},{cache:eh(a.pluralRules),strategy:m.strategies.variadic})})}return Object.defineProperty(e,"defaultLocale",{get:function(){return e.memoizedDefaultLocale||(e.memoizedDefaultLocale=new Intl.NumberFormat().resolvedOptions().locale),e.memoizedDefaultLocale},enumerable:!1,configurable:!0}),e.memoizedDefaultLocale=null,e.resolveLocale=function(e){if(void 0!==Intl.Locale){var t=Intl.NumberFormat.supportedLocalesOf(e);return new Intl.Locale(t.length>0?t[0]:"string"==typeof e?e:e[0])}},e.__parse=eo,e.formats={number:{integer:{maximumFractionDigits:0},currency:{style:"currency"},percent:{style:"percent"}},date:{short:{month:"numeric",day:"numeric",year:"2-digit"},medium:{month:"short",day:"numeric",year:"numeric"},long:{month:"long",day:"numeric",year:"numeric"},full:{weekday:"long",month:"long",day:"numeric",year:"numeric"}},time:{short:{hour:"numeric",minute:"numeric"},medium:{hour:"numeric",minute:"numeric",second:"numeric"},long:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"},full:{hour:"numeric",minute:"numeric",second:"numeric",timeZoneName:"short"}}},e}();let ep=ef},3186:(e,t,r)=>{function n(){return(n=Object.assign?Object.assign.bind():function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)({}).hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(null,arguments)}r.d(t,{Z:()=>p});var o=r(71159),i=r.n(o);let a=(0,r(68570).createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/node_modules/next-intl/dist/esm/shared/NextIntlClientProvider.js#default`);var s=r(35073);let u=(0,o.cache)(async function(){return Promise.resolve((await (0,s.Z)()).locale)}),l=(0,o.cache)(async function(e){return(await (0,s.Z)(e)).now});async function c(e){return l(null==e?void 0:e.locale)}let h=(0,o.cache)(async function(e){return(await (0,s.Z)(e)).timeZone});async function f(e){return h(null==e?void 0:e.locale)}async function p(e){let{locale:t,now:r,timeZone:o,...s}=e;return i().createElement(a,n({locale:null!=t?t:await u(),now:null!=r?r:await c(),timeZone:null!=o?o:await f()},s))}},40055:(e,t,r)=>{r.d(t,{V:()=>o,t:()=>i});let n=(0,r(71159).cache)(function(){return{locale:void 0}});function o(){return n().locale}function i(e){n().locale=e}},35073:(e,t,r)=>{r.d(t,{Z:()=>T});var n=r(58585),o=r(71159),i=r(77872);function a(e){return"function"==typeof e.then}var s=r(71615);let u="X-NEXT-INTL-LOCALE";var l=r(40055);let c=(0,o.cache)(async function(){let e=(0,s.headers)();return a(e)?await e:e}),h=(0,o.cache)(async function(){let e;try{e=(await c()).get(u)||void 0}catch(e){if(e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest){let t=Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e});throw t.digest=e.digest,t}throw e}return e});async function f(){return(0,l.V)()||await h()}let p=(0,o.cache)(function(){let e;try{e=(0,s.headers)().get(u)}catch(e){throw e instanceof Error&&"DYNAMIC_SERVER_USAGE"===e.digest?Error("Usage of next-intl APIs in Server Components currently opts into dynamic rendering. This limitation will eventually be lifted, but as a stopgap solution, you can use the `setRequestLocale` API to enable static rendering, see https://next-intl.dev/docs/getting-started/app-router/with-i18n-routing#static-rendering",{cause:e}):e}return e||(console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n"),(0,n.notFound)()),e});var m=r(69526);let d=!1,E=!1,g=(0,o.cache)(function(){return new Date}),b=(0,o.cache)(function(){return Intl.DateTimeFormat().resolvedOptions().timeZone}),y=(0,o.cache)(async function(e,t){if("function"!=typeof e)throw Error("Invalid i18n request configuration detected.\n\nPlease verify that:\n1. In case you've specified a custom location in your Next.js config, make sure that the path is correct.\n2. You have a default export in your i18n request configuration file.\n\nSee also: https://next-intl.dev/docs/usage/configuration#i18n-request\n");let r={get locale(){return E||(console.warn("\nThe `locale` parameter in `getRequestConfig` is deprecated, please switch to `await requestLocale`. See https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),E=!0),t||(0,l.V)()||p()},get requestLocale(){return t?Promise.resolve(t):f()}},o=e(r);a(o)&&(o=await o);let i=o.locale;return i||(d||(console.error("\nA `locale` is expected to be returned from `getRequestConfig`, but none was returned. This will be an error in the next major version of next-intl.\n\nSee: https://next-intl.dev/blog/next-intl-3-22#await-request-locale\n"),d=!0),(i=await r.requestLocale)||(console.error("\nUnable to find `next-intl` locale because the middleware didn't run on this request and no `locale` was returned in `getRequestConfig`. See https://next-intl.dev/docs/routing/middleware#unable-to-find-locale. The `notFound()` function will be called as a result.\n"),(0,n.notFound)())),{...o,locale:i,now:o.now||g(),timeZone:o.timeZone||b()}}),_=(0,o.cache)(i.PW),v=(0,o.cache)(i.PI),T=(0,o.cache)(async function(e){let t=await y(m.Z,e);return{...(0,i.tC)(t),_formatters:_(v())}})},75031:(e,t,r)=>{r.d(t,{Z:()=>a});var n=r(71159),o=r(35073);let i=(0,n.cache)(async function(e){return function(e){if(!e.messages)throw Error("No messages found. Have you configured them correctly? See https://next-intl.dev/docs/configuration#messages");return e.messages}(await (0,o.Z)(e))});async function a(e){return i(null==e?void 0:e.locale)}},30107:(e,t,r)=>{r.d(t,{Z:()=>n});function n(e){return e}},75013:(e,t,r)=>{r.d(t,{Z:()=>a});var n=r(71159),o=r(77872),i=r(35073),a=(0,n.cache)(async function(e){let t,r;"string"==typeof e?t=e:e&&(r=e.locale,t=e.namespace);let n=await (0,i.Z)(r);return(0,o.eX)({...n,namespace:t,messages:n.messages})})},58585:(e,t,r)=>{var n=r(61085);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}}),r.o(n,"redirect")&&r.d(t,{redirect:function(){return n.redirect}})},61085:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return a},RedirectType:function(){return n.RedirectType},notFound:function(){return o.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect}});let n=r(83953),o=r(16399);class i extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class a extends URLSearchParams{append(){throw new i}delete(){throw new i}set(){throw new i}sort(){throw new i}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},16399:(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{isNotFoundError:function(){return o},notFound:function(){return n}});let r="NEXT_NOT_FOUND";function n(){let e=Error(r);throw e.digest=r,e}function o(e){return"object"==typeof e&&null!==e&&"digest"in e&&e.digest===r}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8586:(e,t)=>{var r;Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"RedirectStatusCode",{enumerable:!0,get:function(){return r}}),function(e){e[e.SeeOther=303]="SeeOther",e[e.TemporaryRedirect=307]="TemporaryRedirect",e[e.PermanentRedirect=308]="PermanentRedirect"}(r||(r={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},83953:(e,t,r)=>{var n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{RedirectType:function(){return n},getRedirectError:function(){return u},getRedirectStatusCodeFromError:function(){return m},getRedirectTypeFromError:function(){return p},getURLFromRedirectError:function(){return f},isRedirectError:function(){return h},permanentRedirect:function(){return c},redirect:function(){return l}});let o=r(54580),i=r(72934),a=r(8586),s="NEXT_REDIRECT";function u(e,t,r){void 0===r&&(r=a.RedirectStatusCode.TemporaryRedirect);let n=Error(s);n.digest=s+";"+t+";"+e+";"+r+";";let i=o.requestAsyncStorage.getStore();return i&&(n.mutableCookies=i.mutableCookies),n}function l(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw u(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.TemporaryRedirect)}function c(e,t){void 0===t&&(t="replace");let r=i.actionAsyncStorage.getStore();throw u(e,t,(null==r?void 0:r.isAction)?a.RedirectStatusCode.SeeOther:a.RedirectStatusCode.PermanentRedirect)}function h(e){if("object"!=typeof e||null===e||!("digest"in e)||"string"!=typeof e.digest)return!1;let[t,r,n,o]=e.digest.split(";",4),i=Number(o);return t===s&&("replace"===r||"push"===r)&&"string"==typeof n&&!isNaN(i)&&i in a.RedirectStatusCode}function f(e){return h(e)?e.digest.split(";",3)[2]:null}function p(e){if(!h(e))throw Error("Not a redirect error");return e.digest.split(";",2)[1]}function m(e){if(!h(e))throw Error("Not a redirect error");return Number(e.digest.split(";",4)[3])}(function(e){e.push="push",e.replace="replace"})(n||(n={})),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},77872:(e,t,r)=>{var n=r(8381),o=r(96798);r(25544),r(71159),r(98995),n.IntlError,n.IntlErrorCode,n.createFormatter,t.PI=o.createCache,t.PW=o.createIntlFormatters,t.tC=o.initializeConfig,t.eX=function(e){let{_cache:t=o.createCache(),_formatters:r=o.createIntlFormatters(t),getMessageFallback:i=o.defaultGetMessageFallback,messages:a,namespace:s,onError:u=o.defaultOnError,...l}=e;return function(e,t){let{messages:r,namespace:o,...i}=e;return r=r["!"],o=n.resolveNamespace(o,"!"),n.createBaseTranslator({...i,messages:r,namespace:o})}({...l,onError:u,cache:t,formatters:r,getMessageFallback:i,messages:{"!":a},namespace:s?"!.".concat(s):"!"},0)}},8381:(e,t,r)=>{var n=r(25544),o=r(71159),i=r(96798),a=function(e){return e&&e.__esModule?e:{default:e}}(n);function s(e,t,r){var n;return(t="symbol"==typeof(n=function(e,t){if("object"!=typeof e||!e)return e;var r=e[Symbol.toPrimitive];if(void 0!==r){var n=r.call(e,t||"default");if("object"!=typeof n)return n;throw TypeError("@@toPrimitive must return a primitive value.")}return("string"===t?String:Number)(e)}(t,"string"))?n:n+"")in e?Object.defineProperty(e,t,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[t]=r,e}let u=function(e){return e.MISSING_MESSAGE="MISSING_MESSAGE",e.MISSING_FORMAT="MISSING_FORMAT",e.ENVIRONMENT_FALLBACK="ENVIRONMENT_FALLBACK",e.INSUFFICIENT_PATH="INSUFFICIENT_PATH",e.INVALID_MESSAGE="INVALID_MESSAGE",e.INVALID_KEY="INVALID_KEY",e.FORMATTING_ERROR="FORMATTING_ERROR",e}({});class l extends Error{constructor(e,t){let r=e;t&&(r+=": "+t),super(r),s(this,"code",void 0),s(this,"originalMessage",void 0),this.code=e,t&&(this.originalMessage=t)}}function c(e,t){return e?Object.keys(e).reduce((r,n)=>(r[n]={timeZone:t,...e[n]},r),{}):e}function h(e,t,r,n){let o=i.joinPath(n,r);if(!t)throw Error(o);let a=t;return r.split(".").forEach(t=>{let r=a[t];if(null==t||null==r)throw Error(o+" (".concat(e,")"));a=r}),a}let f=365/12*86400,p={second:1,seconds:1,minute:60,minutes:60,hour:3600,hours:3600,day:86400,days:86400,week:604800,weeks:604800,month:365/12*86400,months:365/12*86400,quarter:365/12*259200,quarters:365/12*259200,year:31536e3,years:31536e3};t.IntlError=l,t.IntlErrorCode=u,t.createBaseTranslator=function(e){let t=function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:i.defaultOnError;try{if(!t)throw Error(void 0);let n=r?h(e,t,r):t;if(!n)throw Error(r);return n}catch(t){let e=new l(u.MISSING_MESSAGE,t.message);return n(e),e}}(e.locale,e.messages,e.namespace,e.onError);return function(e){let{cache:t,defaultTranslationValues:r,formats:n,formatters:s,getMessageFallback:f=i.defaultGetMessageFallback,locale:p,messagesOrError:m,namespace:d,onError:E,timeZone:g}=e,b=m instanceof l;function y(e,t,r){let n=new l(t,r);return E(n),f({error:n,key:e,namespace:d})}function _(e,l,E){let _,v;if(b)return f({error:m,key:e,namespace:d});try{_=h(p,m,e,d)}catch(t){return y(e,u.MISSING_MESSAGE,t.message)}if("object"==typeof _){let t;return y(e,Array.isArray(_)?u.INVALID_MESSAGE:u.INSUFFICIENT_PATH,t)}let T=function(e,t){if(t)return;let r=e.replace(/'([{}])/gi,"$1");return/<|{/.test(r)?void 0:r}(_,l);if(T)return T;s.getMessageFormat||(s.getMessageFormat=i.memoFn(function(){return new a.default(arguments.length<=0?void 0:arguments[0],arguments.length<=1?void 0:arguments[1],arguments.length<=2?void 0:arguments[2],{formatters:s,...arguments.length<=3?void 0:arguments[3]})},t.message));try{v=s.getMessageFormat(_,p,function(e,t){let r=t?{...e,dateTime:c(e.dateTime,t)}:e,n=a.default.formats.date,o=t?c(n,t):n,i=a.default.formats.time,s=t?c(i,t):i;return{...r,date:{...o,...r.dateTime},time:{...s,...r.dateTime}}}({...n,...E},g),{formatters:{...s,getDateTimeFormat:(e,t)=>s.getDateTimeFormat(e,{timeZone:g,...t})}})}catch(t){return y(e,u.INVALID_MESSAGE,t.message)}try{let e=v.format(function(e){if(0===Object.keys(e).length)return;let t={};return Object.keys(e).forEach(r=>{let n,i=0,a=e[r];n="function"==typeof a?e=>{let t=a(e);return o.isValidElement(t)?o.cloneElement(t,{key:r+i++}):t}:a,t[r]=n}),t}({...r,...l}));if(null==e)throw Error(void 0);return o.isValidElement(e)||Array.isArray(e)||"string"==typeof e?e:String(e)}catch(t){return y(e,u.FORMATTING_ERROR,t.message)}}function v(e,t,r){let n=_(e,t,r);return"string"!=typeof n?y(e,u.INVALID_MESSAGE,void 0):n}return v.rich=_,v.markup=(e,t,r)=>{let n=_(e,t,r);if("string"!=typeof n){let t=new l(u.FORMATTING_ERROR,void 0);return E(t),f({error:t,key:e,namespace:d})}return n},v.raw=e=>{if(b)return f({error:m,key:e,namespace:d});try{return h(p,m,e,d)}catch(t){return y(e,u.MISSING_MESSAGE,t.message)}},v.has=e=>{if(b)return!1;try{return h(p,m,e,d),!0}catch(e){return!1}},v}({...e,messagesOrError:t})},t.createFormatter=function(e){let{_cache:t=i.createCache(),_formatters:r=i.createIntlFormatters(t),formats:n,locale:o,now:a,onError:s=i.defaultOnError,timeZone:c}=e;function h(e){var t;return null!==(t=e)&&void 0!==t&&t.timeZone||(c?e={...e,timeZone:c}:s(new l(u.ENVIRONMENT_FALLBACK,void 0))),e}function m(e,t,r,n){let o;try{o=function(e,t){let r;if("string"==typeof t){if(!(r=null==e?void 0:e[t])){let e=new l(u.MISSING_FORMAT,void 0);throw s(e),e}}else r=t;return r}(t,e)}catch(e){return n()}try{return r(o)}catch(e){return s(new l(u.FORMATTING_ERROR,e.message)),n()}}function d(e,t){return m(t,null==n?void 0:n.dateTime,t=>(t=h(t),r.getDateTimeFormat(o,t).format(e)),()=>String(e))}function E(){return a||(s(new l(u.ENVIRONMENT_FALLBACK,void 0)),new Date)}return{dateTime:d,number:function(e,t){return m(t,null==n?void 0:n.number,t=>r.getNumberFormat(o,t).format(e),()=>String(e))},relativeTime:function(e,t){try{var n;let i,a;let s={};t instanceof Date||"number"==typeof t?i=new Date(t):t&&(i=null!=t.now?new Date(t.now):E(),a=t.unit,s.style=t.style,s.numberingSystem=t.numberingSystem),i||(i=E());let u=(new Date(e).getTime()-i.getTime())/1e3;a||(a=function(e){let t=Math.abs(e);return t<60?"second":t<3600?"minute":t<86400?"hour":t<604800?"day":t<f?"week":t<31536e3?"month":"year"}(u)),s.numeric="second"===a?"auto":"always";let l=(n=a,Math.round(u/p[n]));return r.getRelativeTimeFormat(o,s).format(l,a)}catch(t){return s(new l(u.FORMATTING_ERROR,t.message)),String(e)}},list:function(e,t){let i=[],a=new Map,s=0;for(let t of e){let e;"object"==typeof t?(e=String(s),a.set(e,t)):e=String(t),i.push(e),s++}return m(t,null==n?void 0:n.list,e=>{let t=r.getListFormat(o,e).formatToParts(i).map(e=>"literal"===e.type?e.value:a.get(e.value)||e.value);return a.size>0?t:t.join("")},()=>String(e))},dateTimeRange:function(e,t,i){return m(i,null==n?void 0:n.dateTime,n=>(n=h(n),r.getDateTimeFormat(o,n).formatRange(e,t)),()=>[d(e),d(t)].join(" – "))}}},t.resolveNamespace=function(e,t){return e===t?void 0:e.slice((t+".").length)}},96798:(e,t,r)=>{var n=r(98995);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter(Boolean).join(".")}function i(e){return o(e.namespace,e.key)}function a(e){console.error(e)}function s(e,t){return n.memoize(e,{cache:{create:()=>({get:e=>t[e],set(e,r){t[e]=r}})},strategy:n.strategies.variadic})}function u(e,t){return s(function(){for(var t=arguments.length,r=Array(t),n=0;n<t;n++)r[n]=arguments[n];return new e(...r)},t)}t.createCache=function(){return{dateTime:{},number:{},message:{},relativeTime:{},pluralRules:{},list:{},displayNames:{}}},t.createIntlFormatters=function(e){return{getDateTimeFormat:u(Intl.DateTimeFormat,e.dateTime),getNumberFormat:u(Intl.NumberFormat,e.number),getPluralRules:u(Intl.PluralRules,e.pluralRules),getRelativeTimeFormat:u(Intl.RelativeTimeFormat,e.relativeTime),getListFormat:u(Intl.ListFormat,e.list),getDisplayNames:u(Intl.DisplayNames,e.displayNames)}},t.defaultGetMessageFallback=i,t.defaultOnError=a,t.initializeConfig=function(e){let{getMessageFallback:t,messages:r,onError:n,...o}=e;return{...o,messages:r,onError:n||a,getMessageFallback:t||i}},t.joinPath=o,t.memoFn=s}};