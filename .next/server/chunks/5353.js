"use strict";exports.id=5353,exports.ids=[5353],exports.modules={85353:(e,t,a)=>{a.d(t,{o:()=>r});var s=a(17577),n=a(94033),i=a(91653);let r=e=>{let[t,a]=(0,s.useState)(null),[r,l]=(0,s.useState)(0),[o,m]=(0,s.useState)(null),[c,d]=(0,s.useState)(!1),[u,g]=(0,s.useState)(null),p=(0,i.I6)(e||"anonymous","assessment_session");(0,s.useEffect)(()=>{let e=(0,i.mO)(p);e&&!e.completedAt&&(a(e),l(e.answers.length))},[p]),(0,s.useEffect)(()=>{t&&(0,i.RY)(p,t)},[t,p]);let h=t?n.n[t.locale]||n.n.en:[],y=h[r]||null,f=r>=h.length,v=h.length>0?Math.min(r/h.length*100,100):0,S=(0,s.useCallback)(e=>{a({id:`assessment_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,answers:[],startedAt:new Date().toISOString(),locale:e}),l(0),m(null),g(null)},[]),w=(0,s.useCallback)(e=>{t&&a(t=>{if(!t)return t;let a=[...t.answers.filter(t=>t.questionId!==e.questionId),e];return{...t,answers:a}})},[t]),b=(0,s.useCallback)(e=>{e>=0&&e<=h.length&&l(e)},[h.length]),M=(0,s.useCallback)(()=>{r>0&&l(e=>e-1)},[r]),A=(0,s.useCallback)(()=>{r<h.length&&l(e=>e+1)},[r,h.length]),I=(0,s.useCallback)((e,t)=>{let a=0,s=0;return t.forEach(t=>{let n=e.find(e=>e.questionId===t.id);if(n){if(s+=10*t.weight,"scale"===t.type)a+=n.value*t.weight;else if("single"===t.type){let e=t.options?.find(e=>e.value===n.value);e&&void 0!==e.weight&&(a+=e.weight*t.weight)}else"multiple"===t.type&&n.value.forEach(e=>{let s=t.options?.find(t=>t.value===e);s&&void 0!==s.weight&&(a+=s.weight*t.weight)})}}),Math.min(a,s)},[]),E=(0,s.useCallback)((e,t,a,s,n)=>{let i=[],r="en"===n;if(t>=80){let e=s?s("recommendations.emergencyMedical.title"):r?"Seek Immediate Medical Care":"建议立即就医",t=s?s("recommendations.emergencyMedical.description"):r?"Your symptoms may require professional medical evaluation and treatment":"您的症状可能需要专业医疗评估和治疗",a=s?s("recommendations.emergencyMedical.timeframe"):r?"Immediately":"立即",n=s?s("recommendations.emergencyMedical.actionSteps"):r?["Contact your gynecologist","If pain is severe, consider emergency care","Keep a detailed symptom diary"]:["联系您的妇科医生","如果疼痛剧烈，考虑急诊就医","记录详细的症状日志"],l=e&&!e.includes("recommendations.")?e:r?"Seek Immediate Medical Care":"建议立即就医",o=t&&!t.includes("recommendations.")?t:r?"Your symptoms may require professional medical evaluation and treatment":"您的症状可能需要专业医疗评估和治疗",m=a&&!a.includes("recommendations.")?a:r?"Immediately":"立即",c=Array.isArray(n)&&n.length>0&&!n[0]?.includes?.("recommendations.")?n:r?["Contact your gynecologist","If pain is severe, consider emergency care","Keep a detailed symptom diary"]:["联系您的妇科医生","如果疼痛剧烈，考虑急诊就医","记录详细的症状日志"];i.push({id:"emergency_medical",category:"medical",title:l,description:o,priority:"high",timeframe:m,actionSteps:Array.isArray(c)?c:[c]})}if(t>=40){let e=s?s("recommendations.painManagement.title"):r?"Pain Management Strategies":"疼痛管理策略",t=s?s("recommendations.painManagement.description"):r?"Multiple methods can help relieve menstrual pain":"多种方法可以帮助缓解经期疼痛",a=s?s("recommendations.painManagement.timeframe"):r?"Immediately available":"立即可用",n=s?s("recommendations.painManagement.actionSteps"):r?["Use heating pads or hot water bottles","Try light exercise like walking","Consider over-the-counter pain relievers (follow instructions)"]:["使用热敷垫或热水袋","尝试轻度运动如散步","考虑非处方止痛药（按说明使用）"],l=e&&!e.includes("recommendations.")?e:r?"Pain Management Strategies":"疼痛管理策略",o=t&&!t.includes("recommendations.")?t:r?"Multiple methods can help relieve menstrual pain":"多种方法可以帮助缓解经期疼痛",m=a&&!a.includes("recommendations.")?a:r?"Immediately available":"立即可用",c=Array.isArray(n)&&n.length>0&&!n[0]?.includes?.("recommendations.")?n:r?["Use heating pads or hot water bottles","Try light exercise like walking","Consider over-the-counter pain relievers (follow instructions)"]:["使用热敷垫或热水袋","尝试轻度运动如散步","考虑非处方止痛药（按说明使用）"];i.push({id:"pain_management",category:"immediate",title:l,description:o,priority:"high",timeframe:m,actionSteps:Array.isArray(c)?c:[c]})}let l=s?s("recommendations.lifestyleChanges.title"):r?"Lifestyle Adjustments":"生活方式调整",o=s?s("recommendations.lifestyleChanges.description"):r?"Long-term lifestyle changes can significantly improve symptoms":"长期的生活方式改变可以显著改善症状",m=s?s("recommendations.lifestyleChanges.timeframe"):r?"2-3 months to see effects":"2-3个月见效",c=s?s("recommendations.lifestyleChanges.actionSteps"):r?["Maintain regular exercise habits","Ensure adequate sleep","Learn stress management techniques","Maintain a balanced diet"]:["保持规律的运动习惯","确保充足的睡眠","学习压力管理技巧","保持均衡饮食"],d=l&&!l.includes("recommendations.")?l:r?"Lifestyle Adjustments":"生活方式调整",u=o&&!o.includes("recommendations.")?o:r?"Long-term lifestyle changes can significantly improve symptoms":"长期的生活方式改变可以显著改善症状",g=m&&!m.includes("recommendations.")?m:r?"2-3 months to see effects":"2-3个月见效",p=Array.isArray(c)&&c.length>0&&!c[0]?.includes?.("recommendations.")?c:r?["Maintain regular exercise habits","Ensure adequate sleep","Learn stress management techniques","Maintain a balanced diet"]:["保持规律的运动习惯","确保充足的睡眠","学习压力管理技巧","保持均衡饮食"];i.push({id:"lifestyle_changes",category:"lifestyle",title:d,description:u,priority:"medium",timeframe:g,actionSteps:Array.isArray(p)?p:[p]});let h=s?s("recommendations.selfcarePractices.title"):r?"Self-Care Practices":"自我护理实践",y=s?s("recommendations.selfcarePractices.description"):r?"Daily self-care can help you better manage symptoms":"日常的自我护理可以帮助您更好地管理症状",f=s?s("recommendations.selfcarePractices.timeframe"):r?"Ongoing":"持续进行",v=s?s("recommendations.selfcarePractices.actionSteps"):r?["Practice deep breathing and meditation","Use pain tracker to record symptoms","Build a support network","Learn relaxation techniques"]:["练习深呼吸和冥想","使用疼痛追踪器记录症状","建立支持网络","学习放松技巧"],S=h&&!h.includes("recommendations.")?h:r?"Self-Care Practices":"自我护理实践",w=y&&!y.includes("recommendations.")?y:r?"Daily self-care can help you better manage symptoms":"日常的自我护理可以帮助您更好地管理症状",b=f&&!f.includes("recommendations.")?f:r?"Ongoing":"持续进行",M=Array.isArray(v)&&v.length>0&&!v[0]?.includes?.("recommendations.")?v:r?["Practice deep breathing and meditation","Use pain tracker to record symptoms","Build a support network","Learn relaxation techniques"]:["练习深呼吸和冥想","使用疼痛追踪器记录症状","建立支持网络","学习放松技巧"];return i.push({id:"selfcare_practices",category:"selfcare",title:S,description:w,priority:"medium",timeframe:b,actionSteps:Array.isArray(M)?M:[M]}),i},[]),C=(0,s.useCallback)(e=>{if(!t)return null;d(!0);try{let s,n,i,r;let l=I(t.answers,h),o=h.reduce((e,t)=>e+10*t.weight,0),c=o>0?l/o*100:0,d="en"===t.locale;c>=80?(s="emergency",n="emergency",i=e?e("resultMessages.emergency"):d?"Your symptoms are quite severe. We recommend consulting a healthcare professional as soon as possible.":"您的症状较为严重，建议尽快咨询医疗专业人士。",r=e?e("resultMessages.emergencySummary"):d?"Assessment indicates you may need professional medical attention.":"评估显示您可能需要专业医疗关注。"):c>=60?(s="severe",n="severe",i=e?e("resultMessages.severe"):d?"Your symptoms are quite serious. We recommend adopting comprehensive management strategies.":"您的症状比较严重，建议采取综合管理策略。",r=e?e("resultMessages.severeSummary"):d?"Your symptoms require active management and possible medical intervention.":"您的症状需要积极的管理和可能的医疗干预。"):c>=40?(s="moderate",n="moderate",i=e?e("resultMessages.moderate"):d?"You have moderate symptoms that can be managed through various methods.":"您有中等程度的症状，可以通过多种方法进行管理。",r=e?e("resultMessages.moderateSummary"):d?"Your symptoms are manageable with recommended relief strategies.":"您的症状是可以管理的，建议采用多种缓解策略。"):(s="mild",n="mild",i=e?e("resultMessages.mild"):d?"Your symptoms are relatively mild and can be well managed through simple self-care.":"您的症状相对较轻，通过简单的自我护理就能很好地管理。",r=e?e("resultMessages.mildSummary"):d?"Your symptoms are mild and can be improved through lifestyle adjustments.":"您的症状较轻，可以通过生活方式调整来改善。");let u=E(l,c,t.answers,e,t.locale),g={sessionId:t.id,type:n,severity:s,score:l,maxScore:o,percentage:c,recommendations:u,emergency:c>=80,message:i,summary:r,relatedArticles:["/articles/menstrual-pain-management","/articles/lifestyle-tips-for-period-health","/articles/when-to-see-a-doctor"],nextSteps:e?[e("result.nextSteps.trackSymptoms")||(d?"Use pain tracker to record symptoms":"使用疼痛追踪器记录症状"),e("result.nextSteps.tryRecommendations")||(d?"Try recommended relief methods":"尝试推荐的缓解方法"),e("result.nextSteps.consultDoctor")||(d?"Consult a doctor if symptoms persist or worsen":"如果症状持续或恶化，请咨询医生")]:d?["Use pain tracker to record symptoms","Try recommended relief methods","Consult a doctor if symptoms persist or worsen"]:["使用疼痛追踪器记录症状","尝试推荐的缓解方法","如果症状持续或恶化，请咨询医生"],createdAt:new Date().toISOString()},p={...t,result:g,completedAt:new Date().toISOString()};return a(p),m(g),g}catch(s){let a=t?.locale==="en";return g(e?e("messages.assessmentFailed"):a?"An error occurred while completing the assessment. Please try again.":"评估完成时出现错误，请重试。"),null}finally{d(!1)}},[t,I,E,h]),k=(0,s.useCallback)(()=>{a(null),l(0),m(null),g(null),localStorage.removeItem(p)},[p]);return{currentSession:t,currentQuestionIndex:r,currentQuestion:y,isComplete:f,progress:v,totalQuestions:h.length,startAssessment:S,answerQuestion:w,goToQuestion:b,goToPreviousQuestion:M,goToNextQuestion:A,completeAssessment:C,resetAssessment:k,result:o,isLoading:c,error:u}}},91653:(e,t,a)=>{a.d(t,{B$:()=>u,I6:()=>m,RY:()=>c,Yp:()=>i,i5:()=>r,mO:()=>d,mn:()=>s});let s=e=>{let t="string"==typeof e?new Date(e):e,a=t.getFullYear(),s=String(t.getMonth()+1).padStart(2,"0"),n=String(t.getDate()).padStart(2,"0");return`${a}-${s}-${n}`},n=e=>!isNaN(new Date(e).getTime())&&!!e.match(/^\d{4}-\d{2}-\d{2}$/),i=e=>{let t=[];return e.date?n(e.date)||t.push({field:"date",message:"Invalid date format",code:"INVALID_FORMAT"}):t.push({field:"date",message:"Date is required",code:"REQUIRED"}),void 0===e.painLevel||null===e.painLevel?t.push({field:"painLevel",message:"Pain level is required",code:"REQUIRED"}):(e.painLevel<1||e.painLevel>10)&&t.push({field:"painLevel",message:"Pain level must be between 1 and 10",code:"OUT_OF_RANGE"}),void 0!==e.duration&&(e.duration<0||e.duration>1440)&&t.push({field:"duration",message:"Duration must be between 0 and 1440 minutes",code:"OUT_OF_RANGE"}),void 0!==e.effectiveness&&(e.effectiveness<1||e.effectiveness>5)&&t.push({field:"effectiveness",message:"Effectiveness must be between 1 and 5",code:"OUT_OF_RANGE"}),t},r=e=>{if(0===e.length)return{totalEntries:0,averagePain:0,maxPain:0,minPain:0,mostCommonSymptoms:[],mostEffectiveRemedies:[],painFrequency:{},trendDirection:"stable"};let t=e.map(e=>e.painLevel),a=t.reduce((e,t)=>e+t,0)/t.length,s={};e.forEach(e=>{e.symptoms.forEach(e=>{s[e]=(s[e]||0)+1})});let n=Object.entries(s).sort(([,e],[,t])=>t-e).slice(0,5).map(([e])=>e),i={};e.forEach(e=>{e.effectiveness&&e.remedies.length>0&&e.remedies.forEach(t=>{i[t]||(i[t]={total:0,count:0}),i[t].total+=e.effectiveness,i[t].count+=1})});let r=Object.entries(i).map(([e,t])=>({remedy:e,avgEffectiveness:t.total/t.count})).sort((e,t)=>t.avgEffectiveness-e.avgEffectiveness).slice(0,5).map(e=>e.remedy),m={};t.forEach(e=>{let t=l(e);m[t]=(m[t]||0)+1});let c=o(e);return{totalEntries:e.length,averagePain:Math.round(10*a)/10,maxPain:Math.max(...t),minPain:Math.min(...t),mostCommonSymptoms:n,mostEffectiveRemedies:r,painFrequency:m,trendDirection:c}},l=e=>e<=3?"Mild (1-3)":e<=6?"Moderate (4-6)":"Severe (7-10)",o=e=>{if(e.length<4)return"stable";let t=[...e].sort((e,t)=>new Date(e.date).getTime()-new Date(t.date).getTime()),a=t.slice(0,Math.floor(t.length/2)),s=t.slice(Math.floor(t.length/2)),n=a.reduce((e,t)=>e+t.painLevel,0)/a.length,i=s.reduce((e,t)=>e+t.painLevel,0)/s.length-n;return i>.5?"worsening":i<-.5?"improving":"stable"},m=(e,t)=>`periodhub_${t}_${e||"anonymous"}`,c=(e,t)=>{try{let a={data:t,version:"1.0.0",timestamp:new Date().toISOString()};return localStorage.setItem(e,JSON.stringify(a)),!0}catch(e){return!1}},d=e=>{try{let t=localStorage.getItem(e);if(!t)return null;return JSON.parse(t).data}catch(e){return null}},u=e=>{try{return localStorage.removeItem(e),!0}catch(e){return!1}}}};