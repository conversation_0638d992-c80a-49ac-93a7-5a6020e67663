{"/_not-found/page": "app/_not-found/page.js", "/test-assessment/page": "app/test-assessment/page.js", "/sitemap.xml/route": "app/sitemap.xml/route.js", "/image-requirements/page": "app/image-requirements/page.js", "/page": "app/page.js", "/test-symptom-assessment/page": "app/test-symptom-assessment/page.js", "/robots.txt/route": "app/robots.txt/route.js", "/[locale]/assessment/page": "app/[locale]/assessment/page.js", "/[locale]/cultural-charms/page": "app/[locale]/cultural-charms/page.js", "/[locale]/framework-demo/page": "app/[locale]/framework-demo/page.js", "/[locale]/immediate-relief/page": "app/[locale]/immediate-relief/page.js", "/[locale]/page": "app/[locale]/page.js", "/[locale]/privacy-policy/page": "app/[locale]/privacy-policy/page.js", "/[locale]/natural-therapies/page": "app/[locale]/natural-therapies/page.js", "/[locale]/pain-tracker/page": "app/[locale]/pain-tracker/page.js", "/[locale]/scenario-solutions/lifeStages/page": "app/[locale]/scenario-solutions/lifeStages/page.js", "/[locale]/special-therapies/page": "app/[locale]/special-therapies/page.js", "/[locale]/scenario-solutions/emergency-kit/page": "app/[locale]/scenario-solutions/emergency-kit/page.js", "/[locale]/scenario-solutions/sleep/page": "app/[locale]/scenario-solutions/sleep/page.js", "/[locale]/scenario-solutions/office/page": "app/[locale]/scenario-solutions/office/page.js", "/[locale]/teen-health/campus-guide/page": "app/[locale]/teen-health/campus-guide/page.js", "/[locale]/medical-disclaimer/page": "app/[locale]/medical-disclaimer/page.js", "/[locale]/scenario-solutions/social/page": "app/[locale]/scenario-solutions/social/page.js", "/[locale]/scenario-solutions/commute/page": "app/[locale]/scenario-solutions/commute/page.js", "/[locale]/teen-health/communication-guide/page": "app/[locale]/teen-health/communication-guide/page.js", "/[locale]/teen-health/page": "app/[locale]/teen-health/page.js", "/[locale]/scenario-solutions/page": "app/[locale]/scenario-solutions/page.js", "/[locale]/teen-health/emotional-support/page": "app/[locale]/teen-health/emotional-support/page.js", "/[locale]/terms-of-service/page": "app/[locale]/terms-of-service/page.js", "/[locale]/teen-health/development-pain/page": "app/[locale]/teen-health/development-pain/page.js", "/[locale]/health-guide/lifestyle/page": "app/[locale]/health-guide/lifestyle/page.js", "/[locale]/articles/page": "app/[locale]/articles/page.js", "/[locale]/health-guide/global-perspectives/page": "app/[locale]/health-guide/global-perspectives/page.js", "/[locale]/health-guide/page": "app/[locale]/health-guide/page.js", "/[locale]/articles/[slug]/page": "app/[locale]/articles/[slug]/page.js", "/[locale]/health-guide/medical-care/page": "app/[locale]/health-guide/medical-care/page.js", "/[locale]/health-guide/relief-methods/page": "app/[locale]/health-guide/relief-methods/page.js", "/[locale]/health-guide/myths-facts/page": "app/[locale]/health-guide/myths-facts/page.js", "/[locale]/interactive-tools/page": "app/[locale]/interactive-tools/page.js", "/[locale]/health-guide/understanding-pain/page": "app/[locale]/health-guide/understanding-pain/page.js", "/[locale]/scenario-solutions/exercise/page": "app/[locale]/scenario-solutions/exercise/page.js", "/[locale]/interactive-tools/[tool]/page": "app/[locale]/interactive-tools/[tool]/page.js", "/icon/route": "app/icon/route.js"}