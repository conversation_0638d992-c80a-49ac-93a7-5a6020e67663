(()=>{var e={};e.id=6717,e.ids=[6717],e.modules={60357:(e,t,a)=>{var r={"./en.json":[82559,2559],"./zh.json":[10966,966]};function n(e){if(!a.o(r,e))return Promise.resolve().then(()=>{var t=Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t});var t=r[e],n=t[0];return a.e(t[1]).then(()=>a.t(n,19))}n.keys=()=>Object.keys(r),n.id=60357,e.exports=n},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},30517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},59381:(e,t,a)=>{"use strict";a.r(t),a.d(t,{originalPathname:()=>_,patchFetch:()=>$,requestAsyncStorage:()=>F,routeModule:()=>v,serverHooks:()=>E,staticGenerationAsyncStorage:()=>w});var r={};a.r(r),a.d(r,{default:()=>c});var n={};a.r(n),a.d(n,{GET:()=>x});var i=a(49303),s=a(88716),o=a(60670),l=a(55661);let u=["en","zh"],p=["quick-menstrual-pain-relief-guide","anti-inflammatory-diet-menstrual-pain","heat-therapy-complete-guide","natural-physical-therapy-menstrual-pain","menstrual-pain-medical-guide","when-to-see-doctor-menstrual-pain","specific-situations-menstrual-pain-management","menstrual-pain-content-evaluation-report","comprehensive-menstrual-pain-research-summary","global-traditional-menstrual-pain-relief","nsaid-menstrual-pain-professional-guide"],m=["symptom-assessment","period-pain-assessment","pain-tracker","constitution-test"],d=["","articles","interactive-tools","teen-health","about","contact"];function c(){let e=process.env.NEXT_PUBLIC_BASE_URL||"https://period-hub.com",t=new Date,a=[];return u.forEach(r=>{d.forEach(n=>{let i=""===n?`${e}/${r}`:`${e}/${r}/${n}`;a.push({url:i,lastModified:t,changeFrequency:""===n?"daily":"articles"===n?"weekly":"monthly",priority:""===n?1:"articles"===n?.9:.8})}),p.forEach(n=>{a.push({url:`${e}/${r}/articles/${n}`,lastModified:t,changeFrequency:"monthly",priority:.7})}),m.forEach(n=>{a.push({url:`${e}/${r}/interactive-tools/${n}`,lastModified:t,changeFrequency:"weekly",priority:.8})})}),a}var h=a(60707);let f={...r},y=f.default,g=f.generateSitemaps;if("function"!=typeof y)throw Error('Default export is missing in "/Users/<USER>/Downloads/periodhub-health_副本01版/app/sitemap.ts"');async function x(e,t){let a;let{__metadata_id__:r,...n}=t.params||{},i=g?await g():null;if(i&&null==(a=i.find(e=>{let t=e.id.toString();return(t+=".xml")===r})?.id))return new l.NextResponse("Not Found",{status:404});let s=await y({id:a}),o=(0,h.resolveRouteData)(s,"sitemap");return new l.NextResponse(o,{headers:{"Content-Type":"application/xml","Cache-Control":"public, max-age=0, must-revalidate"}})}let v=new i.AppRouteRouteModule({definition:{kind:s.x.APP_ROUTE,page:"/sitemap.xml/route",pathname:"/sitemap.xml",filename:"sitemap",bundlePath:"app/sitemap.xml/route"},resolvedPagePath:"next-metadata-route-loader?page=%2Fsitemap.xml%2Froute&filePath=%2FUsers%2Fduting%2FDownloads%2Fperiodhub-health_%E5%89%AF%E6%9C%AC01%E7%89%88%2Fapp%2Fsitemap.ts&isDynamic=1!?__next_metadata_route__",nextConfigOutput:"",userland:n}),{requestAsyncStorage:F,staticGenerationAsyncStorage:w,serverHooks:E}=v,_="/sitemap.xml/route";function $(){return(0,o.patchFetch)({serverHooks:E,staticGenerationAsyncStorage:w})}}};var t=require("../../webpack-runtime.js");t.C(e);var a=e=>t(t.s=e),r=t.X(0,[8948,1346],()=>a(59381));module.exports=r})();