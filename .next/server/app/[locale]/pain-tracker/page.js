(()=>{var e={};e.id=5522,e.ids=[5522],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},4499:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>r.a,__next_app__:()=>x,originalPathname:()=>u,pages:()=>o,routeModule:()=>m,tree:()=>c}),s(49608),s(75110),s(21149),s(35866);var a=s(23191),l=s(88716),n=s(37922),r=s.n(n),i=s(95231),d={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>i[e]);s.d(t,d);let c=["",{children:["[locale]",{children:["pain-tracker",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,49608)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/pain-tracker/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,75110)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,21149)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/pain-tracker/page.tsx"],u="/[locale]/pain-tracker/page",x={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/[locale]/pain-tracker/page",pathname:"/[locale]/pain-tracker",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},37281:(e,t,s)=>{Promise.resolve().then(s.bind(s,74584))},74584:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r});var a=s(10326),l=s(17577),n=s(23844);function r(){let e=(0,n.useLocale)(),[t,s]=(0,l.useState)([]),[r,i]=(0,l.useState)(!1),[d,c]=(0,l.useState)({date:new Date().toISOString().split("T")[0],intensity:5,menstrualStatus:"",symptoms:[],treatments:[],effectiveness:5,notes:""}),o=(e,t)=>{c(s=>({...s,[e]:t}))},u=e=>{let t=d.symptoms||[];o("symptoms",t.includes(e)?t.filter(t=>t!==e):[...t,e])},x=()=>{if(t.length<2)return"stable";let e=t.slice(-3),s=t.slice(-6,-3),a=e.reduce((e,t)=>e+t.intensity,0)/e.length,l=s.length>0?s.reduce((e,t)=>e+t.intensity,0)/s.length:a;return a>l+.5?"increasing":a<l-.5?"decreasing":"stable"};return(0,a.jsxs)("div",{className:"space-y-8",children:[a.jsx("header",{className:"container-custom",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[a.jsx("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-800 mb-6",children:"zh"===e?"疼痛追踪器":"Pain Tracker"}),a.jsx("p",{className:"text-neutral-600 mb-8",children:"zh"===e?"记录您的疼痛模式，帮助识别触发因素和有效的治疗方法":"Track your pain patterns to help identify triggers and effective treatments"})]})}),a.jsx("section",{className:"container-custom",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 text-center",children:[a.jsx("div",{className:"text-3xl font-bold text-purple-600 mb-2",children:t.length}),a.jsx("p",{className:"text-neutral-600",children:"zh"===e?"总记录数":"Total Entries"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 text-center",children:[a.jsx("div",{className:"text-3xl font-bold text-red-600 mb-2",children:0===t.length?0:(t.reduce((e,t)=>e+t.intensity,0)/t.length).toFixed(1)}),a.jsx("p",{className:"text-neutral-600",children:"zh"===e?"平均疼痛强度":"Average Pain Intensity"})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6 text-center",children:[a.jsx("div",{className:`text-3xl font-bold mb-2 ${"increasing"===x()?"text-red-600":"decreasing"===x()?"text-green-600":"text-yellow-600"}`,children:"increasing"===x()?"↗":"decreasing"===x()?"↘":"→"}),a.jsx("p",{className:"text-neutral-600",children:"zh"===e?"最近趋势":"Recent Trend"})]})]}),a.jsx("div",{className:"text-center mb-8",children:a.jsx("button",{onClick:()=>i(!0),className:"btn-primary",children:"zh"===e?"添加新记录":"Add New Entry"})})]})}),r&&a.jsx("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50",children:a.jsx("div",{className:"bg-white rounded-lg shadow-lg max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[a.jsx("h2",{className:"text-2xl font-bold text-neutral-800",children:"zh"===e?"添加疼痛记录":"Add Pain Entry"}),a.jsx("button",{onClick:()=>i(!1),className:"text-neutral-500 hover:text-neutral-700",children:"✕"})]}),(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"zh"===e?"日期":"Date"}),a.jsx("input",{type:"date",value:d.date,onChange:e=>o("date",e.target.value),className:"w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-purple-500"})]}),(0,a.jsxs)("div",{children:[(0,a.jsxs)("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:["zh"===e?"疼痛强度":"Pain Intensity"," (",d.intensity,"/10)"]}),a.jsx("input",{type:"range",min:"0",max:"10",value:d.intensity,onChange:e=>o("intensity",parseInt(e.target.value)),className:"w-full h-2 bg-neutral-200 rounded-lg appearance-none cursor-pointer"})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"zh"===e?"月经状态":"Menstrual Status"}),(0,a.jsxs)("select",{value:d.menstrualStatus,onChange:e=>o("menstrualStatus",e.target.value),className:"w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-purple-500",children:[a.jsx("option",{value:"",children:"zh"===e?"请选择":"Please select"}),a.jsx("option",{value:"before",children:"zh"===e?"月经前":"Before period"}),a.jsx("option",{value:"during",children:"zh"===e?"月经期间":"During period"}),a.jsx("option",{value:"after",children:"zh"===e?"月经后":"After period"}),a.jsx("option",{value:"none",children:"zh"===e?"非月经期":"Not related to period"})]})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"zh"===e?"伴随症状":"Associated Symptoms"}),a.jsx("div",{className:"grid grid-cols-2 gap-2",children:[{value:"nausea",label:"zh"===e?"恶心":"Nausea"},{value:"headache",label:"zh"===e?"头痛":"Headache"},{value:"fatigue",label:"zh"===e?"疲劳":"Fatigue"},{value:"bloating",label:"zh"===e?"腹胀":"Bloating"},{value:"mood_changes",label:"zh"===e?"情绪变化":"Mood changes"},{value:"back_pain",label:"zh"===e?"背痛":"Back pain"}].map(e=>(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",checked:d.symptoms?.includes(e.value)||!1,onChange:()=>u(e.value),className:"w-4 h-4 text-purple-600 focus:ring-purple-500"}),a.jsx("span",{className:"ml-2 text-sm text-neutral-700",children:e.label})]},e.value))})]}),(0,a.jsxs)("div",{children:[a.jsx("label",{className:"block text-sm font-medium text-neutral-700 mb-2",children:"zh"===e?"备注":"Notes"}),a.jsx("textarea",{value:d.notes,onChange:e=>o("notes",e.target.value),rows:3,className:"w-full p-3 border border-neutral-300 rounded-lg focus:ring-2 focus:ring-purple-500",placeholder:"zh"===e?"记录任何额外信息...":"Record any additional information..."})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4",children:[a.jsx("button",{onClick:()=>i(!1),className:"px-6 py-2 border border-neutral-300 rounded-lg text-neutral-600 hover:bg-neutral-50",children:"zh"===e?"取消":"Cancel"}),a.jsx("button",{onClick:()=>{let e=[...t,{...d,id:Date.now().toString()}];s(e),localStorage.setItem("painEntries",JSON.stringify(e)),i(!1),c({date:new Date().toISOString().split("T")[0],intensity:5,menstrualStatus:"",symptoms:[],treatments:[],effectiveness:5,notes:""})},className:"btn-primary",children:"zh"===e?"保存":"Save"})]})]})]})})}),a.jsx("section",{className:"container-custom",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto",children:[a.jsx("h2",{className:"text-2xl font-bold text-neutral-800 mb-6",children:"zh"===e?"最近记录":"Recent Entries"}),0===t.length?a.jsx("div",{className:"text-center py-12",children:a.jsx("p",{className:"text-neutral-600",children:"zh"===e?"还没有记录，开始添加您的第一条记录吧！":"No entries yet. Start by adding your first entry!"})}):a.jsx("div",{className:"space-y-4",children:t.slice(-5).reverse().map(t=>(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-6",children:[(0,a.jsxs)("div",{className:"flex justify-between items-start mb-4",children:[(0,a.jsxs)("div",{children:[a.jsx("h3",{className:"font-semibold text-neutral-800",children:t.date}),a.jsx("p",{className:"text-sm text-neutral-600",children:t.menstrualStatus})]}),(0,a.jsxs)("div",{className:"text-right",children:[(0,a.jsxs)("div",{className:"text-2xl font-bold text-red-600",children:[t.intensity,"/10"]}),a.jsx("p",{className:"text-sm text-neutral-600",children:"zh"===e?"疼痛强度":"Pain Intensity"})]})]}),t.symptoms.length>0&&(0,a.jsxs)("div",{className:"mb-2",children:[a.jsx("span",{className:"text-sm font-medium text-neutral-700",children:"zh"===e?"症状：":"Symptoms: "}),a.jsx("span",{className:"text-sm text-neutral-600",children:t.symptoms.join(", ")})]}),t.notes&&a.jsx("p",{className:"text-sm text-neutral-600 italic",children:t.notes})]},t.id))})]})})]})}},49608:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});let a=(0,s(68570).createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/pain-tracker/page.tsx#default`)}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,1386,6621,6929,2810,6544],()=>s(4499));module.exports=a})();