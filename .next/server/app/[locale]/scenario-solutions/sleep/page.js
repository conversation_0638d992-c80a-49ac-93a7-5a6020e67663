(()=>{var e={};e.id=3884,e.ids=[3884],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},11987:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>h,pages:()=>d,routeModule:()=>p,tree:()=>c}),t(32742),t(75110),t(21149),t(35866);var i=t(23191),l=t(88716),a=t(37922),n=t.n(a),r=t(95231),o={};for(let e in r)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>r[e]);t.d(s,o);let c=["",{children:["[locale]",{children:["scenario-solutions",{children:["sleep",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,32742)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/sleep/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,75110)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,21149)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/scenario-solutions/sleep/page.tsx"],h="/[locale]/scenario-solutions/sleep/page",m={require:t,loadChunk:()=>Promise.resolve()},p=new i.AppPageRouteModule({definition:{kind:l.x.APP_PAGE,page:"/[locale]/scenario-solutions/sleep/page",pathname:"/[locale]/scenario-solutions/sleep",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83604:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,79404,23))},32742:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>f,generateMetadata:()=>g});var i=t(19510),l=t(75013),a=t(40055),n=t(57371);let r=(0,t(36478).Z)("Volume2",[["polygon",{points:"11 5 6 9 2 9 2 15 6 15 11 19 11 5",key:"16drj5"}],["path",{d:"M15.54 8.46a5 5 0 0 1 0 7.07",key:"ltjumu"}],["path",{d:"M19.07 4.93a10 10 0 0 1 0 14.14",key:"1kegas"}]]);var o=t(80702),c=t(47702),d=t(26978),h=t(71073),m=t(15064),p=t(75087),x=t(14120),u=t(39755);async function g({params:{locale:e}}){let s=await (0,l.Z)({locale:e,namespace:"scenarioSolutionsPage"});return{title:`${s("scenarios.sleep.title")} - ${s("title")}`,description:s("scenarios.sleep.description")}}async function f({params:{locale:e}}){(0,a.t)(e);let s=await (0,l.Z)("scenarioSolutionsPage");await (0,l.Z)("common");let t=[{type:"zh"===e?"白噪音音频":"White Noise Audio",icon:i.jsx(r,{className:"w-6 h-6"}),color:"bg-blue-50 text-blue-600",description:"zh"===e?"雨声、海浪声、风声、鸟鸣声等自然白噪音":"Rain, ocean waves, wind, bird sounds and other natural white noise",benefits:["zh"===e?"掩盖环境杂音，创造安静睡眠环境":"Mask environmental noise, create quiet sleep environment","zh"===e?"稳定频率刺激大脑产生α脑电波":"Stable frequency stimulates brain to produce alpha waves","zh"===e?"持续30-60分钟，缓解失眠和焦虑":"Play for 30-60 minutes, relieves insomnia and anxiety"]},{type:"zh"===e?"冥想引导音频":"Guided Meditation Audio",icon:i.jsx(o.Z,{className:"w-6 h-6"}),color:"bg-purple-50 text-purple-600",description:"zh"===e?"专业冥想导师引导的睡眠冥想练习":"Sleep meditation practices guided by professional meditation instructors",benefits:["zh"===e?"引导深呼吸、身体扫描、放松冥想":"Guides deep breathing, body scanning, relaxation meditation","zh"===e?"将注意力从疼痛转移到内在平静":"Shifts attention from pain to inner peace","zh"===e?"15-20分钟逐步放松身体各部位":"15-20 minutes gradually relaxing each body part"]},{type:"zh"===e?"生物反馈音乐":"Biofeedback Music",icon:i.jsx(c.Z,{className:"w-6 h-6"}),color:"bg-green-50 text-green-600",description:"zh"===e?"528Hz修复频率，抑制PGF2α合成酶活性":"528Hz healing frequency, inhibits PGF2α synthase activity",benefits:["zh"===e?"科学频率缓解生理疼痛":"Scientific frequency relieves physiological pain","zh"===e?"促进细胞修复和再生":"Promotes cell repair and regeneration","zh"===e?"降低心率，放松肌肉":"Lowers heart rate, relaxes muscles"]}],g=[{position:"zh"===e?"左侧卧位":"Left Side Position",effectiveness:"68%",description:"zh"===e?"双腿夹孕妇枕，骨盆倾斜15\xb0":"Pregnancy pillow between legs, pelvis tilted 15\xb0",benefits:["zh"===e?"减轻子宫对腹主动脉的压迫":"Reduces uterine pressure on abdominal aorta","zh"===e?"改善子宫血液供应，减少疼痛":"Improves uterine blood supply, reduces pain","zh"===e?"促进经血顺畅排出":"Promotes smooth menstrual flow"],tools:["zh"===e?"长条形抱枕":"Long body pillow","zh"===e?"孕妇枕":"Pregnancy pillow"]},{position:"zh"===e?"半仰卧位":"Semi-Supine Position",effectiveness:"55%",description:"zh"===e?"膝下垫楔形枕，腰部完全悬空":"Wedge pillow under knees, lower back completely suspended",benefits:["zh"===e?"促进盆腔血液回流":"Promotes pelvic blood return","zh"===e?"减轻盆腔充血和肿胀":"Reduces pelvic congestion and swelling","zh"===e?"缓解下腹部疼痛":"Relieves lower abdominal pain"],tools:["zh"===e?"楔形枕":"Wedge pillow","zh"===e?"腿部抬高垫":"Leg elevation pad"]},{position:"zh"===e?"蜷卧位（胎儿式）":"Fetal Position",effectiveness:"62%",description:"zh"===e?"双膝间夹枕头，类似子宫内姿势":"Pillow between knees, similar to in-utero position",benefits:["zh"===e?"带来安全感和舒适感":"Provides sense of security and comfort","zh"===e?"缓解身体紧张和疼痛":"Relieves body tension and pain","zh"===e?"减轻髋部和腰部压力":"Reduces hip and lower back pressure"],tools:["zh"===e?"膝间枕":"Knee pillow","zh"===e?"可微波暖宫枕":"Microwaveable warming pillow"]}],f={recommended:[{food:"zh"===e?"温牛奶+蜂蜜":"Warm Milk + Honey",component:"zh"===e?"色氨酸+天然糖分":"Tryptophan + Natural sugars",benefit:"zh"===e?"促进褪黑素分泌，帮助快速入睡":"Promotes melatonin secretion, helps fall asleep quickly"},{food:"zh"===e?"香蕉+杏仁奶":"Banana + Almond Milk",component:"zh"===e?"镁+色氨酸":"Magnesium + Tryptophan",benefit:"zh"===e?"放松肌肉，缓解痉挛":"Relaxes muscles, relieves cramps"},{food:"zh"===e?"樱桃汁":"Cherry Juice",component:"zh"===e?"天然褪黑素":"Natural melatonin",benefit:"zh"===e?"调节睡眠节律，改善睡眠质量":"Regulates sleep rhythm, improves sleep quality"},{food:"zh"===e?"全麦面包+坚果":"Whole Grain Bread + Nuts",component:"zh"===e?"复合碳水+镁":"Complex carbs + Magnesium",benefit:"zh"===e?"稳定血糖，持续释放能量":"Stabilizes blood sugar, sustained energy release"}],avoid:[{item:"zh"===e?"咖啡因饮料":"Caffeinated beverages",reason:"zh"===e?"睡前6小时内避免，刺激神经系统":"Avoid 6 hours before bed, stimulates nervous system"},{item:"zh"===e?"酒精":"Alcohol",reason:"zh"===e?"影响深度睡眠，可能增加经血流量":"Affects deep sleep, may increase menstrual flow"},{item:"zh"===e?"高糖食物":"High-sugar foods",reason:"zh"===e?"造成血糖波动，影响睡眠稳定性":"Causes blood sugar fluctuations, affects sleep stability"},{item:"zh"===e?"辛辣食物":"Spicy foods",reason:"zh"===e?"刺激肠胃，可能影响睡眠质量":"Irritates digestive system, may affect sleep quality"}]},b=[{aspect:"zh"===e?"声波疗法":"Sound Therapy",details:"zh"===e?"432Hz子宫修复频率音频":"432Hz uterine healing frequency audio",icon:i.jsx(r,{className:"w-5 h-5"})},{aspect:"zh"===e?"光环境":"Light Environment",details:"zh"===e?"琥珀色夜灯（抑制褪黑素干扰）":"Amber night light (prevents melatonin interference)",icon:i.jsx(d.Z,{className:"w-5 h-5"})},{aspect:"zh"===e?"触觉反馈":"Tactile Feedback",details:"zh"===e?"重力被（7-9kg压力缓解焦虑）":"Weighted blanket (7-9kg pressure relieves anxiety)",icon:i.jsx(h.Z,{className:"w-5 h-5"})},{aspect:"zh"===e?"温度控制":"Temperature Control",details:"zh"===e?"室温18-22℃，湿度50-60%":"Room temp 18-22℃, humidity 50-60%",icon:i.jsx(c.Z,{className:"w-5 h-5"})}];return(0,i.jsxs)("div",{className:"space-y-12",children:[(0,i.jsxs)("nav",{className:"flex items-center space-x-2 text-sm text-neutral-600",children:[i.jsx(n.default,{href:`/${e}/scenario-solutions`,className:"hover:text-primary-600 transition-colors",children:s("title")}),i.jsx("span",{children:"/"}),i.jsx("span",{className:"text-neutral-800",children:s("scenarios.sleep.title")})]}),(0,i.jsxs)("header",{className:"text-center",children:[i.jsx("div",{className:"w-16 h-16 bg-purple-50 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-6",children:i.jsx(d.Z,{className:"w-8 h-8"})}),i.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-primary-700 mb-4",children:s("scenarios.sleep.title")}),i.jsx("p",{className:"text-lg text-neutral-600 max-w-3xl mx-auto",children:s("scenarios.sleep.description")})]}),(0,i.jsxs)("section",{className:"bg-gradient-to-br from-purple-50 to-neutral-50 p-6 md:p-8 rounded-xl",children:[(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[i.jsx(r,{className:"w-6 h-6 text-purple-600 mr-3"}),i.jsx("h2",{className:"text-2xl font-semibold text-neutral-800",children:"zh"===e?"三维助眠音频系统":"Three-Dimensional Sleep Audio System"})]}),i.jsx("div",{className:"grid md:grid-cols-3 gap-6",children:t.map((e,s)=>(0,i.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[i.jsx("div",{className:`w-12 h-12 rounded-full flex items-center justify-center ${e.color} mb-4`,children:e.icon}),i.jsx("h3",{className:"text-lg font-semibold text-neutral-800 mb-3",children:e.type}),i.jsx("p",{className:"text-neutral-600 mb-4 text-sm",children:e.description}),i.jsx("ul",{className:"space-y-2",children:e.benefits.map((e,s)=>(0,i.jsxs)("li",{className:"flex items-start text-sm text-neutral-700",children:[i.jsx(m.Z,{className:"w-4 h-4 text-green-500 mr-2 mt-0.5 flex-shrink-0"}),e]},s))})]},s))})]}),(0,i.jsxs)("section",{children:[(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[i.jsx(h.Z,{className:"w-6 h-6 text-purple-600 mr-3"}),i.jsx("h2",{className:"text-2xl font-semibold text-neutral-800",children:"zh"===e?"科学睡姿矩阵":"Scientific Sleep Position Matrix"})]}),i.jsx("div",{className:"grid md:grid-cols-1 lg:grid-cols-3 gap-6",children:g.map((s,t)=>(0,i.jsxs)("div",{className:"card",children:[(0,i.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[i.jsx("h3",{className:"text-lg font-semibold text-neutral-800",children:s.position}),(0,i.jsxs)("span",{className:"bg-green-100 text-green-800 text-sm font-medium px-3 py-1 rounded-full",children:["zh"===e?"缓解率":"Relief Rate"," ",s.effectiveness]})]}),(0,i.jsxs)("p",{className:"text-neutral-600 mb-4 text-sm",children:[i.jsx("strong",{children:"zh"===e?"实施要点：":"Key Points: "}),s.description]}),(0,i.jsxs)("div",{className:"mb-4",children:[i.jsx("h4",{className:"font-medium text-neutral-800 mb-2",children:"zh"===e?"功效：":"Benefits:"}),i.jsx("ul",{className:"space-y-1",children:s.benefits.map((e,s)=>(0,i.jsxs)("li",{className:"flex items-start text-sm text-neutral-700",children:[i.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full mr-2 mt-2"}),e]},s))})]}),(0,i.jsxs)("div",{children:[i.jsx("h4",{className:"font-medium text-neutral-800 mb-2",children:"zh"===e?"辅助工具：":"Support Tools:"}),i.jsx("div",{className:"flex flex-wrap gap-2",children:s.tools.map((e,s)=>i.jsx("span",{className:"bg-purple-100 text-purple-700 text-xs px-2 py-1 rounded",children:e},s))})]})]},t))})]}),(0,i.jsxs)("section",{children:[(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[i.jsx(p.Z,{className:"w-6 h-6 text-purple-600 mr-3"}),i.jsx("h2",{className:"text-2xl font-semibold text-neutral-800",children:"zh"===e?"睡前饮食建议":"Bedtime Nutrition Recommendations"})]}),(0,i.jsxs)("div",{className:"grid md:grid-cols-2 gap-8",children:[(0,i.jsxs)("div",{className:"bg-green-50 p-6 rounded-lg",children:[i.jsx("h3",{className:"text-lg font-semibold text-green-800 mb-4",children:"zh"===e?"推荐食物":"Recommended Foods"}),i.jsx("div",{className:"space-y-4",children:f.recommended.map((s,t)=>(0,i.jsxs)("div",{className:"border-l-4 border-green-400 pl-4",children:[i.jsx("h4",{className:"font-medium text-green-800",children:s.food}),(0,i.jsxs)("p",{className:"text-sm text-green-600 mb-1",children:[i.jsx("strong",{children:"zh"===e?"主要成分：":"Key Components: "}),s.component]}),i.jsx("p",{className:"text-sm text-green-700",children:s.benefit})]},t))})]}),(0,i.jsxs)("div",{className:"bg-red-50 p-6 rounded-lg",children:[i.jsx("h3",{className:"text-lg font-semibold text-red-800 mb-4",children:"zh"===e?"避免食物":"Foods to Avoid"}),i.jsx("div",{className:"space-y-4",children:f.avoid.map((e,s)=>(0,i.jsxs)("div",{className:"border-l-4 border-red-400 pl-4",children:[i.jsx("h4",{className:"font-medium text-red-800",children:e.item}),i.jsx("p",{className:"text-sm text-red-700",children:e.reason})]},s))})]})]})]}),(0,i.jsxs)("section",{children:[(0,i.jsxs)("div",{className:"flex items-center mb-6",children:[i.jsx(d.Z,{className:"w-6 h-6 text-purple-600 mr-3"}),i.jsx("h2",{className:"text-2xl font-semibold text-neutral-800",children:"zh"===e?"睡眠环境优化":"Sleep Environment Optimization"})]}),i.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6",children:b.map((e,s)=>(0,i.jsxs)("div",{className:"card text-center",children:[i.jsx("div",{className:"w-12 h-12 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-4",children:e.icon}),i.jsx("h3",{className:"text-lg font-semibold text-neutral-800 mb-2",children:e.aspect}),i.jsx("p",{className:"text-sm text-neutral-600",children:e.details})]},s))})]}),(0,i.jsxs)("section",{className:"bg-yellow-50 p-6 md:p-8 rounded-xl",children:[(0,i.jsxs)("h3",{className:"text-lg font-semibold text-yellow-800 mb-4 flex items-center",children:[i.jsx(x.Z,{className:"w-5 h-5 mr-2"}),"zh"===e?"睡眠时间管理":"Sleep Time Management"]}),(0,i.jsxs)("div",{className:"grid md:grid-cols-3 gap-6 text-sm text-yellow-700",children:[(0,i.jsxs)("div",{children:[i.jsx("h4",{className:"font-medium mb-2",children:"zh"===e?"睡前2小时":"2 Hours Before Bed"}),(0,i.jsxs)("ul",{className:"space-y-1",children:[(0,i.jsxs)("li",{children:["• ","zh"===e?"停止进食，避免消化负担":"Stop eating, avoid digestive burden"]}),(0,i.jsxs)("li",{children:["• ","zh"===e?"开始播放助眠音频":"Start playing sleep audio"]}),(0,i.jsxs)("li",{children:["• ","zh"===e?"调暗室内灯光":"Dim room lighting"]})]})]}),(0,i.jsxs)("div",{children:[i.jsx("h4",{className:"font-medium mb-2",children:"zh"===e?"睡前1小时":"1 Hour Before Bed"}),(0,i.jsxs)("ul",{className:"space-y-1",children:[(0,i.jsxs)("li",{children:["• ","zh"===e?"温水洗澡，放松身心":"Warm bath, relax body and mind"]}),(0,i.jsxs)("li",{children:["• ","zh"===e?"进行轻柔拉伸运动":"Gentle stretching exercises"]}),(0,i.jsxs)("li",{children:["• ","zh"===e?"避免使用电子设备":"Avoid electronic devices"]})]})]}),(0,i.jsxs)("div",{children:[i.jsx("h4",{className:"font-medium mb-2",children:"zh"===e?"睡前30分钟":"30 Minutes Before Bed"}),(0,i.jsxs)("ul",{className:"space-y-1",children:[(0,i.jsxs)("li",{children:["• ","zh"===e?"调整到最佳睡姿":"Adjust to optimal sleep position"]}),(0,i.jsxs)("li",{children:["• ","zh"===e?"使用暖宫贴或热水袋":"Use warming patches or hot water bottle"]}),(0,i.jsxs)("li",{children:["• ","zh"===e?"进行深呼吸练习":"Practice deep breathing"]})]})]})]})]}),i.jsx("div",{className:"text-center",children:(0,i.jsxs)(n.default,{href:`/${e}/scenario-solutions`,className:"inline-flex items-center text-primary-600 hover:text-primary-700 font-medium transition-colors",children:[i.jsx(u.Z,{className:"w-4 h-4 mr-2"}),"zh"===e?"返回场景解决方案总览":"Back to Scenario Solutions Overview"]})})]})}},36478:(e,s,t)=>{"use strict";t.d(s,{Z:()=>n});var i=t(71159),l={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),n=(e,s)=>{let t=(0,i.forwardRef)(({color:t="currentColor",size:n=24,strokeWidth:r=2,absoluteStrokeWidth:o,className:c="",children:d,...h},m)=>(0,i.createElement)("svg",{ref:m,...l,width:n,height:n,stroke:t,strokeWidth:o?24*Number(r)/Number(n):r,className:["lucide",`lucide-${a(e)}`,c].join(" "),...h},[...s.map(([e,s])=>(0,i.createElement)(e,s)),...Array.isArray(d)?d:[d]]));return t.displayName=`${e}`,t}},39755:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i=(0,t(36478).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},71073:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i=(0,t(36478).Z)("Bed",[["path",{d:"M2 4v16",key:"vw9hq8"}],["path",{d:"M2 8h18a2 2 0 0 1 2 2v10",key:"1dgv2r"}],["path",{d:"M2 17h20",key:"18nfp3"}],["path",{d:"M6 8v9",key:"1yriud"}]])},15064:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i=(0,t(36478).Z)("CheckCircle",[["path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14",key:"g774vq"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},14120:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i=(0,t(36478).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},75087:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i=(0,t(36478).Z)("Coffee",[["path",{d:"M17 8h1a4 4 0 1 1 0 8h-1",key:"jx4kbh"}],["path",{d:"M3 8h14v9a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4Z",key:"1bxrl0"}],["line",{x1:"6",x2:"6",y1:"2",y2:"4",key:"1cr9l3"}],["line",{x1:"10",x2:"10",y1:"2",y2:"4",key:"170wym"}],["line",{x1:"14",x2:"14",y1:"2",y2:"4",key:"1c5f70"}]])},80702:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i=(0,t(36478).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},26978:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i=(0,t(36478).Z)("Moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]])},47702:(e,s,t)=>{"use strict";t.d(s,{Z:()=>i});let i=(0,t(36478).Z)("Thermometer",[["path",{d:"M14 4v10.54a4 4 0 1 1-4 0V4a2 2 0 0 1 4 0Z",key:"17jzev"}]])},57371:(e,s,t)=>{"use strict";t.d(s,{default:()=>l.a});var i=t(670),l=t.n(i)},670:(e,s,t)=>{"use strict";let{createProxy:i}=t(68570);e.exports=i("/Users/<USER>/Downloads/periodhub-health_副本01版/node_modules/next/dist/client/link.js")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),i=s.X(0,[8948,1386,6621,6929,2810,6544],()=>t(11987));module.exports=i})();