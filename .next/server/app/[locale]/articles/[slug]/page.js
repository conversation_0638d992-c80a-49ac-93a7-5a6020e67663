(()=>{var e={};e.id=5902,e.ids=[5902],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},78893:e=>{"use strict";e.exports=require("buffer")},92048:e=>{"use strict";e.exports=require("fs")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},83579:(e,t,n)=>{"use strict";n.r(t),n.d(t,{GlobalError:()=>s.a,__next_app__:()=>d,originalPathname:()=>h,pages:()=>u,routeModule:()=>p,tree:()=>c}),n(9079),n(75110),n(21149),n(35866);var r=n(23191),a=n(88716),i=n(37922),s=n.n(i),o=n(95231),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);n.d(t,l);let c=["",{children:["[locale]",{children:["articles",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(n.bind(n,9079)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/articles/[slug]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(n.bind(n,75110)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(n.bind(n,21149)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(n.t.bind(n,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(n.bind(n,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/articles/[slug]/page.tsx"],h="/[locale]/articles/[slug]/page",d={require:n,loadChunk:()=>Promise.resolve()},p=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/articles/[slug]/page",pathname:"/[locale]/articles/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},28785:(e,t,n)=>{Promise.resolve().then(n.bind(n,92515)),Promise.resolve().then(n.bind(n,63350)),Promise.resolve().then(n.bind(n,98258)),Promise.resolve().then(n.bind(n,29197)),Promise.resolve().then(n.bind(n,86561)),Promise.resolve().then(n.bind(n,16403)),Promise.resolve().then(n.t.bind(n,79404,23))},92515:(e,t,n)=>{"use strict";n.d(t,{default:()=>d});var r=n(10326),a=n(17577),i=n(76557);let s=(0,i.Z)("Eye",[["path",{d:"M2 12s3-7 10-7 10 7 10 7-3 7-10 7-10-7-10-7Z",key:"rwhkz3"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]);var o=n(67427);let l=(0,i.Z)("Bookmark",[["path",{d:"m19 21-7-4-7 4V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2v16z",key:"1fy3hk"}]]),c=(0,i.Z)("Share2",[["circle",{cx:"18",cy:"5",r:"3",key:"gq8acd"}],["circle",{cx:"6",cy:"12",r:"3",key:"w7nqdw"}],["circle",{cx:"18",cy:"19",r:"3",key:"1xt0gg"}],["line",{x1:"8.59",x2:"15.42",y1:"13.51",y2:"17.49",key:"47mynk"}],["line",{x1:"15.41",x2:"8.59",y1:"6.51",y2:"10.49",key:"1n3mei"}]]),u=(0,i.Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]);var h=n(43810);function d({articleId:e,articleTitle:t,locale:n,className:i=""}){let[d,p]=(0,a.useState)(0),[m,f]=(0,a.useState)(!1),[E,T]=(0,a.useState)(!1),[g,A]=(0,a.useState)(0),[_,k]=(0,a.useState)(!1),[S,b]=(0,a.useState)(!1),y=async()=>{try{await navigator.clipboard.writeText(window.location.href),b(!0),setTimeout(()=>b(!1),2e3)}catch(e){}},N=e=>{let n=encodeURIComponent(window.location.href),r=encodeURIComponent(t),a={facebook:`https://www.facebook.com/sharer/sharer.php?u=${n}`,twitter:`https://twitter.com/intent/tweet?url=${n}&text=${r}`,linkedin:`https://www.linkedin.com/sharing/share-offsite/?url=${n}`,whatsapp:`https://wa.me/?text=${r} ${n}`,telegram:`https://t.me/share/url?url=${n}&text=${r}`};a[e]&&window.open(a[e],"_blank","width=600,height=400"),k(!1)},C={zh:{like:"点赞文章",liked:"已点赞",bookmark:"收藏",bookmarked:"已收藏",share:"分享",views:"阅读量",likes:"点赞",copyLink:"复制链接",copied:"已复制",shareToFacebook:"分享到 Facebook",shareToTwitter:"分享到 Twitter",shareToLinkedIn:"分享到 LinkedIn",shareToWhatsApp:"分享到 WhatsApp",shareToTelegram:"分享到 Telegram"},en:{like:"Like Article",liked:"Liked",bookmark:"Bookmark",bookmarked:"Bookmarked",share:"Share",views:"Views",likes:"Likes",copyLink:"Copy Link",copied:"Copied",shareToFacebook:"Share to Facebook",shareToTwitter:"Share to Twitter",shareToLinkedIn:"Share to LinkedIn",shareToWhatsApp:"Share to WhatsApp",shareToTelegram:"Share to Telegram"}}[n];return(0,r.jsxs)("div",{className:`bg-white border border-gray-200 rounded-lg p-4 sm:p-6 ${i}`,children:[r.jsx("div",{className:"flex items-center justify-between mb-4 text-sm text-gray-600",children:(0,r.jsxs)("div",{className:"flex items-center gap-4",children:[(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[r.jsx(s,{className:"w-4 h-4"}),g.toLocaleString()," ",C.views]}),(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[r.jsx(o.Z,{className:"w-4 h-4"}),d," ",C.likes]})]})}),(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2 sm:gap-3",children:[(0,r.jsxs)("button",{onClick:()=>{let t=!m,n=t?d+1:d-1;f(t),p(n),localStorage.setItem(`liked_${e}`,t.toString()),localStorage.setItem(`likes_${e}`,n.toString())},className:`flex items-center gap-2 px-3 sm:px-4 py-2 rounded-lg transition-all duration-200 ${m?"bg-red-500 text-white hover:bg-red-600":"bg-gray-100 text-gray-700 hover:bg-red-50 hover:text-red-600"}`,children:[r.jsx(o.Z,{className:`w-4 h-4 ${m?"fill-current":""}`}),r.jsx("span",{className:"text-sm font-medium hidden sm:inline",children:m?C.liked:C.like})]}),(0,r.jsxs)("button",{onClick:()=>{let t=!E;T(t),localStorage.setItem(`bookmarked_${e}`,t.toString());let n=JSON.parse(localStorage.getItem("bookmarks")||"[]");if(t)n.includes(e)||n.push(e);else{let t=n.indexOf(e);t>-1&&n.splice(t,1)}localStorage.setItem("bookmarks",JSON.stringify(n))},className:`flex items-center gap-2 px-3 sm:px-4 py-2 rounded-lg transition-all duration-200 ${E?"bg-blue-500 text-white hover:bg-blue-600":"bg-gray-100 text-gray-700 hover:bg-blue-50 hover:text-blue-600"}`,children:[r.jsx(l,{className:`w-4 h-4 ${E?"fill-current":""}`}),r.jsx("span",{className:"text-sm font-medium hidden sm:inline",children:E?C.bookmarked:C.bookmark})]})]}),(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsxs)("button",{onClick:()=>k(!_),className:"flex items-center gap-2 px-3 sm:px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors",children:[r.jsx(c,{className:"w-4 h-4"}),r.jsx("span",{className:"text-sm font-medium hidden sm:inline",children:C.share})]}),_&&(0,r.jsxs)("div",{className:"absolute right-0 top-full mt-2 bg-white border border-gray-200 rounded-lg shadow-lg py-2 z-10 min-w-48",children:[(0,r.jsxs)("button",{onClick:y,className:"w-full px-4 py-2 text-left text-sm hover:bg-gray-50 flex items-center gap-2",children:[S?r.jsx(u,{className:"w-4 h-4 text-green-500"}):r.jsx(h.Z,{className:"w-4 h-4"}),S?C.copied:C.copyLink]}),r.jsx("div",{className:"border-t border-gray-100 my-1"}),(0,r.jsxs)("button",{onClick:()=>N("facebook"),className:"w-full px-4 py-2 text-left text-sm hover:bg-gray-50",children:["\uD83D\uDCD8 ",C.shareToFacebook]}),(0,r.jsxs)("button",{onClick:()=>N("twitter"),className:"w-full px-4 py-2 text-left text-sm hover:bg-gray-50",children:["\uD83D\uDC26 ",C.shareToTwitter]}),(0,r.jsxs)("button",{onClick:()=>N("linkedin"),className:"w-full px-4 py-2 text-left text-sm hover:bg-gray-50",children:["\uD83D\uDCBC ",C.shareToLinkedIn]}),(0,r.jsxs)("button",{onClick:()=>N("whatsapp"),className:"w-full px-4 py-2 text-left text-sm hover:bg-gray-50",children:["\uD83D\uDCAC ",C.shareToWhatsApp]}),(0,r.jsxs)("button",{onClick:()=>N("telegram"),className:"w-full px-4 py-2 text-left text-sm hover:bg-gray-50",children:["✈️ ",C.shareToTelegram]})]})]})]}),_&&r.jsx("div",{className:"fixed inset-0 z-0",onClick:()=>k(!1)})]})}},63350:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var r=n(10326);function a({content:e}){return r.jsx(r.Fragment,{children:r.jsx("div",{className:"nsaid-article-content",dangerouslySetInnerHTML:{__html:function(e){let t=[],n=e.replace(/<div[\s\S]*?<\/div>/g,e=>{let n=t.length;return t.push(e),`__HTML_BLOCK_${n}__`});return n=n.replace(/^# (.*$)/gim,"<h1>$1</h1>").replace(/^## (.*$)/gim,"<h2>$1</h2>").replace(/^### (.*$)/gim,"<h3>$1</h3>").replace(/^#### (.*$)/gim,"<h4>$1</h4>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/^> (.*$)/gim,"<blockquote>$1</blockquote>").replace(/```([\s\S]*?)```/g,"<pre><code>$1</code></pre>").replace(/`(.*?)`/g,"<code>$1</code>").replace(/\[([^\]]+)\]\(([^)]+)\)/g,'<a href="$2">$1</a>').replace(/\|(.+)\|\n\|[-\s|]+\|\n((?:\|.+\|\n?)*)/g,(e,t,n)=>{let r="<tr>"+t.split("|").map(e=>e.trim()).filter(Boolean).map((e,t)=>`<th class="border border-gray-300 px-4 py-3 bg-primary-100 font-semibold ${0===t?"text-center":"text-left"} text-primary-800">${e}</th>`).join("")+"</tr>",a=n.trim().split("\n").map(e=>'<tr class="even:bg-gray-50 hover:bg-primary-25">'+e.replace(/^\||\|$/g,"").split("|").map(e=>e.trim()).map((e,t)=>`<td class="border border-gray-300 px-4 py-3 text-neutral-700 ${0===t?"text-center":"text-left"}">${e}</td>`).join("")+"</tr>").join("");return`<div class="overflow-x-auto my-6"><table class="min-w-full border-collapse border border-gray-300 bg-white rounded-lg shadow-sm"><thead class="bg-primary-50">${r}</thead><tbody>${a}</tbody></table></div>`}).replace(/\n\n/g,"</p><p>").replace(/\n/g,"<br>").replace(/^(?!<[h1-6]|<div|<blockquote|<pre|<ul|<ol|<li|__HTML_BLOCK)(.+)/gim,"<p>$1</p>"),t.forEach((e,t)=>{n=n.replace(`__HTML_BLOCK_${t}__`,e)}),(n=n.replace(/<video([^>]*id="nsaidAnimationPlayer"[^>]*)>/g,'<video$1 style="display: block !important; width: 100% !important; height: auto !important; min-height: 250px !important; background: #000 !important; opacity: 1 !important; visibility: visible !important; position: relative !important; z-index: 100 !important;" controls playsinline>')).includes("nsaidAnimationPlayer"),n}(e)}})})}n(17577)},98258:(e,t,n)=>{"use strict";n.d(t,{default:()=>s});var r=n(10326);n(17577);var a=n(44064),i=n.n(a);function s({locale:e}){return(0,r.jsxs)(r.Fragment,{children:[r.jsx(i(),{src:"https://unpkg.com/lucide@latest",strategy:"beforeInteractive",onLoad:()=>console.log("✅ Lucide script loaded"),onError:e=>console.error("❌ Lucide script failed:",e)}),r.jsx(i(),{src:"/scripts/nsaid-interactive.js",strategy:"afterInteractive",onLoad:()=>console.log("✅ NSAID interactive script loaded"),onError:e=>console.error("❌ NSAID interactive script failed:",e)})]})}},29197:(e,t,n)=>{"use strict";n.d(t,{default:()=>s});var r=n(10326),a=n(17577);let i=(0,n(76557).Z)("ArrowUp",[["path",{d:"m5 12 7-7 7 7",key:"hav0vg"}],["path",{d:"M12 19V5",key:"x0mq9r"}]]);function s({locale:e}){let[t,n]=(0,a.useState)(0),[s,o]=(0,a.useState)(!1),l={zh:{backToTop:"返回顶部",readingProgress:"阅读进度"},en:{backToTop:"Back to Top",readingProgress:"Reading Progress"}}[e];return(0,r.jsxs)(r.Fragment,{children:[r.jsx("div",{className:"fixed top-0 left-0 w-full h-1 bg-gray-200 z-50",children:r.jsx("div",{className:"h-full bg-gradient-to-r from-purple-500 to-pink-500 transition-all duration-150 ease-out",style:{width:`${t}%`},role:"progressbar","aria-valuenow":Math.round(t),"aria-valuemin":0,"aria-valuemax":100,"aria-label":l.readingProgress})}),s&&r.jsx("button",{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},className:"fixed bottom-6 right-6 w-12 h-12 bg-purple-600 text-white rounded-full shadow-lg hover:bg-purple-700 transition-all duration-200 z-40 flex items-center justify-center","aria-label":l.backToTop,children:r.jsx(i,{className:"w-5 h-5"})})]})}},86561:(e,t,n)=>{"use strict";n.d(t,{default:()=>a});var r=n(10326);function a({type:e,data:t}){let n=process.env.NEXT_PUBLIC_BASE_URL||"https://period-hub.com",a=(()=>{let r={"@context":"https://schema.org","@type":"website"===e?"WebSite":"article"===e?"MedicalWebPage":"medicalWebPage"===e?"MedicalWebPage":"HealthTopicPage",name:t.title,description:t.description,url:t.url,inLanguage:t.locale||"zh-CN",publisher:{"@type":"Organization",name:"Period Hub",url:n,logo:{"@type":"ImageObject",url:`${n}/logo.png`,width:200,height:60}}};return"website"===e?{...r,potentialAction:{"@type":"SearchAction",target:{"@type":"EntryPoint",urlTemplate:`${n}/search?q={search_term_string}`},"query-input":"required name=search_term_string"}}:"article"===e||"medicalWebPage"===e?{...r,"@type":"MedicalWebPage",mainEntity:{"@type":"MedicalCondition",name:"痛经",alternateName:["月经疼痛","经期疼痛","Menstrual Pain","Dysmenorrhea"],description:"月经期间或前后出现的疼痛症状",medicalSpecialty:{"@type":"MedicalSpecialty",name:"妇科学"}},author:{"@type":"Organization",name:t.author||"Period Hub Medical Team"},datePublished:t.datePublished,dateModified:t.dateModified||t.datePublished,image:t.image?{"@type":"ImageObject",url:t.image,width:1200,height:630}:void 0,keywords:t.keywords?.join(", "),medicalAudience:{"@type":"MedicalAudience",audienceType:["Patient","MedicalStudent"]},about:{"@type":"MedicalCondition",name:"痛经管理",description:"痛经的预防、治疗和管理方法"}}:"healthTopicPage"===e?{...r,"@type":"HealthTopicPage",mainEntity:{"@type":"MedicalCondition",name:"女性健康",description:"女性生殖健康和月经健康相关话题"},specialty:{"@type":"MedicalSpecialty",name:"妇科学"}}:r})();return r.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(a,null,2)}})}},16403:(e,t,n)=>{"use strict";n.d(t,{default:()=>l});var r=n(10326),a=n(17577),i=n(29389);let s=(0,n(76557).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var o=n(941);function l({locale:e,className:t=""}){let[n,l]=(0,a.useState)([]),[c,u]=(0,a.useState)(""),[h,d]=(0,a.useState)(!1),p=e=>{let t=document.getElementById(e);if(t){let e=t.offsetTop-80;window.scrollTo({top:e,behavior:"smooth"})}d(!1)},m={zh:{tableOfContents:"文章目录",toggleToc:"切换目录显示"},en:{tableOfContents:"Table of Contents",toggleToc:"Toggle table of contents"}}[e];return 0===n.length?null:(0,r.jsxs)("div",{className:`bg-white border border-gray-200 rounded-lg ${t}`,children:[(0,r.jsxs)("button",{onClick:()=>d(!h),className:"w-full flex items-center justify-between p-4 text-left font-semibold text-gray-800 hover:bg-gray-50 lg:hidden","aria-expanded":h,"aria-label":m.toggleToc,children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(i.Z,{className:"w-5 h-5"}),m.tableOfContents]}),h?r.jsx(s,{className:"w-5 h-5"}):r.jsx(o.Z,{className:"w-5 h-5"})]}),r.jsx("div",{className:"hidden lg:block p-4 border-b border-gray-200",children:(0,r.jsxs)("div",{className:"flex items-center gap-2 font-semibold text-gray-800",children:[r.jsx(i.Z,{className:"w-5 h-5"}),m.tableOfContents]})}),r.jsx("div",{className:`${h?"block":"hidden"} lg:block`,children:r.jsx("nav",{className:"p-4 space-y-1 max-h-96 overflow-y-auto",children:n.map(e=>r.jsx("button",{onClick:()=>p(e.id),className:`block w-full text-left py-2 px-3 rounded text-sm transition-colors ${c===e.id?"bg-purple-100 text-purple-700 font-medium":"text-gray-600 hover:text-purple-600 hover:bg-gray-50"}`,style:{paddingLeft:`${(e.level-1)*12+12}px`},children:r.jsx("span",{className:"line-clamp-2",children:e.text})},e.id))})})]})}},43810:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});let r=(0,n(76557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},67427:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});let r=(0,n(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},29389:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});let r=(0,n(76557).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},69374:(e,t)=>{"use strict";let n;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DOMAttributeNames:function(){return r},default:function(){return s},isEqualNode:function(){return i}});let r={acceptCharset:"accept-charset",className:"class",htmlFor:"for",httpEquiv:"http-equiv",noModule:"noModule"};function a(e){let{type:t,props:n}=e,a=document.createElement(t);for(let e in n){if(!n.hasOwnProperty(e)||"children"===e||"dangerouslySetInnerHTML"===e||void 0===n[e])continue;let i=r[e]||e.toLowerCase();"script"===t&&("async"===i||"defer"===i||"noModule"===i)?a[i]=!!n[e]:a.setAttribute(i,n[e])}let{children:i,dangerouslySetInnerHTML:s}=n;return s?a.innerHTML=s.__html||"":i&&(a.textContent="string"==typeof i?i:Array.isArray(i)?i.join(""):""),a}function i(e,t){if(e instanceof HTMLElement&&t instanceof HTMLElement){let n=t.getAttribute("nonce");if(n&&!e.getAttribute("nonce")){let r=t.cloneNode(!0);return r.setAttribute("nonce",""),r.nonce=n,n===e.nonce&&e.isEqualNode(r)}}return e.isEqualNode(t)}function s(){return{mountedInstances:new Set,updateHead:e=>{let t={};e.forEach(e=>{if("link"===e.type&&e.props["data-optimized-fonts"]){if(document.querySelector('style[data-href="'+e.props["data-href"]+'"]'))return;e.props.href=e.props["data-href"],e.props["data-href"]=void 0}let n=t[e.type]||[];n.push(e),t[e.type]=n});let r=t.title?t.title[0]:null,a="";if(r){let{children:e}=r.props;a="string"==typeof e?e:Array.isArray(e)?e.join(""):""}a!==document.title&&(document.title=a),["meta","base","link","style","script"].forEach(e=>{n(e,t[e]||[])})}}}n=(e,t)=>{let n=document.getElementsByTagName("head")[0],r=n.querySelector("meta[name=next-head-count]"),s=Number(r.content),o=[];for(let t=0,n=r.previousElementSibling;t<s;t++,n=(null==n?void 0:n.previousElementSibling)||null){var l;(null==n?void 0:null==(l=n.tagName)?void 0:l.toLowerCase())===e&&o.push(n)}let c=t.map(a).filter(e=>{for(let t=0,n=o.length;t<n;t++)if(i(o[t],e))return o.splice(t,1),!1;return!0});o.forEach(e=>{var t;return null==(t=e.parentNode)?void 0:t.removeChild(e)}),c.forEach(e=>n.insertBefore(e,r)),r.content=(s-o.length+c.length).toString()},("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},44064:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return A},handleClientScriptLoad:function(){return E},initScriptLoader:function(){return T}});let r=n(91174),a=n(58374),i=n(10326),s=r._(n(60962)),o=a._(n(17577)),l=n(81157),c=n(69374),u=n(10956),h=new Map,d=new Set,p=["onLoad","onReady","dangerouslySetInnerHTML","children","onError","strategy","stylesheets"],m=e=>{if(s.default.preinit){e.forEach(e=>{s.default.preinit(e,{as:"style"})});return}},f=e=>{let{src:t,id:n,onLoad:r=()=>{},onReady:a=null,dangerouslySetInnerHTML:i,children:s="",strategy:o="afterInteractive",onError:l,stylesheets:u}=e,f=n||t;if(f&&d.has(f))return;if(h.has(t)){d.add(f),h.get(t).then(r,l);return}let E=()=>{a&&a(),d.add(f)},T=document.createElement("script"),g=new Promise((e,t)=>{T.addEventListener("load",function(t){e(),r&&r.call(this,t),E()}),T.addEventListener("error",function(e){t(e)})}).catch(function(e){l&&l(e)});for(let[n,r]of(i?(T.innerHTML=i.__html||"",E()):s?(T.textContent="string"==typeof s?s:Array.isArray(s)?s.join(""):"",E()):t&&(T.src=t,h.set(t,g)),Object.entries(e))){if(void 0===r||p.includes(n))continue;let e=c.DOMAttributeNames[n]||n.toLowerCase();T.setAttribute(e,r)}"worker"===o&&T.setAttribute("type","text/partytown"),T.setAttribute("data-nscript",o),u&&m(u),document.body.appendChild(T)};function E(e){let{strategy:t="afterInteractive"}=e;"lazyOnload"===t?window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>f(e))}):f(e)}function T(e){e.forEach(E),[...document.querySelectorAll('[data-nscript="beforeInteractive"]'),...document.querySelectorAll('[data-nscript="beforePageRender"]')].forEach(e=>{let t=e.id||e.getAttribute("src");d.add(t)})}function g(e){let{id:t,src:n="",onLoad:r=()=>{},onReady:a=null,strategy:c="afterInteractive",onError:h,stylesheets:p,...m}=e,{updateScripts:E,scripts:T,getIsSsr:g,appDir:A,nonce:_}=(0,o.useContext)(l.HeadManagerContext),k=(0,o.useRef)(!1);(0,o.useEffect)(()=>{let e=t||n;k.current||(a&&e&&d.has(e)&&a(),k.current=!0)},[a,t,n]);let S=(0,o.useRef)(!1);if((0,o.useEffect)(()=>{!S.current&&("afterInteractive"===c?f(e):"lazyOnload"===c&&("complete"===document.readyState?(0,u.requestIdleCallback)(()=>f(e)):window.addEventListener("load",()=>{(0,u.requestIdleCallback)(()=>f(e))})),S.current=!0)},[e,c]),("beforeInteractive"===c||"worker"===c)&&(E?(T[c]=(T[c]||[]).concat([{id:t,src:n,onLoad:r,onReady:a,onError:h,...m}]),E(T)):g&&g()?d.add(t||n):g&&!g()&&f(e)),A){if(p&&p.forEach(e=>{s.default.preinit(e,{as:"style"})}),"beforeInteractive"===c)return n?(s.default.preload(n,m.integrity?{as:"script",integrity:m.integrity,nonce:_,crossOrigin:m.crossOrigin}:{as:"script",nonce:_,crossOrigin:m.crossOrigin}),(0,i.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([n,{...m,id:t}])+")"}})):(m.dangerouslySetInnerHTML&&(m.children=m.dangerouslySetInnerHTML.__html,delete m.dangerouslySetInnerHTML),(0,i.jsx)("script",{nonce:_,dangerouslySetInnerHTML:{__html:"(self.__next_s=self.__next_s||[]).push("+JSON.stringify([0,{...m,id:t}])+")"}}));"afterInteractive"===c&&n&&s.default.preload(n,m.integrity?{as:"script",integrity:m.integrity,nonce:_,crossOrigin:m.crossOrigin}:{as:"script",nonce:_,crossOrigin:m.crossOrigin})}return null}Object.defineProperty(g,"__nextScript",{value:!0});let A=g;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},81157:(e,t,n)=>{"use strict";e.exports=n(81616).vendored.contexts.HeadManagerContext},9079:(e,t,n)=>{"use strict";n.r(t),n.d(t,{default:()=>s6,generateMetadata:()=>s4,generateStaticParams:()=>s5});var r,a,i,s,o,l,c,u,h,d,p,m,f,E,T,g,A={};n.r(A),n.d(A,{boolean:()=>H,booleanish:()=>G,commaOrSpaceSeparated:()=>V,commaSeparated:()=>Y,number:()=>z,overloadedBoolean:()=>q,spaceSeparated:()=>j});var _={};n.r(_),n.d(_,{attentionMarkers:()=>t2,contentInitial:()=>tX,disable:()=>t3,document:()=>tK,flow:()=>tZ,flowInitial:()=>t$,insideSpan:()=>t1,string:()=>tJ,text:()=>t0});var k={};n.r(k),n.d(k,{boolean:()=>aI,booleanish:()=>aD,commaOrSpaceSeparated:()=>av,commaSeparated:()=>aR,number:()=>aL,overloadedBoolean:()=>aO,spaceSeparated:()=>ax});var S=n(19510),b=n(58585),y=n(40055),N=n(57371),C=n(52095);function I(e){let t=[],n=String(e||""),r=n.indexOf(","),a=0,i=!1;for(;!i;){-1===r&&(r=n.length,i=!0);let e=n.slice(a,r).trim();(e||!i)&&t.push(e),a=r+1,r=n.indexOf(",",a)}return t}function D(e,t){let n=t||{};return(""===e[e.length-1]?[...e,""]:e).join((n.padRight?" ":"")+","+(!1===n.padLeft?"":" ")).trim()}let O=/^[$_\p{ID_Start}][$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,L=/^[$_\p{ID_Start}][-$_\u{200C}\u{200D}\p{ID_Continue}]*$/u,x={};function R(e,t){return((t||x).jsx?L:O).test(e)}let v=/[ \t\n\f\r]/g;function P(e){return""===e.replace(v,"")}class M{constructor(e,t,n){this.normal=t,this.property=e,n&&(this.space=n)}}function w(e,t){let n={},r={};for(let t of e)Object.assign(n,t.property),Object.assign(r,t.normal);return new M(n,r,t)}function B(e){return e.toLowerCase()}M.prototype.normal={},M.prototype.property={},M.prototype.space=void 0;class F{constructor(e,t){this.attribute=t,this.property=e}}F.prototype.attribute="",F.prototype.booleanish=!1,F.prototype.boolean=!1,F.prototype.commaOrSpaceSeparated=!1,F.prototype.commaSeparated=!1,F.prototype.defined=!1,F.prototype.mustUseProperty=!1,F.prototype.number=!1,F.prototype.overloadedBoolean=!1,F.prototype.property="",F.prototype.spaceSeparated=!1,F.prototype.space=void 0;let U=0,H=W(),G=W(),q=W(),z=W(),j=W(),Y=W(),V=W();function W(){return 2**++U}let Q=Object.keys(A);class K extends F{constructor(e,t,n,r){let a=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",r),"number"==typeof n)for(;++a<Q.length;){let e=Q[a];(function(e,t,n){n&&(e[t]=n)})(this,Q[a],(n&A[e])===A[e])}}}function X(e){let t={},n={};for(let[r,a]of Object.entries(e.properties)){let i=new K(r,e.transform(e.attributes||{},r),a,e.space);e.mustUseProperty&&e.mustUseProperty.includes(r)&&(i.mustUseProperty=!0),t[r]=i,n[B(r)]=r,n[B(i.attribute)]=r}return new M(t,n,e.space)}K.prototype.defined=!0;let $=X({properties:{ariaActiveDescendant:null,ariaAtomic:G,ariaAutoComplete:null,ariaBusy:G,ariaChecked:G,ariaColCount:z,ariaColIndex:z,ariaColSpan:z,ariaControls:j,ariaCurrent:null,ariaDescribedBy:j,ariaDetails:null,ariaDisabled:G,ariaDropEffect:j,ariaErrorMessage:null,ariaExpanded:G,ariaFlowTo:j,ariaGrabbed:G,ariaHasPopup:null,ariaHidden:G,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:j,ariaLevel:z,ariaLive:null,ariaModal:G,ariaMultiLine:G,ariaMultiSelectable:G,ariaOrientation:null,ariaOwns:j,ariaPlaceholder:null,ariaPosInSet:z,ariaPressed:G,ariaReadOnly:G,ariaRelevant:null,ariaRequired:G,ariaRoleDescription:j,ariaRowCount:z,ariaRowIndex:z,ariaRowSpan:z,ariaSelected:G,ariaSetSize:z,ariaSort:null,ariaValueMax:z,ariaValueMin:z,ariaValueNow:z,ariaValueText:null,role:null},transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase()});function Z(e,t){return t in e?e[t]:t}function J(e,t){return Z(e,t.toLowerCase())}let ee=X({attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:Y,acceptCharset:j,accessKey:j,action:null,allow:null,allowFullScreen:H,allowPaymentRequest:H,allowUserMedia:H,alt:null,as:null,async:H,autoCapitalize:null,autoComplete:j,autoFocus:H,autoPlay:H,blocking:j,capture:null,charSet:null,checked:H,cite:null,className:j,cols:z,colSpan:null,content:null,contentEditable:G,controls:H,controlsList:j,coords:z|Y,crossOrigin:null,data:null,dateTime:null,decoding:null,default:H,defer:H,dir:null,dirName:null,disabled:H,download:q,draggable:G,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:H,formTarget:null,headers:j,height:z,hidden:q,high:z,href:null,hrefLang:null,htmlFor:j,httpEquiv:j,id:null,imageSizes:null,imageSrcSet:null,inert:H,inputMode:null,integrity:null,is:null,isMap:H,itemId:null,itemProp:j,itemRef:j,itemScope:H,itemType:j,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:H,low:z,manifest:null,max:null,maxLength:z,media:null,method:null,min:null,minLength:z,multiple:H,muted:H,name:null,nonce:null,noModule:H,noValidate:H,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:H,optimum:z,pattern:null,ping:j,placeholder:null,playsInline:H,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:H,referrerPolicy:null,rel:j,required:H,reversed:H,rows:z,rowSpan:z,sandbox:j,scope:null,scoped:H,seamless:H,selected:H,shadowRootClonable:H,shadowRootDelegatesFocus:H,shadowRootMode:null,shape:null,size:z,sizes:null,slot:null,span:z,spellCheck:G,src:null,srcDoc:null,srcLang:null,srcSet:null,start:z,step:null,style:null,tabIndex:z,target:null,title:null,translate:null,type:null,typeMustMatch:H,useMap:null,value:G,width:z,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:j,axis:null,background:null,bgColor:null,border:z,borderColor:null,bottomMargin:z,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:H,declare:H,event:null,face:null,frame:null,frameBorder:null,hSpace:z,leftMargin:z,link:null,longDesc:null,lowSrc:null,marginHeight:z,marginWidth:z,noResize:H,noHref:H,noShade:H,noWrap:H,object:null,profile:null,prompt:null,rev:null,rightMargin:z,rules:null,scheme:null,scrolling:G,standby:null,summary:null,text:null,topMargin:z,valueType:null,version:null,vAlign:null,vLink:null,vSpace:z,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:H,disableRemotePlayback:H,prefix:null,property:null,results:z,security:null,unselectable:null},space:"html",transform:J}),et=X({attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},properties:{about:V,accentHeight:z,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:z,amplitude:z,arabicForm:null,ascent:z,attributeName:null,attributeType:null,azimuth:z,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:z,by:null,calcMode:null,capHeight:z,className:j,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:z,diffuseConstant:z,direction:null,display:null,dur:null,divisor:z,dominantBaseline:null,download:H,dx:null,dy:null,edgeMode:null,editable:null,elevation:z,enableBackground:null,end:null,event:null,exponent:z,externalResourcesRequired:null,fill:null,fillOpacity:z,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:Y,g2:Y,glyphName:Y,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:z,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:z,horizOriginX:z,horizOriginY:z,id:null,ideographic:z,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:z,k:z,k1:z,k2:z,k3:z,k4:z,kernelMatrix:V,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:z,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:z,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:z,overlineThickness:z,paintOrder:null,panose1:null,path:null,pathLength:z,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:j,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:z,pointsAtY:z,pointsAtZ:z,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:V,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:V,rev:V,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:V,requiredFeatures:V,requiredFonts:V,requiredFormats:V,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:z,specularExponent:z,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:z,strikethroughThickness:z,string:null,stroke:null,strokeDashArray:V,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:z,strokeOpacity:z,strokeWidth:null,style:null,surfaceScale:z,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:V,tabIndex:z,tableValues:null,target:null,targetX:z,targetY:z,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:V,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:z,underlineThickness:z,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:z,values:null,vAlphabetic:z,vMathematical:z,vectorEffect:null,vHanging:z,vIdeographic:z,version:null,vertAdvY:z,vertOriginX:z,vertOriginY:z,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:z,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null},space:"svg",transform:Z}),en=X({properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null},space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase()}),er=X({attributes:{xmlnsxlink:"xmlns:xlink"},properties:{xmlnsXLink:null,xmlns:null},space:"xmlns",transform:J}),ea=X({properties:{xmlBase:null,xmlLang:null,xmlSpace:null},space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase()}),ei=w([$,ee,en,er,ea],"html"),es=w([$,et,en,er,ea],"svg"),eo=/[A-Z]/g,el=/-[a-z]/g,ec=/^data[-\w.:]+$/i;function eu(e,t){let n=B(t),r=t,a=F;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&ec.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(el,ed);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!el.test(e)){let n=e.replace(eo,eh);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}a=K}return new a(r,t)}function eh(e){return"-"+e.toLowerCase()}function ed(e){return e.charAt(1).toUpperCase()}let ep={classId:"classID",dataType:"datatype",itemId:"itemID",strokeDashArray:"strokeDasharray",strokeDashOffset:"strokeDashoffset",strokeLineCap:"strokeLinecap",strokeLineJoin:"strokeLinejoin",strokeMiterLimit:"strokeMiterlimit",typeOf:"typeof",xLinkActuate:"xlinkActuate",xLinkArcRole:"xlinkArcrole",xLinkHref:"xlinkHref",xLinkRole:"xlinkRole",xLinkShow:"xlinkShow",xLinkTitle:"xlinkTitle",xLinkType:"xlinkType",xmlnsXLink:"xmlnsXlink"};function em(e){let t=String(e||"").trim();return t?t.split(/[ \t\n\r\f]+/g):[]}function ef(e){return e.join(" ").trim()}var eE=n(94576);let eT=eA("end"),eg=eA("start");function eA(e){return function(t){let n=t&&t.position&&t.position[e]||{};if("number"==typeof n.line&&n.line>0&&"number"==typeof n.column&&n.column>0)return{line:n.line,column:n.column,offset:"number"==typeof n.offset&&n.offset>-1?n.offset:void 0}}}function e_(e){return e&&"object"==typeof e?"position"in e||"type"in e?eS(e.position):"start"in e||"end"in e?eS(e):"line"in e||"column"in e?ek(e):"":""}function ek(e){return eb(e&&e.line)+":"+eb(e&&e.column)}function eS(e){return ek(e&&e.start)+"-"+ek(e&&e.end)}function eb(e){return e&&"number"==typeof e?e:1}class ey extends Error{constructor(e,t,n){super(),"string"==typeof t&&(n=t,t=void 0);let r="",a={},i=!1;if(t&&(a="line"in t&&"column"in t?{place:t}:"start"in t&&"end"in t?{place:t}:"type"in t?{ancestors:[t],place:t.position}:{...t}),"string"==typeof e?r=e:!a.cause&&e&&(i=!0,r=e.message,a.cause=e),!a.ruleId&&!a.source&&"string"==typeof n){let e=n.indexOf(":");-1===e?a.ruleId=n:(a.source=n.slice(0,e),a.ruleId=n.slice(e+1))}if(!a.place&&a.ancestors&&a.ancestors){let e=a.ancestors[a.ancestors.length-1];e&&(a.place=e.position)}let s=a.place&&"start"in a.place?a.place.start:a.place;this.ancestors=a.ancestors||void 0,this.cause=a.cause||void 0,this.column=s?s.column:void 0,this.fatal=void 0,this.file,this.message=r,this.line=s?s.line:void 0,this.name=e_(a.place)||"1:1",this.place=a.place||void 0,this.reason=this.message,this.ruleId=a.ruleId||void 0,this.source=a.source||void 0,this.stack=i&&a.cause&&"string"==typeof a.cause.stack?a.cause.stack:"",this.actual,this.expected,this.note,this.url}}ey.prototype.file="",ey.prototype.name="",ey.prototype.reason="",ey.prototype.message="",ey.prototype.stack="",ey.prototype.column=void 0,ey.prototype.line=void 0,ey.prototype.ancestors=void 0,ey.prototype.cause=void 0,ey.prototype.fatal=void 0,ey.prototype.place=void 0,ey.prototype.ruleId=void 0,ey.prototype.source=void 0;let eN={}.hasOwnProperty,eC=new Map,eI=/[A-Z]/g,eD=new Set(["table","tbody","thead","tfoot","tr"]),eO=new Set(["td","th"]),eL="https://github.com/syntax-tree/hast-util-to-jsx-runtime";function ex(e,t,n){return"element"===t.type?function(e,t,n){let r=e.schema,a=r;"svg"===t.tagName.toLowerCase()&&"html"===r.space&&(a=es,e.schema=a),e.ancestors.push(t);let i=eM(e,t.tagName,!1),s=function(e,t){let n,r;let a={};for(r in t.properties)if("children"!==r&&eN.call(t.properties,r)){let i=function(e,t,n){let r=eu(e.schema,t);if(!(null==n||"number"==typeof n&&Number.isNaN(n))){if(Array.isArray(n)&&(n=r.commaSeparated?D(n):ef(n)),"style"===r.property){let t="object"==typeof n?n:function(e,t){try{return eE(t,{reactCompat:!0})}catch(n){if(e.ignoreInvalidStyle)return{};let t=new ey("Cannot parse `style` attribute",{ancestors:e.ancestors,cause:n,ruleId:"style",source:"hast-util-to-jsx-runtime"});throw t.file=e.filePath||void 0,t.url=eL+"#cannot-parse-style-attribute",t}}(e,String(n));return"css"===e.stylePropertyNameCase&&(t=function(e){let t;let n={};for(t in e)eN.call(e,t)&&(n[function(e){let t=e.replace(eI,eB);return"ms-"===t.slice(0,3)&&(t="-"+t),t}(t)]=e[t]);return n}(t)),["style",t]}return["react"===e.elementAttributeNameCase&&r.space?ep[r.property]||r.property:r.attribute,n]}}(e,r,t.properties[r]);if(i){let[r,s]=i;e.tableCellAlignToStyle&&"align"===r&&"string"==typeof s&&eO.has(t.tagName)?n=s:a[r]=s}}return n&&((a.style||(a.style={}))["css"===e.stylePropertyNameCase?"text-align":"textAlign"]=n),a}(e,t),o=eP(e,t);return eD.has(t.tagName)&&(o=o.filter(function(e){return"string"!=typeof e||!("object"==typeof e?"text"===e.type&&P(e.value):P(e))})),eR(e,s,i,t),ev(s,o),e.ancestors.pop(),e.schema=r,e.create(t,i,s,n)}(e,t,n):"mdxFlowExpression"===t.type||"mdxTextExpression"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater){let n=t.data.estree.body[0];return n.type,e.evaluater.evaluateExpression(n.expression)}ew(e,t.position)}(e,t):"mdxJsxFlowElement"===t.type||"mdxJsxTextElement"===t.type?function(e,t,n){let r=e.schema,a=r;"svg"===t.name&&"html"===r.space&&(a=es,e.schema=a),e.ancestors.push(t);let i=null===t.name?e.Fragment:eM(e,t.name,!0),s=function(e,t){let n={};for(let r of t.attributes)if("mdxJsxExpressionAttribute"===r.type){if(r.data&&r.data.estree&&e.evaluater){let t=r.data.estree.body[0];t.type;let a=t.expression;a.type;let i=a.properties[0];i.type,Object.assign(n,e.evaluater.evaluateExpression(i.argument))}else ew(e,t.position)}else{let a;let i=r.name;if(r.value&&"object"==typeof r.value){if(r.value.data&&r.value.data.estree&&e.evaluater){let t=r.value.data.estree.body[0];t.type,a=e.evaluater.evaluateExpression(t.expression)}else ew(e,t.position)}else a=null===r.value||r.value;n[i]=a}return n}(e,t),o=eP(e,t);return eR(e,s,i,t),ev(s,o),e.ancestors.pop(),e.schema=r,e.create(t,i,s,n)}(e,t,n):"mdxjsEsm"===t.type?function(e,t){if(t.data&&t.data.estree&&e.evaluater)return e.evaluater.evaluateProgram(t.data.estree);ew(e,t.position)}(e,t):"root"===t.type?function(e,t,n){let r={};return ev(r,eP(e,t)),e.create(t,e.Fragment,r,n)}(e,t,n):"text"===t.type?t.value:void 0}function eR(e,t,n,r){"string"!=typeof n&&n!==e.Fragment&&e.passNode&&(t.node=r)}function ev(e,t){if(t.length>0){let n=t.length>1?t:t[0];n&&(e.children=n)}}function eP(e,t){let n=[],r=-1,a=e.passKeys?new Map:eC;for(;++r<t.children.length;){let i;let s=t.children[r];if(e.passKeys){let e="element"===s.type?s.tagName:"mdxJsxFlowElement"===s.type||"mdxJsxTextElement"===s.type?s.name:void 0;if(e){let t=a.get(e)||0;i=e+"-"+t,a.set(e,t+1)}}let o=ex(e,s,i);void 0!==o&&n.push(o)}return n}function eM(e,t,n){let r;if(n){if(t.includes(".")){let e;let n=t.split("."),a=-1;for(;++a<n.length;){let t=R(n[a])?{type:"Identifier",name:n[a]}:{type:"Literal",value:n[a]};e=e?{type:"MemberExpression",object:e,property:t,computed:!!(a&&"Literal"===t.type),optional:!1}:t}r=e}else r=R(t)&&!/^[a-z]/.test(t)?{type:"Identifier",name:t}:{type:"Literal",value:t}}else r={type:"Literal",value:t};if("Literal"===r.type){let t=r.value;return eN.call(e.components,t)?e.components[t]:t}if(e.evaluater)return e.evaluater.evaluateExpression(r);ew(e)}function ew(e,t){let n=new ey("Cannot handle MDX estrees without `createEvaluater`",{ancestors:e.ancestors,place:t,ruleId:"mdx-estree",source:"hast-util-to-jsx-runtime"});throw n.file=e.filePath||void 0,n.url=eL+"#cannot-handle-mdx-estrees-without-createevaluater",n}function eB(e){return"-"+e.toLowerCase()}let eF={action:["form"],cite:["blockquote","del","ins","q"],data:["object"],formAction:["button","input"],href:["a","area","base","link"],icon:["menuitem"],itemId:null,manifest:["html"],ping:["a","area"],poster:["video"],src:["audio","embed","iframe","img","input","script","source","track","video"]};n(71159);let eU={};function eH(e,t){let n=t||eU;return eG(e,"boolean"!=typeof n.includeImageAlt||n.includeImageAlt,"boolean"!=typeof n.includeHtml||n.includeHtml)}function eG(e,t,n){if(e&&"object"==typeof e){if("value"in e)return"html"!==e.type||n?e.value:"";if(t&&"alt"in e&&e.alt)return e.alt;if("children"in e)return eq(e.children,t,n)}return Array.isArray(e)?eq(e,t,n):""}function eq(e,t,n){let r=[],a=-1;for(;++a<e.length;)r[a]=eG(e[a],t,n);return r.join("")}function ez(e,t,n,r){let a;let i=e.length,s=0;if(t=t<0?-t>i?0:i+t:t>i?i:t,n=n>0?n:0,r.length<1e4)(a=Array.from(r)).unshift(t,n),e.splice(...a);else for(n&&e.splice(t,n);s<r.length;)(a=r.slice(s,s+1e4)).unshift(t,0),e.splice(...a),s+=1e4,t+=1e4}function ej(e,t){return e.length>0?(ez(e,e.length,0,t),e):t}class eY{constructor(e){this.left=e?[...e]:[],this.right=[]}get(e){if(e<0||e>=this.left.length+this.right.length)throw RangeError("Cannot access index `"+e+"` in a splice buffer of size `"+(this.left.length+this.right.length)+"`");return e<this.left.length?this.left[e]:this.right[this.right.length-e+this.left.length-1]}get length(){return this.left.length+this.right.length}shift(){return this.setCursor(0),this.right.pop()}slice(e,t){let n=null==t?Number.POSITIVE_INFINITY:t;return n<this.left.length?this.left.slice(e,n):e>this.left.length?this.right.slice(this.right.length-n+this.left.length,this.right.length-e+this.left.length).reverse():this.left.slice(e).concat(this.right.slice(this.right.length-n+this.left.length).reverse())}splice(e,t,n){this.setCursor(Math.trunc(e));let r=this.right.splice(this.right.length-(t||0),Number.POSITIVE_INFINITY);return n&&eV(this.left,n),r.reverse()}pop(){return this.setCursor(Number.POSITIVE_INFINITY),this.left.pop()}push(e){this.setCursor(Number.POSITIVE_INFINITY),this.left.push(e)}pushMany(e){this.setCursor(Number.POSITIVE_INFINITY),eV(this.left,e)}unshift(e){this.setCursor(0),this.right.push(e)}unshiftMany(e){this.setCursor(0),eV(this.right,e.reverse())}setCursor(e){if(e!==this.left.length&&(!(e>this.left.length)||0!==this.right.length)&&(!(e<0)||0!==this.left.length)){if(e<this.left.length){let t=this.left.splice(e,Number.POSITIVE_INFINITY);eV(this.right,t.reverse())}else{let t=this.right.splice(this.left.length+this.right.length-e,Number.POSITIVE_INFINITY);eV(this.left,t.reverse())}}}}function eV(e,t){let n=0;if(t.length<1e4)e.push(...t);else for(;n<t.length;)e.push(...t.slice(n,n+1e4)),n+=1e4}function eW(e){let t,n,r,a,i,s,o;let l={},c=-1,u=new eY(e);for(;++c<u.length;){for(;(c in l);)c=l[c];if(t=u.get(c),c&&"chunkFlow"===t[1].type&&"listItemPrefix"===u.get(c-1)[1].type&&((r=0)<(s=t[1]._tokenizer.events).length&&"lineEndingBlank"===s[r][1].type&&(r+=2),r<s.length&&"content"===s[r][1].type))for(;++r<s.length&&"content"!==s[r][1].type;)"chunkText"===s[r][1].type&&(s[r][1]._isInFirstContentOfListItem=!0,r++);if("enter"===t[0])t[1].contentType&&(Object.assign(l,function(e,t){let n,r;let a=e.get(t)[1],i=e.get(t)[2],s=t-1,o=[],l=a._tokenizer;!l&&(l=i.parser[a.contentType](a.start),a._contentTypeTextTrailing&&(l._contentTypeTextTrailing=!0));let c=l.events,u=[],h={},d=-1,p=a,m=0,f=0,E=[0];for(;p;){for(;e.get(++s)[1]!==p;);o.push(s),!p._tokenizer&&(n=i.sliceStream(p),p.next||n.push(null),r&&l.defineSkip(p.start),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=!0),l.write(n),p._isInFirstContentOfListItem&&(l._gfmTasklistFirstContentOfListItem=void 0)),r=p,p=p.next}for(p=a;++d<c.length;)"exit"===c[d][0]&&"enter"===c[d-1][0]&&c[d][1].type===c[d-1][1].type&&c[d][1].start.line!==c[d][1].end.line&&(f=d+1,E.push(f),p._tokenizer=void 0,p.previous=void 0,p=p.next);for(l.events=[],p?(p._tokenizer=void 0,p.previous=void 0):E.pop(),d=E.length;d--;){let t=c.slice(E[d],E[d+1]),n=o.pop();u.push([n,n+t.length-1]),e.splice(n,2,t)}for(u.reverse(),d=-1;++d<u.length;)h[m+u[d][0]]=m+u[d][1],m+=u[d][1]-u[d][0]-1;return h}(u,c)),c=l[c],o=!0);else if(t[1]._container){for(r=c,n=void 0;r--;)if("lineEnding"===(a=u.get(r))[1].type||"lineEndingBlank"===a[1].type)"enter"===a[0]&&(n&&(u.get(n)[1].type="lineEndingBlank"),a[1].type="lineEnding",n=r);else if("linePrefix"===a[1].type||"listItemIndent"===a[1].type);else break;n&&(t[1].end={...u.get(n)[1].start},(i=u.slice(n,c)).unshift(t),u.splice(n,c-n+1,i))}}return ez(e,0,Number.POSITIVE_INFINITY,u.slice(0)),!o}let eQ={}.hasOwnProperty;function eK(e){let t={},n=-1;for(;++n<e.length;)(function(e,t){let n;for(n in t){let r;let a=(eQ.call(e,n)?e[n]:void 0)||(e[n]={}),i=t[n];if(i)for(r in i){eQ.call(a,r)||(a[r]=[]);let e=i[r];(function(e,t){let n=-1,r=[];for(;++n<t.length;)("after"===t[n].add?e:r).push(t[n]);ez(e,0,0,r)})(a[r],Array.isArray(e)?e:e?[e]:[])}}})(t,e[n]);return t}let eX=e9(/[A-Za-z]/),e$=e9(/[\dA-Za-z]/),eZ=e9(/[#-'*+\--9=?A-Z^-~]/);function eJ(e){return null!==e&&(e<32||127===e)}let e0=e9(/\d/),e1=e9(/[\dA-Fa-f]/),e2=e9(/[!-/:-@[-`{-~]/);function e3(e){return null!==e&&e<-2}function e5(e){return null!==e&&(e<0||32===e)}function e4(e){return -2===e||-1===e||32===e}let e6=e9(/\p{P}|\p{S}/u),e8=e9(/\s/);function e9(e){return function(t){return null!==t&&t>-1&&e.test(String.fromCharCode(t))}}function e7(e,t,n,r){let a=r?r-1:Number.POSITIVE_INFINITY,i=0;return function(r){return e4(r)?(e.enter(n),function r(s){return e4(s)&&i++<a?(e.consume(s),r):(e.exit(n),t(s))}(r)):t(r)}}let te={tokenize:function(e){let t;let n=e.attempt(this.parser.constructs.contentInitial,function(t){if(null===t){e.consume(t);return}return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),e7(e,n,"linePrefix")},function(n){return e.enter("paragraph"),function n(r){let a=e.enter("chunkText",{contentType:"text",previous:t});return t&&(t.next=a),t=a,function t(r){if(null===r){e.exit("chunkText"),e.exit("paragraph"),e.consume(r);return}return e3(r)?(e.consume(r),e.exit("chunkText"),n):(e.consume(r),t)}(r)}(n)});return n}},tt={tokenize:function(e){let t,n,r;let a=this,i=[],s=0;return o;function o(t){if(s<i.length){let n=i[s];return a.containerState=n[1],e.attempt(n[0].continuation,l,c)(t)}return c(t)}function l(e){if(s++,a.containerState._closeFlow){let n;a.containerState._closeFlow=void 0,t&&T();let r=a.events.length,i=r;for(;i--;)if("exit"===a.events[i][0]&&"chunkFlow"===a.events[i][1].type){n=a.events[i][1].end;break}E(s);let o=r;for(;o<a.events.length;)a.events[o][1].end={...n},o++;return ez(a.events,i+1,0,a.events.slice(r)),a.events.length=o,c(e)}return o(e)}function c(n){if(s===i.length){if(!t)return d(n);if(t.currentConstruct&&t.currentConstruct.concrete)return m(n);a.interrupt=!!(t.currentConstruct&&!t._gfmTableDynamicInterruptHack)}return a.containerState={},e.check(tn,u,h)(n)}function u(e){return t&&T(),E(s),d(e)}function h(e){return a.parser.lazy[a.now().line]=s!==i.length,r=a.now().offset,m(e)}function d(t){return a.containerState={},e.attempt(tn,p,m)(t)}function p(e){return s++,i.push([a.currentConstruct,a.containerState]),d(e)}function m(r){if(null===r){t&&T(),E(0),e.consume(r);return}return t=t||a.parser.flow(a.now()),e.enter("chunkFlow",{_tokenizer:t,contentType:"flow",previous:n}),function t(n){if(null===n){f(e.exit("chunkFlow"),!0),E(0),e.consume(n);return}return e3(n)?(e.consume(n),f(e.exit("chunkFlow")),s=0,a.interrupt=void 0,o):(e.consume(n),t)}(r)}function f(e,i){let o=a.sliceStream(e);if(i&&o.push(null),e.previous=n,n&&(n.next=e),n=e,t.defineSkip(e.start),t.write(o),a.parser.lazy[e.start.line]){let e,n,i=t.events.length;for(;i--;)if(t.events[i][1].start.offset<r&&(!t.events[i][1].end||t.events[i][1].end.offset>r))return;let o=a.events.length,l=o;for(;l--;)if("exit"===a.events[l][0]&&"chunkFlow"===a.events[l][1].type){if(e){n=a.events[l][1].end;break}e=!0}for(E(s),i=o;i<a.events.length;)a.events[i][1].end={...n},i++;ez(a.events,l+1,0,a.events.slice(o)),a.events.length=i}}function E(t){let n=i.length;for(;n-- >t;){let t=i[n];a.containerState=t[1],t[0].exit.call(a,e)}i.length=t}function T(){t.write([null]),n=void 0,t=void 0,a.containerState._closeFlow=void 0}}},tn={tokenize:function(e,t,n){return e7(e,e.attempt(this.parser.constructs.document,t,n),"linePrefix",this.parser.constructs.disable.null.includes("codeIndented")?void 0:4)}},tr={partial:!0,tokenize:function(e,t,n){return function(t){return e4(t)?e7(e,r,"linePrefix")(t):r(t)};function r(e){return null===e||e3(e)?t(e):n(e)}}},ta={resolve:function(e){return eW(e),e},tokenize:function(e,t){let n;return function(t){return e.enter("content"),n=e.enter("chunkContent",{contentType:"content"}),r(t)};function r(t){return null===t?a(t):e3(t)?e.check(ti,i,a)(t):(e.consume(t),r)}function a(n){return e.exit("chunkContent"),e.exit("content"),t(n)}function i(t){return e.consume(t),e.exit("chunkContent"),n.next=e.enter("chunkContent",{contentType:"content",previous:n}),n=n.next,r}}},ti={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e.exit("chunkContent"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),e7(e,a,"linePrefix")};function a(a){if(null===a||e3(a))return n(a);let i=r.events[r.events.length-1];return!r.parser.constructs.disable.null.includes("codeIndented")&&i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(a):e.interrupt(r.parser.constructs.flow,n,t)(a)}}},ts={tokenize:function(e){let t=this,n=e.attempt(tr,function(r){if(null===r){e.consume(r);return}return e.enter("lineEndingBlank"),e.consume(r),e.exit("lineEndingBlank"),t.currentConstruct=void 0,n},e.attempt(this.parser.constructs.flowInitial,r,e7(e,e.attempt(this.parser.constructs.flow,r,e.attempt(ta,r)),"linePrefix")));return n;function r(r){if(null===r){e.consume(r);return}return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),t.currentConstruct=void 0,n}}},to={resolveAll:th()},tl=tu("string"),tc=tu("text");function tu(e){return{resolveAll:th("text"===e?td:void 0),tokenize:function(t){let n=this,r=this.parser.constructs[e],a=t.attempt(r,i,s);return i;function i(e){return l(e)?a(e):s(e)}function s(e){if(null===e){t.consume(e);return}return t.enter("data"),t.consume(e),o}function o(e){return l(e)?(t.exit("data"),a(e)):(t.consume(e),o)}function l(e){if(null===e)return!0;let t=r[e],a=-1;if(t)for(;++a<t.length;){let e=t[a];if(!e.previous||e.previous.call(n,n.previous))return!0}return!1}}}}function th(e){return function(t,n){let r,a=-1;for(;++a<=t.length;)void 0===r?t[a]&&"data"===t[a][1].type&&(r=a,a++):t[a]&&"data"===t[a][1].type||(a!==r+2&&(t[r][1].end=t[a-1][1].end,t.splice(r+2,a-r-2),a=r+2),r=void 0);return e?e(t,n):t}}function td(e,t){let n=0;for(;++n<=e.length;)if((n===e.length||"lineEnding"===e[n][1].type)&&"data"===e[n-1][1].type){let r;let a=e[n-1][1],i=t.sliceStream(a),s=i.length,o=-1,l=0;for(;s--;){let e=i[s];if("string"==typeof e){for(o=e.length;32===e.charCodeAt(o-1);)l++,o--;if(o)break;o=-1}else if(-2===e)r=!0,l++;else if(-1===e);else{s++;break}}if(t._contentTypeTextTrailing&&n===e.length&&(l=0),l){let i={type:n===e.length||r||l<2?"lineSuffix":"hardBreakTrailing",start:{_bufferIndex:s?o:a.start._bufferIndex+o,_index:a.start._index+s,line:a.end.line,column:a.end.column-l,offset:a.end.offset-l},end:{...a.end}};a.end={...i.start},a.start.offset===a.end.offset?Object.assign(a,i):(e.splice(n,0,["enter",i,t],["exit",i,t]),n+=2)}n++}return e}let tp={name:"thematicBreak",tokenize:function(e,t,n){let r,a=0;return function(i){return e.enter("thematicBreak"),r=i,function i(s){return s===r?(e.enter("thematicBreakSequence"),function t(n){return n===r?(e.consume(n),a++,t):(e.exit("thematicBreakSequence"),e4(n)?e7(e,i,"whitespace")(n):i(n))}(s)):a>=3&&(null===s||e3(s))?(e.exit("thematicBreak"),t(s)):n(s)}(i)}}},tm={continuation:{tokenize:function(e,t,n){let r=this;return r.containerState._closeFlow=void 0,e.check(tr,function(n){return r.containerState.furtherBlankLines=r.containerState.furtherBlankLines||r.containerState.initialBlankLine,e7(e,t,"listItemIndent",r.containerState.size+1)(n)},function(n){return r.containerState.furtherBlankLines||!e4(n)?(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,a(n)):(r.containerState.furtherBlankLines=void 0,r.containerState.initialBlankLine=void 0,e.attempt(tE,t,a)(n))});function a(a){return r.containerState._closeFlow=!0,r.interrupt=void 0,e7(e,e.attempt(tm,t,n),"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(a)}}},exit:function(e){e.exit(this.containerState.type)},name:"list",tokenize:function(e,t,n){let r=this,a=r.events[r.events.length-1],i=a&&"linePrefix"===a[1].type?a[2].sliceSerialize(a[1],!0).length:0,s=0;return function(t){let a=r.containerState.type||(42===t||43===t||45===t?"listUnordered":"listOrdered");if("listUnordered"===a?!r.containerState.marker||t===r.containerState.marker:e0(t)){if(r.containerState.type||(r.containerState.type=a,e.enter(a,{_container:!0})),"listUnordered"===a)return e.enter("listItemPrefix"),42===t||45===t?e.check(tp,n,o)(t):o(t);if(!r.interrupt||49===t)return e.enter("listItemPrefix"),e.enter("listItemValue"),function t(a){return e0(a)&&++s<10?(e.consume(a),t):(!r.interrupt||s<2)&&(r.containerState.marker?a===r.containerState.marker:41===a||46===a)?(e.exit("listItemValue"),o(a)):n(a)}(t)}return n(t)};function o(t){return e.enter("listItemMarker"),e.consume(t),e.exit("listItemMarker"),r.containerState.marker=r.containerState.marker||t,e.check(tr,r.interrupt?n:l,e.attempt(tf,u,c))}function l(e){return r.containerState.initialBlankLine=!0,i++,u(e)}function c(t){return e4(t)?(e.enter("listItemPrefixWhitespace"),e.consume(t),e.exit("listItemPrefixWhitespace"),u):n(t)}function u(n){return r.containerState.size=i+r.sliceSerialize(e.exit("listItemPrefix"),!0).length,t(n)}}},tf={partial:!0,tokenize:function(e,t,n){let r=this;return e7(e,function(e){let a=r.events[r.events.length-1];return!e4(e)&&a&&"listItemPrefixWhitespace"===a[1].type?t(e):n(e)},"listItemPrefixWhitespace",r.parser.constructs.disable.null.includes("codeIndented")?void 0:5)}},tE={partial:!0,tokenize:function(e,t,n){let r=this;return e7(e,function(e){let a=r.events[r.events.length-1];return a&&"listItemIndent"===a[1].type&&a[2].sliceSerialize(a[1],!0).length===r.containerState.size?t(e):n(e)},"listItemIndent",r.containerState.size+1)}},tT={continuation:{tokenize:function(e,t,n){let r=this;return function(t){return e4(t)?e7(e,a,"linePrefix",r.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):a(t)};function a(r){return e.attempt(tT,t,n)(r)}}},exit:function(e){e.exit("blockQuote")},name:"blockQuote",tokenize:function(e,t,n){let r=this;return function(t){if(62===t){let n=r.containerState;return n.open||(e.enter("blockQuote",{_container:!0}),n.open=!0),e.enter("blockQuotePrefix"),e.enter("blockQuoteMarker"),e.consume(t),e.exit("blockQuoteMarker"),a}return n(t)};function a(n){return e4(n)?(e.enter("blockQuotePrefixWhitespace"),e.consume(n),e.exit("blockQuotePrefixWhitespace"),e.exit("blockQuotePrefix"),t):(e.exit("blockQuotePrefix"),t(n))}}};function tg(e,t,n,r,a,i,s,o,l){let c=l||Number.POSITIVE_INFINITY,u=0;return function(t){return 60===t?(e.enter(r),e.enter(a),e.enter(i),e.consume(t),e.exit(i),h):null===t||32===t||41===t||eJ(t)?n(t):(e.enter(r),e.enter(s),e.enter(o),e.enter("chunkString",{contentType:"string"}),m(t))};function h(n){return 62===n?(e.enter(i),e.consume(n),e.exit(i),e.exit(a),e.exit(r),t):(e.enter(o),e.enter("chunkString",{contentType:"string"}),d(n))}function d(t){return 62===t?(e.exit("chunkString"),e.exit(o),h(t)):null===t||60===t||e3(t)?n(t):(e.consume(t),92===t?p:d)}function p(t){return 60===t||62===t||92===t?(e.consume(t),d):d(t)}function m(a){return!u&&(null===a||41===a||e5(a))?(e.exit("chunkString"),e.exit(o),e.exit(s),e.exit(r),t(a)):u<c&&40===a?(e.consume(a),u++,m):41===a?(e.consume(a),u--,m):null===a||32===a||40===a||eJ(a)?n(a):(e.consume(a),92===a?f:m)}function f(t){return 40===t||41===t||92===t?(e.consume(t),m):m(t)}}function tA(e,t,n,r,a,i){let s;let o=this,l=0;return function(t){return e.enter(r),e.enter(a),e.consume(t),e.exit(a),e.enter(i),c};function c(h){return l>999||null===h||91===h||93===h&&!s||94===h&&!l&&"_hiddenFootnoteSupport"in o.parser.constructs?n(h):93===h?(e.exit(i),e.enter(a),e.consume(h),e.exit(a),e.exit(r),t):e3(h)?(e.enter("lineEnding"),e.consume(h),e.exit("lineEnding"),c):(e.enter("chunkString",{contentType:"string"}),u(h))}function u(t){return null===t||91===t||93===t||e3(t)||l++>999?(e.exit("chunkString"),c(t)):(e.consume(t),s||(s=!e4(t)),92===t?h:u)}function h(t){return 91===t||92===t||93===t?(e.consume(t),l++,u):u(t)}}function t_(e,t,n,r,a,i){let s;return function(t){return 34===t||39===t||40===t?(e.enter(r),e.enter(a),e.consume(t),e.exit(a),s=40===t?41:t,o):n(t)};function o(n){return n===s?(e.enter(a),e.consume(n),e.exit(a),e.exit(r),t):(e.enter(i),l(n))}function l(t){return t===s?(e.exit(i),o(s)):null===t?n(t):e3(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),e7(e,l,"linePrefix")):(e.enter("chunkString",{contentType:"string"}),c(t))}function c(t){return t===s||null===t||e3(t)?(e.exit("chunkString"),l(t)):(e.consume(t),92===t?u:c)}function u(t){return t===s||92===t?(e.consume(t),c):c(t)}}function tk(e,t){let n;return function r(a){return e3(a)?(e.enter("lineEnding"),e.consume(a),e.exit("lineEnding"),n=!0,r):e4(a)?e7(e,r,n?"linePrefix":"lineSuffix")(a):t(a)}}function tS(e){return e.replace(/[\t\n\r ]+/g," ").replace(/^ | $/g,"").toLowerCase().toUpperCase()}let tb={partial:!0,tokenize:function(e,t,n){return function(t){return e5(t)?tk(e,r)(t):n(t)};function r(t){return t_(e,a,n,"definitionTitle","definitionTitleMarker","definitionTitleString")(t)}function a(t){return e4(t)?e7(e,i,"whitespace")(t):i(t)}function i(e){return null===e||e3(e)?t(e):n(e)}}},ty={name:"codeIndented",tokenize:function(e,t,n){let r=this;return function(t){return e.enter("codeIndented"),e7(e,a,"linePrefix",5)(t)};function a(t){let a=r.events[r.events.length-1];return a&&"linePrefix"===a[1].type&&a[2].sliceSerialize(a[1],!0).length>=4?function t(n){return null===n?i(n):e3(n)?e.attempt(tN,t,i)(n):(e.enter("codeFlowValue"),function n(r){return null===r||e3(r)?(e.exit("codeFlowValue"),t(r)):(e.consume(r),n)}(n))}(t):n(t)}function i(n){return e.exit("codeIndented"),t(n)}}},tN={partial:!0,tokenize:function(e,t,n){let r=this;return a;function a(t){return r.parser.lazy[r.now().line]?n(t):e3(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),a):e7(e,i,"linePrefix",5)(t)}function i(e){let i=r.events[r.events.length-1];return i&&"linePrefix"===i[1].type&&i[2].sliceSerialize(i[1],!0).length>=4?t(e):e3(e)?a(e):n(e)}}},tC={name:"setextUnderline",resolveTo:function(e,t){let n,r,a,i=e.length;for(;i--;)if("enter"===e[i][0]){if("content"===e[i][1].type){n=i;break}"paragraph"===e[i][1].type&&(r=i)}else"content"===e[i][1].type&&e.splice(i,1),a||"definition"!==e[i][1].type||(a=i);let s={type:"setextHeading",start:{...e[n][1].start},end:{...e[e.length-1][1].end}};return e[r][1].type="setextHeadingText",a?(e.splice(r,0,["enter",s,t]),e.splice(a+1,0,["exit",e[n][1],t]),e[n][1].end={...e[a][1].end}):e[n][1]=s,e.push(["exit",s,t]),e},tokenize:function(e,t,n){let r;let a=this;return function(t){let s,o=a.events.length;for(;o--;)if("lineEnding"!==a.events[o][1].type&&"linePrefix"!==a.events[o][1].type&&"content"!==a.events[o][1].type){s="paragraph"===a.events[o][1].type;break}return!a.parser.lazy[a.now().line]&&(a.interrupt||s)?(e.enter("setextHeadingLine"),r=t,e.enter("setextHeadingLineSequence"),function t(n){return n===r?(e.consume(n),t):(e.exit("setextHeadingLineSequence"),e4(n)?e7(e,i,"lineSuffix")(n):i(n))}(t)):n(t)};function i(r){return null===r||e3(r)?(e.exit("setextHeadingLine"),t(r)):n(r)}}},tI=["address","article","aside","base","basefont","blockquote","body","caption","center","col","colgroup","dd","details","dialog","dir","div","dl","dt","fieldset","figcaption","figure","footer","form","frame","frameset","h1","h2","h3","h4","h5","h6","head","header","hr","html","iframe","legend","li","link","main","menu","menuitem","nav","noframes","ol","optgroup","option","p","param","search","section","summary","table","tbody","td","tfoot","th","thead","title","tr","track","ul"],tD=["pre","script","style","textarea"],tO={partial:!0,tokenize:function(e,t,n){return function(r){return e.enter("lineEnding"),e.consume(r),e.exit("lineEnding"),e.attempt(tr,t,n)}}},tL={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return e3(t)?(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),a):n(t)};function a(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},tx={partial:!0,tokenize:function(e,t,n){let r=this;return function(t){return null===t?n(t):(e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),a)};function a(e){return r.parser.lazy[r.now().line]?n(e):t(e)}}},tR={concrete:!0,name:"codeFenced",tokenize:function(e,t,n){let r;let a=this,i={partial:!0,tokenize:function(e,t,n){let i=0;return function(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),s};function s(t){return e.enter("codeFencedFence"),e4(t)?e7(e,l,"linePrefix",a.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):l(t)}function l(t){return t===r?(e.enter("codeFencedFenceSequence"),function t(a){return a===r?(i++,e.consume(a),t):i>=o?(e.exit("codeFencedFenceSequence"),e4(a)?e7(e,c,"whitespace")(a):c(a)):n(a)}(t)):n(t)}function c(r){return null===r||e3(r)?(e.exit("codeFencedFence"),t(r)):n(r)}}},s=0,o=0;return function(t){return function(t){let i=a.events[a.events.length-1];return s=i&&"linePrefix"===i[1].type?i[2].sliceSerialize(i[1],!0).length:0,r=t,e.enter("codeFenced"),e.enter("codeFencedFence"),e.enter("codeFencedFenceSequence"),function t(a){return a===r?(o++,e.consume(a),t):o<3?n(a):(e.exit("codeFencedFenceSequence"),e4(a)?e7(e,l,"whitespace")(a):l(a))}(t)}(t)};function l(i){return null===i||e3(i)?(e.exit("codeFencedFence"),a.interrupt?t(i):e.check(tx,u,m)(i)):(e.enter("codeFencedFenceInfo"),e.enter("chunkString",{contentType:"string"}),function t(a){return null===a||e3(a)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),l(a)):e4(a)?(e.exit("chunkString"),e.exit("codeFencedFenceInfo"),e7(e,c,"whitespace")(a)):96===a&&a===r?n(a):(e.consume(a),t)}(i))}function c(t){return null===t||e3(t)?l(t):(e.enter("codeFencedFenceMeta"),e.enter("chunkString",{contentType:"string"}),function t(a){return null===a||e3(a)?(e.exit("chunkString"),e.exit("codeFencedFenceMeta"),l(a)):96===a&&a===r?n(a):(e.consume(a),t)}(t))}function u(t){return e.attempt(i,m,h)(t)}function h(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),d}function d(t){return s>0&&e4(t)?e7(e,p,"linePrefix",s+1)(t):p(t)}function p(t){return null===t||e3(t)?e.check(tx,u,m)(t):(e.enter("codeFlowValue"),function t(n){return null===n||e3(n)?(e.exit("codeFlowValue"),p(n)):(e.consume(n),t)}(t))}function m(n){return e.exit("codeFenced"),t(n)}}},tv={AElig:"\xc6",AMP:"&",Aacute:"\xc1",Abreve:"Ă",Acirc:"\xc2",Acy:"А",Afr:"\uD835\uDD04",Agrave:"\xc0",Alpha:"Α",Amacr:"Ā",And:"⩓",Aogon:"Ą",Aopf:"\uD835\uDD38",ApplyFunction:"⁡",Aring:"\xc5",Ascr:"\uD835\uDC9C",Assign:"≔",Atilde:"\xc3",Auml:"\xc4",Backslash:"∖",Barv:"⫧",Barwed:"⌆",Bcy:"Б",Because:"∵",Bernoullis:"ℬ",Beta:"Β",Bfr:"\uD835\uDD05",Bopf:"\uD835\uDD39",Breve:"˘",Bscr:"ℬ",Bumpeq:"≎",CHcy:"Ч",COPY:"\xa9",Cacute:"Ć",Cap:"⋒",CapitalDifferentialD:"ⅅ",Cayleys:"ℭ",Ccaron:"Č",Ccedil:"\xc7",Ccirc:"Ĉ",Cconint:"∰",Cdot:"Ċ",Cedilla:"\xb8",CenterDot:"\xb7",Cfr:"ℭ",Chi:"Χ",CircleDot:"⊙",CircleMinus:"⊖",CirclePlus:"⊕",CircleTimes:"⊗",ClockwiseContourIntegral:"∲",CloseCurlyDoubleQuote:"”",CloseCurlyQuote:"’",Colon:"∷",Colone:"⩴",Congruent:"≡",Conint:"∯",ContourIntegral:"∮",Copf:"ℂ",Coproduct:"∐",CounterClockwiseContourIntegral:"∳",Cross:"⨯",Cscr:"\uD835\uDC9E",Cup:"⋓",CupCap:"≍",DD:"ⅅ",DDotrahd:"⤑",DJcy:"Ђ",DScy:"Ѕ",DZcy:"Џ",Dagger:"‡",Darr:"↡",Dashv:"⫤",Dcaron:"Ď",Dcy:"Д",Del:"∇",Delta:"Δ",Dfr:"\uD835\uDD07",DiacriticalAcute:"\xb4",DiacriticalDot:"˙",DiacriticalDoubleAcute:"˝",DiacriticalGrave:"`",DiacriticalTilde:"˜",Diamond:"⋄",DifferentialD:"ⅆ",Dopf:"\uD835\uDD3B",Dot:"\xa8",DotDot:"⃜",DotEqual:"≐",DoubleContourIntegral:"∯",DoubleDot:"\xa8",DoubleDownArrow:"⇓",DoubleLeftArrow:"⇐",DoubleLeftRightArrow:"⇔",DoubleLeftTee:"⫤",DoubleLongLeftArrow:"⟸",DoubleLongLeftRightArrow:"⟺",DoubleLongRightArrow:"⟹",DoubleRightArrow:"⇒",DoubleRightTee:"⊨",DoubleUpArrow:"⇑",DoubleUpDownArrow:"⇕",DoubleVerticalBar:"∥",DownArrow:"↓",DownArrowBar:"⤓",DownArrowUpArrow:"⇵",DownBreve:"̑",DownLeftRightVector:"⥐",DownLeftTeeVector:"⥞",DownLeftVector:"↽",DownLeftVectorBar:"⥖",DownRightTeeVector:"⥟",DownRightVector:"⇁",DownRightVectorBar:"⥗",DownTee:"⊤",DownTeeArrow:"↧",Downarrow:"⇓",Dscr:"\uD835\uDC9F",Dstrok:"Đ",ENG:"Ŋ",ETH:"\xd0",Eacute:"\xc9",Ecaron:"Ě",Ecirc:"\xca",Ecy:"Э",Edot:"Ė",Efr:"\uD835\uDD08",Egrave:"\xc8",Element:"∈",Emacr:"Ē",EmptySmallSquare:"◻",EmptyVerySmallSquare:"▫",Eogon:"Ę",Eopf:"\uD835\uDD3C",Epsilon:"Ε",Equal:"⩵",EqualTilde:"≂",Equilibrium:"⇌",Escr:"ℰ",Esim:"⩳",Eta:"Η",Euml:"\xcb",Exists:"∃",ExponentialE:"ⅇ",Fcy:"Ф",Ffr:"\uD835\uDD09",FilledSmallSquare:"◼",FilledVerySmallSquare:"▪",Fopf:"\uD835\uDD3D",ForAll:"∀",Fouriertrf:"ℱ",Fscr:"ℱ",GJcy:"Ѓ",GT:">",Gamma:"Γ",Gammad:"Ϝ",Gbreve:"Ğ",Gcedil:"Ģ",Gcirc:"Ĝ",Gcy:"Г",Gdot:"Ġ",Gfr:"\uD835\uDD0A",Gg:"⋙",Gopf:"\uD835\uDD3E",GreaterEqual:"≥",GreaterEqualLess:"⋛",GreaterFullEqual:"≧",GreaterGreater:"⪢",GreaterLess:"≷",GreaterSlantEqual:"⩾",GreaterTilde:"≳",Gscr:"\uD835\uDCA2",Gt:"≫",HARDcy:"Ъ",Hacek:"ˇ",Hat:"^",Hcirc:"Ĥ",Hfr:"ℌ",HilbertSpace:"ℋ",Hopf:"ℍ",HorizontalLine:"─",Hscr:"ℋ",Hstrok:"Ħ",HumpDownHump:"≎",HumpEqual:"≏",IEcy:"Е",IJlig:"Ĳ",IOcy:"Ё",Iacute:"\xcd",Icirc:"\xce",Icy:"И",Idot:"İ",Ifr:"ℑ",Igrave:"\xcc",Im:"ℑ",Imacr:"Ī",ImaginaryI:"ⅈ",Implies:"⇒",Int:"∬",Integral:"∫",Intersection:"⋂",InvisibleComma:"⁣",InvisibleTimes:"⁢",Iogon:"Į",Iopf:"\uD835\uDD40",Iota:"Ι",Iscr:"ℐ",Itilde:"Ĩ",Iukcy:"І",Iuml:"\xcf",Jcirc:"Ĵ",Jcy:"Й",Jfr:"\uD835\uDD0D",Jopf:"\uD835\uDD41",Jscr:"\uD835\uDCA5",Jsercy:"Ј",Jukcy:"Є",KHcy:"Х",KJcy:"Ќ",Kappa:"Κ",Kcedil:"Ķ",Kcy:"К",Kfr:"\uD835\uDD0E",Kopf:"\uD835\uDD42",Kscr:"\uD835\uDCA6",LJcy:"Љ",LT:"<",Lacute:"Ĺ",Lambda:"Λ",Lang:"⟪",Laplacetrf:"ℒ",Larr:"↞",Lcaron:"Ľ",Lcedil:"Ļ",Lcy:"Л",LeftAngleBracket:"⟨",LeftArrow:"←",LeftArrowBar:"⇤",LeftArrowRightArrow:"⇆",LeftCeiling:"⌈",LeftDoubleBracket:"⟦",LeftDownTeeVector:"⥡",LeftDownVector:"⇃",LeftDownVectorBar:"⥙",LeftFloor:"⌊",LeftRightArrow:"↔",LeftRightVector:"⥎",LeftTee:"⊣",LeftTeeArrow:"↤",LeftTeeVector:"⥚",LeftTriangle:"⊲",LeftTriangleBar:"⧏",LeftTriangleEqual:"⊴",LeftUpDownVector:"⥑",LeftUpTeeVector:"⥠",LeftUpVector:"↿",LeftUpVectorBar:"⥘",LeftVector:"↼",LeftVectorBar:"⥒",Leftarrow:"⇐",Leftrightarrow:"⇔",LessEqualGreater:"⋚",LessFullEqual:"≦",LessGreater:"≶",LessLess:"⪡",LessSlantEqual:"⩽",LessTilde:"≲",Lfr:"\uD835\uDD0F",Ll:"⋘",Lleftarrow:"⇚",Lmidot:"Ŀ",LongLeftArrow:"⟵",LongLeftRightArrow:"⟷",LongRightArrow:"⟶",Longleftarrow:"⟸",Longleftrightarrow:"⟺",Longrightarrow:"⟹",Lopf:"\uD835\uDD43",LowerLeftArrow:"↙",LowerRightArrow:"↘",Lscr:"ℒ",Lsh:"↰",Lstrok:"Ł",Lt:"≪",Map:"⤅",Mcy:"М",MediumSpace:" ",Mellintrf:"ℳ",Mfr:"\uD835\uDD10",MinusPlus:"∓",Mopf:"\uD835\uDD44",Mscr:"ℳ",Mu:"Μ",NJcy:"Њ",Nacute:"Ń",Ncaron:"Ň",Ncedil:"Ņ",Ncy:"Н",NegativeMediumSpace:"​",NegativeThickSpace:"​",NegativeThinSpace:"​",NegativeVeryThinSpace:"​",NestedGreaterGreater:"≫",NestedLessLess:"≪",NewLine:"\n",Nfr:"\uD835\uDD11",NoBreak:"⁠",NonBreakingSpace:"\xa0",Nopf:"ℕ",Not:"⫬",NotCongruent:"≢",NotCupCap:"≭",NotDoubleVerticalBar:"∦",NotElement:"∉",NotEqual:"≠",NotEqualTilde:"≂̸",NotExists:"∄",NotGreater:"≯",NotGreaterEqual:"≱",NotGreaterFullEqual:"≧̸",NotGreaterGreater:"≫̸",NotGreaterLess:"≹",NotGreaterSlantEqual:"⩾̸",NotGreaterTilde:"≵",NotHumpDownHump:"≎̸",NotHumpEqual:"≏̸",NotLeftTriangle:"⋪",NotLeftTriangleBar:"⧏̸",NotLeftTriangleEqual:"⋬",NotLess:"≮",NotLessEqual:"≰",NotLessGreater:"≸",NotLessLess:"≪̸",NotLessSlantEqual:"⩽̸",NotLessTilde:"≴",NotNestedGreaterGreater:"⪢̸",NotNestedLessLess:"⪡̸",NotPrecedes:"⊀",NotPrecedesEqual:"⪯̸",NotPrecedesSlantEqual:"⋠",NotReverseElement:"∌",NotRightTriangle:"⋫",NotRightTriangleBar:"⧐̸",NotRightTriangleEqual:"⋭",NotSquareSubset:"⊏̸",NotSquareSubsetEqual:"⋢",NotSquareSuperset:"⊐̸",NotSquareSupersetEqual:"⋣",NotSubset:"⊂⃒",NotSubsetEqual:"⊈",NotSucceeds:"⊁",NotSucceedsEqual:"⪰̸",NotSucceedsSlantEqual:"⋡",NotSucceedsTilde:"≿̸",NotSuperset:"⊃⃒",NotSupersetEqual:"⊉",NotTilde:"≁",NotTildeEqual:"≄",NotTildeFullEqual:"≇",NotTildeTilde:"≉",NotVerticalBar:"∤",Nscr:"\uD835\uDCA9",Ntilde:"\xd1",Nu:"Ν",OElig:"Œ",Oacute:"\xd3",Ocirc:"\xd4",Ocy:"О",Odblac:"Ő",Ofr:"\uD835\uDD12",Ograve:"\xd2",Omacr:"Ō",Omega:"Ω",Omicron:"Ο",Oopf:"\uD835\uDD46",OpenCurlyDoubleQuote:"“",OpenCurlyQuote:"‘",Or:"⩔",Oscr:"\uD835\uDCAA",Oslash:"\xd8",Otilde:"\xd5",Otimes:"⨷",Ouml:"\xd6",OverBar:"‾",OverBrace:"⏞",OverBracket:"⎴",OverParenthesis:"⏜",PartialD:"∂",Pcy:"П",Pfr:"\uD835\uDD13",Phi:"Φ",Pi:"Π",PlusMinus:"\xb1",Poincareplane:"ℌ",Popf:"ℙ",Pr:"⪻",Precedes:"≺",PrecedesEqual:"⪯",PrecedesSlantEqual:"≼",PrecedesTilde:"≾",Prime:"″",Product:"∏",Proportion:"∷",Proportional:"∝",Pscr:"\uD835\uDCAB",Psi:"Ψ",QUOT:'"',Qfr:"\uD835\uDD14",Qopf:"ℚ",Qscr:"\uD835\uDCAC",RBarr:"⤐",REG:"\xae",Racute:"Ŕ",Rang:"⟫",Rarr:"↠",Rarrtl:"⤖",Rcaron:"Ř",Rcedil:"Ŗ",Rcy:"Р",Re:"ℜ",ReverseElement:"∋",ReverseEquilibrium:"⇋",ReverseUpEquilibrium:"⥯",Rfr:"ℜ",Rho:"Ρ",RightAngleBracket:"⟩",RightArrow:"→",RightArrowBar:"⇥",RightArrowLeftArrow:"⇄",RightCeiling:"⌉",RightDoubleBracket:"⟧",RightDownTeeVector:"⥝",RightDownVector:"⇂",RightDownVectorBar:"⥕",RightFloor:"⌋",RightTee:"⊢",RightTeeArrow:"↦",RightTeeVector:"⥛",RightTriangle:"⊳",RightTriangleBar:"⧐",RightTriangleEqual:"⊵",RightUpDownVector:"⥏",RightUpTeeVector:"⥜",RightUpVector:"↾",RightUpVectorBar:"⥔",RightVector:"⇀",RightVectorBar:"⥓",Rightarrow:"⇒",Ropf:"ℝ",RoundImplies:"⥰",Rrightarrow:"⇛",Rscr:"ℛ",Rsh:"↱",RuleDelayed:"⧴",SHCHcy:"Щ",SHcy:"Ш",SOFTcy:"Ь",Sacute:"Ś",Sc:"⪼",Scaron:"Š",Scedil:"Ş",Scirc:"Ŝ",Scy:"С",Sfr:"\uD835\uDD16",ShortDownArrow:"↓",ShortLeftArrow:"←",ShortRightArrow:"→",ShortUpArrow:"↑",Sigma:"Σ",SmallCircle:"∘",Sopf:"\uD835\uDD4A",Sqrt:"√",Square:"□",SquareIntersection:"⊓",SquareSubset:"⊏",SquareSubsetEqual:"⊑",SquareSuperset:"⊐",SquareSupersetEqual:"⊒",SquareUnion:"⊔",Sscr:"\uD835\uDCAE",Star:"⋆",Sub:"⋐",Subset:"⋐",SubsetEqual:"⊆",Succeeds:"≻",SucceedsEqual:"⪰",SucceedsSlantEqual:"≽",SucceedsTilde:"≿",SuchThat:"∋",Sum:"∑",Sup:"⋑",Superset:"⊃",SupersetEqual:"⊇",Supset:"⋑",THORN:"\xde",TRADE:"™",TSHcy:"Ћ",TScy:"Ц",Tab:"	",Tau:"Τ",Tcaron:"Ť",Tcedil:"Ţ",Tcy:"Т",Tfr:"\uD835\uDD17",Therefore:"∴",Theta:"Θ",ThickSpace:"  ",ThinSpace:" ",Tilde:"∼",TildeEqual:"≃",TildeFullEqual:"≅",TildeTilde:"≈",Topf:"\uD835\uDD4B",TripleDot:"⃛",Tscr:"\uD835\uDCAF",Tstrok:"Ŧ",Uacute:"\xda",Uarr:"↟",Uarrocir:"⥉",Ubrcy:"Ў",Ubreve:"Ŭ",Ucirc:"\xdb",Ucy:"У",Udblac:"Ű",Ufr:"\uD835\uDD18",Ugrave:"\xd9",Umacr:"Ū",UnderBar:"_",UnderBrace:"⏟",UnderBracket:"⎵",UnderParenthesis:"⏝",Union:"⋃",UnionPlus:"⊎",Uogon:"Ų",Uopf:"\uD835\uDD4C",UpArrow:"↑",UpArrowBar:"⤒",UpArrowDownArrow:"⇅",UpDownArrow:"↕",UpEquilibrium:"⥮",UpTee:"⊥",UpTeeArrow:"↥",Uparrow:"⇑",Updownarrow:"⇕",UpperLeftArrow:"↖",UpperRightArrow:"↗",Upsi:"ϒ",Upsilon:"Υ",Uring:"Ů",Uscr:"\uD835\uDCB0",Utilde:"Ũ",Uuml:"\xdc",VDash:"⊫",Vbar:"⫫",Vcy:"В",Vdash:"⊩",Vdashl:"⫦",Vee:"⋁",Verbar:"‖",Vert:"‖",VerticalBar:"∣",VerticalLine:"|",VerticalSeparator:"❘",VerticalTilde:"≀",VeryThinSpace:" ",Vfr:"\uD835\uDD19",Vopf:"\uD835\uDD4D",Vscr:"\uD835\uDCB1",Vvdash:"⊪",Wcirc:"Ŵ",Wedge:"⋀",Wfr:"\uD835\uDD1A",Wopf:"\uD835\uDD4E",Wscr:"\uD835\uDCB2",Xfr:"\uD835\uDD1B",Xi:"Ξ",Xopf:"\uD835\uDD4F",Xscr:"\uD835\uDCB3",YAcy:"Я",YIcy:"Ї",YUcy:"Ю",Yacute:"\xdd",Ycirc:"Ŷ",Ycy:"Ы",Yfr:"\uD835\uDD1C",Yopf:"\uD835\uDD50",Yscr:"\uD835\uDCB4",Yuml:"Ÿ",ZHcy:"Ж",Zacute:"Ź",Zcaron:"Ž",Zcy:"З",Zdot:"Ż",ZeroWidthSpace:"​",Zeta:"Ζ",Zfr:"ℨ",Zopf:"ℤ",Zscr:"\uD835\uDCB5",aacute:"\xe1",abreve:"ă",ac:"∾",acE:"∾̳",acd:"∿",acirc:"\xe2",acute:"\xb4",acy:"а",aelig:"\xe6",af:"⁡",afr:"\uD835\uDD1E",agrave:"\xe0",alefsym:"ℵ",aleph:"ℵ",alpha:"α",amacr:"ā",amalg:"⨿",amp:"&",and:"∧",andand:"⩕",andd:"⩜",andslope:"⩘",andv:"⩚",ang:"∠",ange:"⦤",angle:"∠",angmsd:"∡",angmsdaa:"⦨",angmsdab:"⦩",angmsdac:"⦪",angmsdad:"⦫",angmsdae:"⦬",angmsdaf:"⦭",angmsdag:"⦮",angmsdah:"⦯",angrt:"∟",angrtvb:"⊾",angrtvbd:"⦝",angsph:"∢",angst:"\xc5",angzarr:"⍼",aogon:"ą",aopf:"\uD835\uDD52",ap:"≈",apE:"⩰",apacir:"⩯",ape:"≊",apid:"≋",apos:"'",approx:"≈",approxeq:"≊",aring:"\xe5",ascr:"\uD835\uDCB6",ast:"*",asymp:"≈",asympeq:"≍",atilde:"\xe3",auml:"\xe4",awconint:"∳",awint:"⨑",bNot:"⫭",backcong:"≌",backepsilon:"϶",backprime:"‵",backsim:"∽",backsimeq:"⋍",barvee:"⊽",barwed:"⌅",barwedge:"⌅",bbrk:"⎵",bbrktbrk:"⎶",bcong:"≌",bcy:"б",bdquo:"„",becaus:"∵",because:"∵",bemptyv:"⦰",bepsi:"϶",bernou:"ℬ",beta:"β",beth:"ℶ",between:"≬",bfr:"\uD835\uDD1F",bigcap:"⋂",bigcirc:"◯",bigcup:"⋃",bigodot:"⨀",bigoplus:"⨁",bigotimes:"⨂",bigsqcup:"⨆",bigstar:"★",bigtriangledown:"▽",bigtriangleup:"△",biguplus:"⨄",bigvee:"⋁",bigwedge:"⋀",bkarow:"⤍",blacklozenge:"⧫",blacksquare:"▪",blacktriangle:"▴",blacktriangledown:"▾",blacktriangleleft:"◂",blacktriangleright:"▸",blank:"␣",blk12:"▒",blk14:"░",blk34:"▓",block:"█",bne:"=⃥",bnequiv:"≡⃥",bnot:"⌐",bopf:"\uD835\uDD53",bot:"⊥",bottom:"⊥",bowtie:"⋈",boxDL:"╗",boxDR:"╔",boxDl:"╖",boxDr:"╓",boxH:"═",boxHD:"╦",boxHU:"╩",boxHd:"╤",boxHu:"╧",boxUL:"╝",boxUR:"╚",boxUl:"╜",boxUr:"╙",boxV:"║",boxVH:"╬",boxVL:"╣",boxVR:"╠",boxVh:"╫",boxVl:"╢",boxVr:"╟",boxbox:"⧉",boxdL:"╕",boxdR:"╒",boxdl:"┐",boxdr:"┌",boxh:"─",boxhD:"╥",boxhU:"╨",boxhd:"┬",boxhu:"┴",boxminus:"⊟",boxplus:"⊞",boxtimes:"⊠",boxuL:"╛",boxuR:"╘",boxul:"┘",boxur:"└",boxv:"│",boxvH:"╪",boxvL:"╡",boxvR:"╞",boxvh:"┼",boxvl:"┤",boxvr:"├",bprime:"‵",breve:"˘",brvbar:"\xa6",bscr:"\uD835\uDCB7",bsemi:"⁏",bsim:"∽",bsime:"⋍",bsol:"\\",bsolb:"⧅",bsolhsub:"⟈",bull:"•",bullet:"•",bump:"≎",bumpE:"⪮",bumpe:"≏",bumpeq:"≏",cacute:"ć",cap:"∩",capand:"⩄",capbrcup:"⩉",capcap:"⩋",capcup:"⩇",capdot:"⩀",caps:"∩︀",caret:"⁁",caron:"ˇ",ccaps:"⩍",ccaron:"č",ccedil:"\xe7",ccirc:"ĉ",ccups:"⩌",ccupssm:"⩐",cdot:"ċ",cedil:"\xb8",cemptyv:"⦲",cent:"\xa2",centerdot:"\xb7",cfr:"\uD835\uDD20",chcy:"ч",check:"✓",checkmark:"✓",chi:"χ",cir:"○",cirE:"⧃",circ:"ˆ",circeq:"≗",circlearrowleft:"↺",circlearrowright:"↻",circledR:"\xae",circledS:"Ⓢ",circledast:"⊛",circledcirc:"⊚",circleddash:"⊝",cire:"≗",cirfnint:"⨐",cirmid:"⫯",cirscir:"⧂",clubs:"♣",clubsuit:"♣",colon:":",colone:"≔",coloneq:"≔",comma:",",commat:"@",comp:"∁",compfn:"∘",complement:"∁",complexes:"ℂ",cong:"≅",congdot:"⩭",conint:"∮",copf:"\uD835\uDD54",coprod:"∐",copy:"\xa9",copysr:"℗",crarr:"↵",cross:"✗",cscr:"\uD835\uDCB8",csub:"⫏",csube:"⫑",csup:"⫐",csupe:"⫒",ctdot:"⋯",cudarrl:"⤸",cudarrr:"⤵",cuepr:"⋞",cuesc:"⋟",cularr:"↶",cularrp:"⤽",cup:"∪",cupbrcap:"⩈",cupcap:"⩆",cupcup:"⩊",cupdot:"⊍",cupor:"⩅",cups:"∪︀",curarr:"↷",curarrm:"⤼",curlyeqprec:"⋞",curlyeqsucc:"⋟",curlyvee:"⋎",curlywedge:"⋏",curren:"\xa4",curvearrowleft:"↶",curvearrowright:"↷",cuvee:"⋎",cuwed:"⋏",cwconint:"∲",cwint:"∱",cylcty:"⌭",dArr:"⇓",dHar:"⥥",dagger:"†",daleth:"ℸ",darr:"↓",dash:"‐",dashv:"⊣",dbkarow:"⤏",dblac:"˝",dcaron:"ď",dcy:"д",dd:"ⅆ",ddagger:"‡",ddarr:"⇊",ddotseq:"⩷",deg:"\xb0",delta:"δ",demptyv:"⦱",dfisht:"⥿",dfr:"\uD835\uDD21",dharl:"⇃",dharr:"⇂",diam:"⋄",diamond:"⋄",diamondsuit:"♦",diams:"♦",die:"\xa8",digamma:"ϝ",disin:"⋲",div:"\xf7",divide:"\xf7",divideontimes:"⋇",divonx:"⋇",djcy:"ђ",dlcorn:"⌞",dlcrop:"⌍",dollar:"$",dopf:"\uD835\uDD55",dot:"˙",doteq:"≐",doteqdot:"≑",dotminus:"∸",dotplus:"∔",dotsquare:"⊡",doublebarwedge:"⌆",downarrow:"↓",downdownarrows:"⇊",downharpoonleft:"⇃",downharpoonright:"⇂",drbkarow:"⤐",drcorn:"⌟",drcrop:"⌌",dscr:"\uD835\uDCB9",dscy:"ѕ",dsol:"⧶",dstrok:"đ",dtdot:"⋱",dtri:"▿",dtrif:"▾",duarr:"⇵",duhar:"⥯",dwangle:"⦦",dzcy:"џ",dzigrarr:"⟿",eDDot:"⩷",eDot:"≑",eacute:"\xe9",easter:"⩮",ecaron:"ě",ecir:"≖",ecirc:"\xea",ecolon:"≕",ecy:"э",edot:"ė",ee:"ⅇ",efDot:"≒",efr:"\uD835\uDD22",eg:"⪚",egrave:"\xe8",egs:"⪖",egsdot:"⪘",el:"⪙",elinters:"⏧",ell:"ℓ",els:"⪕",elsdot:"⪗",emacr:"ē",empty:"∅",emptyset:"∅",emptyv:"∅",emsp13:" ",emsp14:" ",emsp:" ",eng:"ŋ",ensp:" ",eogon:"ę",eopf:"\uD835\uDD56",epar:"⋕",eparsl:"⧣",eplus:"⩱",epsi:"ε",epsilon:"ε",epsiv:"ϵ",eqcirc:"≖",eqcolon:"≕",eqsim:"≂",eqslantgtr:"⪖",eqslantless:"⪕",equals:"=",equest:"≟",equiv:"≡",equivDD:"⩸",eqvparsl:"⧥",erDot:"≓",erarr:"⥱",escr:"ℯ",esdot:"≐",esim:"≂",eta:"η",eth:"\xf0",euml:"\xeb",euro:"€",excl:"!",exist:"∃",expectation:"ℰ",exponentiale:"ⅇ",fallingdotseq:"≒",fcy:"ф",female:"♀",ffilig:"ﬃ",fflig:"ﬀ",ffllig:"ﬄ",ffr:"\uD835\uDD23",filig:"ﬁ",fjlig:"fj",flat:"♭",fllig:"ﬂ",fltns:"▱",fnof:"ƒ",fopf:"\uD835\uDD57",forall:"∀",fork:"⋔",forkv:"⫙",fpartint:"⨍",frac12:"\xbd",frac13:"⅓",frac14:"\xbc",frac15:"⅕",frac16:"⅙",frac18:"⅛",frac23:"⅔",frac25:"⅖",frac34:"\xbe",frac35:"⅗",frac38:"⅜",frac45:"⅘",frac56:"⅚",frac58:"⅝",frac78:"⅞",frasl:"⁄",frown:"⌢",fscr:"\uD835\uDCBB",gE:"≧",gEl:"⪌",gacute:"ǵ",gamma:"γ",gammad:"ϝ",gap:"⪆",gbreve:"ğ",gcirc:"ĝ",gcy:"г",gdot:"ġ",ge:"≥",gel:"⋛",geq:"≥",geqq:"≧",geqslant:"⩾",ges:"⩾",gescc:"⪩",gesdot:"⪀",gesdoto:"⪂",gesdotol:"⪄",gesl:"⋛︀",gesles:"⪔",gfr:"\uD835\uDD24",gg:"≫",ggg:"⋙",gimel:"ℷ",gjcy:"ѓ",gl:"≷",glE:"⪒",gla:"⪥",glj:"⪤",gnE:"≩",gnap:"⪊",gnapprox:"⪊",gne:"⪈",gneq:"⪈",gneqq:"≩",gnsim:"⋧",gopf:"\uD835\uDD58",grave:"`",gscr:"ℊ",gsim:"≳",gsime:"⪎",gsiml:"⪐",gt:">",gtcc:"⪧",gtcir:"⩺",gtdot:"⋗",gtlPar:"⦕",gtquest:"⩼",gtrapprox:"⪆",gtrarr:"⥸",gtrdot:"⋗",gtreqless:"⋛",gtreqqless:"⪌",gtrless:"≷",gtrsim:"≳",gvertneqq:"≩︀",gvnE:"≩︀",hArr:"⇔",hairsp:" ",half:"\xbd",hamilt:"ℋ",hardcy:"ъ",harr:"↔",harrcir:"⥈",harrw:"↭",hbar:"ℏ",hcirc:"ĥ",hearts:"♥",heartsuit:"♥",hellip:"…",hercon:"⊹",hfr:"\uD835\uDD25",hksearow:"⤥",hkswarow:"⤦",hoarr:"⇿",homtht:"∻",hookleftarrow:"↩",hookrightarrow:"↪",hopf:"\uD835\uDD59",horbar:"―",hscr:"\uD835\uDCBD",hslash:"ℏ",hstrok:"ħ",hybull:"⁃",hyphen:"‐",iacute:"\xed",ic:"⁣",icirc:"\xee",icy:"и",iecy:"е",iexcl:"\xa1",iff:"⇔",ifr:"\uD835\uDD26",igrave:"\xec",ii:"ⅈ",iiiint:"⨌",iiint:"∭",iinfin:"⧜",iiota:"℩",ijlig:"ĳ",imacr:"ī",image:"ℑ",imagline:"ℐ",imagpart:"ℑ",imath:"ı",imof:"⊷",imped:"Ƶ",in:"∈",incare:"℅",infin:"∞",infintie:"⧝",inodot:"ı",int:"∫",intcal:"⊺",integers:"ℤ",intercal:"⊺",intlarhk:"⨗",intprod:"⨼",iocy:"ё",iogon:"į",iopf:"\uD835\uDD5A",iota:"ι",iprod:"⨼",iquest:"\xbf",iscr:"\uD835\uDCBE",isin:"∈",isinE:"⋹",isindot:"⋵",isins:"⋴",isinsv:"⋳",isinv:"∈",it:"⁢",itilde:"ĩ",iukcy:"і",iuml:"\xef",jcirc:"ĵ",jcy:"й",jfr:"\uD835\uDD27",jmath:"ȷ",jopf:"\uD835\uDD5B",jscr:"\uD835\uDCBF",jsercy:"ј",jukcy:"є",kappa:"κ",kappav:"ϰ",kcedil:"ķ",kcy:"к",kfr:"\uD835\uDD28",kgreen:"ĸ",khcy:"х",kjcy:"ќ",kopf:"\uD835\uDD5C",kscr:"\uD835\uDCC0",lAarr:"⇚",lArr:"⇐",lAtail:"⤛",lBarr:"⤎",lE:"≦",lEg:"⪋",lHar:"⥢",lacute:"ĺ",laemptyv:"⦴",lagran:"ℒ",lambda:"λ",lang:"⟨",langd:"⦑",langle:"⟨",lap:"⪅",laquo:"\xab",larr:"←",larrb:"⇤",larrbfs:"⤟",larrfs:"⤝",larrhk:"↩",larrlp:"↫",larrpl:"⤹",larrsim:"⥳",larrtl:"↢",lat:"⪫",latail:"⤙",late:"⪭",lates:"⪭︀",lbarr:"⤌",lbbrk:"❲",lbrace:"{",lbrack:"[",lbrke:"⦋",lbrksld:"⦏",lbrkslu:"⦍",lcaron:"ľ",lcedil:"ļ",lceil:"⌈",lcub:"{",lcy:"л",ldca:"⤶",ldquo:"“",ldquor:"„",ldrdhar:"⥧",ldrushar:"⥋",ldsh:"↲",le:"≤",leftarrow:"←",leftarrowtail:"↢",leftharpoondown:"↽",leftharpoonup:"↼",leftleftarrows:"⇇",leftrightarrow:"↔",leftrightarrows:"⇆",leftrightharpoons:"⇋",leftrightsquigarrow:"↭",leftthreetimes:"⋋",leg:"⋚",leq:"≤",leqq:"≦",leqslant:"⩽",les:"⩽",lescc:"⪨",lesdot:"⩿",lesdoto:"⪁",lesdotor:"⪃",lesg:"⋚︀",lesges:"⪓",lessapprox:"⪅",lessdot:"⋖",lesseqgtr:"⋚",lesseqqgtr:"⪋",lessgtr:"≶",lesssim:"≲",lfisht:"⥼",lfloor:"⌊",lfr:"\uD835\uDD29",lg:"≶",lgE:"⪑",lhard:"↽",lharu:"↼",lharul:"⥪",lhblk:"▄",ljcy:"љ",ll:"≪",llarr:"⇇",llcorner:"⌞",llhard:"⥫",lltri:"◺",lmidot:"ŀ",lmoust:"⎰",lmoustache:"⎰",lnE:"≨",lnap:"⪉",lnapprox:"⪉",lne:"⪇",lneq:"⪇",lneqq:"≨",lnsim:"⋦",loang:"⟬",loarr:"⇽",lobrk:"⟦",longleftarrow:"⟵",longleftrightarrow:"⟷",longmapsto:"⟼",longrightarrow:"⟶",looparrowleft:"↫",looparrowright:"↬",lopar:"⦅",lopf:"\uD835\uDD5D",loplus:"⨭",lotimes:"⨴",lowast:"∗",lowbar:"_",loz:"◊",lozenge:"◊",lozf:"⧫",lpar:"(",lparlt:"⦓",lrarr:"⇆",lrcorner:"⌟",lrhar:"⇋",lrhard:"⥭",lrm:"‎",lrtri:"⊿",lsaquo:"‹",lscr:"\uD835\uDCC1",lsh:"↰",lsim:"≲",lsime:"⪍",lsimg:"⪏",lsqb:"[",lsquo:"‘",lsquor:"‚",lstrok:"ł",lt:"<",ltcc:"⪦",ltcir:"⩹",ltdot:"⋖",lthree:"⋋",ltimes:"⋉",ltlarr:"⥶",ltquest:"⩻",ltrPar:"⦖",ltri:"◃",ltrie:"⊴",ltrif:"◂",lurdshar:"⥊",luruhar:"⥦",lvertneqq:"≨︀",lvnE:"≨︀",mDDot:"∺",macr:"\xaf",male:"♂",malt:"✠",maltese:"✠",map:"↦",mapsto:"↦",mapstodown:"↧",mapstoleft:"↤",mapstoup:"↥",marker:"▮",mcomma:"⨩",mcy:"м",mdash:"—",measuredangle:"∡",mfr:"\uD835\uDD2A",mho:"℧",micro:"\xb5",mid:"∣",midast:"*",midcir:"⫰",middot:"\xb7",minus:"−",minusb:"⊟",minusd:"∸",minusdu:"⨪",mlcp:"⫛",mldr:"…",mnplus:"∓",models:"⊧",mopf:"\uD835\uDD5E",mp:"∓",mscr:"\uD835\uDCC2",mstpos:"∾",mu:"μ",multimap:"⊸",mumap:"⊸",nGg:"⋙̸",nGt:"≫⃒",nGtv:"≫̸",nLeftarrow:"⇍",nLeftrightarrow:"⇎",nLl:"⋘̸",nLt:"≪⃒",nLtv:"≪̸",nRightarrow:"⇏",nVDash:"⊯",nVdash:"⊮",nabla:"∇",nacute:"ń",nang:"∠⃒",nap:"≉",napE:"⩰̸",napid:"≋̸",napos:"ŉ",napprox:"≉",natur:"♮",natural:"♮",naturals:"ℕ",nbsp:"\xa0",nbump:"≎̸",nbumpe:"≏̸",ncap:"⩃",ncaron:"ň",ncedil:"ņ",ncong:"≇",ncongdot:"⩭̸",ncup:"⩂",ncy:"н",ndash:"–",ne:"≠",neArr:"⇗",nearhk:"⤤",nearr:"↗",nearrow:"↗",nedot:"≐̸",nequiv:"≢",nesear:"⤨",nesim:"≂̸",nexist:"∄",nexists:"∄",nfr:"\uD835\uDD2B",ngE:"≧̸",nge:"≱",ngeq:"≱",ngeqq:"≧̸",ngeqslant:"⩾̸",nges:"⩾̸",ngsim:"≵",ngt:"≯",ngtr:"≯",nhArr:"⇎",nharr:"↮",nhpar:"⫲",ni:"∋",nis:"⋼",nisd:"⋺",niv:"∋",njcy:"њ",nlArr:"⇍",nlE:"≦̸",nlarr:"↚",nldr:"‥",nle:"≰",nleftarrow:"↚",nleftrightarrow:"↮",nleq:"≰",nleqq:"≦̸",nleqslant:"⩽̸",nles:"⩽̸",nless:"≮",nlsim:"≴",nlt:"≮",nltri:"⋪",nltrie:"⋬",nmid:"∤",nopf:"\uD835\uDD5F",not:"\xac",notin:"∉",notinE:"⋹̸",notindot:"⋵̸",notinva:"∉",notinvb:"⋷",notinvc:"⋶",notni:"∌",notniva:"∌",notnivb:"⋾",notnivc:"⋽",npar:"∦",nparallel:"∦",nparsl:"⫽⃥",npart:"∂̸",npolint:"⨔",npr:"⊀",nprcue:"⋠",npre:"⪯̸",nprec:"⊀",npreceq:"⪯̸",nrArr:"⇏",nrarr:"↛",nrarrc:"⤳̸",nrarrw:"↝̸",nrightarrow:"↛",nrtri:"⋫",nrtrie:"⋭",nsc:"⊁",nsccue:"⋡",nsce:"⪰̸",nscr:"\uD835\uDCC3",nshortmid:"∤",nshortparallel:"∦",nsim:"≁",nsime:"≄",nsimeq:"≄",nsmid:"∤",nspar:"∦",nsqsube:"⋢",nsqsupe:"⋣",nsub:"⊄",nsubE:"⫅̸",nsube:"⊈",nsubset:"⊂⃒",nsubseteq:"⊈",nsubseteqq:"⫅̸",nsucc:"⊁",nsucceq:"⪰̸",nsup:"⊅",nsupE:"⫆̸",nsupe:"⊉",nsupset:"⊃⃒",nsupseteq:"⊉",nsupseteqq:"⫆̸",ntgl:"≹",ntilde:"\xf1",ntlg:"≸",ntriangleleft:"⋪",ntrianglelefteq:"⋬",ntriangleright:"⋫",ntrianglerighteq:"⋭",nu:"ν",num:"#",numero:"№",numsp:" ",nvDash:"⊭",nvHarr:"⤄",nvap:"≍⃒",nvdash:"⊬",nvge:"≥⃒",nvgt:">⃒",nvinfin:"⧞",nvlArr:"⤂",nvle:"≤⃒",nvlt:"<⃒",nvltrie:"⊴⃒",nvrArr:"⤃",nvrtrie:"⊵⃒",nvsim:"∼⃒",nwArr:"⇖",nwarhk:"⤣",nwarr:"↖",nwarrow:"↖",nwnear:"⤧",oS:"Ⓢ",oacute:"\xf3",oast:"⊛",ocir:"⊚",ocirc:"\xf4",ocy:"о",odash:"⊝",odblac:"ő",odiv:"⨸",odot:"⊙",odsold:"⦼",oelig:"œ",ofcir:"⦿",ofr:"\uD835\uDD2C",ogon:"˛",ograve:"\xf2",ogt:"⧁",ohbar:"⦵",ohm:"Ω",oint:"∮",olarr:"↺",olcir:"⦾",olcross:"⦻",oline:"‾",olt:"⧀",omacr:"ō",omega:"ω",omicron:"ο",omid:"⦶",ominus:"⊖",oopf:"\uD835\uDD60",opar:"⦷",operp:"⦹",oplus:"⊕",or:"∨",orarr:"↻",ord:"⩝",order:"ℴ",orderof:"ℴ",ordf:"\xaa",ordm:"\xba",origof:"⊶",oror:"⩖",orslope:"⩗",orv:"⩛",oscr:"ℴ",oslash:"\xf8",osol:"⊘",otilde:"\xf5",otimes:"⊗",otimesas:"⨶",ouml:"\xf6",ovbar:"⌽",par:"∥",para:"\xb6",parallel:"∥",parsim:"⫳",parsl:"⫽",part:"∂",pcy:"п",percnt:"%",period:".",permil:"‰",perp:"⊥",pertenk:"‱",pfr:"\uD835\uDD2D",phi:"φ",phiv:"ϕ",phmmat:"ℳ",phone:"☎",pi:"π",pitchfork:"⋔",piv:"ϖ",planck:"ℏ",planckh:"ℎ",plankv:"ℏ",plus:"+",plusacir:"⨣",plusb:"⊞",pluscir:"⨢",plusdo:"∔",plusdu:"⨥",pluse:"⩲",plusmn:"\xb1",plussim:"⨦",plustwo:"⨧",pm:"\xb1",pointint:"⨕",popf:"\uD835\uDD61",pound:"\xa3",pr:"≺",prE:"⪳",prap:"⪷",prcue:"≼",pre:"⪯",prec:"≺",precapprox:"⪷",preccurlyeq:"≼",preceq:"⪯",precnapprox:"⪹",precneqq:"⪵",precnsim:"⋨",precsim:"≾",prime:"′",primes:"ℙ",prnE:"⪵",prnap:"⪹",prnsim:"⋨",prod:"∏",profalar:"⌮",profline:"⌒",profsurf:"⌓",prop:"∝",propto:"∝",prsim:"≾",prurel:"⊰",pscr:"\uD835\uDCC5",psi:"ψ",puncsp:" ",qfr:"\uD835\uDD2E",qint:"⨌",qopf:"\uD835\uDD62",qprime:"⁗",qscr:"\uD835\uDCC6",quaternions:"ℍ",quatint:"⨖",quest:"?",questeq:"≟",quot:'"',rAarr:"⇛",rArr:"⇒",rAtail:"⤜",rBarr:"⤏",rHar:"⥤",race:"∽̱",racute:"ŕ",radic:"√",raemptyv:"⦳",rang:"⟩",rangd:"⦒",range:"⦥",rangle:"⟩",raquo:"\xbb",rarr:"→",rarrap:"⥵",rarrb:"⇥",rarrbfs:"⤠",rarrc:"⤳",rarrfs:"⤞",rarrhk:"↪",rarrlp:"↬",rarrpl:"⥅",rarrsim:"⥴",rarrtl:"↣",rarrw:"↝",ratail:"⤚",ratio:"∶",rationals:"ℚ",rbarr:"⤍",rbbrk:"❳",rbrace:"}",rbrack:"]",rbrke:"⦌",rbrksld:"⦎",rbrkslu:"⦐",rcaron:"ř",rcedil:"ŗ",rceil:"⌉",rcub:"}",rcy:"р",rdca:"⤷",rdldhar:"⥩",rdquo:"”",rdquor:"”",rdsh:"↳",real:"ℜ",realine:"ℛ",realpart:"ℜ",reals:"ℝ",rect:"▭",reg:"\xae",rfisht:"⥽",rfloor:"⌋",rfr:"\uD835\uDD2F",rhard:"⇁",rharu:"⇀",rharul:"⥬",rho:"ρ",rhov:"ϱ",rightarrow:"→",rightarrowtail:"↣",rightharpoondown:"⇁",rightharpoonup:"⇀",rightleftarrows:"⇄",rightleftharpoons:"⇌",rightrightarrows:"⇉",rightsquigarrow:"↝",rightthreetimes:"⋌",ring:"˚",risingdotseq:"≓",rlarr:"⇄",rlhar:"⇌",rlm:"‏",rmoust:"⎱",rmoustache:"⎱",rnmid:"⫮",roang:"⟭",roarr:"⇾",robrk:"⟧",ropar:"⦆",ropf:"\uD835\uDD63",roplus:"⨮",rotimes:"⨵",rpar:")",rpargt:"⦔",rppolint:"⨒",rrarr:"⇉",rsaquo:"›",rscr:"\uD835\uDCC7",rsh:"↱",rsqb:"]",rsquo:"’",rsquor:"’",rthree:"⋌",rtimes:"⋊",rtri:"▹",rtrie:"⊵",rtrif:"▸",rtriltri:"⧎",ruluhar:"⥨",rx:"℞",sacute:"ś",sbquo:"‚",sc:"≻",scE:"⪴",scap:"⪸",scaron:"š",sccue:"≽",sce:"⪰",scedil:"ş",scirc:"ŝ",scnE:"⪶",scnap:"⪺",scnsim:"⋩",scpolint:"⨓",scsim:"≿",scy:"с",sdot:"⋅",sdotb:"⊡",sdote:"⩦",seArr:"⇘",searhk:"⤥",searr:"↘",searrow:"↘",sect:"\xa7",semi:";",seswar:"⤩",setminus:"∖",setmn:"∖",sext:"✶",sfr:"\uD835\uDD30",sfrown:"⌢",sharp:"♯",shchcy:"щ",shcy:"ш",shortmid:"∣",shortparallel:"∥",shy:"\xad",sigma:"σ",sigmaf:"ς",sigmav:"ς",sim:"∼",simdot:"⩪",sime:"≃",simeq:"≃",simg:"⪞",simgE:"⪠",siml:"⪝",simlE:"⪟",simne:"≆",simplus:"⨤",simrarr:"⥲",slarr:"←",smallsetminus:"∖",smashp:"⨳",smeparsl:"⧤",smid:"∣",smile:"⌣",smt:"⪪",smte:"⪬",smtes:"⪬︀",softcy:"ь",sol:"/",solb:"⧄",solbar:"⌿",sopf:"\uD835\uDD64",spades:"♠",spadesuit:"♠",spar:"∥",sqcap:"⊓",sqcaps:"⊓︀",sqcup:"⊔",sqcups:"⊔︀",sqsub:"⊏",sqsube:"⊑",sqsubset:"⊏",sqsubseteq:"⊑",sqsup:"⊐",sqsupe:"⊒",sqsupset:"⊐",sqsupseteq:"⊒",squ:"□",square:"□",squarf:"▪",squf:"▪",srarr:"→",sscr:"\uD835\uDCC8",ssetmn:"∖",ssmile:"⌣",sstarf:"⋆",star:"☆",starf:"★",straightepsilon:"ϵ",straightphi:"ϕ",strns:"\xaf",sub:"⊂",subE:"⫅",subdot:"⪽",sube:"⊆",subedot:"⫃",submult:"⫁",subnE:"⫋",subne:"⊊",subplus:"⪿",subrarr:"⥹",subset:"⊂",subseteq:"⊆",subseteqq:"⫅",subsetneq:"⊊",subsetneqq:"⫋",subsim:"⫇",subsub:"⫕",subsup:"⫓",succ:"≻",succapprox:"⪸",succcurlyeq:"≽",succeq:"⪰",succnapprox:"⪺",succneqq:"⪶",succnsim:"⋩",succsim:"≿",sum:"∑",sung:"♪",sup1:"\xb9",sup2:"\xb2",sup3:"\xb3",sup:"⊃",supE:"⫆",supdot:"⪾",supdsub:"⫘",supe:"⊇",supedot:"⫄",suphsol:"⟉",suphsub:"⫗",suplarr:"⥻",supmult:"⫂",supnE:"⫌",supne:"⊋",supplus:"⫀",supset:"⊃",supseteq:"⊇",supseteqq:"⫆",supsetneq:"⊋",supsetneqq:"⫌",supsim:"⫈",supsub:"⫔",supsup:"⫖",swArr:"⇙",swarhk:"⤦",swarr:"↙",swarrow:"↙",swnwar:"⤪",szlig:"\xdf",target:"⌖",tau:"τ",tbrk:"⎴",tcaron:"ť",tcedil:"ţ",tcy:"т",tdot:"⃛",telrec:"⌕",tfr:"\uD835\uDD31",there4:"∴",therefore:"∴",theta:"θ",thetasym:"ϑ",thetav:"ϑ",thickapprox:"≈",thicksim:"∼",thinsp:" ",thkap:"≈",thksim:"∼",thorn:"\xfe",tilde:"˜",times:"\xd7",timesb:"⊠",timesbar:"⨱",timesd:"⨰",tint:"∭",toea:"⤨",top:"⊤",topbot:"⌶",topcir:"⫱",topf:"\uD835\uDD65",topfork:"⫚",tosa:"⤩",tprime:"‴",trade:"™",triangle:"▵",triangledown:"▿",triangleleft:"◃",trianglelefteq:"⊴",triangleq:"≜",triangleright:"▹",trianglerighteq:"⊵",tridot:"◬",trie:"≜",triminus:"⨺",triplus:"⨹",trisb:"⧍",tritime:"⨻",trpezium:"⏢",tscr:"\uD835\uDCC9",tscy:"ц",tshcy:"ћ",tstrok:"ŧ",twixt:"≬",twoheadleftarrow:"↞",twoheadrightarrow:"↠",uArr:"⇑",uHar:"⥣",uacute:"\xfa",uarr:"↑",ubrcy:"ў",ubreve:"ŭ",ucirc:"\xfb",ucy:"у",udarr:"⇅",udblac:"ű",udhar:"⥮",ufisht:"⥾",ufr:"\uD835\uDD32",ugrave:"\xf9",uharl:"↿",uharr:"↾",uhblk:"▀",ulcorn:"⌜",ulcorner:"⌜",ulcrop:"⌏",ultri:"◸",umacr:"ū",uml:"\xa8",uogon:"ų",uopf:"\uD835\uDD66",uparrow:"↑",updownarrow:"↕",upharpoonleft:"↿",upharpoonright:"↾",uplus:"⊎",upsi:"υ",upsih:"ϒ",upsilon:"υ",upuparrows:"⇈",urcorn:"⌝",urcorner:"⌝",urcrop:"⌎",uring:"ů",urtri:"◹",uscr:"\uD835\uDCCA",utdot:"⋰",utilde:"ũ",utri:"▵",utrif:"▴",uuarr:"⇈",uuml:"\xfc",uwangle:"⦧",vArr:"⇕",vBar:"⫨",vBarv:"⫩",vDash:"⊨",vangrt:"⦜",varepsilon:"ϵ",varkappa:"ϰ",varnothing:"∅",varphi:"ϕ",varpi:"ϖ",varpropto:"∝",varr:"↕",varrho:"ϱ",varsigma:"ς",varsubsetneq:"⊊︀",varsubsetneqq:"⫋︀",varsupsetneq:"⊋︀",varsupsetneqq:"⫌︀",vartheta:"ϑ",vartriangleleft:"⊲",vartriangleright:"⊳",vcy:"в",vdash:"⊢",vee:"∨",veebar:"⊻",veeeq:"≚",vellip:"⋮",verbar:"|",vert:"|",vfr:"\uD835\uDD33",vltri:"⊲",vnsub:"⊂⃒",vnsup:"⊃⃒",vopf:"\uD835\uDD67",vprop:"∝",vrtri:"⊳",vscr:"\uD835\uDCCB",vsubnE:"⫋︀",vsubne:"⊊︀",vsupnE:"⫌︀",vsupne:"⊋︀",vzigzag:"⦚",wcirc:"ŵ",wedbar:"⩟",wedge:"∧",wedgeq:"≙",weierp:"℘",wfr:"\uD835\uDD34",wopf:"\uD835\uDD68",wp:"℘",wr:"≀",wreath:"≀",wscr:"\uD835\uDCCC",xcap:"⋂",xcirc:"◯",xcup:"⋃",xdtri:"▽",xfr:"\uD835\uDD35",xhArr:"⟺",xharr:"⟷",xi:"ξ",xlArr:"⟸",xlarr:"⟵",xmap:"⟼",xnis:"⋻",xodot:"⨀",xopf:"\uD835\uDD69",xoplus:"⨁",xotime:"⨂",xrArr:"⟹",xrarr:"⟶",xscr:"\uD835\uDCCD",xsqcup:"⨆",xuplus:"⨄",xutri:"△",xvee:"⋁",xwedge:"⋀",yacute:"\xfd",yacy:"я",ycirc:"ŷ",ycy:"ы",yen:"\xa5",yfr:"\uD835\uDD36",yicy:"ї",yopf:"\uD835\uDD6A",yscr:"\uD835\uDCCE",yucy:"ю",yuml:"\xff",zacute:"ź",zcaron:"ž",zcy:"з",zdot:"ż",zeetrf:"ℨ",zeta:"ζ",zfr:"\uD835\uDD37",zhcy:"ж",zigrarr:"⇝",zopf:"\uD835\uDD6B",zscr:"\uD835\uDCCF",zwj:"‍",zwnj:"‌"},tP={}.hasOwnProperty;function tM(e){return!!tP.call(tv,e)&&tv[e]}let tw={name:"characterReference",tokenize:function(e,t,n){let r,a;let i=this,s=0;return function(t){return e.enter("characterReference"),e.enter("characterReferenceMarker"),e.consume(t),e.exit("characterReferenceMarker"),o};function o(t){return 35===t?(e.enter("characterReferenceMarkerNumeric"),e.consume(t),e.exit("characterReferenceMarkerNumeric"),l):(e.enter("characterReferenceValue"),r=31,a=e$,c(t))}function l(t){return 88===t||120===t?(e.enter("characterReferenceMarkerHexadecimal"),e.consume(t),e.exit("characterReferenceMarkerHexadecimal"),e.enter("characterReferenceValue"),r=6,a=e1,c):(e.enter("characterReferenceValue"),r=7,a=e0,c(t))}function c(o){if(59===o&&s){let r=e.exit("characterReferenceValue");return a!==e$||tM(i.sliceSerialize(r))?(e.enter("characterReferenceMarker"),e.consume(o),e.exit("characterReferenceMarker"),e.exit("characterReference"),t):n(o)}return a(o)&&s++<r?(e.consume(o),c):n(o)}}},tB={name:"characterEscape",tokenize:function(e,t,n){return function(t){return e.enter("characterEscape"),e.enter("escapeMarker"),e.consume(t),e.exit("escapeMarker"),r};function r(r){return e2(r)?(e.enter("characterEscapeValue"),e.consume(r),e.exit("characterEscapeValue"),e.exit("characterEscape"),t):n(r)}}},tF={name:"lineEnding",tokenize:function(e,t){return function(n){return e.enter("lineEnding"),e.consume(n),e.exit("lineEnding"),e7(e,t,"linePrefix")}}};function tU(e,t,n){let r=[],a=-1;for(;++a<e.length;){let i=e[a].resolveAll;i&&!r.includes(i)&&(t=i(t,n),r.push(i))}return t}let tH={name:"labelEnd",resolveAll:function(e){let t=-1,n=[];for(;++t<e.length;){let r=e[t][1];if(n.push(e[t]),"labelImage"===r.type||"labelLink"===r.type||"labelEnd"===r.type){let e="labelImage"===r.type?4:2;r.type="data",t+=e}}return e.length!==n.length&&ez(e,0,e.length,n),e},resolveTo:function(e,t){let n,r,a,i,s=e.length,o=0;for(;s--;)if(n=e[s][1],r){if("link"===n.type||"labelLink"===n.type&&n._inactive)break;"enter"===e[s][0]&&"labelLink"===n.type&&(n._inactive=!0)}else if(a){if("enter"===e[s][0]&&("labelImage"===n.type||"labelLink"===n.type)&&!n._balanced&&(r=s,"labelLink"!==n.type)){o=2;break}}else"labelEnd"===n.type&&(a=s);let l={type:"labelLink"===e[r][1].type?"link":"image",start:{...e[r][1].start},end:{...e[e.length-1][1].end}},c={type:"label",start:{...e[r][1].start},end:{...e[a][1].end}},u={type:"labelText",start:{...e[r+o+2][1].end},end:{...e[a-2][1].start}};return i=ej(i=[["enter",l,t],["enter",c,t]],e.slice(r+1,r+o+3)),i=ej(i,[["enter",u,t]]),i=ej(i,tU(t.parser.constructs.insideSpan.null,e.slice(r+o+4,a-3),t)),i=ej(i,[["exit",u,t],e[a-2],e[a-1],["exit",c,t]]),i=ej(i,e.slice(a+1)),i=ej(i,[["exit",l,t]]),ez(e,r,e.length,i),e},tokenize:function(e,t,n){let r,a;let i=this,s=i.events.length;for(;s--;)if(("labelImage"===i.events[s][1].type||"labelLink"===i.events[s][1].type)&&!i.events[s][1]._balanced){r=i.events[s][1];break}return function(t){return r?r._inactive?u(t):(a=i.parser.defined.includes(tS(i.sliceSerialize({start:r.end,end:i.now()}))),e.enter("labelEnd"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelEnd"),o):n(t)};function o(t){return 40===t?e.attempt(tG,c,a?c:u)(t):91===t?e.attempt(tq,c,a?l:u)(t):a?c(t):u(t)}function l(t){return e.attempt(tz,c,u)(t)}function c(e){return t(e)}function u(e){return r._balanced=!0,n(e)}}},tG={tokenize:function(e,t,n){return function(t){return e.enter("resource"),e.enter("resourceMarker"),e.consume(t),e.exit("resourceMarker"),r};function r(t){return e5(t)?tk(e,a)(t):a(t)}function a(t){return 41===t?c(t):tg(e,i,s,"resourceDestination","resourceDestinationLiteral","resourceDestinationLiteralMarker","resourceDestinationRaw","resourceDestinationString",32)(t)}function i(t){return e5(t)?tk(e,o)(t):c(t)}function s(e){return n(e)}function o(t){return 34===t||39===t||40===t?t_(e,l,n,"resourceTitle","resourceTitleMarker","resourceTitleString")(t):c(t)}function l(t){return e5(t)?tk(e,c)(t):c(t)}function c(r){return 41===r?(e.enter("resourceMarker"),e.consume(r),e.exit("resourceMarker"),e.exit("resource"),t):n(r)}}},tq={tokenize:function(e,t,n){let r=this;return function(t){return tA.call(r,e,a,i,"reference","referenceMarker","referenceString")(t)};function a(e){return r.parser.defined.includes(tS(r.sliceSerialize(r.events[r.events.length-1][1]).slice(1,-1)))?t(e):n(e)}function i(e){return n(e)}}},tz={tokenize:function(e,t,n){return function(t){return e.enter("reference"),e.enter("referenceMarker"),e.consume(t),e.exit("referenceMarker"),r};function r(r){return 93===r?(e.enter("referenceMarker"),e.consume(r),e.exit("referenceMarker"),e.exit("reference"),t):n(r)}}},tj={name:"labelStartImage",resolveAll:tH.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelImage"),e.enter("labelImageMarker"),e.consume(t),e.exit("labelImageMarker"),a};function a(t){return 91===t?(e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelImage"),i):n(t)}function i(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}};function tY(e){return null===e||e5(e)||e8(e)?1:e6(e)?2:void 0}let tV={name:"attention",resolveAll:function(e,t){let n,r,a,i,s,o,l,c,u=-1;for(;++u<e.length;)if("enter"===e[u][0]&&"attentionSequence"===e[u][1].type&&e[u][1]._close){for(n=u;n--;)if("exit"===e[n][0]&&"attentionSequence"===e[n][1].type&&e[n][1]._open&&t.sliceSerialize(e[n][1]).charCodeAt(0)===t.sliceSerialize(e[u][1]).charCodeAt(0)){if((e[n][1]._close||e[u][1]._open)&&(e[u][1].end.offset-e[u][1].start.offset)%3&&!((e[n][1].end.offset-e[n][1].start.offset+e[u][1].end.offset-e[u][1].start.offset)%3))continue;o=e[n][1].end.offset-e[n][1].start.offset>1&&e[u][1].end.offset-e[u][1].start.offset>1?2:1;let h={...e[n][1].end},d={...e[u][1].start};tW(h,-o),tW(d,o),i={type:o>1?"strongSequence":"emphasisSequence",start:h,end:{...e[n][1].end}},s={type:o>1?"strongSequence":"emphasisSequence",start:{...e[u][1].start},end:d},a={type:o>1?"strongText":"emphasisText",start:{...e[n][1].end},end:{...e[u][1].start}},r={type:o>1?"strong":"emphasis",start:{...i.start},end:{...s.end}},e[n][1].end={...i.start},e[u][1].start={...s.end},l=[],e[n][1].end.offset-e[n][1].start.offset&&(l=ej(l,[["enter",e[n][1],t],["exit",e[n][1],t]])),l=ej(l,[["enter",r,t],["enter",i,t],["exit",i,t],["enter",a,t]]),l=ej(l,tU(t.parser.constructs.insideSpan.null,e.slice(n+1,u),t)),l=ej(l,[["exit",a,t],["enter",s,t],["exit",s,t],["exit",r,t]]),e[u][1].end.offset-e[u][1].start.offset?(c=2,l=ej(l,[["enter",e[u][1],t],["exit",e[u][1],t]])):c=0,ez(e,n-1,u-n+3,l),u=n+l.length-c-2;break}}for(u=-1;++u<e.length;)"attentionSequence"===e[u][1].type&&(e[u][1].type="data");return e},tokenize:function(e,t){let n;let r=this.parser.constructs.attentionMarkers.null,a=this.previous,i=tY(a);return function(s){return n=s,e.enter("attentionSequence"),function s(o){if(o===n)return e.consume(o),s;let l=e.exit("attentionSequence"),c=tY(o),u=!c||2===c&&i||r.includes(o),h=!i||2===i&&c||r.includes(a);return l._open=!!(42===n?u:u&&(i||!h)),l._close=!!(42===n?h:h&&(c||!u)),t(o)}(s)}}};function tW(e,t){e.column+=t,e.offset+=t,e._bufferIndex+=t}let tQ={name:"labelStartLink",resolveAll:tH.resolveAll,tokenize:function(e,t,n){let r=this;return function(t){return e.enter("labelLink"),e.enter("labelMarker"),e.consume(t),e.exit("labelMarker"),e.exit("labelLink"),a};function a(e){return 94===e&&"_hiddenFootnoteSupport"in r.parser.constructs?n(e):t(e)}}},tK={42:tm,43:tm,45:tm,48:tm,49:tm,50:tm,51:tm,52:tm,53:tm,54:tm,55:tm,56:tm,57:tm,62:tT},tX={91:{name:"definition",tokenize:function(e,t,n){let r;let a=this;return function(t){return e.enter("definition"),tA.call(a,e,i,n,"definitionLabel","definitionLabelMarker","definitionLabelString")(t)};function i(t){return(r=tS(a.sliceSerialize(a.events[a.events.length-1][1]).slice(1,-1)),58===t)?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),s):n(t)}function s(t){return e5(t)?tk(e,o)(t):o(t)}function o(t){return tg(e,l,n,"definitionDestination","definitionDestinationLiteral","definitionDestinationLiteralMarker","definitionDestinationRaw","definitionDestinationString")(t)}function l(t){return e.attempt(tb,c,c)(t)}function c(t){return e4(t)?e7(e,u,"whitespace")(t):u(t)}function u(i){return null===i||e3(i)?(e.exit("definition"),a.parser.defined.push(r),t(i)):n(i)}}}},t$={[-2]:ty,[-1]:ty,32:ty},tZ={35:{name:"headingAtx",resolve:function(e,t){let n,r,a=e.length-2,i=3;return"whitespace"===e[3][1].type&&(i+=2),a-2>i&&"whitespace"===e[a][1].type&&(a-=2),"atxHeadingSequence"===e[a][1].type&&(i===a-1||a-4>i&&"whitespace"===e[a-2][1].type)&&(a-=i+1===a?2:4),a>i&&(n={type:"atxHeadingText",start:e[i][1].start,end:e[a][1].end},r={type:"chunkText",start:e[i][1].start,end:e[a][1].end,contentType:"text"},ez(e,i,a-i+1,[["enter",n,t],["enter",r,t],["exit",r,t],["exit",n,t]])),e},tokenize:function(e,t,n){let r=0;return function(a){return e.enter("atxHeading"),e.enter("atxHeadingSequence"),function a(i){return 35===i&&r++<6?(e.consume(i),a):null===i||e5(i)?(e.exit("atxHeadingSequence"),function n(r){return 35===r?(e.enter("atxHeadingSequence"),function t(r){return 35===r?(e.consume(r),t):(e.exit("atxHeadingSequence"),n(r))}(r)):null===r||e3(r)?(e.exit("atxHeading"),t(r)):e4(r)?e7(e,n,"whitespace")(r):(e.enter("atxHeadingText"),function t(r){return null===r||35===r||e5(r)?(e.exit("atxHeadingText"),n(r)):(e.consume(r),t)}(r))}(i)):n(i)}(a)}}},42:tp,45:[tC,tp],60:{concrete:!0,name:"htmlFlow",resolveTo:function(e){let t=e.length;for(;t--&&("enter"!==e[t][0]||"htmlFlow"!==e[t][1].type););return t>1&&"linePrefix"===e[t-2][1].type&&(e[t][1].start=e[t-2][1].start,e[t+1][1].start=e[t-2][1].start,e.splice(t-2,2)),e},tokenize:function(e,t,n){let r,a,i,s,o;let l=this;return function(t){return e.enter("htmlFlow"),e.enter("htmlFlowData"),e.consume(t),c};function c(s){return 33===s?(e.consume(s),u):47===s?(e.consume(s),a=!0,p):63===s?(e.consume(s),r=3,l.interrupt?t:R):eX(s)?(e.consume(s),i=String.fromCharCode(s),m):n(s)}function u(a){return 45===a?(e.consume(a),r=2,h):91===a?(e.consume(a),r=5,s=0,d):eX(a)?(e.consume(a),r=4,l.interrupt?t:R):n(a)}function h(r){return 45===r?(e.consume(r),l.interrupt?t:R):n(r)}function d(r){let a="CDATA[";return r===a.charCodeAt(s++)?(e.consume(r),s===a.length)?l.interrupt?t:y:d:n(r)}function p(t){return eX(t)?(e.consume(t),i=String.fromCharCode(t),m):n(t)}function m(s){if(null===s||47===s||62===s||e5(s)){let o=47===s,c=i.toLowerCase();return!o&&!a&&tD.includes(c)?(r=1,l.interrupt?t(s):y(s)):tI.includes(i.toLowerCase())?(r=6,o)?(e.consume(s),f):l.interrupt?t(s):y(s):(r=7,l.interrupt&&!l.parser.lazy[l.now().line]?n(s):a?function t(n){return e4(n)?(e.consume(n),t):S(n)}(s):E(s))}return 45===s||e$(s)?(e.consume(s),i+=String.fromCharCode(s),m):n(s)}function f(r){return 62===r?(e.consume(r),l.interrupt?t:y):n(r)}function E(t){return 47===t?(e.consume(t),S):58===t||95===t||eX(t)?(e.consume(t),T):e4(t)?(e.consume(t),E):S(t)}function T(t){return 45===t||46===t||58===t||95===t||e$(t)?(e.consume(t),T):g(t)}function g(t){return 61===t?(e.consume(t),A):e4(t)?(e.consume(t),g):E(t)}function A(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),o=t,_):e4(t)?(e.consume(t),A):function t(n){return null===n||34===n||39===n||47===n||60===n||61===n||62===n||96===n||e5(n)?g(n):(e.consume(n),t)}(t)}function _(t){return t===o?(e.consume(t),o=null,k):null===t||e3(t)?n(t):(e.consume(t),_)}function k(e){return 47===e||62===e||e4(e)?E(e):n(e)}function S(t){return 62===t?(e.consume(t),b):n(t)}function b(t){return null===t||e3(t)?y(t):e4(t)?(e.consume(t),b):n(t)}function y(t){return 45===t&&2===r?(e.consume(t),D):60===t&&1===r?(e.consume(t),O):62===t&&4===r?(e.consume(t),v):63===t&&3===r?(e.consume(t),R):93===t&&5===r?(e.consume(t),x):e3(t)&&(6===r||7===r)?(e.exit("htmlFlowData"),e.check(tO,P,N)(t)):null===t||e3(t)?(e.exit("htmlFlowData"),N(t)):(e.consume(t),y)}function N(t){return e.check(tL,C,P)(t)}function C(t){return e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),I}function I(t){return null===t||e3(t)?N(t):(e.enter("htmlFlowData"),y(t))}function D(t){return 45===t?(e.consume(t),R):y(t)}function O(t){return 47===t?(e.consume(t),i="",L):y(t)}function L(t){if(62===t){let n=i.toLowerCase();return tD.includes(n)?(e.consume(t),v):y(t)}return eX(t)&&i.length<8?(e.consume(t),i+=String.fromCharCode(t),L):y(t)}function x(t){return 93===t?(e.consume(t),R):y(t)}function R(t){return 62===t?(e.consume(t),v):45===t&&2===r?(e.consume(t),R):y(t)}function v(t){return null===t||e3(t)?(e.exit("htmlFlowData"),P(t)):(e.consume(t),v)}function P(n){return e.exit("htmlFlow"),t(n)}}},61:tC,95:tp,96:tR,126:tR},tJ={38:tw,92:tB},t0={[-5]:tF,[-4]:tF,[-3]:tF,33:tj,38:tw,42:tV,60:[{name:"autolink",tokenize:function(e,t,n){let r=0;return function(t){return e.enter("autolink"),e.enter("autolinkMarker"),e.consume(t),e.exit("autolinkMarker"),e.enter("autolinkProtocol"),a};function a(t){return eX(t)?(e.consume(t),i):64===t?n(t):o(t)}function i(t){return 43===t||45===t||46===t||e$(t)?(r=1,function t(n){return 58===n?(e.consume(n),r=0,s):(43===n||45===n||46===n||e$(n))&&r++<32?(e.consume(n),t):(r=0,o(n))}(t)):o(t)}function s(r){return 62===r?(e.exit("autolinkProtocol"),e.enter("autolinkMarker"),e.consume(r),e.exit("autolinkMarker"),e.exit("autolink"),t):null===r||32===r||60===r||eJ(r)?n(r):(e.consume(r),s)}function o(t){return 64===t?(e.consume(t),l):eZ(t)?(e.consume(t),o):n(t)}function l(a){return e$(a)?function a(i){return 46===i?(e.consume(i),r=0,l):62===i?(e.exit("autolinkProtocol").type="autolinkEmail",e.enter("autolinkMarker"),e.consume(i),e.exit("autolinkMarker"),e.exit("autolink"),t):function t(i){if((45===i||e$(i))&&r++<63){let n=45===i?t:a;return e.consume(i),n}return n(i)}(i)}(a):n(a)}}},{name:"htmlText",tokenize:function(e,t,n){let r,a,i;let s=this;return function(t){return e.enter("htmlText"),e.enter("htmlTextData"),e.consume(t),o};function o(t){return 33===t?(e.consume(t),l):47===t?(e.consume(t),_):63===t?(e.consume(t),g):eX(t)?(e.consume(t),S):n(t)}function l(t){return 45===t?(e.consume(t),c):91===t?(e.consume(t),a=0,p):eX(t)?(e.consume(t),T):n(t)}function c(t){return 45===t?(e.consume(t),d):n(t)}function u(t){return null===t?n(t):45===t?(e.consume(t),h):e3(t)?(i=u,L(t)):(e.consume(t),u)}function h(t){return 45===t?(e.consume(t),d):u(t)}function d(e){return 62===e?O(e):45===e?h(e):u(e)}function p(t){let r="CDATA[";return t===r.charCodeAt(a++)?(e.consume(t),a===r.length?m:p):n(t)}function m(t){return null===t?n(t):93===t?(e.consume(t),f):e3(t)?(i=m,L(t)):(e.consume(t),m)}function f(t){return 93===t?(e.consume(t),E):m(t)}function E(t){return 62===t?O(t):93===t?(e.consume(t),E):m(t)}function T(t){return null===t||62===t?O(t):e3(t)?(i=T,L(t)):(e.consume(t),T)}function g(t){return null===t?n(t):63===t?(e.consume(t),A):e3(t)?(i=g,L(t)):(e.consume(t),g)}function A(e){return 62===e?O(e):g(e)}function _(t){return eX(t)?(e.consume(t),k):n(t)}function k(t){return 45===t||e$(t)?(e.consume(t),k):function t(n){return e3(n)?(i=t,L(n)):e4(n)?(e.consume(n),t):O(n)}(t)}function S(t){return 45===t||e$(t)?(e.consume(t),S):47===t||62===t||e5(t)?b(t):n(t)}function b(t){return 47===t?(e.consume(t),O):58===t||95===t||eX(t)?(e.consume(t),y):e3(t)?(i=b,L(t)):e4(t)?(e.consume(t),b):O(t)}function y(t){return 45===t||46===t||58===t||95===t||e$(t)?(e.consume(t),y):function t(n){return 61===n?(e.consume(n),N):e3(n)?(i=t,L(n)):e4(n)?(e.consume(n),t):b(n)}(t)}function N(t){return null===t||60===t||61===t||62===t||96===t?n(t):34===t||39===t?(e.consume(t),r=t,C):e3(t)?(i=N,L(t)):e4(t)?(e.consume(t),N):(e.consume(t),I)}function C(t){return t===r?(e.consume(t),r=void 0,D):null===t?n(t):e3(t)?(i=C,L(t)):(e.consume(t),C)}function I(t){return null===t||34===t||39===t||60===t||61===t||96===t?n(t):47===t||62===t||e5(t)?b(t):(e.consume(t),I)}function D(e){return 47===e||62===e||e5(e)?b(e):n(e)}function O(r){return 62===r?(e.consume(r),e.exit("htmlTextData"),e.exit("htmlText"),t):n(r)}function L(t){return e.exit("htmlTextData"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),x}function x(t){return e4(t)?e7(e,R,"linePrefix",s.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):R(t)}function R(t){return e.enter("htmlTextData"),i(t)}}}],91:tQ,92:[{name:"hardBreakEscape",tokenize:function(e,t,n){return function(t){return e.enter("hardBreakEscape"),e.consume(t),r};function r(r){return e3(r)?(e.exit("hardBreakEscape"),t(r)):n(r)}}},tB],93:tH,95:tV,96:{name:"codeText",previous:function(e){return 96!==e||"characterEscape"===this.events[this.events.length-1][1].type},resolve:function(e){let t,n,r=e.length-4,a=3;if(("lineEnding"===e[3][1].type||"space"===e[a][1].type)&&("lineEnding"===e[r][1].type||"space"===e[r][1].type)){for(t=a;++t<r;)if("codeTextData"===e[t][1].type){e[a][1].type="codeTextPadding",e[r][1].type="codeTextPadding",a+=2,r-=2;break}}for(t=a-1,r++;++t<=r;)void 0===n?t!==r&&"lineEnding"!==e[t][1].type&&(n=t):(t===r||"lineEnding"===e[t][1].type)&&(e[n][1].type="codeTextData",t!==n+2&&(e[n][1].end=e[t-1][1].end,e.splice(n+2,t-n-2),r-=t-n-2,t=n+2),n=void 0);return e},tokenize:function(e,t,n){let r,a,i=0;return function(t){return e.enter("codeText"),e.enter("codeTextSequence"),function t(n){return 96===n?(e.consume(n),i++,t):(e.exit("codeTextSequence"),s(n))}(t)};function s(l){return null===l?n(l):32===l?(e.enter("space"),e.consume(l),e.exit("space"),s):96===l?(a=e.enter("codeTextSequence"),r=0,function n(s){return 96===s?(e.consume(s),r++,n):r===i?(e.exit("codeTextSequence"),e.exit("codeText"),t(s)):(a.type="codeTextData",o(s))}(l)):e3(l)?(e.enter("lineEnding"),e.consume(l),e.exit("lineEnding"),s):(e.enter("codeTextData"),o(l))}function o(t){return null===t||32===t||96===t||e3(t)?(e.exit("codeTextData"),s(t)):(e.consume(t),o)}}}},t1={null:[tV,to]},t2={null:[42,95]},t3={null:[]},t5=/[\0\t\n\r]/g;function t4(e,t){let n=Number.parseInt(e,t);return n<9||11===n||n>13&&n<32||n>126&&n<160||n>55295&&n<57344||n>64975&&n<65008||(65535&n)==65535||(65535&n)==65534||n>1114111?"�":String.fromCodePoint(n)}let t6=/\\([!-/:-@[-`{-~])|&(#(?:\d{1,7}|x[\da-f]{1,6})|[\da-z]{1,31});/gi;function t8(e,t,n){if(t)return t;if(35===n.charCodeAt(0)){let e=n.charCodeAt(1),t=120===e||88===e;return t4(n.slice(t?2:1),t?16:10)}return tM(n)||e}let t9={}.hasOwnProperty;function t7(e){return{line:e.line,column:e.column,offset:e.offset}}function ne(e,t){if(e)throw Error("Cannot close `"+e.type+"` ("+e_({start:e.start,end:e.end})+"): a different token (`"+t.type+"`, "+e_({start:t.start,end:t.end})+") is open");throw Error("Cannot close document, a token (`"+t.type+"`, "+e_({start:t.start,end:t.end})+") is still open")}function nt(e){let t=this;t.parser=function(n){var r,a;let i,s,o,l;return"string"!=typeof(r={...t.data("settings"),...e,extensions:t.data("micromarkExtensions")||[],mdastExtensions:t.data("fromMarkdownExtensions")||[]})&&(a=r,r=void 0),(function(e){let t={transforms:[],canContainEols:["emphasis","fragment","heading","paragraph","strong"],enter:{autolink:r(T),autolinkProtocol:c,autolinkEmail:c,atxHeading:r(m),blockQuote:r(function(){return{type:"blockquote",children:[]}}),characterEscape:c,characterReference:c,codeFenced:r(p),codeFencedFenceInfo:a,codeFencedFenceMeta:a,codeIndented:r(p,a),codeText:r(function(){return{type:"inlineCode",value:""}},a),codeTextData:c,data:c,codeFlowValue:c,definition:r(function(){return{type:"definition",identifier:"",label:null,title:null,url:""}}),definitionDestinationString:a,definitionLabelString:a,definitionTitleString:a,emphasis:r(function(){return{type:"emphasis",children:[]}}),hardBreakEscape:r(f),hardBreakTrailing:r(f),htmlFlow:r(E,a),htmlFlowData:c,htmlText:r(E,a),htmlTextData:c,image:r(function(){return{type:"image",title:null,url:"",alt:null}}),label:a,link:r(T),listItem:r(function(e){return{type:"listItem",spread:e._spread,checked:null,children:[]}}),listItemValue:function(e){this.data.expectingFirstListItemValue&&(this.stack[this.stack.length-2].start=Number.parseInt(this.sliceSerialize(e),10),this.data.expectingFirstListItemValue=void 0)},listOrdered:r(g,function(){this.data.expectingFirstListItemValue=!0}),listUnordered:r(g),paragraph:r(function(){return{type:"paragraph",children:[]}}),reference:function(){this.data.referenceType="collapsed"},referenceString:a,resourceDestinationString:a,resourceTitleString:a,setextHeading:r(m),strong:r(function(){return{type:"strong",children:[]}}),thematicBreak:r(function(){return{type:"thematicBreak"}})},exit:{atxHeading:s(),atxHeadingSequence:function(e){let t=this.stack[this.stack.length-1];if(!t.depth){let n=this.sliceSerialize(e).length;t.depth=n}},autolink:s(),autolinkEmail:function(e){u.call(this,e),this.stack[this.stack.length-1].url="mailto:"+this.sliceSerialize(e)},autolinkProtocol:function(e){u.call(this,e),this.stack[this.stack.length-1].url=this.sliceSerialize(e)},blockQuote:s(),characterEscapeValue:u,characterReferenceMarkerHexadecimal:d,characterReferenceMarkerNumeric:d,characterReferenceValue:function(e){let t;let n=this.sliceSerialize(e),r=this.data.characterReferenceType;r?(t=t4(n,"characterReferenceMarkerNumeric"===r?10:16),this.data.characterReferenceType=void 0):t=tM(n);let a=this.stack[this.stack.length-1];a.value+=t},characterReference:function(e){this.stack.pop().position.end=t7(e.end)},codeFenced:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/^(\r?\n|\r)|(\r?\n|\r)$/g,""),this.data.flowCodeInside=void 0}),codeFencedFence:function(){this.data.flowCodeInside||(this.buffer(),this.data.flowCodeInside=!0)},codeFencedFenceInfo:function(){let e=this.resume();this.stack[this.stack.length-1].lang=e},codeFencedFenceMeta:function(){let e=this.resume();this.stack[this.stack.length-1].meta=e},codeFlowValue:u,codeIndented:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e.replace(/(\r?\n|\r)$/g,"")}),codeText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),codeTextData:u,data:u,definition:s(),definitionDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},definitionLabelString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=tS(this.sliceSerialize(e)).toLowerCase()},definitionTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},emphasis:s(),hardBreakEscape:s(h),hardBreakTrailing:s(h),htmlFlow:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlFlowData:u,htmlText:s(function(){let e=this.resume();this.stack[this.stack.length-1].value=e}),htmlTextData:u,image:s(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),label:function(){let e=this.stack[this.stack.length-1],t=this.resume(),n=this.stack[this.stack.length-1];if(this.data.inReference=!0,"link"===n.type){let t=e.children;n.children=t}else n.alt=t},labelText:function(e){let t=this.sliceSerialize(e),n=this.stack[this.stack.length-2];n.label=t.replace(t6,t8),n.identifier=tS(t).toLowerCase()},lineEnding:function(e){let n=this.stack[this.stack.length-1];if(this.data.atHardBreak){n.children[n.children.length-1].position.end=t7(e.end),this.data.atHardBreak=void 0;return}!this.data.setextHeadingSlurpLineEnding&&t.canContainEols.includes(n.type)&&(c.call(this,e),u.call(this,e))},link:s(function(){let e=this.stack[this.stack.length-1];if(this.data.inReference){let t=this.data.referenceType||"shortcut";e.type+="Reference",e.referenceType=t,delete e.url,delete e.title}else delete e.identifier,delete e.label;this.data.referenceType=void 0}),listItem:s(),listOrdered:s(),listUnordered:s(),paragraph:s(),referenceString:function(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.label=t,n.identifier=tS(this.sliceSerialize(e)).toLowerCase(),this.data.referenceType="full"},resourceDestinationString:function(){let e=this.resume();this.stack[this.stack.length-1].url=e},resourceTitleString:function(){let e=this.resume();this.stack[this.stack.length-1].title=e},resource:function(){this.data.inReference=void 0},setextHeading:s(function(){this.data.setextHeadingSlurpLineEnding=void 0}),setextHeadingLineSequence:function(e){this.stack[this.stack.length-1].depth=61===this.sliceSerialize(e).codePointAt(0)?1:2},setextHeadingText:function(){this.data.setextHeadingSlurpLineEnding=!0},strong:s(),thematicBreak:s()}};(function e(t,n){let r=-1;for(;++r<n.length;){let a=n[r];Array.isArray(a)?e(t,a):function(e,t){let n;for(n in t)if(t9.call(t,n))switch(n){case"canContainEols":{let r=t[n];r&&e[n].push(...r);break}case"transforms":{let r=t[n];r&&e[n].push(...r);break}case"enter":case"exit":{let r=t[n];r&&Object.assign(e[n],r)}}}(t,a)}})(t,(e||{}).mdastExtensions||[]);let n={};return function(e){let r={type:"root",children:[]},s={stack:[r],tokenStack:[],config:t,enter:i,exit:o,buffer:a,resume:l,data:n},c=[],u=-1;for(;++u<e.length;)("listOrdered"===e[u][1].type||"listUnordered"===e[u][1].type)&&("enter"===e[u][0]?c.push(u):u=function(e,t,n){let r,a,i,s,o=t-1,l=-1,c=!1;for(;++o<=n;){let t=e[o];switch(t[1].type){case"listUnordered":case"listOrdered":case"blockQuote":"enter"===t[0]?l++:l--,s=void 0;break;case"lineEndingBlank":"enter"===t[0]&&(!r||s||l||i||(i=o),s=void 0);break;case"linePrefix":case"listItemValue":case"listItemMarker":case"listItemPrefix":case"listItemPrefixWhitespace":break;default:s=void 0}if(!l&&"enter"===t[0]&&"listItemPrefix"===t[1].type||-1===l&&"exit"===t[0]&&("listUnordered"===t[1].type||"listOrdered"===t[1].type)){if(r){let s=o;for(a=void 0;s--;){let t=e[s];if("lineEnding"===t[1].type||"lineEndingBlank"===t[1].type){if("exit"===t[0])continue;a&&(e[a][1].type="lineEndingBlank",c=!0),t[1].type="lineEnding",a=s}else if("linePrefix"===t[1].type||"blockQuotePrefix"===t[1].type||"blockQuotePrefixWhitespace"===t[1].type||"blockQuoteMarker"===t[1].type||"listItemIndent"===t[1].type);else break}i&&(!a||i<a)&&(r._spread=!0),r.end=Object.assign({},a?e[a][1].start:t[1].end),e.splice(a||o,0,["exit",r,t[2]]),o++,n++}if("listItemPrefix"===t[1].type){let a={type:"listItem",_spread:!1,start:Object.assign({},t[1].start),end:void 0};r=a,e.splice(o,0,["enter",a,t[2]]),o++,n++,i=void 0,s=!0}}}return e[t][1]._spread=c,n}(e,c.pop(),u));for(u=-1;++u<e.length;){let n=t[e[u][0]];t9.call(n,e[u][1].type)&&n[e[u][1].type].call(Object.assign({sliceSerialize:e[u][2].sliceSerialize},s),e[u][1])}if(s.tokenStack.length>0){let e=s.tokenStack[s.tokenStack.length-1];(e[1]||ne).call(s,void 0,e[0])}for(r.position={start:t7(e.length>0?e[0][1].start:{line:1,column:1,offset:0}),end:t7(e.length>0?e[e.length-2][1].end:{line:1,column:1,offset:0})},u=-1;++u<t.transforms.length;)r=t.transforms[u](r)||r;return r};function r(e,t){return function(n){i.call(this,e(n),n),t&&t.call(this,n)}}function a(){this.stack.push({type:"fragment",children:[]})}function i(e,t,n){this.stack[this.stack.length-1].children.push(e),this.stack.push(e),this.tokenStack.push([t,n||void 0]),e.position={start:t7(t.start),end:void 0}}function s(e){return function(t){e&&e.call(this,t),o.call(this,t)}}function o(e,t){let n=this.stack.pop(),r=this.tokenStack.pop();if(r)r[0].type!==e.type&&(t?t.call(this,e,r[0]):(r[1]||ne).call(this,e,r[0]));else throw Error("Cannot close `"+e.type+"` ("+e_({start:e.start,end:e.end})+"): it’s not open");n.position.end=t7(e.end)}function l(){return eH(this.stack.pop())}function c(e){let t=this.stack[this.stack.length-1].children,n=t[t.length-1];n&&"text"===n.type||((n={type:"text",value:""}).position={start:t7(e.start),end:void 0},t.push(n)),this.stack.push(n)}function u(e){let t=this.stack.pop();t.value+=this.sliceSerialize(e),t.position.end=t7(e.end)}function h(){this.data.atHardBreak=!0}function d(e){this.data.characterReferenceType=e.type}function p(){return{type:"code",lang:null,meta:null,value:""}}function m(){return{type:"heading",depth:0,children:[]}}function f(){return{type:"break"}}function E(){return{type:"html",value:""}}function T(){return{type:"link",title:null,url:"",children:[]}}function g(e){return{type:"list",ordered:"listOrdered"===e.type,start:null,spread:e._spread,children:[]}}})(a)(function(e){for(;!eW(e););return e}((function(e){let t={constructs:eK([_,...(e||{}).extensions||[]]),content:n(te),defined:[],document:n(tt),flow:n(ts),lazy:{},string:n(tl),text:n(tc)};return t;function n(e){return function(n){return function(e,t,n){let r={_bufferIndex:-1,_index:0,line:n&&n.line||1,column:n&&n.column||1,offset:n&&n.offset||0},a={},i=[],s=[],o=[],l={attempt:m(function(e,t){f(e,t.from)}),check:m(p),consume:function(e){e3(e)?(r.line++,r.column=1,r.offset+=-3===e?2:1,E()):-1!==e&&(r.column++,r.offset++),r._bufferIndex<0?r._index++:(r._bufferIndex++,r._bufferIndex===s[r._index].length&&(r._bufferIndex=-1,r._index++)),c.previous=e},enter:function(e,t){let n=t||{};return n.type=e,n.start=d(),c.events.push(["enter",n,c]),o.push(n),n},exit:function(e){let t=o.pop();return t.end=d(),c.events.push(["exit",t,c]),t},interrupt:m(p,{interrupt:!0})},c={code:null,containerState:{},defineSkip:function(e){a[e.line]=e.column,E()},events:[],now:d,parser:e,previous:null,sliceSerialize:function(e,t){return function(e,t){let n,r=-1,a=[];for(;++r<e.length;){let i;let s=e[r];if("string"==typeof s)i=s;else switch(s){case -5:i="\r";break;case -4:i="\n";break;case -3:i="\r\n";break;case -2:i=t?" ":"	";break;case -1:if(!t&&n)continue;i=" ";break;default:i=String.fromCharCode(s)}n=-2===s,a.push(i)}return a.join("")}(h(e),t)},sliceStream:h,write:function(e){return(s=ej(s,e),function(){let e;for(;r._index<s.length;){let n=s[r._index];if("string"==typeof n)for(e=r._index,r._bufferIndex<0&&(r._bufferIndex=0);r._index===e&&r._bufferIndex<n.length;){var t;t=n.charCodeAt(r._bufferIndex),u=u(t)}else u=u(n)}}(),null!==s[s.length-1])?[]:(f(t,0),c.events=tU(i,c.events,c),c.events)}},u=t.tokenize.call(c,l);return t.resolveAll&&i.push(t),c;function h(e){return function(e,t){let n;let r=t.start._index,a=t.start._bufferIndex,i=t.end._index,s=t.end._bufferIndex;if(r===i)n=[e[r].slice(a,s)];else{if(n=e.slice(r,i),a>-1){let e=n[0];"string"==typeof e?n[0]=e.slice(a):n.shift()}s>0&&n.push(e[i].slice(0,s))}return n}(s,e)}function d(){let{_bufferIndex:e,_index:t,line:n,column:a,offset:i}=r;return{_bufferIndex:e,_index:t,line:n,column:a,offset:i}}function p(e,t){t.restore()}function m(e,t){return function(n,a,i){let s,u,h,p;return Array.isArray(n)?m(n):"tokenize"in n?m([n]):function(e){let t=null!==e&&n[e],r=null!==e&&n.null;return m([...Array.isArray(t)?t:t?[t]:[],...Array.isArray(r)?r:r?[r]:[]])(e)};function m(e){return(s=e,u=0,0===e.length)?i:f(e[u])}function f(e){return function(n){return(p=function(){let e=d(),t=c.previous,n=c.currentConstruct,a=c.events.length,i=Array.from(o);return{from:a,restore:function(){r=e,c.previous=t,c.currentConstruct=n,c.events.length=a,o=i,E()}}}(),h=e,e.partial||(c.currentConstruct=e),e.name&&c.parser.constructs.disable.null.includes(e.name))?g(n):e.tokenize.call(t?Object.assign(Object.create(c),t):c,l,T,g)(n)}}function T(t){return e(h,p),a}function g(e){return(p.restore(),++u<s.length)?f(s[u]):i}}}function f(e,t){e.resolveAll&&!i.includes(e)&&i.push(e),e.resolve&&ez(c.events,t,c.events.length-t,e.resolve(c.events.slice(t),c)),e.resolveTo&&(c.events=e.resolveTo(c.events,c))}function E(){r.line in a&&r.column<2&&(r.column=a[r.line],r.offset+=a[r.line]-1)}}(t,e,n)}}})(a).document().write((s=1,o="",l=!0,function(e,t,n){let r,a,c,u,h;let d=[];for(e=o+("string"==typeof e?e.toString():new TextDecoder(t||void 0).decode(e)),c=0,o="",l&&(65279===e.charCodeAt(0)&&c++,l=void 0);c<e.length;){if(t5.lastIndex=c,u=(r=t5.exec(e))&&void 0!==r.index?r.index:e.length,h=e.charCodeAt(u),!r){o=e.slice(c);break}if(10===h&&c===u&&i)d.push(-3),i=void 0;else switch(i&&(d.push(-5),i=void 0),c<u&&(d.push(e.slice(c,u)),s+=u-c),h){case 0:d.push(65533),s++;break;case 9:for(a=4*Math.ceil(s/4),d.push(-2);s++<a;)d.push(-1);break;case 10:d.push(-4),s=1;break;default:i=!0,s=1}c=u+1}return n&&(i&&d.push(-5),o&&d.push(o),d.push(null)),d})(n,r,!0))))}}let nn="object"==typeof self?self:globalThis,nr=(e,t)=>{let n=(t,n)=>(e.set(n,t),t),r=a=>{if(e.has(a))return e.get(a);let[i,s]=t[a];switch(i){case 0:case -1:return n(s,a);case 1:{let e=n([],a);for(let t of s)e.push(r(t));return e}case 2:{let e=n({},a);for(let[t,n]of s)e[r(t)]=r(n);return e}case 3:return n(new Date(s),a);case 4:{let{source:e,flags:t}=s;return n(new RegExp(e,t),a)}case 5:{let e=n(new Map,a);for(let[t,n]of s)e.set(r(t),r(n));return e}case 6:{let e=n(new Set,a);for(let t of s)e.add(r(t));return e}case 7:{let{name:e,message:t}=s;return n(new nn[e](t),a)}case 8:return n(BigInt(s),a);case"BigInt":return n(Object(BigInt(s)),a);case"ArrayBuffer":return n(new Uint8Array(s).buffer,s);case"DataView":{let{buffer:e}=new Uint8Array(s);return n(new DataView(e),s)}}return n(new nn[i](s),a)};return r},na=e=>nr(new Map,e)(0),{toString:ni}={},{keys:ns}=Object,no=e=>{let t=typeof e;if("object"!==t||!e)return[0,t];let n=ni.call(e).slice(8,-1);switch(n){case"Array":return[1,""];case"Object":return[2,""];case"Date":return[3,""];case"RegExp":return[4,""];case"Map":return[5,""];case"Set":return[6,""];case"DataView":return[1,n]}return n.includes("Array")?[1,n]:n.includes("Error")?[7,n]:[2,n]},nl=([e,t])=>0===e&&("function"===t||"symbol"===t),nc=(e,t,n,r)=>{let a=(e,t)=>{let a=r.push(e)-1;return n.set(t,a),a},i=r=>{if(n.has(r))return n.get(r);let[s,o]=no(r);switch(s){case 0:{let t=r;switch(o){case"bigint":s=8,t=r.toString();break;case"function":case"symbol":if(e)throw TypeError("unable to serialize "+o);t=null;break;case"undefined":return a([-1],r)}return a([s,t],r)}case 1:{if(o){let e=r;return"DataView"===o?e=new Uint8Array(r.buffer):"ArrayBuffer"===o&&(e=new Uint8Array(r)),a([o,[...e]],r)}let e=[],t=a([s,e],r);for(let t of r)e.push(i(t));return t}case 2:{if(o)switch(o){case"BigInt":return a([o,r.toString()],r);case"Boolean":case"Number":case"String":return a([o,r.valueOf()],r)}if(t&&"toJSON"in r)return i(r.toJSON());let n=[],l=a([s,n],r);for(let t of ns(r))(e||!nl(no(r[t])))&&n.push([i(t),i(r[t])]);return l}case 3:return a([s,r.toISOString()],r);case 4:{let{source:e,flags:t}=r;return a([s,{source:e,flags:t}],r)}case 5:{let t=[],n=a([s,t],r);for(let[n,a]of r)(e||!(nl(no(n))||nl(no(a))))&&t.push([i(n),i(a)]);return n}case 6:{let t=[],n=a([s,t],r);for(let n of r)(e||!nl(no(n)))&&t.push(i(n));return n}}let{message:l}=r;return a([s,{name:o,message:l}],r)};return i},nu=(e,{json:t,lossy:n}={})=>{let r=[];return nc(!(t||n),!!t,new Map,r)(e),r},nh="function"==typeof structuredClone?(e,t)=>t&&("json"in t||"lossy"in t)?na(nu(e,t)):structuredClone(e):(e,t)=>na(nu(e,t));function nd(e){let t=[],n=-1,r=0,a=0;for(;++n<e.length;){let i=e.charCodeAt(n),s="";if(37===i&&e$(e.charCodeAt(n+1))&&e$(e.charCodeAt(n+2)))a=2;else if(i<128)/[!#$&-;=?-Z_a-z~]/.test(String.fromCharCode(i))||(s=String.fromCharCode(i));else if(i>55295&&i<57344){let t=e.charCodeAt(n+1);i<56320&&t>56319&&t<57344?(s=String.fromCharCode(i,t),a=1):s="�"}else s=String.fromCharCode(i);s&&(t.push(e.slice(r,n),encodeURIComponent(s)),r=n+a+1,s=""),a&&(n+=a,a=0)}return t.join("")+e.slice(r)}function np(e,t){let n=[{type:"text",value:"↩"}];return t>1&&n.push({type:"element",tagName:"sup",properties:{},children:[{type:"text",value:String(t)}]}),n}function nm(e,t){return"Back to reference "+(e+1)+(t>1?"-"+t:"")}let nf=function(e){if(null==e)return nT;if("function"==typeof e)return nE(e);if("object"==typeof e)return Array.isArray(e)?function(e){let t=[],n=-1;for(;++n<e.length;)t[n]=nf(e[n]);return nE(function(...e){let n=-1;for(;++n<t.length;)if(t[n].apply(this,e))return!0;return!1})}(e):nE(function(t){let n;for(n in e)if(t[n]!==e[n])return!1;return!0});if("string"==typeof e)return nE(function(t){return t&&t.type===e});throw Error("Expected function, string, or object as test")};function nE(e){return function(t,n,r){var a;return!!(null!==(a=t)&&"object"==typeof a&&"type"in a&&e.call(this,t,"number"==typeof n?n:void 0,r||void 0))}}function nT(){return!0}let ng=[];function nA(e,t,n,r){let a;"function"==typeof t&&"function"!=typeof n?(r=n,n=t):a=t;let i=nf(a),s=r?-1:1;(function e(a,o,l){let c=a&&"object"==typeof a?a:{};if("string"==typeof c.type){let e="string"==typeof c.tagName?c.tagName:"string"==typeof c.name?c.name:void 0;Object.defineProperty(u,"name",{value:"node (\x1b[33m"+a.type+(e?"<"+e+">":"")+"\x1b[39m)"})}return u;function u(){var c;let u,h,d,p=ng;if((!t||i(a,o,l[l.length-1]||void 0))&&!1===(p=Array.isArray(c=n(a,l))?c:"number"==typeof c?[!0,c]:null==c?ng:[c])[0])return p;if("children"in a&&a.children&&a.children&&"skip"!==p[0])for(h=(r?a.children.length:-1)+s,d=l.concat(a);h>-1&&h<a.children.length;){if(!1===(u=e(a.children[h],h,d)())[0])return u;h="number"==typeof u[1]?u[1]:h+s}return p}})(e,void 0,[])()}function n_(e,t,n,r){let a,i,s;"function"==typeof t&&"function"!=typeof n?(i=void 0,s=t,a=n):(i=t,s=n,a=r),nA(e,i,function(e,t){let n=t[t.length-1],r=n?n.children.indexOf(e):void 0;return s(e,r,n)},a)}function nk(e,t){let n=t.referenceType,r="]";if("collapsed"===n?r+="[]":"full"===n&&(r+="["+(t.label||t.identifier)+"]"),"imageReference"===t.type)return[{type:"text",value:"!["+t.alt+r}];let a=e.all(t),i=a[0];i&&"text"===i.type?i.value="["+i.value:a.unshift({type:"text",value:"["});let s=a[a.length-1];return s&&"text"===s.type?s.value+=r:a.push({type:"text",value:r}),a}function nS(e){let t=e.spread;return null==t?e.children.length>1:t}function nb(e,t,n){let r=0,a=e.length;if(t){let t=e.codePointAt(r);for(;9===t||32===t;)r++,t=e.codePointAt(r)}if(n){let t=e.codePointAt(a-1);for(;9===t||32===t;)a--,t=e.codePointAt(a-1)}return a>r?e.slice(r,a):""}let ny={blockquote:function(e,t){let n={type:"element",tagName:"blockquote",properties:{},children:e.wrap(e.all(t),!0)};return e.patch(t,n),e.applyData(t,n)},break:function(e,t){let n={type:"element",tagName:"br",properties:{},children:[]};return e.patch(t,n),[e.applyData(t,n),{type:"text",value:"\n"}]},code:function(e,t){let n=t.value?t.value+"\n":"",r={};t.lang&&(r.className=["language-"+t.lang]);let a={type:"element",tagName:"code",properties:r,children:[{type:"text",value:n}]};return t.meta&&(a.data={meta:t.meta}),e.patch(t,a),a={type:"element",tagName:"pre",properties:{},children:[a=e.applyData(t,a)]},e.patch(t,a),a},delete:function(e,t){let n={type:"element",tagName:"del",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},emphasis:function(e,t){let n={type:"element",tagName:"em",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},footnoteReference:function(e,t){let n;let r="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",a=String(t.identifier).toUpperCase(),i=nd(a.toLowerCase()),s=e.footnoteOrder.indexOf(a),o=e.footnoteCounts.get(a);void 0===o?(o=0,e.footnoteOrder.push(a),n=e.footnoteOrder.length):n=s+1,o+=1,e.footnoteCounts.set(a,o);let l={type:"element",tagName:"a",properties:{href:"#"+r+"fn-"+i,id:r+"fnref-"+i+(o>1?"-"+o:""),dataFootnoteRef:!0,ariaDescribedBy:["footnote-label"]},children:[{type:"text",value:String(n)}]};e.patch(t,l);let c={type:"element",tagName:"sup",properties:{},children:[l]};return e.patch(t,c),e.applyData(t,c)},heading:function(e,t){let n={type:"element",tagName:"h"+t.depth,properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},html:function(e,t){if(e.options.allowDangerousHtml){let n={type:"raw",value:t.value};return e.patch(t,n),e.applyData(t,n)}},imageReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return nk(e,t);let a={src:nd(r.url||""),alt:t.alt};null!==r.title&&void 0!==r.title&&(a.title=r.title);let i={type:"element",tagName:"img",properties:a,children:[]};return e.patch(t,i),e.applyData(t,i)},image:function(e,t){let n={src:nd(t.url)};null!==t.alt&&void 0!==t.alt&&(n.alt=t.alt),null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"img",properties:n,children:[]};return e.patch(t,r),e.applyData(t,r)},inlineCode:function(e,t){let n={type:"text",value:t.value.replace(/\r?\n|\r/g," ")};e.patch(t,n);let r={type:"element",tagName:"code",properties:{},children:[n]};return e.patch(t,r),e.applyData(t,r)},linkReference:function(e,t){let n=String(t.identifier).toUpperCase(),r=e.definitionById.get(n);if(!r)return nk(e,t);let a={href:nd(r.url||"")};null!==r.title&&void 0!==r.title&&(a.title=r.title);let i={type:"element",tagName:"a",properties:a,children:e.all(t)};return e.patch(t,i),e.applyData(t,i)},link:function(e,t){let n={href:nd(t.url)};null!==t.title&&void 0!==t.title&&(n.title=t.title);let r={type:"element",tagName:"a",properties:n,children:e.all(t)};return e.patch(t,r),e.applyData(t,r)},listItem:function(e,t,n){let r=e.all(t),a=n?function(e){let t=!1;if("list"===e.type){t=e.spread||!1;let n=e.children,r=-1;for(;!t&&++r<n.length;)t=nS(n[r])}return t}(n):nS(t),i={},s=[];if("boolean"==typeof t.checked){let e;let n=r[0];n&&"element"===n.type&&"p"===n.tagName?e=n:(e={type:"element",tagName:"p",properties:{},children:[]},r.unshift(e)),e.children.length>0&&e.children.unshift({type:"text",value:" "}),e.children.unshift({type:"element",tagName:"input",properties:{type:"checkbox",checked:t.checked,disabled:!0},children:[]}),i.className=["task-list-item"]}let o=-1;for(;++o<r.length;){let e=r[o];(a||0!==o||"element"!==e.type||"p"!==e.tagName)&&s.push({type:"text",value:"\n"}),"element"!==e.type||"p"!==e.tagName||a?s.push(e):s.push(...e.children)}let l=r[r.length-1];l&&(a||"element"!==l.type||"p"!==l.tagName)&&s.push({type:"text",value:"\n"});let c={type:"element",tagName:"li",properties:i,children:s};return e.patch(t,c),e.applyData(t,c)},list:function(e,t){let n={},r=e.all(t),a=-1;for("number"==typeof t.start&&1!==t.start&&(n.start=t.start);++a<r.length;){let e=r[a];if("element"===e.type&&"li"===e.tagName&&e.properties&&Array.isArray(e.properties.className)&&e.properties.className.includes("task-list-item")){n.className=["contains-task-list"];break}}let i={type:"element",tagName:t.ordered?"ol":"ul",properties:n,children:e.wrap(r,!0)};return e.patch(t,i),e.applyData(t,i)},paragraph:function(e,t){let n={type:"element",tagName:"p",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},root:function(e,t){let n={type:"root",children:e.wrap(e.all(t))};return e.patch(t,n),e.applyData(t,n)},strong:function(e,t){let n={type:"element",tagName:"strong",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},table:function(e,t){let n=e.all(t),r=n.shift(),a=[];if(r){let n={type:"element",tagName:"thead",properties:{},children:e.wrap([r],!0)};e.patch(t.children[0],n),a.push(n)}if(n.length>0){let r={type:"element",tagName:"tbody",properties:{},children:e.wrap(n,!0)},i=eg(t.children[1]),s=eT(t.children[t.children.length-1]);i&&s&&(r.position={start:i,end:s}),a.push(r)}let i={type:"element",tagName:"table",properties:{},children:e.wrap(a,!0)};return e.patch(t,i),e.applyData(t,i)},tableCell:function(e,t){let n={type:"element",tagName:"td",properties:{},children:e.all(t)};return e.patch(t,n),e.applyData(t,n)},tableRow:function(e,t,n){let r=n?n.children:void 0,a=0===(r?r.indexOf(t):1)?"th":"td",i=n&&"table"===n.type?n.align:void 0,s=i?i.length:t.children.length,o=-1,l=[];for(;++o<s;){let n=t.children[o],r={},s=i?i[o]:void 0;s&&(r.align=s);let c={type:"element",tagName:a,properties:r,children:[]};n&&(c.children=e.all(n),e.patch(n,c),c=e.applyData(n,c)),l.push(c)}let c={type:"element",tagName:"tr",properties:{},children:e.wrap(l,!0)};return e.patch(t,c),e.applyData(t,c)},text:function(e,t){let n={type:"text",value:function(e){let t=String(e),n=/\r?\n|\r/g,r=n.exec(t),a=0,i=[];for(;r;)i.push(nb(t.slice(a,r.index),a>0,!0),r[0]),a=r.index+r[0].length,r=n.exec(t);return i.push(nb(t.slice(a),a>0,!1)),i.join("")}(String(t.value))};return e.patch(t,n),e.applyData(t,n)},thematicBreak:function(e,t){let n={type:"element",tagName:"hr",properties:{},children:[]};return e.patch(t,n),e.applyData(t,n)},toml:nN,yaml:nN,definition:nN,footnoteDefinition:nN};function nN(){}let nC={}.hasOwnProperty,nI={};function nD(e,t){e.position&&(t.position=function(e){let t=eg(e),n=eT(e);if(t&&n)return{start:t,end:n}}(e))}function nO(e,t){let n=t;if(e&&e.data){let t=e.data.hName,r=e.data.hChildren,a=e.data.hProperties;"string"==typeof t&&("element"===n.type?n.tagName=t:n={type:"element",tagName:t,properties:{},children:"children"in n?n.children:[n]}),"element"===n.type&&a&&Object.assign(n.properties,nh(a)),"children"in n&&n.children&&null!=r&&(n.children=r)}return n}function nL(e,t){let n=[],r=-1;for(t&&n.push({type:"text",value:"\n"});++r<e.length;)r&&n.push({type:"text",value:"\n"}),n.push(e[r]);return t&&e.length>0&&n.push({type:"text",value:"\n"}),n}function nx(e){let t=0,n=e.charCodeAt(t);for(;9===n||32===n;)t++,n=e.charCodeAt(t);return e.slice(t)}function nR(e,t){let n=function(e,t){let n=t||nI,r=new Map,a=new Map,i={all:function(e){let t=[];if("children"in e){let n=e.children,r=-1;for(;++r<n.length;){let a=i.one(n[r],e);if(a){if(r&&"break"===n[r-1].type&&(Array.isArray(a)||"text"!==a.type||(a.value=nx(a.value)),!Array.isArray(a)&&"element"===a.type)){let e=a.children[0];e&&"text"===e.type&&(e.value=nx(e.value))}Array.isArray(a)?t.push(...a):t.push(a)}}}return t},applyData:nO,definitionById:r,footnoteById:a,footnoteCounts:new Map,footnoteOrder:[],handlers:{...ny,...n.handlers},one:function(e,t){let n=e.type,r=i.handlers[n];if(nC.call(i.handlers,n)&&r)return r(i,e,t);if(i.options.passThrough&&i.options.passThrough.includes(n)){if("children"in e){let{children:t,...n}=e,r=nh(n);return r.children=i.all(e),r}return nh(e)}return(i.options.unknownHandler||function(e,t){let n=t.data||{},r="value"in t&&!(nC.call(n,"hProperties")||nC.call(n,"hChildren"))?{type:"text",value:t.value}:{type:"element",tagName:"div",properties:{},children:e.all(t)};return e.patch(t,r),e.applyData(t,r)})(i,e,t)},options:n,patch:nD,wrap:nL};return n_(e,function(e){if("definition"===e.type||"footnoteDefinition"===e.type){let t="definition"===e.type?r:a,n=String(e.identifier).toUpperCase();t.has(n)||t.set(n,e)}}),i}(e,t),r=n.one(e,void 0),a=function(e){let t="string"==typeof e.options.clobberPrefix?e.options.clobberPrefix:"user-content-",n=e.options.footnoteBackContent||np,r=e.options.footnoteBackLabel||nm,a=e.options.footnoteLabel||"Footnotes",i=e.options.footnoteLabelTagName||"h2",s=e.options.footnoteLabelProperties||{className:["sr-only"]},o=[],l=-1;for(;++l<e.footnoteOrder.length;){let a=e.footnoteById.get(e.footnoteOrder[l]);if(!a)continue;let i=e.all(a),s=String(a.identifier).toUpperCase(),c=nd(s.toLowerCase()),u=0,h=[],d=e.footnoteCounts.get(s);for(;void 0!==d&&++u<=d;){h.length>0&&h.push({type:"text",value:" "});let e="string"==typeof n?n:n(l,u);"string"==typeof e&&(e={type:"text",value:e}),h.push({type:"element",tagName:"a",properties:{href:"#"+t+"fnref-"+c+(u>1?"-"+u:""),dataFootnoteBackref:"",ariaLabel:"string"==typeof r?r:r(l,u),className:["data-footnote-backref"]},children:Array.isArray(e)?e:[e]})}let p=i[i.length-1];if(p&&"element"===p.type&&"p"===p.tagName){let e=p.children[p.children.length-1];e&&"text"===e.type?e.value+=" ":p.children.push({type:"text",value:" "}),p.children.push(...h)}else i.push(...h);let m={type:"element",tagName:"li",properties:{id:t+"fn-"+c},children:e.wrap(i,!0)};e.patch(a,m),o.push(m)}if(0!==o.length)return{type:"element",tagName:"section",properties:{dataFootnotes:!0,className:["footnotes"]},children:[{type:"element",tagName:i,properties:{...nh(s),id:"footnote-label"},children:[{type:"text",value:a}]},{type:"text",value:"\n"},{type:"element",tagName:"ol",properties:{},children:e.wrap(o,!0)},{type:"text",value:"\n"}]}}(n),i=Array.isArray(r)?{type:"root",children:r}:r||{type:"root",children:[]};return a&&i.children.push({type:"text",value:"\n"},a),i}function nv(e,t){return e&&"run"in e?async function(n,r){let a=nR(n,{file:r,...t});await e.run(a,r)}:function(n,r){return nR(n,{file:r,...e||t})}}function nP(e){if(e)throw e}var nM=n(19675);function nw(e){if("object"!=typeof e||null===e)return!1;let t=Object.getPrototypeOf(e);return(null===t||t===Object.prototype||null===Object.getPrototypeOf(t))&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)}let nB=require("node:path"),nF=require("node:process");function nU(e){return!!(null!==e&&"object"==typeof e&&"href"in e&&e.href&&"protocol"in e&&e.protocol&&void 0===e.auth)}let nH=require("node:url"),nG=["history","path","basename","stem","extname","dirname"];class nq{constructor(e){let t,n;t=e?nU(e)?{path:e}:"string"==typeof e||function(e){return!!(e&&"object"==typeof e&&"byteLength"in e&&"byteOffset"in e)}(e)?{value:e}:e:{},this.cwd="cwd"in t?"":nF.cwd(),this.data={},this.history=[],this.messages=[],this.value,this.map,this.result,this.stored;let r=-1;for(;++r<nG.length;){let e=nG[r];e in t&&void 0!==t[e]&&null!==t[e]&&(this[e]="history"===e?[...t[e]]:t[e])}for(n in t)nG.includes(n)||(this[n]=t[n])}get basename(){return"string"==typeof this.path?nB.basename(this.path):void 0}set basename(e){nj(e,"basename"),nz(e,"basename"),this.path=nB.join(this.dirname||"",e)}get dirname(){return"string"==typeof this.path?nB.dirname(this.path):void 0}set dirname(e){nY(this.basename,"dirname"),this.path=nB.join(e||"",this.basename)}get extname(){return"string"==typeof this.path?nB.extname(this.path):void 0}set extname(e){if(nz(e,"extname"),nY(this.dirname,"extname"),e){if(46!==e.codePointAt(0))throw Error("`extname` must start with `.`");if(e.includes(".",1))throw Error("`extname` cannot contain multiple dots")}this.path=nB.join(this.dirname,this.stem+(e||""))}get path(){return this.history[this.history.length-1]}set path(e){nU(e)&&(e=(0,nH.fileURLToPath)(e)),nj(e,"path"),this.path!==e&&this.history.push(e)}get stem(){return"string"==typeof this.path?nB.basename(this.path,this.extname):void 0}set stem(e){nj(e,"stem"),nz(e,"stem"),this.path=nB.join(this.dirname||"",e+(this.extname||""))}fail(e,t,n){let r=this.message(e,t,n);throw r.fatal=!0,r}info(e,t,n){let r=this.message(e,t,n);return r.fatal=void 0,r}message(e,t,n){let r=new ey(e,t,n);return this.path&&(r.name=this.path+":"+r.name,r.file=this.path),r.fatal=!1,this.messages.push(r),r}toString(e){return void 0===this.value?"":"string"==typeof this.value?this.value:new TextDecoder(e||void 0).decode(this.value)}}function nz(e,t){if(e&&e.includes(nB.sep))throw Error("`"+t+"` cannot be a path: did not expect `"+nB.sep+"`")}function nj(e,t){if(!e)throw Error("`"+t+"` cannot be empty")}function nY(e,t){if(!e)throw Error("Setting `"+t+"` requires `path` to be set too")}let nV=function(e){let t=this.constructor.prototype,n=t[e],r=function(){return n.apply(r,arguments)};return Object.setPrototypeOf(r,t),r},nW={}.hasOwnProperty;class nQ extends nV{constructor(){super("copy"),this.Compiler=void 0,this.Parser=void 0,this.attachers=[],this.compiler=void 0,this.freezeIndex=-1,this.frozen=void 0,this.namespace={},this.parser=void 0,this.transformers=function(){let e=[],t={run:function(...t){let n=-1,r=t.pop();if("function"!=typeof r)throw TypeError("Expected function as last argument, not "+r);(function a(i,...s){let o=e[++n],l=-1;if(i){r(i);return}for(;++l<t.length;)(null===s[l]||void 0===s[l])&&(s[l]=t[l]);t=s,o?(function(e,t){let n;return function(...t){let i;let s=e.length>t.length;s&&t.push(r);try{i=e.apply(this,t)}catch(e){if(s&&n)throw e;return r(e)}s||(i&&i.then&&"function"==typeof i.then?i.then(a,r):i instanceof Error?r(i):a(i))};function r(e,...a){n||(n=!0,t(e,...a))}function a(e){r(null,e)}})(o,a)(...s):r(null,...s)})(null,...t)},use:function(n){if("function"!=typeof n)throw TypeError("Expected `middelware` to be a function, not "+n);return e.push(n),t}};return t}()}copy(){let e=new nQ,t=-1;for(;++t<this.attachers.length;){let n=this.attachers[t];e.use(...n)}return e.data(nM(!0,{},this.namespace)),e}data(e,t){return"string"==typeof e?2==arguments.length?(nZ("data",this.frozen),this.namespace[e]=t,this):nW.call(this.namespace,e)&&this.namespace[e]||void 0:e?(nZ("data",this.frozen),this.namespace=e,this):this.namespace}freeze(){if(this.frozen)return this;for(;++this.freezeIndex<this.attachers.length;){let[e,...t]=this.attachers[this.freezeIndex];if(!1===t[0])continue;!0===t[0]&&(t[0]=void 0);let n=e.call(this,...t);"function"==typeof n&&this.transformers.use(n)}return this.frozen=!0,this.freezeIndex=Number.POSITIVE_INFINITY,this}parse(e){this.freeze();let t=n1(e),n=this.parser||this.Parser;return nX("parse",n),n(String(t),t)}process(e,t){let n=this;return this.freeze(),nX("process",this.parser||this.Parser),n$("process",this.compiler||this.Compiler),t?r(void 0,t):new Promise(r);function r(r,a){let i=n1(e),s=n.parse(i);function o(e,n){e||!n?a(e):r?r(n):t(void 0,n)}n.run(s,i,function(e,t,r){if(e||!t||!r)return o(e);let a=n.stringify(t,r);"string"==typeof a||a&&"object"==typeof a&&"byteLength"in a&&"byteOffset"in a?r.value=a:r.result=a,o(e,r)})}}processSync(e){let t,n=!1;return this.freeze(),nX("processSync",this.parser||this.Parser),n$("processSync",this.compiler||this.Compiler),this.process(e,function(e,r){n=!0,nP(e),t=r}),n0("processSync","process",n),t}run(e,t,n){nJ(e),this.freeze();let r=this.transformers;return n||"function"!=typeof t||(n=t,t=void 0),n?a(void 0,n):new Promise(a);function a(a,i){let s=n1(t);r.run(e,s,function(t,r,s){let o=r||e;t?i(t):a?a(o):n(void 0,o,s)})}}runSync(e,t){let n,r=!1;return this.run(e,t,function(e,t){nP(e),n=t,r=!0}),n0("runSync","run",r),n}stringify(e,t){this.freeze();let n=n1(t),r=this.compiler||this.Compiler;return n$("stringify",r),nJ(e),r(e,n)}use(e,...t){let n=this.attachers,r=this.namespace;if(nZ("use",this.frozen),null==e);else if("function"==typeof e)s(e,t);else if("object"==typeof e)Array.isArray(e)?i(e):a(e);else throw TypeError("Expected usable value, not `"+e+"`");return this;function a(e){if(!("plugins"in e)&&!("settings"in e))throw Error("Expected usable value but received an empty preset, which is probably a mistake: presets typically come with `plugins` and sometimes with `settings`, but this has neither");i(e.plugins),e.settings&&(r.settings=nM(!0,r.settings,e.settings))}function i(e){let t=-1;if(null==e);else if(Array.isArray(e))for(;++t<e.length;)!function(e){if("function"==typeof e)s(e,[]);else if("object"==typeof e){if(Array.isArray(e)){let[t,...n]=e;s(t,n)}else a(e)}else throw TypeError("Expected usable value, not `"+e+"`")}(e[t]);else throw TypeError("Expected a list of plugins, not `"+e+"`")}function s(e,t){let r=-1,a=-1;for(;++r<n.length;)if(n[r][0]===e){a=r;break}if(-1===a)n.push([e,...t]);else if(t.length>0){let[r,...i]=t,s=n[a][1];nw(s)&&nw(r)&&(r=nM(!0,s,r)),n[a]=[e,r,...i]}}}}let nK=new nQ().freeze();function nX(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `parser`")}function n$(e,t){if("function"!=typeof t)throw TypeError("Cannot `"+e+"` without `compiler`")}function nZ(e,t){if(t)throw Error("Cannot call `"+e+"` on a frozen processor.\nCreate a new processor first, by calling it: use `processor()` instead of `processor`.")}function nJ(e){if(!nw(e)||"string"!=typeof e.type)throw TypeError("Expected node, got `"+e+"`")}function n0(e,t,n){if(!n)throw Error("`"+e+"` finished async. Use `"+t+"` instead")}function n1(e){return e&&"object"==typeof e&&"message"in e&&"messages"in e?e:new nq(e)}let n2=[],n3={allowDangerousHtml:!0},n5=/^(https?|ircs?|mailto|xmpp)$/i,n4=[{from:"astPlugins",id:"remove-buggy-html-in-markdown-parser"},{from:"allowDangerousHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"allowNode",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowElement"},{from:"allowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"allowedElements"},{from:"className",id:"remove-classname"},{from:"disallowedTypes",id:"replace-allownode-allowedtypes-and-disallowedtypes",to:"disallowedElements"},{from:"escapeHtml",id:"remove-buggy-html-in-markdown-parser"},{from:"includeElementIndex",id:"#remove-includeelementindex"},{from:"includeNodeIndex",id:"change-includenodeindex-to-includeelementindex"},{from:"linkTarget",id:"remove-linktarget"},{from:"plugins",id:"change-plugins-to-remarkplugins",to:"remarkPlugins"},{from:"rawSourcePos",id:"#remove-rawsourcepos"},{from:"renderers",id:"change-renderers-to-components",to:"components"},{from:"source",id:"change-source-to-children",to:"children"},{from:"sourcePos",id:"#remove-sourcepos"},{from:"transformImageUri",id:"#add-urltransform",to:"urlTransform"},{from:"transformLinkUri",id:"#add-urltransform",to:"urlTransform"}];function n6(e){let t=function(e){let t=e.rehypePlugins||n2,n=e.remarkPlugins||n2,r=e.remarkRehypeOptions?{...e.remarkRehypeOptions,...n3}:n3;return nK().use(nt).use(n).use(nv,r).use(t)}(e),n=function(e){let t=e.children||"",n=new nq;return"string"==typeof t&&(n.value=t),n}(e);return function(e,t){let n=t.allowedElements,r=t.allowElement,a=t.components,i=t.disallowedElements,s=t.skipHtml,o=t.unwrapDisallowed,l=t.urlTransform||n8;for(let e of n4)Object.hasOwn(t,e.from)&&(e.from,e.to&&e.to,e.id);return n_(e,function(e,t,a){if("raw"===e.type&&a&&"number"==typeof t)return s?a.children.splice(t,1):a.children[t]={type:"text",value:e.value},t;if("element"===e.type){let t;for(t in eF)if(Object.hasOwn(eF,t)&&Object.hasOwn(e.properties,t)){let n=e.properties[t],r=eF[t];(null===r||r.includes(e.tagName))&&(e.properties[t]=l(String(n||""),t,e))}}if("element"===e.type){let s=n?!n.includes(e.tagName):!!i&&i.includes(e.tagName);if(!s&&r&&"number"==typeof t&&(s=!r(e,t,a)),s&&a&&"number"==typeof t)return o&&e.children?a.children.splice(t,1,...e.children):a.children.splice(t,1),t}}),function(e,t){var n,r,a;let i;if(!t||void 0===t.Fragment)throw TypeError("Expected `Fragment` in options");let s=t.filePath||void 0;if(t.development){if("function"!=typeof t.jsxDEV)throw TypeError("Expected `jsxDEV` in options when `development: true`");n=t.jsxDEV,i=function(e,t,r,a){let i=Array.isArray(r.children),o=eg(e);return n(t,r,a,i,{columnNumber:o?o.column-1:void 0,fileName:s,lineNumber:o?o.line:void 0},void 0)}}else{if("function"!=typeof t.jsx)throw TypeError("Expected `jsx` in production options");if("function"!=typeof t.jsxs)throw TypeError("Expected `jsxs` in production options");r=t.jsx,a=t.jsxs,i=function(e,t,n,i){let s=Array.isArray(n.children)?a:r;return i?s(t,n,i):s(t,n)}}let o={Fragment:t.Fragment,ancestors:[],components:t.components||{},create:i,elementAttributeNameCase:t.elementAttributeNameCase||"react",evaluater:t.createEvaluater?t.createEvaluater():void 0,filePath:s,ignoreInvalidStyle:t.ignoreInvalidStyle||!1,passKeys:!1!==t.passKeys,passNode:t.passNode||!1,schema:"svg"===t.space?es:ei,stylePropertyNameCase:t.stylePropertyNameCase||"dom",tableCellAlignToStyle:!1!==t.tableCellAlignToStyle},l=ex(o,e,void 0);return l&&"string"!=typeof l?l:o.create(e,o.Fragment,{children:l||void 0},void 0)}(e,{Fragment:S.Fragment,components:a,ignoreInvalidStyle:!0,jsx:S.jsx,jsxs:S.jsxs,passKeys:!0,passNode:!0})}(t.runSync(t.parse(n),n),e)}function n8(e){let t=e.indexOf(":"),n=e.indexOf("?"),r=e.indexOf("#"),a=e.indexOf("/");return -1===t||-1!==a&&t>a||-1!==n&&t>n||-1!==r&&t>r||n5.test(e.slice(0,t))?e:""}function n9(e,t){let n=String(e);if("string"!=typeof t)throw TypeError("Expected character");let r=0,a=n.indexOf(t);for(;-1!==a;)r++,a=n.indexOf(t,a+t.length);return r}let n7="phrasing",re=["autolink","link","image","label"];function rt(e){this.enter({type:"link",title:null,url:"",children:[]},e)}function rn(e){this.config.enter.autolinkProtocol.call(this,e)}function rr(e){this.config.exit.autolinkProtocol.call(this,e)}function ra(e){this.config.exit.data.call(this,e);let t=this.stack[this.stack.length-1];t.type,t.url="http://"+this.sliceSerialize(e)}function ri(e){this.config.exit.autolinkEmail.call(this,e)}function rs(e){this.exit(e)}function ro(e){!function(e,t,n){let r=nf((n||{}).ignore||[]),a=function(e){let t=[];if(!Array.isArray(e))throw TypeError("Expected find and replace tuple or list of tuples");let n=!e[0]||Array.isArray(e[0])?e:[e],r=-1;for(;++r<n.length;){var a;let e=n[r];t.push(["string"==typeof(a=e[0])?RegExp(function(e){if("string"!=typeof e)throw TypeError("Expected a string");return e.replace(/[|\\{}()[\]^$+*?.]/g,"\\$&").replace(/-/g,"\\x2d")}(a),"g"):a,function(e){return"function"==typeof e?e:function(){return e}}(e[1])])}return t}(t),i=-1;for(;++i<a.length;)nA(e,"text",s);function s(e,t){let n,s=-1;for(;++s<t.length;){let e=t[s],a=n?n.children:void 0;if(r(e,a?a.indexOf(e):void 0,n))return;n=e}if(n)return function(e,t){let n=t[t.length-1],r=a[i][0],s=a[i][1],o=0,l=n.children.indexOf(e),c=!1,u=[];r.lastIndex=0;let h=r.exec(e.value);for(;h;){let n=h.index,a={index:h.index,input:h.input,stack:[...t,e]},i=s(...h,a);if("string"==typeof i&&(i=i.length>0?{type:"text",value:i}:void 0),!1===i?r.lastIndex=n+1:(o!==n&&u.push({type:"text",value:e.value.slice(o,n)}),Array.isArray(i)?u.push(...i):i&&u.push(i),o=n+h[0].length,c=!0),!r.global)break;h=r.exec(e.value)}return c?(o<e.value.length&&u.push({type:"text",value:e.value.slice(o)}),n.children.splice(l,1,...u)):u=[e],l+u.length}(e,t)}}(e,[[/(https?:\/\/|www(?=\.))([-.\w]+)([^ \t\r\n]*)/gi,rl],[/(?<=^|\s|\p{P}|\p{S})([-.\w+]+)@([-\w]+(?:\.[-\w]+)+)/gu,rc]],{ignore:["link","linkReference"]})}function rl(e,t,n,r,a){let i="";if(!ru(a)||(/^w/i.test(t)&&(n=t+n,t="",i="http://"),!function(e){let t=e.split(".");return!(t.length<2||t[t.length-1]&&(/_/.test(t[t.length-1])||!/[a-zA-Z\d]/.test(t[t.length-1]))||t[t.length-2]&&(/_/.test(t[t.length-2])||!/[a-zA-Z\d]/.test(t[t.length-2])))}(n)))return!1;let s=function(e){let t=/[!"&'),.:;<>?\]}]+$/.exec(e);if(!t)return[e,void 0];e=e.slice(0,t.index);let n=t[0],r=n.indexOf(")"),a=n9(e,"("),i=n9(e,")");for(;-1!==r&&a>i;)e+=n.slice(0,r+1),r=(n=n.slice(r+1)).indexOf(")"),i++;return[e,n]}(n+r);if(!s[0])return!1;let o={type:"link",title:null,url:i+t+s[0],children:[{type:"text",value:t+s[0]}]};return s[1]?[o,{type:"text",value:s[1]}]:o}function rc(e,t,n,r){return!(!ru(r,!0)||/[-\d_]$/.test(n))&&{type:"link",title:null,url:"mailto:"+t+"@"+n,children:[{type:"text",value:t+"@"+n}]}}function ru(e,t){let n=e.input.charCodeAt(e.index-1);return(0===e.index||e8(n)||e6(n))&&(!t||47!==n)}function rh(){this.buffer()}function rd(e){this.enter({type:"footnoteReference",identifier:"",label:""},e)}function rp(){this.buffer()}function rm(e){this.enter({type:"footnoteDefinition",identifier:"",label:"",children:[]},e)}function rf(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=tS(this.sliceSerialize(e)).toLowerCase(),n.label=t}function rE(e){this.exit(e)}function rT(e){let t=this.resume(),n=this.stack[this.stack.length-1];n.type,n.identifier=tS(this.sliceSerialize(e)).toLowerCase(),n.label=t}function rg(e){this.exit(e)}function rA(e,t,n,r){let a=n.createTracker(r),i=a.move("[^"),s=n.enter("footnoteReference"),o=n.enter("reference");return i+=a.move(n.safe(n.associationId(e),{after:"]",before:i})),o(),s(),i+=a.move("]")}function r_(e,t,n){return 0===t?e:rk(e,t,n)}function rk(e,t,n){return(n?"":"    ")+e}rA.peek=function(){return"["};let rS=["autolink","destinationLiteral","destinationRaw","reference","titleQuote","titleApostrophe"];function rb(e){this.enter({type:"delete",children:[]},e)}function ry(e){this.exit(e)}function rN(e,t,n,r){let a=n.createTracker(r),i=n.enter("strikethrough"),s=a.move("~~");return s+=n.containerPhrasing(e,{...a.current(),before:s,after:"~"})+a.move("~~"),i(),s}function rC(e){return e.length}function rI(e){let t="string"==typeof e?e.codePointAt(0):0;return 67===t||99===t?99:76===t||108===t?108:82===t||114===t?114:0}rN.peek=function(){return"~"};function rD(e,t,n){let r=e.value||"",a="`",i=-1;for(;RegExp("(^|[^`])"+a+"([^`]|$)").test(r);)a+="`";for(/[^ \r\n]/.test(r)&&(/^[ \r\n]/.test(r)&&/[ \r\n]$/.test(r)||/^`|`$/.test(r))&&(r=" "+r+" ");++i<n.unsafe.length;){let e;let t=n.unsafe[i],a=n.compilePattern(t);if(t.atBreak)for(;e=a.exec(r);){let t=e.index;10===r.charCodeAt(t)&&13===r.charCodeAt(t-1)&&t--,r=r.slice(0,t)+" "+r.slice(e.index+1)}}return a+r+a}rD.peek=function(){return"`"},nf(["break","delete","emphasis","footnote","footnoteReference","image","imageReference","inlineCode","inlineMath","link","linkReference","mdxJsxTextElement","mdxTextExpression","strong","text","textDirective"]);let rO={inlineCode:rD,listItem:function(e,t,n,r){let a=function(e){let t=e.options.listItemIndent||"one";if("tab"!==t&&"one"!==t&&"mixed"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.listItemIndent`, expected `tab`, `one`, or `mixed`");return t}(n),i=n.bulletCurrent||function(e){let t=e.options.bullet||"*";if("*"!==t&&"+"!==t&&"-"!==t)throw Error("Cannot serialize items with `"+t+"` for `options.bullet`, expected `*`, `+`, or `-`");return t}(n);t&&"list"===t.type&&t.ordered&&(i=("number"==typeof t.start&&t.start>-1?t.start:1)+(!1===n.options.incrementListMarker?0:t.children.indexOf(e))+i);let s=i.length+1;("tab"===a||"mixed"===a&&(t&&"list"===t.type&&t.spread||e.spread))&&(s=4*Math.ceil(s/4));let o=n.createTracker(r);o.move(i+" ".repeat(s-i.length)),o.shift(s);let l=n.enter("listItem"),c=n.indentLines(n.containerFlow(e,o.current()),function(e,t,n){return t?(n?"":" ".repeat(s))+e:(n?i:i+" ".repeat(s-i.length))+e});return l(),c}};function rL(e){let t=e._align;this.enter({type:"table",align:t.map(function(e){return"none"===e?null:e}),children:[]},e),this.data.inTable=!0}function rx(e){this.exit(e),this.data.inTable=void 0}function rR(e){this.enter({type:"tableRow",children:[]},e)}function rv(e){this.exit(e)}function rP(e){this.enter({type:"tableCell",children:[]},e)}function rM(e){let t=this.resume();this.data.inTable&&(t=t.replace(/\\([\\|])/g,rw));let n=this.stack[this.stack.length-1];n.type,n.value=t,this.exit(e)}function rw(e,t){return"|"===t?t:e}function rB(e){let t=this.stack[this.stack.length-2];t.type,t.checked="taskListCheckValueChecked"===e.type}function rF(e){let t=this.stack[this.stack.length-2];if(t&&"listItem"===t.type&&"boolean"==typeof t.checked){let e=this.stack[this.stack.length-1];e.type;let n=e.children[0];if(n&&"text"===n.type){let r;let a=t.children,i=-1;for(;++i<a.length;){let e=a[i];if("paragraph"===e.type){r=e;break}}r===e&&(n.value=n.value.slice(1),0===n.value.length?e.children.shift():e.position&&n.position&&"number"==typeof n.position.start.offset&&(n.position.start.column++,n.position.start.offset++,e.position.start=Object.assign({},n.position.start)))}}this.exit(e)}function rU(e,t,n,r){let a=e.children[0],i="boolean"==typeof e.checked&&a&&"paragraph"===a.type,s="["+(e.checked?"x":" ")+"] ",o=n.createTracker(r);i&&o.move(s);let l=rO.listItem(e,t,n,{...r,...o.current()});return i&&(l=l.replace(/^(?:[*+-]|\d+\.)([\r\n]| {1,3})/,function(e){return e+s})),l}let rH={tokenize:function(e,t,n){let r=0;return function t(i){return(87===i||119===i)&&r<3?(r++,e.consume(i),t):46===i&&3===r?(e.consume(i),a):n(i)};function a(e){return null===e?n(e):t(e)}},partial:!0},rG={tokenize:function(e,t,n){let r,a,i;return s;function s(t){return 46===t||95===t?e.check(rz,l,o)(t):null===t||e5(t)||e8(t)||45!==t&&e6(t)?l(t):(i=!0,e.consume(t),s)}function o(t){return 95===t?r=!0:(a=r,r=void 0),e.consume(t),s}function l(e){return a||r||!i?n(e):t(e)}},partial:!0},rq={tokenize:function(e,t){let n=0,r=0;return a;function a(s){return 40===s?(n++,e.consume(s),a):41===s&&r<n?i(s):33===s||34===s||38===s||39===s||41===s||42===s||44===s||46===s||58===s||59===s||60===s||63===s||93===s||95===s||126===s?e.check(rz,t,i)(s):null===s||e5(s)||e8(s)?t(s):(e.consume(s),a)}function i(t){return 41===t&&r++,e.consume(t),a}},partial:!0},rz={tokenize:function(e,t,n){return r;function r(s){return 33===s||34===s||39===s||41===s||42===s||44===s||46===s||58===s||59===s||63===s||95===s||126===s?(e.consume(s),r):38===s?(e.consume(s),i):93===s?(e.consume(s),a):60===s||null===s||e5(s)||e8(s)?t(s):n(s)}function a(e){return null===e||40===e||91===e||e5(e)||e8(e)?t(e):r(e)}function i(t){return eX(t)?function t(a){return 59===a?(e.consume(a),r):eX(a)?(e.consume(a),t):n(a)}(t):n(t)}},partial:!0},rj={tokenize:function(e,t,n){return function(t){return e.consume(t),r};function r(e){return e$(e)?n(e):t(e)}},partial:!0},rY={name:"wwwAutolink",tokenize:function(e,t,n){let r=this;return function(t){return 87!==t&&119!==t||!rX.call(r,r.previous)||r0(r.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkWww"),e.check(rH,e.attempt(rG,e.attempt(rq,a),n),n)(t))};function a(n){return e.exit("literalAutolinkWww"),e.exit("literalAutolink"),t(n)}},previous:rX},rV={name:"protocolAutolink",tokenize:function(e,t,n){let r=this,a="",i=!1;return function(t){return(72===t||104===t)&&r$.call(r,r.previous)&&!r0(r.events)?(e.enter("literalAutolink"),e.enter("literalAutolinkHttp"),a+=String.fromCodePoint(t),e.consume(t),s):n(t)};function s(t){if(eX(t)&&a.length<5)return a+=String.fromCodePoint(t),e.consume(t),s;if(58===t){let n=a.toLowerCase();if("http"===n||"https"===n)return e.consume(t),o}return n(t)}function o(t){return 47===t?(e.consume(t),i)?l:(i=!0,o):n(t)}function l(t){return null===t||eJ(t)||e5(t)||e8(t)||e6(t)?n(t):e.attempt(rG,e.attempt(rq,c),n)(t)}function c(n){return e.exit("literalAutolinkHttp"),e.exit("literalAutolink"),t(n)}},previous:r$},rW={name:"emailAutolink",tokenize:function(e,t,n){let r,a;let i=this;return function(t){return!rJ(t)||!rZ.call(i,i.previous)||r0(i.events)?n(t):(e.enter("literalAutolink"),e.enter("literalAutolinkEmail"),function t(r){return rJ(r)?(e.consume(r),t):64===r?(e.consume(r),s):n(r)}(t))};function s(t){return 46===t?e.check(rj,l,o)(t):45===t||95===t||e$(t)?(a=!0,e.consume(t),s):l(t)}function o(t){return e.consume(t),r=!0,s}function l(s){return a&&r&&eX(i.previous)?(e.exit("literalAutolinkEmail"),e.exit("literalAutolink"),t(s)):n(s)}},previous:rZ},rQ={},rK=48;for(;rK<123;)rQ[rK]=rW,58==++rK?rK=65:91===rK&&(rK=97);function rX(e){return null===e||40===e||42===e||95===e||91===e||93===e||126===e||e5(e)}function r$(e){return!eX(e)}function rZ(e){return!(47===e||rJ(e))}function rJ(e){return 43===e||45===e||46===e||95===e||e$(e)}function r0(e){let t=e.length,n=!1;for(;t--;){let r=e[t][1];if(("labelLink"===r.type||"labelImage"===r.type)&&!r._balanced){n=!0;break}if(r._gfmAutolinkLiteralWalkedInto){n=!1;break}}return e.length>0&&!n&&(e[e.length-1][1]._gfmAutolinkLiteralWalkedInto=!0),n}rQ[43]=rW,rQ[45]=rW,rQ[46]=rW,rQ[95]=rW,rQ[72]=[rW,rV],rQ[104]=[rW,rV],rQ[87]=[rW,rY],rQ[119]=[rW,rY];let r1={tokenize:function(e,t,n){let r=this;return e7(e,function(e){let a=r.events[r.events.length-1];return a&&"gfmFootnoteDefinitionIndent"===a[1].type&&4===a[2].sliceSerialize(a[1],!0).length?t(e):n(e)},"gfmFootnoteDefinitionIndent",5)},partial:!0};function r2(e,t,n){let r;let a=this,i=a.events.length,s=a.parser.gfmFootnotes||(a.parser.gfmFootnotes=[]);for(;i--;){let e=a.events[i][1];if("labelImage"===e.type){r=e;break}if("gfmFootnoteCall"===e.type||"labelLink"===e.type||"label"===e.type||"image"===e.type||"link"===e.type)break}return function(i){if(!r||!r._balanced)return n(i);let o=tS(a.sliceSerialize({start:r.end,end:a.now()}));return 94===o.codePointAt(0)&&s.includes(o.slice(1))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(i),e.exit("gfmFootnoteCallLabelMarker"),t(i)):n(i)}}function r3(e,t){let n=e.length;for(;n--;)if("labelImage"===e[n][1].type&&"enter"===e[n][0]){e[n][1];break}e[n+1][1].type="data",e[n+3][1].type="gfmFootnoteCallLabelMarker";let r={type:"gfmFootnoteCall",start:Object.assign({},e[n+3][1].start),end:Object.assign({},e[e.length-1][1].end)},a={type:"gfmFootnoteCallMarker",start:Object.assign({},e[n+3][1].end),end:Object.assign({},e[n+3][1].end)};a.end.column++,a.end.offset++,a.end._bufferIndex++;let i={type:"gfmFootnoteCallString",start:Object.assign({},a.end),end:Object.assign({},e[e.length-1][1].start)},s={type:"chunkString",contentType:"string",start:Object.assign({},i.start),end:Object.assign({},i.end)},o=[e[n+1],e[n+2],["enter",r,t],e[n+3],e[n+4],["enter",a,t],["exit",a,t],["enter",i,t],["enter",s,t],["exit",s,t],["exit",i,t],e[e.length-2],e[e.length-1],["exit",r,t]];return e.splice(n,e.length-n+1,...o),e}function r5(e,t,n){let r;let a=this,i=a.parser.gfmFootnotes||(a.parser.gfmFootnotes=[]),s=0;return function(t){return e.enter("gfmFootnoteCall"),e.enter("gfmFootnoteCallLabelMarker"),e.consume(t),e.exit("gfmFootnoteCallLabelMarker"),o};function o(t){return 94!==t?n(t):(e.enter("gfmFootnoteCallMarker"),e.consume(t),e.exit("gfmFootnoteCallMarker"),e.enter("gfmFootnoteCallString"),e.enter("chunkString").contentType="string",l)}function l(o){if(s>999||93===o&&!r||null===o||91===o||e5(o))return n(o);if(93===o){e.exit("chunkString");let r=e.exit("gfmFootnoteCallString");return i.includes(tS(a.sliceSerialize(r)))?(e.enter("gfmFootnoteCallLabelMarker"),e.consume(o),e.exit("gfmFootnoteCallLabelMarker"),e.exit("gfmFootnoteCall"),t):n(o)}return e5(o)||(r=!0),s++,e.consume(o),92===o?c:l}function c(t){return 91===t||92===t||93===t?(e.consume(t),s++,l):l(t)}}function r4(e,t,n){let r,a;let i=this,s=i.parser.gfmFootnotes||(i.parser.gfmFootnotes=[]),o=0;return function(t){return e.enter("gfmFootnoteDefinition")._container=!0,e.enter("gfmFootnoteDefinitionLabel"),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),l};function l(t){return 94===t?(e.enter("gfmFootnoteDefinitionMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionMarker"),e.enter("gfmFootnoteDefinitionLabelString"),e.enter("chunkString").contentType="string",c):n(t)}function c(t){if(o>999||93===t&&!a||null===t||91===t||e5(t))return n(t);if(93===t){e.exit("chunkString");let n=e.exit("gfmFootnoteDefinitionLabelString");return r=tS(i.sliceSerialize(n)),e.enter("gfmFootnoteDefinitionLabelMarker"),e.consume(t),e.exit("gfmFootnoteDefinitionLabelMarker"),e.exit("gfmFootnoteDefinitionLabel"),h}return e5(t)||(a=!0),o++,e.consume(t),92===t?u:c}function u(t){return 91===t||92===t||93===t?(e.consume(t),o++,c):c(t)}function h(t){return 58===t?(e.enter("definitionMarker"),e.consume(t),e.exit("definitionMarker"),s.includes(r)||s.push(r),e7(e,d,"gfmFootnoteDefinitionWhitespace")):n(t)}function d(e){return t(e)}}function r6(e,t,n){return e.check(tr,t,e.attempt(r1,t,n))}function r8(e){e.exit("gfmFootnoteDefinition")}class r9{constructor(){this.map=[]}add(e,t,n){(function(e,t,n,r){let a=0;if(0!==n||0!==r.length){for(;a<e.map.length;){if(e.map[a][0]===t){e.map[a][1]+=n,e.map[a][2].push(...r);return}a+=1}e.map.push([t,n,r])}})(this,e,t,n)}consume(e){if(this.map.sort(function(e,t){return e[0]-t[0]}),0===this.map.length)return;let t=this.map.length,n=[];for(;t>0;)t-=1,n.push(e.slice(this.map[t][0]+this.map[t][1]),this.map[t][2]),e.length=this.map[t][0];n.push(e.slice()),e.length=0;let r=n.pop();for(;r;){for(let t of r)e.push(t);r=n.pop()}this.map.length=0}}function r7(e,t,n){let r;let a=this,i=0,s=0;return function(e){let t=a.events.length-1;for(;t>-1;){let e=a.events[t][1].type;if("lineEnding"===e||"linePrefix"===e)t--;else break}let r=t>-1?a.events[t][1].type:null,i="tableHead"===r||"tableRow"===r?g:o;return i===g&&a.parser.lazy[a.now().line]?n(e):i(e)};function o(t){return e.enter("tableHead"),e.enter("tableRow"),124===t||(r=!0,s+=1),l(t)}function l(t){return null===t?n(t):e3(t)?s>1?(s=0,a.interrupt=!0,e.exit("tableRow"),e.enter("lineEnding"),e.consume(t),e.exit("lineEnding"),h):n(t):e4(t)?e7(e,l,"whitespace")(t):(s+=1,r&&(r=!1,i+=1),124===t)?(e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),r=!0,l):(e.enter("data"),c(t))}function c(t){return null===t||124===t||e5(t)?(e.exit("data"),l(t)):(e.consume(t),92===t?u:c)}function u(t){return 92===t||124===t?(e.consume(t),c):c(t)}function h(t){return(a.interrupt=!1,a.parser.lazy[a.now().line])?n(t):(e.enter("tableDelimiterRow"),r=!1,e4(t))?e7(e,d,"linePrefix",a.parser.constructs.disable.null.includes("codeIndented")?void 0:4)(t):d(t)}function d(t){return 45===t||58===t?m(t):124===t?(r=!0,e.enter("tableCellDivider"),e.consume(t),e.exit("tableCellDivider"),p):n(t)}function p(t){return e4(t)?e7(e,m,"whitespace")(t):m(t)}function m(t){return 58===t?(s+=1,r=!0,e.enter("tableDelimiterMarker"),e.consume(t),e.exit("tableDelimiterMarker"),f):45===t?(s+=1,f(t)):null===t||e3(t)?T(t):n(t)}function f(t){return 45===t?(e.enter("tableDelimiterFiller"),function t(n){return 45===n?(e.consume(n),t):58===n?(r=!0,e.exit("tableDelimiterFiller"),e.enter("tableDelimiterMarker"),e.consume(n),e.exit("tableDelimiterMarker"),E):(e.exit("tableDelimiterFiller"),E(n))}(t)):n(t)}function E(t){return e4(t)?e7(e,T,"whitespace")(t):T(t)}function T(a){return 124===a?d(a):null===a||e3(a)?r&&i===s?(e.exit("tableDelimiterRow"),e.exit("tableHead"),t(a)):n(a):n(a)}function g(t){return e.enter("tableRow"),A(t)}function A(n){return 124===n?(e.enter("tableCellDivider"),e.consume(n),e.exit("tableCellDivider"),A):null===n||e3(n)?(e.exit("tableRow"),t(n)):e4(n)?e7(e,A,"whitespace")(n):(e.enter("data"),_(n))}function _(t){return null===t||124===t||e5(t)?(e.exit("data"),A(t)):(e.consume(t),92===t?k:_)}function k(t){return 92===t||124===t?(e.consume(t),_):_(t)}}function ae(e,t){let n,r,a,i=-1,s=!0,o=0,l=[0,0,0,0],c=[0,0,0,0],u=!1,h=0,d=new r9;for(;++i<e.length;){let p=e[i],m=p[1];"enter"===p[0]?"tableHead"===m.type?(u=!1,0!==h&&(an(d,t,h,n,r),r=void 0,h=0),n={type:"table",start:Object.assign({},m.start),end:Object.assign({},m.end)},d.add(i,0,[["enter",n,t]])):"tableRow"===m.type||"tableDelimiterRow"===m.type?(s=!0,a=void 0,l=[0,0,0,0],c=[0,i+1,0,0],u&&(u=!1,r={type:"tableBody",start:Object.assign({},m.start),end:Object.assign({},m.end)},d.add(i,0,[["enter",r,t]])),o="tableDelimiterRow"===m.type?2:r?3:1):o&&("data"===m.type||"tableDelimiterMarker"===m.type||"tableDelimiterFiller"===m.type)?(s=!1,0===c[2]&&(0!==l[1]&&(c[0]=c[1],a=at(d,t,l,o,void 0,a),l=[0,0,0,0]),c[2]=i)):"tableCellDivider"===m.type&&(s?s=!1:(0!==l[1]&&(c[0]=c[1],a=at(d,t,l,o,void 0,a)),c=[(l=c)[1],i,0,0])):"tableHead"===m.type?(u=!0,h=i):"tableRow"===m.type||"tableDelimiterRow"===m.type?(h=i,0!==l[1]?(c[0]=c[1],a=at(d,t,l,o,i,a)):0!==c[1]&&(a=at(d,t,c,o,i,a)),o=0):o&&("data"===m.type||"tableDelimiterMarker"===m.type||"tableDelimiterFiller"===m.type)&&(c[3]=i)}for(0!==h&&an(d,t,h,n,r),d.consume(t.events),i=-1;++i<t.events.length;){let e=t.events[i];"enter"===e[0]&&"table"===e[1].type&&(e[1]._align=function(e,t){let n=!1,r=[];for(;t<e.length;){let a=e[t];if(n){if("enter"===a[0])"tableContent"===a[1].type&&r.push("tableDelimiterMarker"===e[t+1][1].type?"left":"none");else if("tableContent"===a[1].type){if("tableDelimiterMarker"===e[t-1][1].type){let e=r.length-1;r[e]="left"===r[e]?"center":"right"}}else if("tableDelimiterRow"===a[1].type)break}else"enter"===a[0]&&"tableDelimiterRow"===a[1].type&&(n=!0);t+=1}return r}(t.events,i))}return e}function at(e,t,n,r,a,i){0!==n[0]&&(i.end=Object.assign({},ar(t.events,n[0])),e.add(n[0],0,[["exit",i,t]]));let s=ar(t.events,n[1]);if(i={type:1===r?"tableHeader":2===r?"tableDelimiter":"tableData",start:Object.assign({},s),end:Object.assign({},s)},e.add(n[1],0,[["enter",i,t]]),0!==n[2]){let a=ar(t.events,n[2]),i=ar(t.events,n[3]),s={type:"tableContent",start:Object.assign({},a),end:Object.assign({},i)};if(e.add(n[2],0,[["enter",s,t]]),2!==r){let r=t.events[n[2]],a=t.events[n[3]];if(r[1].end=Object.assign({},a[1].end),r[1].type="chunkText",r[1].contentType="text",n[3]>n[2]+1){let t=n[2]+1,r=n[3]-n[2]-1;e.add(t,r,[])}}e.add(n[3]+1,0,[["exit",s,t]])}return void 0!==a&&(i.end=Object.assign({},ar(t.events,a)),e.add(a,0,[["exit",i,t]]),i=void 0),i}function an(e,t,n,r,a){let i=[],s=ar(t.events,n);a&&(a.end=Object.assign({},s),i.push(["exit",a,t])),r.end=Object.assign({},s),i.push(["exit",r,t]),e.add(n+1,0,i)}function ar(e,t){let n=e[t],r="enter"===n[0]?"start":"end";return n[1][r]}let aa={name:"tasklistCheck",tokenize:function(e,t,n){let r=this;return function(t){return null===r.previous&&r._gfmTasklistFirstContentOfListItem?(e.enter("taskListCheck"),e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),a):n(t)};function a(t){return e5(t)?(e.enter("taskListCheckValueUnchecked"),e.consume(t),e.exit("taskListCheckValueUnchecked"),i):88===t||120===t?(e.enter("taskListCheckValueChecked"),e.consume(t),e.exit("taskListCheckValueChecked"),i):n(t)}function i(t){return 93===t?(e.enter("taskListCheckMarker"),e.consume(t),e.exit("taskListCheckMarker"),e.exit("taskListCheck"),s):n(t)}function s(r){return e3(r)?t(r):e4(r)?e.check({tokenize:ai},t,n)(r):n(r)}}};function ai(e,t,n){return e7(e,function(e){return null===e?n(e):t(e)},"whitespace")}let as={};function ao(e){let t;let n=e||as,r=this.data(),a=r.micromarkExtensions||(r.micromarkExtensions=[]),i=r.fromMarkdownExtensions||(r.fromMarkdownExtensions=[]),s=r.toMarkdownExtensions||(r.toMarkdownExtensions=[]);a.push(eK([{text:rQ},{document:{91:{name:"gfmFootnoteDefinition",tokenize:r4,continuation:{tokenize:r6},exit:r8}},text:{91:{name:"gfmFootnoteCall",tokenize:r5},93:{name:"gfmPotentialFootnoteCall",add:"after",tokenize:r2,resolveTo:r3}}},function(e){let t=(e||{}).singleTilde,n={name:"strikethrough",tokenize:function(e,n,r){let a=this.previous,i=this.events,s=0;return function(o){return 126===a&&"characterEscape"!==i[i.length-1][1].type?r(o):(e.enter("strikethroughSequenceTemporary"),function i(o){let l=tY(a);if(126===o)return s>1?r(o):(e.consume(o),s++,i);if(s<2&&!t)return r(o);let c=e.exit("strikethroughSequenceTemporary"),u=tY(o);return c._open=!u||2===u&&!!l,c._close=!l||2===l&&!!u,n(o)}(o))}},resolveAll:function(e,t){let n=-1;for(;++n<e.length;)if("enter"===e[n][0]&&"strikethroughSequenceTemporary"===e[n][1].type&&e[n][1]._close){let r=n;for(;r--;)if("exit"===e[r][0]&&"strikethroughSequenceTemporary"===e[r][1].type&&e[r][1]._open&&e[n][1].end.offset-e[n][1].start.offset==e[r][1].end.offset-e[r][1].start.offset){e[n][1].type="strikethroughSequence",e[r][1].type="strikethroughSequence";let a={type:"strikethrough",start:Object.assign({},e[r][1].start),end:Object.assign({},e[n][1].end)},i={type:"strikethroughText",start:Object.assign({},e[r][1].end),end:Object.assign({},e[n][1].start)},s=[["enter",a,t],["enter",e[r][1],t],["exit",e[r][1],t],["enter",i,t]],o=t.parser.constructs.insideSpan.null;o&&ez(s,s.length,0,tU(o,e.slice(r+1,n),t)),ez(s,s.length,0,[["exit",i,t],["enter",e[n][1],t],["exit",e[n][1],t],["exit",a,t]]),ez(e,r-1,n-r+3,s),n=r+s.length-2;break}}for(n=-1;++n<e.length;)"strikethroughSequenceTemporary"===e[n][1].type&&(e[n][1].type="data");return e}};return null==t&&(t=!0),{text:{126:n},insideSpan:{null:[n]},attentionMarkers:{null:[126]}}}(n),{flow:{null:{name:"table",tokenize:r7,resolveAll:ae}}},{text:{91:aa}}])),i.push([{transforms:[ro],enter:{literalAutolink:rt,literalAutolinkEmail:rn,literalAutolinkHttp:rn,literalAutolinkWww:rn},exit:{literalAutolink:rs,literalAutolinkEmail:ri,literalAutolinkHttp:rr,literalAutolinkWww:ra}},{enter:{gfmFootnoteCallString:rh,gfmFootnoteCall:rd,gfmFootnoteDefinitionLabelString:rp,gfmFootnoteDefinition:rm},exit:{gfmFootnoteCallString:rf,gfmFootnoteCall:rE,gfmFootnoteDefinitionLabelString:rT,gfmFootnoteDefinition:rg}},{canContainEols:["delete"],enter:{strikethrough:rb},exit:{strikethrough:ry}},{enter:{table:rL,tableData:rP,tableHeader:rP,tableRow:rR},exit:{codeText:rM,table:rx,tableData:rv,tableHeader:rv,tableRow:rv}},{exit:{taskListCheckValueChecked:rB,taskListCheckValueUnchecked:rB,paragraph:rF}}]),s.push({extensions:[{unsafe:[{character:"@",before:"[+\\-.\\w]",after:"[\\-.\\w]",inConstruct:n7,notInConstruct:re},{character:".",before:"[Ww]",after:"[\\-.\\w]",inConstruct:n7,notInConstruct:re},{character:":",before:"[ps]",after:"\\/",inConstruct:n7,notInConstruct:re}]},(t=!1,n&&n.firstLineBlank&&(t=!0),{handlers:{footnoteDefinition:function(e,n,r,a){let i=r.createTracker(a),s=i.move("[^"),o=r.enter("footnoteDefinition"),l=r.enter("label");return s+=i.move(r.safe(r.associationId(e),{before:s,after:"]"})),l(),s+=i.move("]:"),e.children&&e.children.length>0&&(i.shift(4),s+=i.move((t?"\n":" ")+r.indentLines(r.containerFlow(e,i.current()),t?rk:r_))),o(),s},footnoteReference:rA},unsafe:[{character:"[",inConstruct:["label","phrasing","reference"]}]}),{unsafe:[{character:"~",inConstruct:"phrasing",notInConstruct:rS}],handlers:{delete:rN}},function(e){let t=e||{},n=t.tableCellPadding,r=t.tablePipeAlign,a=t.stringLength,i=n?" ":"|";return{unsafe:[{character:"\r",inConstruct:"tableCell"},{character:"\n",inConstruct:"tableCell"},{atBreak:!0,character:"|",after:"[	 :-]"},{character:"|",inConstruct:"tableCell"},{atBreak:!0,character:":",after:"-"},{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{inlineCode:function(e,t,n){let r=rO.inlineCode(e,t,n);return n.stack.includes("tableCell")&&(r=r.replace(/\|/g,"\\$&")),r},table:function(e,t,n,r){return o(function(e,t,n){let r=e.children,a=-1,i=[],s=t.enter("table");for(;++a<r.length;)i[a]=l(r[a],t,n);return s(),i}(e,n,r),e.align)},tableCell:s,tableRow:function(e,t,n,r){let a=o([l(e,n,r)]);return a.slice(0,a.indexOf("\n"))}}};function s(e,t,n,r){let a=n.enter("tableCell"),s=n.enter("phrasing"),o=n.containerPhrasing(e,{...r,before:i,after:i});return s(),a(),o}function o(e,t){return function(e,t){let n=t||{},r=(n.align||[]).concat(),a=n.stringLength||rC,i=[],s=[],o=[],l=[],c=0,u=-1;for(;++u<e.length;){let t=[],r=[],i=-1;for(e[u].length>c&&(c=e[u].length);++i<e[u].length;){var h;let s=null==(h=e[u][i])?"":String(h);if(!1!==n.alignDelimiters){let e=a(s);r[i]=e,(void 0===l[i]||e>l[i])&&(l[i]=e)}t.push(s)}s[u]=t,o[u]=r}let d=-1;if("object"==typeof r&&"length"in r)for(;++d<c;)i[d]=rI(r[d]);else{let e=rI(r);for(;++d<c;)i[d]=e}d=-1;let p=[],m=[];for(;++d<c;){let e=i[d],t="",r="";99===e?(t=":",r=":"):108===e?t=":":114===e&&(r=":");let a=!1===n.alignDelimiters?1:Math.max(1,l[d]-t.length-r.length),s=t+"-".repeat(a)+r;!1!==n.alignDelimiters&&((a=t.length+a+r.length)>l[d]&&(l[d]=a),m[d]=a),p[d]=s}s.splice(1,0,p),o.splice(1,0,m),u=-1;let f=[];for(;++u<s.length;){let e=s[u],t=o[u];d=-1;let r=[];for(;++d<c;){let a=e[d]||"",s="",o="";if(!1!==n.alignDelimiters){let e=l[d]-(t[d]||0),n=i[d];114===n?s=" ".repeat(e):99===n?e%2?(s=" ".repeat(e/2+.5),o=" ".repeat(e/2-.5)):o=s=" ".repeat(e/2):o=" ".repeat(e)}!1===n.delimiterStart||d||r.push("|"),!1!==n.padding&&!(!1===n.alignDelimiters&&""===a)&&(!1!==n.delimiterStart||d)&&r.push(" "),!1!==n.alignDelimiters&&r.push(s),r.push(a),!1!==n.alignDelimiters&&r.push(o),!1!==n.padding&&r.push(" "),(!1!==n.delimiterEnd||d!==c-1)&&r.push("|")}f.push(!1===n.delimiterEnd?r.join("").replace(/ +$/,""):r.join(""))}return f.join("\n")}(e,{align:t,alignDelimiters:r,padding:n,stringLength:a})}function l(e,t,n){let r=e.children,a=-1,i=[],o=t.enter("tableRow");for(;++a<r.length;)i[a]=s(r[a],e,t,n);return o(),i}}(n),{unsafe:[{atBreak:!0,character:"-",after:"[:|-]"}],handlers:{listItem:rU}}]})}let al=/[#.]/g;function ac(e,t,n){let r=n?function(e){let t=new Map;for(let n of e)t.set(n.toLowerCase(),n);return t}(n):void 0;return function(n,a,...i){let s;if(null==n)s={type:"root",children:[]},i.unshift(a);else{let o=(s=function(e,t){let n,r;let a=e||"",i={},s=0;for(;s<a.length;){al.lastIndex=s;let e=al.exec(a),t=a.slice(s,e?e.index:a.length);t&&(n?"#"===n?i.id=t:Array.isArray(i.className)?i.className.push(t):i.className=[t]:r=t,s+=t.length),e&&(n=e[0],s++)}return{type:"element",tagName:r||t||"div",properties:i,children:[]}}(n,t)).tagName.toLowerCase(),l=r?r.get(o):void 0;if(s.tagName=l||o,function(e){if(null===e||"object"!=typeof e||Array.isArray(e))return!0;if("string"!=typeof e.type)return!1;for(let t of Object.keys(e)){let n=e[t];if(n&&"object"==typeof n){if(!Array.isArray(n))return!0;for(let e of n)if("number"!=typeof e&&"string"!=typeof e)return!0}}return!!("children"in e&&Array.isArray(e.children))}(a))i.unshift(a);else for(let[t,n]of Object.entries(a))(function(e,t,n,r){let a;let i=eu(e,n);if(null!=r){if("number"==typeof r){if(Number.isNaN(r))return;a=r}else a="boolean"==typeof r?r:"string"==typeof r?i.spaceSeparated?em(r):i.commaSeparated?I(r):i.commaOrSpaceSeparated?em(I(r).join(" ")):au(i,i.property,r):Array.isArray(r)?[...r]:"style"===i.property?function(e){let t=[];for(let[n,r]of Object.entries(e))t.push([n,r].join(": "));return t.join("; ")}(r):String(r);if(Array.isArray(a)){let e=[];for(let t of a)e.push(au(i,i.property,t));a=e}"className"===i.property&&Array.isArray(t.className)&&(a=t.className.concat(a)),t[i.property]=a}})(e,s.properties,t,n)}for(let e of i)(function e(t,n){if(null==n);else if("number"==typeof n||"string"==typeof n)t.push({type:"text",value:String(n)});else if(Array.isArray(n))for(let r of n)e(t,r);else if("object"==typeof n&&"type"in n)"root"===n.type?e(t,n.children):t.push(n);else throw Error("Expected node, nodes, or string, got `"+n+"`")})(s.children,e);return"element"===s.type&&"template"===s.tagName&&(s.content={type:"root",children:s.children},s.children=[]),s}}function au(e,t,n){if("string"==typeof n){if(e.number&&n&&!Number.isNaN(Number(n)))return Number(n);if((e.boolean||e.overloadedBoolean)&&(""===n||B(n)===B(t)))return!0}return n}let ah=ac(ei,"div"),ad=ac(es,"g",["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","solidColor","textArea","textPath"]);function ap(e,t){let n=e.indexOf("\r",t),r=e.indexOf("\n",t);return -1===r?n:-1===n||n+1===r?r:n<r?n:r}let am={html:"http://www.w3.org/1999/xhtml",mathml:"http://www.w3.org/1998/Math/MathML",svg:"http://www.w3.org/2000/svg",xlink:"http://www.w3.org/1999/xlink",xml:"http://www.w3.org/XML/1998/namespace",xmlns:"http://www.w3.org/2000/xmlns/"},af={}.hasOwnProperty,aE=Object.prototype;function aT(e,t){let n;switch(t.nodeName){case"#comment":return n={type:"comment",value:t.data},aA(e,t,n),n;case"#document":case"#document-fragment":{let r="mode"in t&&("quirks"===t.mode||"limited-quirks"===t.mode);if(n={type:"root",children:ag(e,t.childNodes),data:{quirksMode:r}},e.file&&e.location){let t=String(e.file),r=function(e){let t=String(e),n=[];return{toOffset:function(e){if(e&&"number"==typeof e.line&&"number"==typeof e.column&&!Number.isNaN(e.line)&&!Number.isNaN(e.column)){for(;n.length<e.line;){let e=n[n.length-1],r=ap(t,e),a=-1===r?t.length+1:r+1;if(e===a)break;n.push(a)}let r=(e.line>1?n[e.line-2]:0)+e.column-1;if(r<n[e.line-1])return r}},toPoint:function(e){if("number"==typeof e&&e>-1&&e<=t.length){let r=0;for(;;){let a=n[r];if(void 0===a){let e=ap(t,n[r-1]);a=-1===e?t.length+1:e+1,n[r]=a}if(a>e)return{line:r+1,column:e-(r>0?n[r-1]:0)+1,offset:e};r++}}}}}(t),a=r.toPoint(0),i=r.toPoint(t.length);n.position={start:a,end:i}}return n}case"#documentType":return aA(e,t,n={type:"doctype"}),n;case"#text":return n={type:"text",value:t.value},aA(e,t,n),n;default:return function(e,t){let n=e.schema;e.schema=t.namespaceURI===am.svg?es:ei;let r=-1,a={};for(;++r<t.attrs.length;){let e=t.attrs[r],n=(e.prefix?e.prefix+":":"")+e.name;af.call(aE,n)||(a[n]=e.value)}let i=("svg"===e.schema.space?ad:ah)(t.tagName,a,ag(e,t.childNodes));if(aA(e,t,i),"template"===i.tagName){let n=t.sourceCodeLocation,r=n&&n.startTag&&a_(n.startTag),a=n&&n.endTag&&a_(n.endTag),s=aT(e,t.content);r&&a&&e.file&&(s.position={start:r.end,end:a.start}),i.content=s}return e.schema=n,i}(e,t)}}function ag(e,t){let n=-1,r=[];for(;++n<t.length;){let a=aT(e,t[n]);r.push(a)}return r}function aA(e,t,n){if("sourceCodeLocation"in t&&t.sourceCodeLocation&&e.file){let r=function(e,t,n){let r=a_(n);if("element"===t.type){let a=t.children[t.children.length-1];if(r&&!n.endTag&&a&&a.position&&a.position.end&&(r.end=Object.assign({},a.position.end)),e.verbose){let r;let a={};if(n.attrs)for(r in n.attrs)af.call(n.attrs,r)&&(a[eu(e.schema,r).property]=a_(n.attrs[r]));n.startTag;let i=a_(n.startTag),s=n.endTag?a_(n.endTag):void 0,o={opening:i};s&&(o.closing=s),o.properties=a,t.data={position:o}}}return r}(e,n,t.sourceCodeLocation);r&&(e.location=!0,n.position=r)}}function a_(e){let t=ak({line:e.startLine,column:e.startCol,offset:e.startOffset}),n=ak({line:e.endLine,column:e.endCol,offset:e.endOffset});return t||n?{start:t,end:n}:void 0}function ak(e){return e.line&&e.column?e:void 0}class aS{constructor(e,t,n){this.property=e,this.normal=t,n&&(this.space=n)}}function ab(e,t){let n={},r={},a=-1;for(;++a<e.length;)Object.assign(n,e[a].property),Object.assign(r,e[a].normal);return new aS(n,r,t)}function ay(e){return e.toLowerCase()}aS.prototype.property={},aS.prototype.normal={},aS.prototype.space=null;class aN{constructor(e,t){this.property=e,this.attribute=t}}aN.prototype.space=null,aN.prototype.boolean=!1,aN.prototype.booleanish=!1,aN.prototype.overloadedBoolean=!1,aN.prototype.number=!1,aN.prototype.commaSeparated=!1,aN.prototype.spaceSeparated=!1,aN.prototype.commaOrSpaceSeparated=!1,aN.prototype.mustUseProperty=!1,aN.prototype.defined=!1;let aC=0,aI=aP(),aD=aP(),aO=aP(),aL=aP(),ax=aP(),aR=aP(),av=aP();function aP(){return 2**++aC}let aM=Object.keys(k);class aw extends aN{constructor(e,t,n,r){let a=-1;if(super(e,t),function(e,t,n){n&&(e[t]=n)}(this,"space",r),"number"==typeof n)for(;++a<aM.length;){let e=aM[a];(function(e,t,n){n&&(e[t]=n)})(this,aM[a],(n&k[e])===k[e])}}}aw.prototype.defined=!0;let aB={}.hasOwnProperty;function aF(e){let t;let n={},r={};for(t in e.properties)if(aB.call(e.properties,t)){let a=e.properties[t],i=new aw(t,e.transform(e.attributes||{},t),a,e.space);e.mustUseProperty&&e.mustUseProperty.includes(t)&&(i.mustUseProperty=!0),n[t]=i,r[ay(t)]=t,r[ay(i.attribute)]=t}return new aS(n,r,e.space)}let aU=aF({space:"xlink",transform:(e,t)=>"xlink:"+t.slice(5).toLowerCase(),properties:{xLinkActuate:null,xLinkArcRole:null,xLinkHref:null,xLinkRole:null,xLinkShow:null,xLinkTitle:null,xLinkType:null}}),aH=aF({space:"xml",transform:(e,t)=>"xml:"+t.slice(3).toLowerCase(),properties:{xmlLang:null,xmlBase:null,xmlSpace:null}});function aG(e,t){return t in e?e[t]:t}function aq(e,t){return aG(e,t.toLowerCase())}let az=aF({space:"xmlns",attributes:{xmlnsxlink:"xmlns:xlink"},transform:aq,properties:{xmlns:null,xmlnsXLink:null}}),aj=aF({transform:(e,t)=>"role"===t?t:"aria-"+t.slice(4).toLowerCase(),properties:{ariaActiveDescendant:null,ariaAtomic:aD,ariaAutoComplete:null,ariaBusy:aD,ariaChecked:aD,ariaColCount:aL,ariaColIndex:aL,ariaColSpan:aL,ariaControls:ax,ariaCurrent:null,ariaDescribedBy:ax,ariaDetails:null,ariaDisabled:aD,ariaDropEffect:ax,ariaErrorMessage:null,ariaExpanded:aD,ariaFlowTo:ax,ariaGrabbed:aD,ariaHasPopup:null,ariaHidden:aD,ariaInvalid:null,ariaKeyShortcuts:null,ariaLabel:null,ariaLabelledBy:ax,ariaLevel:aL,ariaLive:null,ariaModal:aD,ariaMultiLine:aD,ariaMultiSelectable:aD,ariaOrientation:null,ariaOwns:ax,ariaPlaceholder:null,ariaPosInSet:aL,ariaPressed:aD,ariaReadOnly:aD,ariaRelevant:null,ariaRequired:aD,ariaRoleDescription:ax,ariaRowCount:aL,ariaRowIndex:aL,ariaRowSpan:aL,ariaSelected:aD,ariaSetSize:aL,ariaSort:null,ariaValueMax:aL,ariaValueMin:aL,ariaValueNow:aL,ariaValueText:null,role:null}}),aY=aF({space:"html",attributes:{acceptcharset:"accept-charset",classname:"class",htmlfor:"for",httpequiv:"http-equiv"},transform:aq,mustUseProperty:["checked","multiple","muted","selected"],properties:{abbr:null,accept:aR,acceptCharset:ax,accessKey:ax,action:null,allow:null,allowFullScreen:aI,allowPaymentRequest:aI,allowUserMedia:aI,alt:null,as:null,async:aI,autoCapitalize:null,autoComplete:ax,autoFocus:aI,autoPlay:aI,blocking:ax,capture:null,charSet:null,checked:aI,cite:null,className:ax,cols:aL,colSpan:null,content:null,contentEditable:aD,controls:aI,controlsList:ax,coords:aL|aR,crossOrigin:null,data:null,dateTime:null,decoding:null,default:aI,defer:aI,dir:null,dirName:null,disabled:aI,download:aO,draggable:aD,encType:null,enterKeyHint:null,fetchPriority:null,form:null,formAction:null,formEncType:null,formMethod:null,formNoValidate:aI,formTarget:null,headers:ax,height:aL,hidden:aI,high:aL,href:null,hrefLang:null,htmlFor:ax,httpEquiv:ax,id:null,imageSizes:null,imageSrcSet:null,inert:aI,inputMode:null,integrity:null,is:null,isMap:aI,itemId:null,itemProp:ax,itemRef:ax,itemScope:aI,itemType:ax,kind:null,label:null,lang:null,language:null,list:null,loading:null,loop:aI,low:aL,manifest:null,max:null,maxLength:aL,media:null,method:null,min:null,minLength:aL,multiple:aI,muted:aI,name:null,nonce:null,noModule:aI,noValidate:aI,onAbort:null,onAfterPrint:null,onAuxClick:null,onBeforeMatch:null,onBeforePrint:null,onBeforeToggle:null,onBeforeUnload:null,onBlur:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onContextLost:null,onContextMenu:null,onContextRestored:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnded:null,onError:null,onFocus:null,onFormData:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLanguageChange:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadEnd:null,onLoadStart:null,onMessage:null,onMessageError:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRejectionHandled:null,onReset:null,onResize:null,onScroll:null,onScrollEnd:null,onSecurityPolicyViolation:null,onSeeked:null,onSeeking:null,onSelect:null,onSlotChange:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnhandledRejection:null,onUnload:null,onVolumeChange:null,onWaiting:null,onWheel:null,open:aI,optimum:aL,pattern:null,ping:ax,placeholder:null,playsInline:aI,popover:null,popoverTarget:null,popoverTargetAction:null,poster:null,preload:null,readOnly:aI,referrerPolicy:null,rel:ax,required:aI,reversed:aI,rows:aL,rowSpan:aL,sandbox:ax,scope:null,scoped:aI,seamless:aI,selected:aI,shadowRootClonable:aI,shadowRootDelegatesFocus:aI,shadowRootMode:null,shape:null,size:aL,sizes:null,slot:null,span:aL,spellCheck:aD,src:null,srcDoc:null,srcLang:null,srcSet:null,start:aL,step:null,style:null,tabIndex:aL,target:null,title:null,translate:null,type:null,typeMustMatch:aI,useMap:null,value:aD,width:aL,wrap:null,writingSuggestions:null,align:null,aLink:null,archive:ax,axis:null,background:null,bgColor:null,border:aL,borderColor:null,bottomMargin:aL,cellPadding:null,cellSpacing:null,char:null,charOff:null,classId:null,clear:null,code:null,codeBase:null,codeType:null,color:null,compact:aI,declare:aI,event:null,face:null,frame:null,frameBorder:null,hSpace:aL,leftMargin:aL,link:null,longDesc:null,lowSrc:null,marginHeight:aL,marginWidth:aL,noResize:aI,noHref:aI,noShade:aI,noWrap:aI,object:null,profile:null,prompt:null,rev:null,rightMargin:aL,rules:null,scheme:null,scrolling:aD,standby:null,summary:null,text:null,topMargin:aL,valueType:null,version:null,vAlign:null,vLink:null,vSpace:aL,allowTransparency:null,autoCorrect:null,autoSave:null,disablePictureInPicture:aI,disableRemotePlayback:aI,prefix:null,property:null,results:aL,security:null,unselectable:null}}),aV=aF({space:"svg",attributes:{accentHeight:"accent-height",alignmentBaseline:"alignment-baseline",arabicForm:"arabic-form",baselineShift:"baseline-shift",capHeight:"cap-height",className:"class",clipPath:"clip-path",clipRule:"clip-rule",colorInterpolation:"color-interpolation",colorInterpolationFilters:"color-interpolation-filters",colorProfile:"color-profile",colorRendering:"color-rendering",crossOrigin:"crossorigin",dataType:"datatype",dominantBaseline:"dominant-baseline",enableBackground:"enable-background",fillOpacity:"fill-opacity",fillRule:"fill-rule",floodColor:"flood-color",floodOpacity:"flood-opacity",fontFamily:"font-family",fontSize:"font-size",fontSizeAdjust:"font-size-adjust",fontStretch:"font-stretch",fontStyle:"font-style",fontVariant:"font-variant",fontWeight:"font-weight",glyphName:"glyph-name",glyphOrientationHorizontal:"glyph-orientation-horizontal",glyphOrientationVertical:"glyph-orientation-vertical",hrefLang:"hreflang",horizAdvX:"horiz-adv-x",horizOriginX:"horiz-origin-x",horizOriginY:"horiz-origin-y",imageRendering:"image-rendering",letterSpacing:"letter-spacing",lightingColor:"lighting-color",markerEnd:"marker-end",markerMid:"marker-mid",markerStart:"marker-start",navDown:"nav-down",navDownLeft:"nav-down-left",navDownRight:"nav-down-right",navLeft:"nav-left",navNext:"nav-next",navPrev:"nav-prev",navRight:"nav-right",navUp:"nav-up",navUpLeft:"nav-up-left",navUpRight:"nav-up-right",onAbort:"onabort",onActivate:"onactivate",onAfterPrint:"onafterprint",onBeforePrint:"onbeforeprint",onBegin:"onbegin",onCancel:"oncancel",onCanPlay:"oncanplay",onCanPlayThrough:"oncanplaythrough",onChange:"onchange",onClick:"onclick",onClose:"onclose",onCopy:"oncopy",onCueChange:"oncuechange",onCut:"oncut",onDblClick:"ondblclick",onDrag:"ondrag",onDragEnd:"ondragend",onDragEnter:"ondragenter",onDragExit:"ondragexit",onDragLeave:"ondragleave",onDragOver:"ondragover",onDragStart:"ondragstart",onDrop:"ondrop",onDurationChange:"ondurationchange",onEmptied:"onemptied",onEnd:"onend",onEnded:"onended",onError:"onerror",onFocus:"onfocus",onFocusIn:"onfocusin",onFocusOut:"onfocusout",onHashChange:"onhashchange",onInput:"oninput",onInvalid:"oninvalid",onKeyDown:"onkeydown",onKeyPress:"onkeypress",onKeyUp:"onkeyup",onLoad:"onload",onLoadedData:"onloadeddata",onLoadedMetadata:"onloadedmetadata",onLoadStart:"onloadstart",onMessage:"onmessage",onMouseDown:"onmousedown",onMouseEnter:"onmouseenter",onMouseLeave:"onmouseleave",onMouseMove:"onmousemove",onMouseOut:"onmouseout",onMouseOver:"onmouseover",onMouseUp:"onmouseup",onMouseWheel:"onmousewheel",onOffline:"onoffline",onOnline:"ononline",onPageHide:"onpagehide",onPageShow:"onpageshow",onPaste:"onpaste",onPause:"onpause",onPlay:"onplay",onPlaying:"onplaying",onPopState:"onpopstate",onProgress:"onprogress",onRateChange:"onratechange",onRepeat:"onrepeat",onReset:"onreset",onResize:"onresize",onScroll:"onscroll",onSeeked:"onseeked",onSeeking:"onseeking",onSelect:"onselect",onShow:"onshow",onStalled:"onstalled",onStorage:"onstorage",onSubmit:"onsubmit",onSuspend:"onsuspend",onTimeUpdate:"ontimeupdate",onToggle:"ontoggle",onUnload:"onunload",onVolumeChange:"onvolumechange",onWaiting:"onwaiting",onZoom:"onzoom",overlinePosition:"overline-position",overlineThickness:"overline-thickness",paintOrder:"paint-order",panose1:"panose-1",pointerEvents:"pointer-events",referrerPolicy:"referrerpolicy",renderingIntent:"rendering-intent",shapeRendering:"shape-rendering",stopColor:"stop-color",stopOpacity:"stop-opacity",strikethroughPosition:"strikethrough-position",strikethroughThickness:"strikethrough-thickness",strokeDashArray:"stroke-dasharray",strokeDashOffset:"stroke-dashoffset",strokeLineCap:"stroke-linecap",strokeLineJoin:"stroke-linejoin",strokeMiterLimit:"stroke-miterlimit",strokeOpacity:"stroke-opacity",strokeWidth:"stroke-width",tabIndex:"tabindex",textAnchor:"text-anchor",textDecoration:"text-decoration",textRendering:"text-rendering",transformOrigin:"transform-origin",typeOf:"typeof",underlinePosition:"underline-position",underlineThickness:"underline-thickness",unicodeBidi:"unicode-bidi",unicodeRange:"unicode-range",unitsPerEm:"units-per-em",vAlphabetic:"v-alphabetic",vHanging:"v-hanging",vIdeographic:"v-ideographic",vMathematical:"v-mathematical",vectorEffect:"vector-effect",vertAdvY:"vert-adv-y",vertOriginX:"vert-origin-x",vertOriginY:"vert-origin-y",wordSpacing:"word-spacing",writingMode:"writing-mode",xHeight:"x-height",playbackOrder:"playbackorder",timelineBegin:"timelinebegin"},transform:aG,properties:{about:av,accentHeight:aL,accumulate:null,additive:null,alignmentBaseline:null,alphabetic:aL,amplitude:aL,arabicForm:null,ascent:aL,attributeName:null,attributeType:null,azimuth:aL,bandwidth:null,baselineShift:null,baseFrequency:null,baseProfile:null,bbox:null,begin:null,bias:aL,by:null,calcMode:null,capHeight:aL,className:ax,clip:null,clipPath:null,clipPathUnits:null,clipRule:null,color:null,colorInterpolation:null,colorInterpolationFilters:null,colorProfile:null,colorRendering:null,content:null,contentScriptType:null,contentStyleType:null,crossOrigin:null,cursor:null,cx:null,cy:null,d:null,dataType:null,defaultAction:null,descent:aL,diffuseConstant:aL,direction:null,display:null,dur:null,divisor:aL,dominantBaseline:null,download:aI,dx:null,dy:null,edgeMode:null,editable:null,elevation:aL,enableBackground:null,end:null,event:null,exponent:aL,externalResourcesRequired:null,fill:null,fillOpacity:aL,fillRule:null,filter:null,filterRes:null,filterUnits:null,floodColor:null,floodOpacity:null,focusable:null,focusHighlight:null,fontFamily:null,fontSize:null,fontSizeAdjust:null,fontStretch:null,fontStyle:null,fontVariant:null,fontWeight:null,format:null,fr:null,from:null,fx:null,fy:null,g1:aR,g2:aR,glyphName:aR,glyphOrientationHorizontal:null,glyphOrientationVertical:null,glyphRef:null,gradientTransform:null,gradientUnits:null,handler:null,hanging:aL,hatchContentUnits:null,hatchUnits:null,height:null,href:null,hrefLang:null,horizAdvX:aL,horizOriginX:aL,horizOriginY:aL,id:null,ideographic:aL,imageRendering:null,initialVisibility:null,in:null,in2:null,intercept:aL,k:aL,k1:aL,k2:aL,k3:aL,k4:aL,kernelMatrix:av,kernelUnitLength:null,keyPoints:null,keySplines:null,keyTimes:null,kerning:null,lang:null,lengthAdjust:null,letterSpacing:null,lightingColor:null,limitingConeAngle:aL,local:null,markerEnd:null,markerMid:null,markerStart:null,markerHeight:null,markerUnits:null,markerWidth:null,mask:null,maskContentUnits:null,maskUnits:null,mathematical:null,max:null,media:null,mediaCharacterEncoding:null,mediaContentEncodings:null,mediaSize:aL,mediaTime:null,method:null,min:null,mode:null,name:null,navDown:null,navDownLeft:null,navDownRight:null,navLeft:null,navNext:null,navPrev:null,navRight:null,navUp:null,navUpLeft:null,navUpRight:null,numOctaves:null,observer:null,offset:null,onAbort:null,onActivate:null,onAfterPrint:null,onBeforePrint:null,onBegin:null,onCancel:null,onCanPlay:null,onCanPlayThrough:null,onChange:null,onClick:null,onClose:null,onCopy:null,onCueChange:null,onCut:null,onDblClick:null,onDrag:null,onDragEnd:null,onDragEnter:null,onDragExit:null,onDragLeave:null,onDragOver:null,onDragStart:null,onDrop:null,onDurationChange:null,onEmptied:null,onEnd:null,onEnded:null,onError:null,onFocus:null,onFocusIn:null,onFocusOut:null,onHashChange:null,onInput:null,onInvalid:null,onKeyDown:null,onKeyPress:null,onKeyUp:null,onLoad:null,onLoadedData:null,onLoadedMetadata:null,onLoadStart:null,onMessage:null,onMouseDown:null,onMouseEnter:null,onMouseLeave:null,onMouseMove:null,onMouseOut:null,onMouseOver:null,onMouseUp:null,onMouseWheel:null,onOffline:null,onOnline:null,onPageHide:null,onPageShow:null,onPaste:null,onPause:null,onPlay:null,onPlaying:null,onPopState:null,onProgress:null,onRateChange:null,onRepeat:null,onReset:null,onResize:null,onScroll:null,onSeeked:null,onSeeking:null,onSelect:null,onShow:null,onStalled:null,onStorage:null,onSubmit:null,onSuspend:null,onTimeUpdate:null,onToggle:null,onUnload:null,onVolumeChange:null,onWaiting:null,onZoom:null,opacity:null,operator:null,order:null,orient:null,orientation:null,origin:null,overflow:null,overlay:null,overlinePosition:aL,overlineThickness:aL,paintOrder:null,panose1:null,path:null,pathLength:aL,patternContentUnits:null,patternTransform:null,patternUnits:null,phase:null,ping:ax,pitch:null,playbackOrder:null,pointerEvents:null,points:null,pointsAtX:aL,pointsAtY:aL,pointsAtZ:aL,preserveAlpha:null,preserveAspectRatio:null,primitiveUnits:null,propagate:null,property:av,r:null,radius:null,referrerPolicy:null,refX:null,refY:null,rel:av,rev:av,renderingIntent:null,repeatCount:null,repeatDur:null,requiredExtensions:av,requiredFeatures:av,requiredFonts:av,requiredFormats:av,resource:null,restart:null,result:null,rotate:null,rx:null,ry:null,scale:null,seed:null,shapeRendering:null,side:null,slope:null,snapshotTime:null,specularConstant:aL,specularExponent:aL,spreadMethod:null,spacing:null,startOffset:null,stdDeviation:null,stemh:null,stemv:null,stitchTiles:null,stopColor:null,stopOpacity:null,strikethroughPosition:aL,strikethroughThickness:aL,string:null,stroke:null,strokeDashArray:av,strokeDashOffset:null,strokeLineCap:null,strokeLineJoin:null,strokeMiterLimit:aL,strokeOpacity:aL,strokeWidth:null,style:null,surfaceScale:aL,syncBehavior:null,syncBehaviorDefault:null,syncMaster:null,syncTolerance:null,syncToleranceDefault:null,systemLanguage:av,tabIndex:aL,tableValues:null,target:null,targetX:aL,targetY:aL,textAnchor:null,textDecoration:null,textRendering:null,textLength:null,timelineBegin:null,title:null,transformBehavior:null,type:null,typeOf:av,to:null,transform:null,transformOrigin:null,u1:null,u2:null,underlinePosition:aL,underlineThickness:aL,unicode:null,unicodeBidi:null,unicodeRange:null,unitsPerEm:aL,values:null,vAlphabetic:aL,vMathematical:aL,vectorEffect:null,vHanging:aL,vIdeographic:aL,version:null,vertAdvY:aL,vertOriginX:aL,vertOriginY:aL,viewBox:null,viewTarget:null,visibility:null,width:null,widths:null,wordSpacing:null,writingMode:null,x:null,x1:null,x2:null,xChannelSelector:null,xHeight:aL,y:null,y1:null,y2:null,yChannelSelector:null,z:null,zoomAndPan:null}}),aW=ab([aH,aU,az,aj,aY],"html"),aQ=ab([aH,aU,az,aj,aV],"svg"),aK=/^data[-\w.:]+$/i,aX=/-[a-z]/g,a$=/[A-Z]/g;function aZ(e){return"-"+e.toLowerCase()}function aJ(e){return e.charAt(1).toUpperCase()}let a0={}.hasOwnProperty;function a1(e,t){let n=t||{};function r(t,...n){let a=r.invalid,i=r.handlers;if(t&&a0.call(t,e)){let n=String(t[e]);a=a0.call(i,n)?i[n]:r.unknown}if(a)return a.call(this,t,...n)}return r.handlers=n.handlers||{},r.invalid=n.invalid,r.unknown=n.unknown,r}let a2={}.hasOwnProperty,a3=a1("type",{handlers:{root:function(e,t){let n={nodeName:"#document",mode:(e.data||{}).quirksMode?"quirks":"no-quirks",childNodes:[]};return n.childNodes=a5(e.children,n,t),a4(e,n),n},element:function(e,t){let n;let r=t;"element"===e.type&&"svg"===e.tagName.toLowerCase()&&"html"===t.space&&(r=aQ);let a=[];if(e.properties){for(n in e.properties)if("children"!==n&&a2.call(e.properties,n)){let t=function(e,t,n){let r=function(e,t){let n=ay(t),r=t,a=aN;if(n in e.normal)return e.property[e.normal[n]];if(n.length>4&&"data"===n.slice(0,4)&&aK.test(t)){if("-"===t.charAt(4)){let e=t.slice(5).replace(aX,aJ);r="data"+e.charAt(0).toUpperCase()+e.slice(1)}else{let e=t.slice(4);if(!aX.test(e)){let n=e.replace(a$,aZ);"-"!==n.charAt(0)&&(n="-"+n),t="data"+n}}a=aw}return new a(r,t)}(e,t);if(!1===n||null==n||"number"==typeof n&&Number.isNaN(n)||!n&&r.boolean)return;Array.isArray(n)&&(n=r.commaSeparated?D(n):ef(n));let a={name:r.attribute,value:!0===n?"":String(n)};if(r.space&&"html"!==r.space&&"svg"!==r.space){let e=a.name.indexOf(":");e<0?a.prefix="":(a.name=a.name.slice(e+1),a.prefix=r.attribute.slice(0,e)),a.namespace=am[r.space]}return a}(r,n,e.properties[n]);t&&a.push(t)}}let i=r.space,s={nodeName:e.tagName,tagName:e.tagName,attrs:a,namespaceURI:am[i],childNodes:[],parentNode:null};return s.childNodes=a5(e.children,s,r),a4(e,s),"template"===e.tagName&&e.content&&(s.content=function(e,t){let n={nodeName:"#document-fragment",childNodes:[]};return n.childNodes=a5(e.children,n,t),a4(e,n),n}(e.content,r)),s},text:function(e){let t={nodeName:"#text",value:e.value,parentNode:null};return a4(e,t),t},comment:function(e){let t={nodeName:"#comment",data:e.value,parentNode:null};return a4(e,t),t},doctype:function(e){let t={nodeName:"#documentType",name:"html",publicId:"",systemId:"",parentNode:null};return a4(e,t),t}}});function a5(e,t,n){let r=-1,a=[];if(e)for(;++r<e.length;){let i=a3(e[r],n);i.parentNode=t,a.push(i)}return a}function a4(e,t){let n=e.position;n&&n.start&&n.end&&(n.start.offset,n.end.offset,t.sourceCodeLocation={startLine:n.start.line,startCol:n.start.column,startOffset:n.start.offset,endLine:n.end.line,endCol:n.end.column,endOffset:n.end.offset})}let a6=["area","base","basefont","bgsound","br","col","command","embed","frame","hr","image","img","input","keygen","link","meta","param","source","track","wbr"],a8=new Set([65534,65535,131070,131071,196606,196607,262142,262143,327678,327679,393214,393215,458750,458751,524286,524287,589822,589823,655358,655359,720894,720895,786430,786431,851966,851967,917502,917503,983038,983039,1048574,1048575,1114110,1114111]);!function(e){e[e.EOF=-1]="EOF",e[e.NULL=0]="NULL",e[e.TABULATION=9]="TABULATION",e[e.CARRIAGE_RETURN=13]="CARRIAGE_RETURN",e[e.LINE_FEED=10]="LINE_FEED",e[e.FORM_FEED=12]="FORM_FEED",e[e.SPACE=32]="SPACE",e[e.EXCLAMATION_MARK=33]="EXCLAMATION_MARK",e[e.QUOTATION_MARK=34]="QUOTATION_MARK",e[e.AMPERSAND=38]="AMPERSAND",e[e.APOSTROPHE=39]="APOSTROPHE",e[e.HYPHEN_MINUS=45]="HYPHEN_MINUS",e[e.SOLIDUS=47]="SOLIDUS",e[e.DIGIT_0=48]="DIGIT_0",e[e.DIGIT_9=57]="DIGIT_9",e[e.SEMICOLON=59]="SEMICOLON",e[e.LESS_THAN_SIGN=60]="LESS_THAN_SIGN",e[e.EQUALS_SIGN=61]="EQUALS_SIGN",e[e.GREATER_THAN_SIGN=62]="GREATER_THAN_SIGN",e[e.QUESTION_MARK=63]="QUESTION_MARK",e[e.LATIN_CAPITAL_A=65]="LATIN_CAPITAL_A",e[e.LATIN_CAPITAL_Z=90]="LATIN_CAPITAL_Z",e[e.RIGHT_SQUARE_BRACKET=93]="RIGHT_SQUARE_BRACKET",e[e.GRAVE_ACCENT=96]="GRAVE_ACCENT",e[e.LATIN_SMALL_A=97]="LATIN_SMALL_A",e[e.LATIN_SMALL_Z=122]="LATIN_SMALL_Z"}(r||(r={}));let a9={DASH_DASH:"--",CDATA_START:"[CDATA[",DOCTYPE:"doctype",SCRIPT:"script",PUBLIC:"public",SYSTEM:"system"};function a7(e){return e>=55296&&e<=57343}function ie(e){return 32!==e&&10!==e&&13!==e&&9!==e&&12!==e&&e>=1&&e<=31||e>=127&&e<=159}function it(e){return e>=64976&&e<=65007||a8.has(e)}!function(e){e.controlCharacterInInputStream="control-character-in-input-stream",e.noncharacterInInputStream="noncharacter-in-input-stream",e.surrogateInInputStream="surrogate-in-input-stream",e.nonVoidHtmlElementStartTagWithTrailingSolidus="non-void-html-element-start-tag-with-trailing-solidus",e.endTagWithAttributes="end-tag-with-attributes",e.endTagWithTrailingSolidus="end-tag-with-trailing-solidus",e.unexpectedSolidusInTag="unexpected-solidus-in-tag",e.unexpectedNullCharacter="unexpected-null-character",e.unexpectedQuestionMarkInsteadOfTagName="unexpected-question-mark-instead-of-tag-name",e.invalidFirstCharacterOfTagName="invalid-first-character-of-tag-name",e.unexpectedEqualsSignBeforeAttributeName="unexpected-equals-sign-before-attribute-name",e.missingEndTagName="missing-end-tag-name",e.unexpectedCharacterInAttributeName="unexpected-character-in-attribute-name",e.unknownNamedCharacterReference="unknown-named-character-reference",e.missingSemicolonAfterCharacterReference="missing-semicolon-after-character-reference",e.unexpectedCharacterAfterDoctypeSystemIdentifier="unexpected-character-after-doctype-system-identifier",e.unexpectedCharacterInUnquotedAttributeValue="unexpected-character-in-unquoted-attribute-value",e.eofBeforeTagName="eof-before-tag-name",e.eofInTag="eof-in-tag",e.missingAttributeValue="missing-attribute-value",e.missingWhitespaceBetweenAttributes="missing-whitespace-between-attributes",e.missingWhitespaceAfterDoctypePublicKeyword="missing-whitespace-after-doctype-public-keyword",e.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers="missing-whitespace-between-doctype-public-and-system-identifiers",e.missingWhitespaceAfterDoctypeSystemKeyword="missing-whitespace-after-doctype-system-keyword",e.missingQuoteBeforeDoctypePublicIdentifier="missing-quote-before-doctype-public-identifier",e.missingQuoteBeforeDoctypeSystemIdentifier="missing-quote-before-doctype-system-identifier",e.missingDoctypePublicIdentifier="missing-doctype-public-identifier",e.missingDoctypeSystemIdentifier="missing-doctype-system-identifier",e.abruptDoctypePublicIdentifier="abrupt-doctype-public-identifier",e.abruptDoctypeSystemIdentifier="abrupt-doctype-system-identifier",e.cdataInHtmlContent="cdata-in-html-content",e.incorrectlyOpenedComment="incorrectly-opened-comment",e.eofInScriptHtmlCommentLikeText="eof-in-script-html-comment-like-text",e.eofInDoctype="eof-in-doctype",e.nestedComment="nested-comment",e.abruptClosingOfEmptyComment="abrupt-closing-of-empty-comment",e.eofInComment="eof-in-comment",e.incorrectlyClosedComment="incorrectly-closed-comment",e.eofInCdata="eof-in-cdata",e.absenceOfDigitsInNumericCharacterReference="absence-of-digits-in-numeric-character-reference",e.nullCharacterReference="null-character-reference",e.surrogateCharacterReference="surrogate-character-reference",e.characterReferenceOutsideUnicodeRange="character-reference-outside-unicode-range",e.controlCharacterReference="control-character-reference",e.noncharacterCharacterReference="noncharacter-character-reference",e.missingWhitespaceBeforeDoctypeName="missing-whitespace-before-doctype-name",e.missingDoctypeName="missing-doctype-name",e.invalidCharacterSequenceAfterDoctypeName="invalid-character-sequence-after-doctype-name",e.duplicateAttribute="duplicate-attribute",e.nonConformingDoctype="non-conforming-doctype",e.missingDoctype="missing-doctype",e.misplacedDoctype="misplaced-doctype",e.endTagWithoutMatchingOpenElement="end-tag-without-matching-open-element",e.closingOfElementWithOpenChildElements="closing-of-element-with-open-child-elements",e.disallowedContentInNoscriptInHead="disallowed-content-in-noscript-in-head",e.openElementsLeftAfterEof="open-elements-left-after-eof",e.abandonedHeadElementChild="abandoned-head-element-child",e.misplacedStartTagForHeadElement="misplaced-start-tag-for-head-element",e.nestedNoscriptInHead="nested-noscript-in-head",e.eofInElementThatCanContainOnlyText="eof-in-element-that-can-contain-only-text"}(a||(a={}));class ir{constructor(e){this.handler=e,this.html="",this.pos=-1,this.lastGapPos=-2,this.gapStack=[],this.skipNextNewLine=!1,this.lastChunkWritten=!1,this.endOfChunkHit=!1,this.bufferWaterline=65536,this.isEol=!1,this.lineStartPos=0,this.droppedBufferSize=0,this.line=1,this.lastErrOffset=-1}get col(){return this.pos-this.lineStartPos+Number(this.lastGapPos!==this.pos)}get offset(){return this.droppedBufferSize+this.pos}getError(e,t){let{line:n,col:r,offset:a}=this,i=r+t,s=a+t;return{code:e,startLine:n,endLine:n,startCol:i,endCol:i,startOffset:s,endOffset:s}}_err(e){this.handler.onParseError&&this.lastErrOffset!==this.offset&&(this.lastErrOffset=this.offset,this.handler.onParseError(this.getError(e,0)))}_addGap(){this.gapStack.push(this.lastGapPos),this.lastGapPos=this.pos}_processSurrogate(e){if(this.pos!==this.html.length-1){let t=this.html.charCodeAt(this.pos+1);if(t>=56320&&t<=57343)return this.pos++,this._addGap(),(e-55296)*1024+9216+t}else if(!this.lastChunkWritten)return this.endOfChunkHit=!0,r.EOF;return this._err(a.surrogateInInputStream),e}willDropParsedChunk(){return this.pos>this.bufferWaterline}dropParsedChunk(){this.willDropParsedChunk()&&(this.html=this.html.substring(this.pos),this.lineStartPos-=this.pos,this.droppedBufferSize+=this.pos,this.pos=0,this.lastGapPos=-2,this.gapStack.length=0)}write(e,t){this.html.length>0?this.html+=e:this.html=e,this.endOfChunkHit=!1,this.lastChunkWritten=t}insertHtmlAtCurrentPos(e){this.html=this.html.substring(0,this.pos+1)+e+this.html.substring(this.pos+1),this.endOfChunkHit=!1}startsWith(e,t){if(this.pos+e.length>this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,!1;if(t)return this.html.startsWith(e,this.pos);for(let t=0;t<e.length;t++)if((32|this.html.charCodeAt(this.pos+t))!==e.charCodeAt(t))return!1;return!0}peek(e){let t=this.pos+e;if(t>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,r.EOF;let n=this.html.charCodeAt(t);return n===r.CARRIAGE_RETURN?r.LINE_FEED:n}advance(){if(this.pos++,this.isEol&&(this.isEol=!1,this.line++,this.lineStartPos=this.pos),this.pos>=this.html.length)return this.endOfChunkHit=!this.lastChunkWritten,r.EOF;let e=this.html.charCodeAt(this.pos);return e===r.CARRIAGE_RETURN?(this.isEol=!0,this.skipNextNewLine=!0,r.LINE_FEED):e===r.LINE_FEED&&(this.isEol=!0,this.skipNextNewLine)?(this.line--,this.skipNextNewLine=!1,this._addGap(),this.advance()):(this.skipNextNewLine=!1,a7(e)&&(e=this._processSurrogate(e)),null===this.handler.onParseError||e>31&&e<127||e===r.LINE_FEED||e===r.CARRIAGE_RETURN||e>159&&e<64976||this._checkForProblematicCharacters(e),e)}_checkForProblematicCharacters(e){ie(e)?this._err(a.controlCharacterInInputStream):it(e)&&this._err(a.noncharacterInInputStream)}retreat(e){for(this.pos-=e;this.pos<this.lastGapPos;)this.lastGapPos=this.gapStack.pop(),this.pos--;this.isEol=!1}}function ia(e,t){for(let n=e.attrs.length-1;n>=0;n--)if(e.attrs[n].name===t)return e.attrs[n].value;return null}!function(e){e[e.CHARACTER=0]="CHARACTER",e[e.NULL_CHARACTER=1]="NULL_CHARACTER",e[e.WHITESPACE_CHARACTER=2]="WHITESPACE_CHARACTER",e[e.START_TAG=3]="START_TAG",e[e.END_TAG=4]="END_TAG",e[e.COMMENT=5]="COMMENT",e[e.DOCTYPE=6]="DOCTYPE",e[e.EOF=7]="EOF",e[e.HIBERNATION=8]="HIBERNATION"}(i||(i={}));let ii=new Uint16Array('ᵁ<\xd5ıʊҝջאٵ۞ޢߖࠏ੊ઑඡ๭༉༦჊ረዡᐕᒝᓃᓟᔥ\0\0\0\0\0\0ᕫᛍᦍᰒᷝ὾⁠↰⊍⏀⏻⑂⠤⤒ⴈ⹈⿎〖㊺㘹㞬㣾㨨㩱㫠㬮ࠀEMabcfglmnoprstu\\bfms\x7f\x84\x8b\x90\x95\x98\xa6\xb3\xb9\xc8\xcflig耻\xc6䃆P耻&䀦cute耻\xc1䃁reve;䄂Āiyx}rc耻\xc2䃂;䐐r;쀀\ud835\udd04rave耻\xc0䃀pha;䎑acr;䄀d;橓Āgp\x9d\xa1on;䄄f;쀀\ud835\udd38plyFunction;恡ing耻\xc5䃅Ācs\xbe\xc3r;쀀\ud835\udc9cign;扔ilde耻\xc3䃃ml耻\xc4䃄Ѐaceforsu\xe5\xfb\xfeėĜĢħĪĀcr\xea\xf2kslash;或Ŷ\xf6\xf8;櫧ed;挆y;䐑ƀcrtąċĔause;戵noullis;愬a;䎒r;쀀\ud835\udd05pf;쀀\ud835\udd39eve;䋘c\xf2ēmpeq;扎܀HOacdefhilorsuōőŖƀƞƢƵƷƺǜȕɳɸɾcy;䐧PY耻\xa9䂩ƀcpyŝŢźute;䄆Ā;iŧŨ拒talDifferentialD;慅leys;愭ȀaeioƉƎƔƘron;䄌dil耻\xc7䃇rc;䄈nint;戰ot;䄊ĀdnƧƭilla;䂸terDot;䂷\xf2ſi;䎧rcleȀDMPTǇǋǑǖot;抙inus;抖lus;投imes;抗oĀcsǢǸkwiseContourIntegral;戲eCurlyĀDQȃȏoubleQuote;思uote;怙ȀlnpuȞȨɇɕonĀ;eȥȦ户;橴ƀgitȯȶȺruent;扡nt;戯ourIntegral;戮ĀfrɌɎ;愂oduct;成nterClockwiseContourIntegral;戳oss;樯cr;쀀\ud835\udc9epĀ;Cʄʅ拓ap;才րDJSZacefiosʠʬʰʴʸˋ˗ˡ˦̳ҍĀ;oŹʥtrahd;椑cy;䐂cy;䐅cy;䐏ƀgrsʿ˄ˇger;怡r;憡hv;櫤Āayː˕ron;䄎;䐔lĀ;t˝˞戇a;䎔r;쀀\ud835\udd07Āaf˫̧Ācm˰̢riticalȀADGT̖̜̀̆cute;䂴oŴ̋̍;䋙bleAcute;䋝rave;䁠ilde;䋜ond;拄ferentialD;慆Ѱ̽\0\0\0͔͂\0Ѕf;쀀\ud835\udd3bƀ;DE͈͉͍䂨ot;惜qual;扐blèCDLRUVͣͲ΂ϏϢϸontourIntegra\xecȹoɴ͹\0\0ͻ\xbb͉nArrow;懓Āeo·ΤftƀARTΐΖΡrrow;懐ightArrow;懔e\xe5ˊngĀLRΫτeftĀARγιrrow;柸ightArrow;柺ightArrow;柹ightĀATϘϞrrow;懒ee;抨pɁϩ\0\0ϯrrow;懑ownArrow;懕erticalBar;戥ǹABLRTaВЪаўѿͼrrowƀ;BUНОТ憓ar;椓pArrow;懵reve;䌑eft˒к\0ц\0ѐightVector;楐eeVector;楞ectorĀ;Bљњ憽ar;楖ightǔѧ\0ѱeeVector;楟ectorĀ;BѺѻ懁ar;楗eeĀ;A҆҇护rrow;憧ĀctҒҗr;쀀\ud835\udc9frok;䄐ࠀNTacdfglmopqstuxҽӀӄӋӞӢӧӮӵԡԯԶՒ՝ՠեG;䅊H耻\xd0䃐cute耻\xc9䃉ƀaiyӒӗӜron;䄚rc耻\xca䃊;䐭ot;䄖r;쀀\ud835\udd08rave耻\xc8䃈ement;戈ĀapӺӾcr;䄒tyɓԆ\0\0ԒmallSquare;旻erySmallSquare;斫ĀgpԦԪon;䄘f;쀀\ud835\udd3csilon;䎕uĀaiԼՉlĀ;TՂՃ橵ilde;扂librium;懌Āci՗՚r;愰m;橳a;䎗ml耻\xcb䃋Āipժկsts;戃onentialE;慇ʀcfiosօֈ֍ֲ׌y;䐤r;쀀\ud835\udd09lledɓ֗\0\0֣mallSquare;旼erySmallSquare;斪Ͱֺ\0ֿ\0\0ׄf;쀀\ud835\udd3dAll;戀riertrf;愱c\xf2׋؀JTabcdfgorstר׬ׯ׺؀ؒؖ؛؝أ٬ٲcy;䐃耻>䀾mmaĀ;d׷׸䎓;䏜reve;䄞ƀeiy؇،ؐdil;䄢rc;䄜;䐓ot;䄠r;쀀\ud835\udd0a;拙pf;쀀\ud835\udd3eeater̀EFGLSTصلَٖٛ٦qualĀ;Lؾؿ扥ess;招ullEqual;执reater;檢ess;扷lantEqual;橾ilde;扳cr;쀀\ud835\udca2;扫ЀAacfiosuڅڋږڛڞڪھۊRDcy;䐪Āctڐڔek;䋇;䁞irc;䄤r;愌lbertSpace;愋ǰگ\0ڲf;愍izontalLine;攀Āctۃۅ\xf2کrok;䄦mpńېۘownHum\xf0įqual;扏܀EJOacdfgmnostuۺ۾܃܇܎ܚܞܡܨ݄ݸދޏޕcy;䐕lig;䄲cy;䐁cute耻\xcd䃍Āiyܓܘrc耻\xce䃎;䐘ot;䄰r;愑rave耻\xcc䃌ƀ;apܠܯܿĀcgܴܷr;䄪inaryI;慈lie\xf3ϝǴ݉\0ݢĀ;eݍݎ戬Āgrݓݘral;戫section;拂isibleĀCTݬݲomma;恣imes;恢ƀgptݿރވon;䄮f;쀀\ud835\udd40a;䎙cr;愐ilde;䄨ǫޚ\0ޞcy;䐆l耻\xcf䃏ʀcfosuެ޷޼߂ߐĀiyޱ޵rc;䄴;䐙r;쀀\ud835\udd0dpf;쀀\ud835\udd41ǣ߇\0ߌr;쀀\ud835\udca5rcy;䐈kcy;䐄΀HJacfosߤߨ߽߬߱ࠂࠈcy;䐥cy;䐌ppa;䎚Āey߶߻dil;䄶;䐚r;쀀\ud835\udd0epf;쀀\ud835\udd42cr;쀀\ud835\udca6րJTaceflmostࠥࠩࠬࡐࡣ঳সে্਷ੇcy;䐉耻<䀼ʀcmnpr࠷࠼ࡁࡄࡍute;䄹bda;䎛g;柪lacetrf;愒r;憞ƀaeyࡗ࡜ࡡron;䄽dil;䄻;䐛Āfsࡨ॰tԀACDFRTUVarࡾࢩࢱࣦ࣠ࣼयज़ΐ४Ānrࢃ࢏gleBracket;柨rowƀ;BR࢙࢚࢞憐ar;懤ightArrow;懆eiling;挈oǵࢷ\0ࣃbleBracket;柦nǔࣈ\0࣒eeVector;楡ectorĀ;Bࣛࣜ懃ar;楙loor;挊ightĀAV࣯ࣵrrow;憔ector;楎Āerँगeƀ;AVउऊऐ抣rrow;憤ector;楚iangleƀ;BEतथऩ抲ar;槏qual;抴pƀDTVषूौownVector;楑eeVector;楠ectorĀ;Bॖॗ憿ar;楘ectorĀ;B॥०憼ar;楒ight\xe1Μs̀EFGLSTॾঋকঝঢভqualGreater;拚ullEqual;扦reater;扶ess;檡lantEqual;橽ilde;扲r;쀀\ud835\udd0fĀ;eঽা拘ftarrow;懚idot;䄿ƀnpw৔ਖਛgȀLRlr৞৷ਂਐeftĀAR০৬rrow;柵ightArrow;柷ightArrow;柶eftĀarγਊight\xe1οight\xe1ϊf;쀀\ud835\udd43erĀLRਢਬeftArrow;憙ightArrow;憘ƀchtਾੀੂ\xf2ࡌ;憰rok;䅁;扪Ѐacefiosuਗ਼੝੠੷੼અઋ઎p;椅y;䐜Ādl੥੯iumSpace;恟lintrf;愳r;쀀\ud835\udd10nusPlus;戓pf;쀀\ud835\udd44c\xf2੶;䎜ҀJacefostuણધભીଔଙඑ඗ඞcy;䐊cute;䅃ƀaey઴હાron;䅇dil;䅅;䐝ƀgswે૰଎ativeƀMTV૓૟૨ediumSpace;怋hiĀcn૦૘\xeb૙eryThi\xee૙tedĀGL૸ଆreaterGreate\xf2ٳessLes\xf3ੈLine;䀊r;쀀\ud835\udd11ȀBnptଢନଷ଺reak;恠BreakingSpace;䂠f;愕ڀ;CDEGHLNPRSTV୕ୖ୪୼஡௫ఄ౞಄ದ೘ൡඅ櫬Āou୛୤ngruent;扢pCap;扭oubleVerticalBar;戦ƀlqxஃஊ஛ement;戉ualĀ;Tஒஓ扠ilde;쀀≂̸ists;戄reater΀;EFGLSTஶஷ஽௉௓௘௥扯qual;扱ullEqual;쀀≧̸reater;쀀≫̸ess;批lantEqual;쀀⩾̸ilde;扵umpń௲௽ownHump;쀀≎̸qual;쀀≏̸eĀfsఊధtTriangleƀ;BEచఛడ拪ar;쀀⧏̸qual;括s̀;EGLSTవశ఼ౄోౘ扮qual;扰reater;扸ess;쀀≪̸lantEqual;쀀⩽̸ilde;扴estedĀGL౨౹reaterGreater;쀀⪢̸essLess;쀀⪡̸recedesƀ;ESಒಓಛ技qual;쀀⪯̸lantEqual;拠ĀeiಫಹverseElement;戌ghtTriangleƀ;BEೋೌ೒拫ar;쀀⧐̸qual;拭ĀquೝഌuareSuĀbp೨೹setĀ;E೰ೳ쀀⊏̸qual;拢ersetĀ;Eഃആ쀀⊐̸qual;拣ƀbcpഓതൎsetĀ;Eഛഞ쀀⊂⃒qual;抈ceedsȀ;ESTലള഻െ抁qual;쀀⪰̸lantEqual;拡ilde;쀀≿̸ersetĀ;E൘൛쀀⊃⃒qual;抉ildeȀ;EFT൮൯൵ൿ扁qual;扄ullEqual;扇ilde;扉erticalBar;戤cr;쀀\ud835\udca9ilde耻\xd1䃑;䎝܀Eacdfgmoprstuvලෂ෉෕ෛ෠෧෼ขภยา฿ไlig;䅒cute耻\xd3䃓Āiy෎ීrc耻\xd4䃔;䐞blac;䅐r;쀀\ud835\udd12rave耻\xd2䃒ƀaei෮ෲ෶cr;䅌ga;䎩cron;䎟pf;쀀\ud835\udd46enCurlyĀDQฎบoubleQuote;怜uote;怘;橔Āclวฬr;쀀\ud835\udcaaash耻\xd8䃘iŬื฼de耻\xd5䃕es;樷ml耻\xd6䃖erĀBP๋๠Āar๐๓r;怾acĀek๚๜;揞et;掴arenthesis;揜Ҁacfhilors๿ງຊຏຒດຝະ໼rtialD;戂y;䐟r;쀀\ud835\udd13i;䎦;䎠usMinus;䂱Āipຢອncareplan\xe5ڝf;愙Ȁ;eio຺ູ໠໤檻cedesȀ;EST່້໏໚扺qual;檯lantEqual;扼ilde;找me;怳Ādp໩໮uct;戏ortionĀ;aȥ໹l;戝Āci༁༆r;쀀\ud835\udcab;䎨ȀUfos༑༖༛༟OT耻"䀢r;쀀\ud835\udd14pf;愚cr;쀀\ud835\udcac؀BEacefhiorsu༾གྷཇའཱིྦྷྪྭ႖ႩႴႾarr;椐G耻\xae䂮ƀcnrཎནབute;䅔g;柫rĀ;tཛྷཝ憠l;椖ƀaeyཧཬཱron;䅘dil;䅖;䐠Ā;vླྀཹ愜erseĀEUྂྙĀlq྇ྎement;戋uilibrium;懋pEquilibrium;楯r\xbbཹo;䎡ghtЀACDFTUVa࿁࿫࿳ဢဨၛႇϘĀnr࿆࿒gleBracket;柩rowƀ;BL࿜࿝࿡憒ar;懥eftArrow;懄eiling;按oǵ࿹\0စbleBracket;柧nǔည\0နeeVector;楝ectorĀ;Bဝသ懂ar;楕loor;挋Āerိ၃eƀ;AVဵံြ抢rrow;憦ector;楛iangleƀ;BEၐၑၕ抳ar;槐qual;抵pƀDTVၣၮၸownVector;楏eeVector;楜ectorĀ;Bႂႃ憾ar;楔ectorĀ;B႑႒懀ar;楓Āpuႛ႞f;愝ndImplies;楰ightarrow;懛ĀchႹႼr;愛;憱leDelayed;槴ڀHOacfhimoqstuფჱჷჽᄙᄞᅑᅖᅡᅧᆵᆻᆿĀCcჩხHcy;䐩y;䐨FTcy;䐬cute;䅚ʀ;aeiyᄈᄉᄎᄓᄗ檼ron;䅠dil;䅞rc;䅜;䐡r;쀀\ud835\udd16ortȀDLRUᄪᄴᄾᅉownArrow\xbbОeftArrow\xbb࢚ightArrow\xbb࿝pArrow;憑gma;䎣allCircle;战pf;쀀\ud835\udd4aɲᅭ\0\0ᅰt;戚areȀ;ISUᅻᅼᆉᆯ斡ntersection;抓uĀbpᆏᆞsetĀ;Eᆗᆘ抏qual;抑ersetĀ;Eᆨᆩ抐qual;抒nion;抔cr;쀀\ud835\udcaear;拆ȀbcmpᇈᇛሉላĀ;sᇍᇎ拐etĀ;Eᇍᇕqual;抆ĀchᇠህeedsȀ;ESTᇭᇮᇴᇿ扻qual;檰lantEqual;扽ilde;承Th\xe1ྌ;我ƀ;esሒሓሣ拑rsetĀ;Eሜም抃qual;抇et\xbbሓրHRSacfhiorsሾቄ቉ቕ቞ቱቶኟዂወዑORN耻\xde䃞ADE;愢ĀHc቎ቒcy;䐋y;䐦Ābuቚቜ;䀉;䎤ƀaeyብቪቯron;䅤dil;䅢;䐢r;쀀\ud835\udd17Āeiቻ኉ǲኀ\0ኇefore;戴a;䎘Ācn኎ኘkSpace;쀀  Space;怉ldeȀ;EFTካኬኲኼ戼qual;扃ullEqual;扅ilde;扈pf;쀀\ud835\udd4bipleDot;惛Āctዖዛr;쀀\ud835\udcafrok;䅦ૡዷጎጚጦ\0ጬጱ\0\0\0\0\0ጸጽ፷ᎅ\0᏿ᐄᐊᐐĀcrዻጁute耻\xda䃚rĀ;oጇገ憟cir;楉rǣጓ\0጖y;䐎ve;䅬Āiyጞጣrc耻\xdb䃛;䐣blac;䅰r;쀀\ud835\udd18rave耻\xd9䃙acr;䅪Ādiፁ፩erĀBPፈ፝Āarፍፐr;䁟acĀekፗፙ;揟et;掵arenthesis;揝onĀ;P፰፱拃lus;抎Āgp፻፿on;䅲f;쀀\ud835\udd4cЀADETadps᎕ᎮᎸᏄϨᏒᏗᏳrrowƀ;BDᅐᎠᎤar;椒ownArrow;懅ownArrow;憕quilibrium;楮eeĀ;AᏋᏌ报rrow;憥own\xe1ϳerĀLRᏞᏨeftArrow;憖ightArrow;憗iĀ;lᏹᏺ䏒on;䎥ing;䅮cr;쀀\ud835\udcb0ilde;䅨ml耻\xdc䃜ҀDbcdefosvᐧᐬᐰᐳᐾᒅᒊᒐᒖash;披ar;櫫y;䐒ashĀ;lᐻᐼ抩;櫦Āerᑃᑅ;拁ƀbtyᑌᑐᑺar;怖Ā;iᑏᑕcalȀBLSTᑡᑥᑪᑴar;戣ine;䁼eparator;杘ilde;所ThinSpace;怊r;쀀\ud835\udd19pf;쀀\ud835\udd4dcr;쀀\ud835\udcb1dash;抪ʀcefosᒧᒬᒱᒶᒼirc;䅴dge;拀r;쀀\ud835\udd1apf;쀀\ud835\udd4ecr;쀀\ud835\udcb2Ȁfiosᓋᓐᓒᓘr;쀀\ud835\udd1b;䎞pf;쀀\ud835\udd4fcr;쀀\ud835\udcb3ҀAIUacfosuᓱᓵᓹᓽᔄᔏᔔᔚᔠcy;䐯cy;䐇cy;䐮cute耻\xdd䃝Āiyᔉᔍrc;䅶;䐫r;쀀\ud835\udd1cpf;쀀\ud835\udd50cr;쀀\ud835\udcb4ml;䅸ЀHacdefosᔵᔹᔿᕋᕏᕝᕠᕤcy;䐖cute;䅹Āayᕄᕉron;䅽;䐗ot;䅻ǲᕔ\0ᕛoWidt\xe8૙a;䎖r;愨pf;愤cr;쀀\ud835\udcb5௡ᖃᖊᖐ\0ᖰᖶᖿ\0\0\0\0ᗆᗛᗫᙟ᙭\0ᚕ᚛ᚲᚹ\0ᚾcute耻\xe1䃡reve;䄃̀;Ediuyᖜᖝᖡᖣᖨᖭ戾;쀀∾̳;房rc耻\xe2䃢te肻\xb4̆;䐰lig耻\xe6䃦Ā;r\xb2ᖺ;쀀\ud835\udd1erave耻\xe0䃠ĀepᗊᗖĀfpᗏᗔsym;愵\xe8ᗓha;䎱ĀapᗟcĀclᗤᗧr;䄁g;樿ɤᗰ\0\0ᘊʀ;adsvᗺᗻᗿᘁᘇ戧nd;橕;橜lope;橘;橚΀;elmrszᘘᘙᘛᘞᘿᙏᙙ戠;榤e\xbbᘙsdĀ;aᘥᘦ戡ѡᘰᘲᘴᘶᘸᘺᘼᘾ;榨;榩;榪;榫;榬;榭;榮;榯tĀ;vᙅᙆ戟bĀ;dᙌᙍ抾;榝Āptᙔᙗh;戢\xbb\xb9arr;捼Āgpᙣᙧon;䄅f;쀀\ud835\udd52΀;Eaeiop዁ᙻᙽᚂᚄᚇᚊ;橰cir;橯;扊d;手s;䀧roxĀ;e዁ᚒ\xf1ᚃing耻\xe5䃥ƀctyᚡᚦᚨr;쀀\ud835\udcb6;䀪mpĀ;e዁ᚯ\xf1ʈilde耻\xe3䃣ml耻\xe4䃤Āciᛂᛈonin\xf4ɲnt;樑ࠀNabcdefiklnoprsu᛭ᛱᜰ᜼ᝃᝈ᝸᝽០៦ᠹᡐᜍ᤽᥈ᥰot;櫭Ācrᛶ᜞kȀcepsᜀᜅᜍᜓong;扌psilon;䏶rime;怵imĀ;e᜚᜛戽q;拍Ŷᜢᜦee;抽edĀ;gᜬᜭ挅e\xbbᜭrkĀ;t፜᜷brk;掶Āoyᜁᝁ;䐱quo;怞ʀcmprtᝓ᝛ᝡᝤᝨausĀ;eĊĉptyv;榰s\xe9ᜌno\xf5ēƀahwᝯ᝱ᝳ;䎲;愶een;扬r;쀀\ud835\udd1fg΀costuvwឍឝឳេ៕៛៞ƀaiuបពរ\xf0ݠrc;旯p\xbb፱ƀdptឤឨឭot;樀lus;樁imes;樂ɱឹ\0\0ើcup;樆ar;昅riangleĀdu៍្own;施p;斳plus;樄e\xe5ᑄ\xe5ᒭarow;植ƀako៭ᠦᠵĀcn៲ᠣkƀlst៺֫᠂ozenge;槫riangleȀ;dlr᠒᠓᠘᠝斴own;斾eft;旂ight;斸k;搣Ʊᠫ\0ᠳƲᠯ\0ᠱ;斒;斑4;斓ck;斈ĀeoᠾᡍĀ;qᡃᡆ쀀=⃥uiv;쀀≡⃥t;挐Ȁptwxᡙᡞᡧᡬf;쀀\ud835\udd53Ā;tᏋᡣom\xbbᏌtie;拈؀DHUVbdhmptuvᢅᢖᢪᢻᣗᣛᣬ᣿ᤅᤊᤐᤡȀLRlrᢎᢐᢒᢔ;敗;敔;敖;敓ʀ;DUduᢡᢢᢤᢦᢨ敐;敦;敩;敤;敧ȀLRlrᢳᢵᢷᢹ;敝;敚;敜;教΀;HLRhlrᣊᣋᣍᣏᣑᣓᣕ救;敬;散;敠;敫;敢;敟ox;槉ȀLRlrᣤᣦᣨᣪ;敕;敒;攐;攌ʀ;DUduڽ᣷᣹᣻᣽;敥;敨;攬;攴inus;抟lus;択imes;抠ȀLRlrᤙᤛᤝ᤟;敛;敘;攘;攔΀;HLRhlrᤰᤱᤳᤵᤷ᤻᤹攂;敪;敡;敞;攼;攤;攜Āevģ᥂bar耻\xa6䂦Ȁceioᥑᥖᥚᥠr;쀀\ud835\udcb7mi;恏mĀ;e᜚᜜lƀ;bhᥨᥩᥫ䁜;槅sub;柈Ŭᥴ᥾lĀ;e᥹᥺怢t\xbb᥺pƀ;Eeįᦅᦇ;檮Ā;qۜۛೡᦧ\0᧨ᨑᨕᨲ\0ᨷᩐ\0\0᪴\0\0᫁\0\0ᬡᬮ᭍᭒\0᯽\0ᰌƀcpr᦭ᦲ᧝ute;䄇̀;abcdsᦿᧀᧄ᧊᧕᧙戩nd;橄rcup;橉Āau᧏᧒p;橋p;橇ot;橀;쀀∩︀Āeo᧢᧥t;恁\xeeړȀaeiu᧰᧻ᨁᨅǰ᧵\0᧸s;橍on;䄍dil耻\xe7䃧rc;䄉psĀ;sᨌᨍ橌m;橐ot;䄋ƀdmnᨛᨠᨦil肻\xb8ƭptyv;榲t脀\xa2;eᨭᨮ䂢r\xe4Ʋr;쀀\ud835\udd20ƀceiᨽᩀᩍy;䑇ckĀ;mᩇᩈ朓ark\xbbᩈ;䏇r΀;Ecefms᩟᩠ᩢᩫ᪤᪪᪮旋;槃ƀ;elᩩᩪᩭ䋆q;扗eɡᩴ\0\0᪈rrowĀlr᩼᪁eft;憺ight;憻ʀRSacd᪒᪔᪖᪚᪟\xbbཇ;擈st;抛irc;抚ash;抝nint;樐id;櫯cir;槂ubsĀ;u᪻᪼晣it\xbb᪼ˬ᫇᫔᫺\0ᬊonĀ;eᫍᫎ䀺Ā;q\xc7\xc6ɭ᫙\0\0᫢aĀ;t᫞᫟䀬;䁀ƀ;fl᫨᫩᫫戁\xeeᅠeĀmx᫱᫶ent\xbb᫩e\xf3ɍǧ᫾\0ᬇĀ;dኻᬂot;橭n\xf4Ɇƀfryᬐᬔᬗ;쀀\ud835\udd54o\xe4ɔ脀\xa9;sŕᬝr;愗Āaoᬥᬩrr;憵ss;朗Ācuᬲᬷr;쀀\ud835\udcb8Ābpᬼ᭄Ā;eᭁᭂ櫏;櫑Ā;eᭉᭊ櫐;櫒dot;拯΀delprvw᭠᭬᭷ᮂᮬᯔ᯹arrĀlr᭨᭪;椸;椵ɰ᭲\0\0᭵r;拞c;拟arrĀ;p᭿ᮀ憶;椽̀;bcdosᮏᮐᮖᮡᮥᮨ截rcap;橈Āauᮛᮞp;橆p;橊ot;抍r;橅;쀀∪︀Ȁalrv᮵ᮿᯞᯣrrĀ;mᮼᮽ憷;椼yƀevwᯇᯔᯘqɰᯎ\0\0ᯒre\xe3᭳u\xe3᭵ee;拎edge;拏en耻\xa4䂤earrowĀlrᯮ᯳eft\xbbᮀight\xbbᮽe\xe4ᯝĀciᰁᰇonin\xf4Ƿnt;戱lcty;挭ঀAHabcdefhijlorstuwz᰸᰻᰿ᱝᱩᱵᲊᲞᲬᲷ᳻᳿ᴍᵻᶑᶫᶻ᷆᷍r\xf2΁ar;楥Ȁglrs᱈ᱍ᱒᱔ger;怠eth;愸\xf2ᄳhĀ;vᱚᱛ怐\xbbऊūᱡᱧarow;椏a\xe3̕Āayᱮᱳron;䄏;䐴ƀ;ao̲ᱼᲄĀgrʿᲁr;懊tseq;橷ƀglmᲑᲔᲘ耻\xb0䂰ta;䎴ptyv;榱ĀirᲣᲨsht;楿;쀀\ud835\udd21arĀlrᲳᲵ\xbbࣜ\xbbသʀaegsv᳂͸᳖᳜᳠mƀ;oș᳊᳔ndĀ;ș᳑uit;晦amma;䏝in;拲ƀ;io᳧᳨᳸䃷de脀\xf7;o᳧ᳰntimes;拇n\xf8᳷cy;䑒cɯᴆ\0\0ᴊrn;挞op;挍ʀlptuwᴘᴝᴢᵉᵕlar;䀤f;쀀\ud835\udd55ʀ;emps̋ᴭᴷᴽᵂqĀ;d͒ᴳot;扑inus;戸lus;戔quare;抡blebarwedg\xe5\xfanƀadhᄮᵝᵧownarrow\xf3ᲃarpoonĀlrᵲᵶef\xf4Ჴigh\xf4ᲶŢᵿᶅkaro\xf7གɯᶊ\0\0ᶎrn;挟op;挌ƀcotᶘᶣᶦĀryᶝᶡ;쀀\ud835\udcb9;䑕l;槶rok;䄑Ādrᶰᶴot;拱iĀ;fᶺ᠖斿Āah᷀᷃r\xf2Щa\xf2ྦangle;榦Āci᷒ᷕy;䑟grarr;柿ऀDacdefglmnopqrstuxḁḉḙḸոḼṉṡṾấắẽỡἪἷὄ὎὚ĀDoḆᴴo\xf4ᲉĀcsḎḔute耻\xe9䃩ter;橮ȀaioyḢḧḱḶron;䄛rĀ;cḭḮ扖耻\xea䃪lon;払;䑍ot;䄗ĀDrṁṅot;扒;쀀\ud835\udd22ƀ;rsṐṑṗ檚ave耻\xe8䃨Ā;dṜṝ檖ot;檘Ȁ;ilsṪṫṲṴ檙nters;揧;愓Ā;dṹṺ檕ot;檗ƀapsẅẉẗcr;䄓tyƀ;svẒẓẕ戅et\xbbẓpĀ1;ẝẤĳạả;怄;怅怃ĀgsẪẬ;䅋p;怂ĀgpẴẸon;䄙f;쀀\ud835\udd56ƀalsỄỎỒrĀ;sỊị拕l;槣us;橱iƀ;lvỚớở䎵on\xbbớ;䏵ȀcsuvỪỳἋἣĀioữḱrc\xbbḮɩỹ\0\0ỻ\xedՈantĀglἂἆtr\xbbṝess\xbbṺƀaeiἒ἖Ἒls;䀽st;扟vĀ;DȵἠD;橸parsl;槥ĀDaἯἳot;打rr;楱ƀcdiἾὁỸr;愯o\xf4͒ĀahὉὋ;䎷耻\xf0䃰Āmrὓὗl耻\xeb䃫o;悬ƀcipὡὤὧl;䀡s\xf4ծĀeoὬὴctatio\xeeՙnential\xe5չৡᾒ\0ᾞ\0ᾡᾧ\0\0ῆῌ\0ΐ\0ῦῪ \0 ⁚llingdotse\xf1Ṅy;䑄male;晀ƀilrᾭᾳ῁lig;耀ﬃɩᾹ\0\0᾽g;耀ﬀig;耀ﬄ;쀀\ud835\udd23lig;耀ﬁlig;쀀fjƀaltῙ῜ῡt;晭ig;耀ﬂns;斱of;䆒ǰ΅\0ῳf;쀀\ud835\udd57ĀakֿῷĀ;vῼ´拔;櫙artint;樍Āao‌⁕Ācs‑⁒α‚‰‸⁅⁈\0⁐β•‥‧‪‬\0‮耻\xbd䂽;慓耻\xbc䂼;慕;慙;慛Ƴ‴\0‶;慔;慖ʴ‾⁁\0\0⁃耻\xbe䂾;慗;慜5;慘ƶ⁌\0⁎;慚;慝8;慞l;恄wn;挢cr;쀀\ud835\udcbbࢀEabcdefgijlnorstv₂₉₟₥₰₴⃰⃵⃺⃿℃ℒℸ̗ℾ⅒↞Ā;lٍ₇;檌ƀcmpₐₕ₝ute;䇵maĀ;dₜ᳚䎳;檆reve;䄟Āiy₪₮rc;䄝;䐳ot;䄡Ȁ;lqsؾق₽⃉ƀ;qsؾٌ⃄lan\xf4٥Ȁ;cdl٥⃒⃥⃕c;檩otĀ;o⃜⃝檀Ā;l⃢⃣檂;檄Ā;e⃪⃭쀀⋛︀s;檔r;쀀\ud835\udd24Ā;gٳ؛mel;愷cy;䑓Ȁ;Eajٚℌℎℐ;檒;檥;檤ȀEaesℛℝ℩ℴ;扩pĀ;p℣ℤ檊rox\xbbℤĀ;q℮ℯ檈Ā;q℮ℛim;拧pf;쀀\ud835\udd58Āci⅃ⅆr;愊mƀ;el٫ⅎ⅐;檎;檐茀>;cdlqr׮ⅠⅪⅮⅳⅹĀciⅥⅧ;檧r;橺ot;拗Par;榕uest;橼ʀadelsↄⅪ←ٖ↛ǰ↉\0↎pro\xf8₞r;楸qĀlqؿ↖les\xf3₈i\xed٫Āen↣↭rtneqq;쀀≩︀\xc5↪ԀAabcefkosy⇄⇇⇱⇵⇺∘∝∯≨≽r\xf2ΠȀilmr⇐⇔⇗⇛rs\xf0ᒄf\xbb․il\xf4کĀdr⇠⇤cy;䑊ƀ;cwࣴ⇫⇯ir;楈;憭ar;意irc;䄥ƀalr∁∎∓rtsĀ;u∉∊晥it\xbb∊lip;怦con;抹r;쀀\ud835\udd25sĀew∣∩arow;椥arow;椦ʀamopr∺∾≃≞≣rr;懿tht;戻kĀlr≉≓eftarrow;憩ightarrow;憪f;쀀\ud835\udd59bar;怕ƀclt≯≴≸r;쀀\ud835\udcbdas\xe8⇴rok;䄧Ābp⊂⊇ull;恃hen\xbbᱛૡ⊣\0⊪\0⊸⋅⋎\0⋕⋳\0\0⋸⌢⍧⍢⍿\0⎆⎪⎴cute耻\xed䃭ƀ;iyݱ⊰⊵rc耻\xee䃮;䐸Ācx⊼⊿y;䐵cl耻\xa1䂡ĀfrΟ⋉;쀀\ud835\udd26rave耻\xec䃬Ȁ;inoܾ⋝⋩⋮Āin⋢⋦nt;樌t;戭fin;槜ta;愩lig;䄳ƀaop⋾⌚⌝ƀcgt⌅⌈⌗r;䄫ƀelpܟ⌏⌓in\xe5ގar\xf4ܠh;䄱f;抷ed;䆵ʀ;cfotӴ⌬⌱⌽⍁are;愅inĀ;t⌸⌹戞ie;槝do\xf4⌙ʀ;celpݗ⍌⍐⍛⍡al;抺Āgr⍕⍙er\xf3ᕣ\xe3⍍arhk;樗rod;樼Ȁcgpt⍯⍲⍶⍻y;䑑on;䄯f;쀀\ud835\udd5aa;䎹uest耻\xbf䂿Āci⎊⎏r;쀀\ud835\udcbenʀ;EdsvӴ⎛⎝⎡ӳ;拹ot;拵Ā;v⎦⎧拴;拳Ā;iݷ⎮lde;䄩ǫ⎸\0⎼cy;䑖l耻\xef䃯̀cfmosu⏌⏗⏜⏡⏧⏵Āiy⏑⏕rc;䄵;䐹r;쀀\ud835\udd27ath;䈷pf;쀀\ud835\udd5bǣ⏬\0⏱r;쀀\ud835\udcbfrcy;䑘kcy;䑔Ѐacfghjos␋␖␢␧␭␱␵␻ppaĀ;v␓␔䎺;䏰Āey␛␠dil;䄷;䐺r;쀀\ud835\udd28reen;䄸cy;䑅cy;䑜pf;쀀\ud835\udd5ccr;쀀\ud835\udcc0஀ABEHabcdefghjlmnoprstuv⑰⒁⒆⒍⒑┎┽╚▀♎♞♥♹♽⚚⚲⛘❝❨➋⟀⠁⠒ƀart⑷⑺⑼r\xf2৆\xf2Εail;椛arr;椎Ā;gঔ⒋;檋ar;楢ॣ⒥\0⒪\0⒱\0\0\0\0\0⒵Ⓔ\0ⓆⓈⓍ\0⓹ute;䄺mptyv;榴ra\xeeࡌbda;䎻gƀ;dlࢎⓁⓃ;榑\xe5ࢎ;檅uo耻\xab䂫rЀ;bfhlpst࢙ⓞⓦⓩ⓫⓮⓱⓵Ā;f࢝ⓣs;椟s;椝\xeb≒p;憫l;椹im;楳l;憢ƀ;ae⓿─┄檫il;椙Ā;s┉┊檭;쀀⪭︀ƀabr┕┙┝rr;椌rk;杲Āak┢┬cĀek┨┪;䁻;䁛Āes┱┳;榋lĀdu┹┻;榏;榍Ȁaeuy╆╋╖╘ron;䄾Ādi═╔il;䄼\xecࢰ\xe2┩;䐻Ȁcqrs╣╦╭╽a;椶uoĀ;rนᝆĀdu╲╷har;楧shar;楋h;憲ʀ;fgqs▋▌উ◳◿扤tʀahlrt▘▤▷◂◨rrowĀ;t࢙□a\xe9⓶arpoonĀdu▯▴own\xbbњp\xbb०eftarrows;懇ightƀahs◍◖◞rrowĀ;sࣴࢧarpoon\xf3྘quigarro\xf7⇰hreetimes;拋ƀ;qs▋ও◺lan\xf4বʀ;cdgsব☊☍☝☨c;檨otĀ;o☔☕橿Ā;r☚☛檁;檃Ā;e☢☥쀀⋚︀s;檓ʀadegs☳☹☽♉♋ppro\xf8Ⓠot;拖qĀgq♃♅\xf4উgt\xf2⒌\xf4ছi\xedলƀilr♕࣡♚sht;楼;쀀\ud835\udd29Ā;Eজ♣;檑š♩♶rĀdu▲♮Ā;l॥♳;楪lk;斄cy;䑙ʀ;achtੈ⚈⚋⚑⚖r\xf2◁orne\xf2ᴈard;楫ri;旺Āio⚟⚤dot;䅀ustĀ;a⚬⚭掰che\xbb⚭ȀEaes⚻⚽⛉⛔;扨pĀ;p⛃⛄檉rox\xbb⛄Ā;q⛎⛏檇Ā;q⛎⚻im;拦Ѐabnoptwz⛩⛴⛷✚✯❁❇❐Ānr⛮⛱g;柬r;懽r\xebࣁgƀlmr⛿✍✔eftĀar০✇ight\xe1৲apsto;柼ight\xe1৽parrowĀlr✥✩ef\xf4⓭ight;憬ƀafl✶✹✽r;榅;쀀\ud835\udd5dus;樭imes;樴š❋❏st;戗\xe1ፎƀ;ef❗❘᠀旊nge\xbb❘arĀ;l❤❥䀨t;榓ʀachmt❳❶❼➅➇r\xf2ࢨorne\xf2ᶌarĀ;d྘➃;業;怎ri;抿̀achiqt➘➝ੀ➢➮➻quo;怹r;쀀\ud835\udcc1mƀ;egল➪➬;檍;檏Ābu┪➳oĀ;rฟ➹;怚rok;䅂萀<;cdhilqrࠫ⟒☹⟜⟠⟥⟪⟰Āci⟗⟙;檦r;橹re\xe5◲mes;拉arr;楶uest;橻ĀPi⟵⟹ar;榖ƀ;ef⠀भ᠛旃rĀdu⠇⠍shar;楊har;楦Āen⠗⠡rtneqq;쀀≨︀\xc5⠞܀Dacdefhilnopsu⡀⡅⢂⢎⢓⢠⢥⢨⣚⣢⣤ઃ⣳⤂Dot;戺Ȁclpr⡎⡒⡣⡽r耻\xaf䂯Āet⡗⡙;時Ā;e⡞⡟朠se\xbb⡟Ā;sျ⡨toȀ;dluျ⡳⡷⡻ow\xeeҌef\xf4ए\xf0Ꮡker;斮Āoy⢇⢌mma;権;䐼ash;怔asuredangle\xbbᘦr;쀀\ud835\udd2ao;愧ƀcdn⢯⢴⣉ro耻\xb5䂵Ȁ;acdᑤ⢽⣀⣄s\xf4ᚧir;櫰ot肻\xb7Ƶusƀ;bd⣒ᤃ⣓戒Ā;uᴼ⣘;横ţ⣞⣡p;櫛\xf2−\xf0ઁĀdp⣩⣮els;抧f;쀀\ud835\udd5eĀct⣸⣽r;쀀\ud835\udcc2pos\xbbᖝƀ;lm⤉⤊⤍䎼timap;抸ఀGLRVabcdefghijlmoprstuvw⥂⥓⥾⦉⦘⧚⧩⨕⨚⩘⩝⪃⪕⪤⪨⬄⬇⭄⭿⮮ⰴⱧⱼ⳩Āgt⥇⥋;쀀⋙̸Ā;v⥐௏쀀≫⃒ƀelt⥚⥲⥶ftĀar⥡⥧rrow;懍ightarrow;懎;쀀⋘̸Ā;v⥻ే쀀≪⃒ightarrow;懏ĀDd⦎⦓ash;抯ash;抮ʀbcnpt⦣⦧⦬⦱⧌la\xbb˞ute;䅄g;쀀∠⃒ʀ;Eiop඄⦼⧀⧅⧈;쀀⩰̸d;쀀≋̸s;䅉ro\xf8඄urĀ;a⧓⧔普lĀ;s⧓ସǳ⧟\0⧣p肻\xa0ଷmpĀ;e௹ఀʀaeouy⧴⧾⨃⨐⨓ǰ⧹\0⧻;橃on;䅈dil;䅆ngĀ;dൾ⨊ot;쀀⩭̸p;橂;䐽ash;怓΀;Aadqsxஒ⨩⨭⨻⩁⩅⩐rr;懗rĀhr⨳⨶k;椤Ā;oᏲᏰot;쀀≐̸ui\xf6ୣĀei⩊⩎ar;椨\xed஘istĀ;s஠டr;쀀\ud835\udd2bȀEest௅⩦⩹⩼ƀ;qs஼⩭௡ƀ;qs஼௅⩴lan\xf4௢i\xed௪Ā;rஶ⪁\xbbஷƀAap⪊⪍⪑r\xf2⥱rr;憮ar;櫲ƀ;svྍ⪜ྌĀ;d⪡⪢拼;拺cy;䑚΀AEadest⪷⪺⪾⫂⫅⫶⫹r\xf2⥦;쀀≦̸rr;憚r;急Ȁ;fqs఻⫎⫣⫯tĀar⫔⫙rro\xf7⫁ightarro\xf7⪐ƀ;qs఻⪺⫪lan\xf4ౕĀ;sౕ⫴\xbbశi\xedౝĀ;rవ⫾iĀ;eచథi\xe4ඐĀpt⬌⬑f;쀀\ud835\udd5f膀\xac;in⬙⬚⬶䂬nȀ;Edvஉ⬤⬨⬮;쀀⋹̸ot;쀀⋵̸ǡஉ⬳⬵;拷;拶iĀ;vಸ⬼ǡಸ⭁⭃;拾;拽ƀaor⭋⭣⭩rȀ;ast୻⭕⭚⭟lle\xec୻l;쀀⫽⃥;쀀∂̸lint;樔ƀ;ceಒ⭰⭳u\xe5ಥĀ;cಘ⭸Ā;eಒ⭽\xf1ಘȀAait⮈⮋⮝⮧r\xf2⦈rrƀ;cw⮔⮕⮙憛;쀀⤳̸;쀀↝̸ghtarrow\xbb⮕riĀ;eೋೖ΀chimpqu⮽⯍⯙⬄୸⯤⯯Ȁ;cerല⯆ഷ⯉u\xe5൅;쀀\ud835\udcc3ortɭ⬅\0\0⯖ar\xe1⭖mĀ;e൮⯟Ā;q൴൳suĀbp⯫⯭\xe5೸\xe5ഋƀbcp⯶ⰑⰙȀ;Ees⯿ⰀഢⰄ抄;쀀⫅̸etĀ;eഛⰋqĀ;qണⰀcĀ;eലⰗ\xf1സȀ;EesⰢⰣൟⰧ抅;쀀⫆̸etĀ;e൘ⰮqĀ;qൠⰣȀgilrⰽⰿⱅⱇ\xecௗlde耻\xf1䃱\xe7ృiangleĀlrⱒⱜeftĀ;eచⱚ\xf1దightĀ;eೋⱥ\xf1೗Ā;mⱬⱭ䎽ƀ;esⱴⱵⱹ䀣ro;愖p;怇ҀDHadgilrsⲏⲔⲙⲞⲣⲰⲶⳓⳣash;抭arr;椄p;쀀≍⃒ash;抬ĀetⲨⲬ;쀀≥⃒;쀀>⃒nfin;槞ƀAetⲽⳁⳅrr;椂;쀀≤⃒Ā;rⳊⳍ쀀<⃒ie;쀀⊴⃒ĀAtⳘⳜrr;椃rie;쀀⊵⃒im;쀀∼⃒ƀAan⳰⳴ⴂrr;懖rĀhr⳺⳽k;椣Ā;oᏧᏥear;椧ቓ᪕\0\0\0\0\0\0\0\0\0\0\0\0\0ⴭ\0ⴸⵈⵠⵥ⵲ⶄᬇ\0\0ⶍⶫ\0ⷈⷎ\0ⷜ⸙⸫⸾⹃Ācsⴱ᪗ute耻\xf3䃳ĀiyⴼⵅrĀ;c᪞ⵂ耻\xf4䃴;䐾ʀabios᪠ⵒⵗǈⵚlac;䅑v;樸old;榼lig;䅓Ācr⵩⵭ir;榿;쀀\ud835\udd2cͯ⵹\0\0⵼\0ⶂn;䋛ave耻\xf2䃲;槁Ābmⶈ෴ar;榵Ȁacitⶕ⶘ⶥⶨr\xf2᪀Āir⶝ⶠr;榾oss;榻n\xe5๒;槀ƀaeiⶱⶵⶹcr;䅍ga;䏉ƀcdnⷀⷅǍron;䎿;榶pf;쀀\ud835\udd60ƀaelⷔ⷗ǒr;榷rp;榹΀;adiosvⷪⷫⷮ⸈⸍⸐⸖戨r\xf2᪆Ȁ;efmⷷⷸ⸂⸅橝rĀ;oⷾⷿ愴f\xbbⷿ耻\xaa䂪耻\xba䂺gof;抶r;橖lope;橗;橛ƀclo⸟⸡⸧\xf2⸁ash耻\xf8䃸l;折iŬⸯ⸴de耻\xf5䃵esĀ;aǛ⸺s;樶ml耻\xf6䃶bar;挽ૡ⹞\0⹽\0⺀⺝\0⺢⺹\0\0⻋ຜ\0⼓\0\0⼫⾼\0⿈rȀ;astЃ⹧⹲຅脀\xb6;l⹭⹮䂶le\xecЃɩ⹸\0\0⹻m;櫳;櫽y;䐿rʀcimpt⺋⺏⺓ᡥ⺗nt;䀥od;䀮il;怰enk;怱r;쀀\ud835\udd2dƀimo⺨⺰⺴Ā;v⺭⺮䏆;䏕ma\xf4੶ne;明ƀ;tv⺿⻀⻈䏀chfork\xbb´;䏖Āau⻏⻟nĀck⻕⻝kĀ;h⇴⻛;愎\xf6⇴sҀ;abcdemst⻳⻴ᤈ⻹⻽⼄⼆⼊⼎䀫cir;樣ir;樢Āouᵀ⼂;樥;橲n肻\xb1ຝim;樦wo;樧ƀipu⼙⼠⼥ntint;樕f;쀀\ud835\udd61nd耻\xa3䂣Ԁ;Eaceinosu່⼿⽁⽄⽇⾁⾉⾒⽾⾶;檳p;檷u\xe5໙Ā;c໎⽌̀;acens່⽙⽟⽦⽨⽾ppro\xf8⽃urlye\xf1໙\xf1໎ƀaes⽯⽶⽺pprox;檹qq;檵im;拨i\xedໟmeĀ;s⾈ຮ怲ƀEas⽸⾐⽺\xf0⽵ƀdfp໬⾙⾯ƀals⾠⾥⾪lar;挮ine;挒urf;挓Ā;t໻⾴\xef໻rel;抰Āci⿀⿅r;쀀\ud835\udcc5;䏈ncsp;怈̀fiopsu⿚⋢⿟⿥⿫⿱r;쀀\ud835\udd2epf;쀀\ud835\udd62rime;恗cr;쀀\ud835\udcc6ƀaeo⿸〉〓tĀei⿾々rnion\xf3ڰnt;樖stĀ;e【】䀿\xf1Ἑ\xf4༔઀ABHabcdefhilmnoprstux぀けさすムㄎㄫㅇㅢㅲㆎ㈆㈕㈤㈩㉘㉮㉲㊐㊰㊷ƀartぇおがr\xf2Ⴓ\xf2ϝail;検ar\xf2ᱥar;楤΀cdenqrtとふへみわゔヌĀeuねぱ;쀀∽̱te;䅕i\xe3ᅮmptyv;榳gȀ;del࿑らるろ;榒;榥\xe5࿑uo耻\xbb䂻rր;abcfhlpstw࿜ガクシスゼゾダッデナp;極Ā;f࿠ゴs;椠;椳s;椞\xeb≝\xf0✮l;楅im;楴l;憣;憝Āaiパフil;椚oĀ;nホボ戶al\xf3༞ƀabrョリヮr\xf2៥rk;杳ĀakンヽcĀekヹ・;䁽;䁝Āes㄂㄄;榌lĀduㄊㄌ;榎;榐Ȁaeuyㄗㄜㄧㄩron;䅙Ādiㄡㄥil;䅗\xec࿲\xe2ヺ;䑀Ȁclqsㄴㄷㄽㅄa;椷dhar;楩uoĀ;rȎȍh;憳ƀacgㅎㅟངlȀ;ipsླྀㅘㅛႜn\xe5Ⴛar\xf4ྩt;断ƀilrㅩဣㅮsht;楽;쀀\ud835\udd2fĀaoㅷㆆrĀduㅽㅿ\xbbѻĀ;l႑ㆄ;楬Ā;vㆋㆌ䏁;䏱ƀgns㆕ㇹㇼht̀ahlrstㆤㆰ㇂㇘㇤㇮rrowĀ;t࿜ㆭa\xe9トarpoonĀduㆻㆿow\xeeㅾp\xbb႒eftĀah㇊㇐rrow\xf3࿪arpoon\xf3Ցightarrows;應quigarro\xf7ニhreetimes;拌g;䋚ingdotse\xf1ἲƀahm㈍㈐㈓r\xf2࿪a\xf2Ց;怏oustĀ;a㈞㈟掱che\xbb㈟mid;櫮Ȁabpt㈲㈽㉀㉒Ānr㈷㈺g;柭r;懾r\xebဃƀafl㉇㉊㉎r;榆;쀀\ud835\udd63us;樮imes;樵Āap㉝㉧rĀ;g㉣㉤䀩t;榔olint;樒ar\xf2㇣Ȁachq㉻㊀Ⴜ㊅quo;怺r;쀀\ud835\udcc7Ābu・㊊oĀ;rȔȓƀhir㊗㊛㊠re\xe5ㇸmes;拊iȀ;efl㊪ၙᠡ㊫方tri;槎luhar;楨;愞ൡ㋕㋛㋟㌬㌸㍱\0㍺㎤\0\0㏬㏰\0㐨㑈㑚㒭㒱㓊㓱\0㘖\0\0㘳cute;䅛qu\xef➺Ԁ;Eaceinpsyᇭ㋳㋵㋿㌂㌋㌏㌟㌦㌩;檴ǰ㋺\0㋼;檸on;䅡u\xe5ᇾĀ;dᇳ㌇il;䅟rc;䅝ƀEas㌖㌘㌛;檶p;檺im;择olint;樓i\xedሄ;䑁otƀ;be㌴ᵇ㌵担;橦΀Aacmstx㍆㍊㍗㍛㍞㍣㍭rr;懘rĀhr㍐㍒\xeb∨Ā;oਸ਼਴t耻\xa7䂧i;䀻war;椩mĀin㍩\xf0nu\xf3\xf1t;朶rĀ;o㍶⁕쀀\ud835\udd30Ȁacoy㎂㎆㎑㎠rp;景Āhy㎋㎏cy;䑉;䑈rtɭ㎙\0\0㎜i\xe4ᑤara\xec⹯耻\xad䂭Āgm㎨㎴maƀ;fv㎱㎲㎲䏃;䏂Ѐ;deglnprካ㏅㏉㏎㏖㏞㏡㏦ot;橪Ā;q኱ኰĀ;E㏓㏔檞;檠Ā;E㏛㏜檝;檟e;扆lus;樤arr;楲ar\xf2ᄽȀaeit㏸㐈㐏㐗Āls㏽㐄lsetm\xe9㍪hp;樳parsl;槤Ādlᑣ㐔e;挣Ā;e㐜㐝檪Ā;s㐢㐣檬;쀀⪬︀ƀflp㐮㐳㑂tcy;䑌Ā;b㐸㐹䀯Ā;a㐾㐿槄r;挿f;쀀\ud835\udd64aĀdr㑍ЂesĀ;u㑔㑕晠it\xbb㑕ƀcsu㑠㑹㒟Āau㑥㑯pĀ;sᆈ㑫;쀀⊓︀pĀ;sᆴ㑵;쀀⊔︀uĀbp㑿㒏ƀ;esᆗᆜ㒆etĀ;eᆗ㒍\xf1ᆝƀ;esᆨᆭ㒖etĀ;eᆨ㒝\xf1ᆮƀ;afᅻ㒦ְrť㒫ֱ\xbbᅼar\xf2ᅈȀcemt㒹㒾㓂㓅r;쀀\ud835\udcc8tm\xee\xf1i\xec㐕ar\xe6ᆾĀar㓎㓕rĀ;f㓔ឿ昆Āan㓚㓭ightĀep㓣㓪psilo\xeeỠh\xe9⺯s\xbb⡒ʀbcmnp㓻㕞ሉ㖋㖎Ҁ;Edemnprs㔎㔏㔑㔕㔞㔣㔬㔱㔶抂;櫅ot;檽Ā;dᇚ㔚ot;櫃ult;櫁ĀEe㔨㔪;櫋;把lus;檿arr;楹ƀeiu㔽㕒㕕tƀ;en㔎㕅㕋qĀ;qᇚ㔏eqĀ;q㔫㔨m;櫇Ābp㕚㕜;櫕;櫓c̀;acensᇭ㕬㕲㕹㕻㌦ppro\xf8㋺urlye\xf1ᇾ\xf1ᇳƀaes㖂㖈㌛ppro\xf8㌚q\xf1㌗g;晪ڀ123;Edehlmnps㖩㖬㖯ሜ㖲㖴㗀㗉㗕㗚㗟㗨㗭耻\xb9䂹耻\xb2䂲耻\xb3䂳;櫆Āos㖹㖼t;檾ub;櫘Ā;dሢ㗅ot;櫄sĀou㗏㗒l;柉b;櫗arr;楻ult;櫂ĀEe㗤㗦;櫌;抋lus;櫀ƀeiu㗴㘉㘌tƀ;enሜ㗼㘂qĀ;qሢ㖲eqĀ;q㗧㗤m;櫈Ābp㘑㘓;櫔;櫖ƀAan㘜㘠㘭rr;懙rĀhr㘦㘨\xeb∮Ā;oਫ਩war;椪lig耻\xdf䃟௡㙑㙝㙠ዎ㙳㙹\0㙾㛂\0\0\0\0\0㛛㜃\0㜉㝬\0\0\0㞇ɲ㙖\0\0㙛get;挖;䏄r\xeb๟ƀaey㙦㙫㙰ron;䅥dil;䅣;䑂lrec;挕r;쀀\ud835\udd31Ȁeiko㚆㚝㚵㚼ǲ㚋\0㚑eĀ4fኄኁaƀ;sv㚘㚙㚛䎸ym;䏑Ācn㚢㚲kĀas㚨㚮ppro\xf8዁im\xbbኬs\xf0ኞĀas㚺㚮\xf0዁rn耻\xfe䃾Ǭ̟㛆⋧es膀\xd7;bd㛏㛐㛘䃗Ā;aᤏ㛕r;樱;樰ƀeps㛡㛣㜀\xe1⩍Ȁ;bcf҆㛬㛰㛴ot;挶ir;櫱Ā;o㛹㛼쀀\ud835\udd65rk;櫚\xe1㍢rime;怴ƀaip㜏㜒㝤d\xe5ቈ΀adempst㜡㝍㝀㝑㝗㝜㝟ngleʀ;dlqr㜰㜱㜶㝀㝂斵own\xbbᶻeftĀ;e⠀㜾\xf1म;扜ightĀ;e㊪㝋\xf1ၚot;旬inus;樺lus;樹b;槍ime;樻ezium;揢ƀcht㝲㝽㞁Āry㝷㝻;쀀\ud835\udcc9;䑆cy;䑛rok;䅧Āio㞋㞎x\xf4᝷headĀlr㞗㞠eftarro\xf7ࡏightarrow\xbbཝऀAHabcdfghlmoprstuw㟐㟓㟗㟤㟰㟼㠎㠜㠣㠴㡑㡝㡫㢩㣌㣒㣪㣶r\xf2ϭar;楣Ācr㟜㟢ute耻\xfa䃺\xf2ᅐrǣ㟪\0㟭y;䑞ve;䅭Āiy㟵㟺rc耻\xfb䃻;䑃ƀabh㠃㠆㠋r\xf2Ꭽlac;䅱a\xf2ᏃĀir㠓㠘sht;楾;쀀\ud835\udd32rave耻\xf9䃹š㠧㠱rĀlr㠬㠮\xbbॗ\xbbႃlk;斀Āct㠹㡍ɯ㠿\0\0㡊rnĀ;e㡅㡆挜r\xbb㡆op;挏ri;旸Āal㡖㡚cr;䅫肻\xa8͉Āgp㡢㡦on;䅳f;쀀\ud835\udd66̀adhlsuᅋ㡸㡽፲㢑㢠own\xe1ᎳarpoonĀlr㢈㢌ef\xf4㠭igh\xf4㠯iƀ;hl㢙㢚㢜䏅\xbbᏺon\xbb㢚parrows;懈ƀcit㢰㣄㣈ɯ㢶\0\0㣁rnĀ;e㢼㢽挝r\xbb㢽op;挎ng;䅯ri;旹cr;쀀\ud835\udccaƀdir㣙㣝㣢ot;拰lde;䅩iĀ;f㜰㣨\xbb᠓Āam㣯㣲r\xf2㢨l耻\xfc䃼angle;榧ހABDacdeflnoprsz㤜㤟㤩㤭㦵㦸㦽㧟㧤㧨㧳㧹㧽㨁㨠r\xf2ϷarĀ;v㤦㤧櫨;櫩as\xe8ϡĀnr㤲㤷grt;榜΀eknprst㓣㥆㥋㥒㥝㥤㦖app\xe1␕othin\xe7ẖƀhir㓫⻈㥙op\xf4⾵Ā;hᎷ㥢\xefㆍĀiu㥩㥭gm\xe1㎳Ābp㥲㦄setneqĀ;q㥽㦀쀀⊊︀;쀀⫋︀setneqĀ;q㦏㦒쀀⊋︀;쀀⫌︀Āhr㦛㦟et\xe1㚜iangleĀlr㦪㦯eft\xbbथight\xbbၑy;䐲ash\xbbံƀelr㧄㧒㧗ƀ;beⷪ㧋㧏ar;抻q;扚lip;拮Ābt㧜ᑨa\xf2ᑩr;쀀\ud835\udd33tr\xe9㦮suĀbp㧯㧱\xbbജ\xbb൙pf;쀀\ud835\udd67ro\xf0໻tr\xe9㦴Ācu㨆㨋r;쀀\ud835\udccbĀbp㨐㨘nĀEe㦀㨖\xbb㥾nĀEe㦒㨞\xbb㦐igzag;榚΀cefoprs㨶㨻㩖㩛㩔㩡㩪irc;䅵Ādi㩀㩑Ābg㩅㩉ar;機eĀ;qᗺ㩏;扙erp;愘r;쀀\ud835\udd34pf;쀀\ud835\udd68Ā;eᑹ㩦at\xe8ᑹcr;쀀\ud835\udcccૣណ㪇\0㪋\0㪐㪛\0\0㪝㪨㪫㪯\0\0㫃㫎\0㫘ៜ៟tr\xe9៑r;쀀\ud835\udd35ĀAa㪔㪗r\xf2σr\xf2৶;䎾ĀAa㪡㪤r\xf2θr\xf2৫a\xf0✓is;拻ƀdptឤ㪵㪾Āfl㪺ឩ;쀀\ud835\udd69im\xe5ឲĀAa㫇㫊r\xf2ώr\xf2ਁĀcq㫒ីr;쀀\ud835\udccdĀpt៖㫜r\xe9។Ѐacefiosu㫰㫽㬈㬌㬑㬕㬛㬡cĀuy㫶㫻te耻\xfd䃽;䑏Āiy㬂㬆rc;䅷;䑋n耻\xa5䂥r;쀀\ud835\udd36cy;䑗pf;쀀\ud835\udd6acr;쀀\ud835\udcceĀcm㬦㬩y;䑎l耻\xff䃿Ԁacdefhiosw㭂㭈㭔㭘㭤㭩㭭㭴㭺㮀cute;䅺Āay㭍㭒ron;䅾;䐷ot;䅼Āet㭝㭡tr\xe6ᕟa;䎶r;쀀\ud835\udd37cy;䐶grarr;懝pf;쀀\ud835\udd6bcr;쀀\ud835\udccfĀjn㮅㮇;怍j;怌'.split("").map(e=>e.charCodeAt(0))),is=new Map([[0,65533],[128,8364],[130,8218],[131,402],[132,8222],[133,8230],[134,8224],[135,8225],[136,710],[137,8240],[138,352],[139,8249],[140,338],[142,381],[145,8216],[146,8217],[147,8220],[148,8221],[149,8226],[150,8211],[151,8212],[152,732],[153,8482],[154,353],[155,8250],[156,339],[158,382],[159,376]]);function io(e){return e>=o.ZERO&&e<=o.NINE}String.fromCodePoint,function(e){e[e.NUM=35]="NUM",e[e.SEMI=59]="SEMI",e[e.EQUALS=61]="EQUALS",e[e.ZERO=48]="ZERO",e[e.NINE=57]="NINE",e[e.LOWER_A=97]="LOWER_A",e[e.LOWER_F=102]="LOWER_F",e[e.LOWER_X=120]="LOWER_X",e[e.LOWER_Z=122]="LOWER_Z",e[e.UPPER_A=65]="UPPER_A",e[e.UPPER_F=70]="UPPER_F",e[e.UPPER_Z=90]="UPPER_Z"}(o||(o={})),function(e){e[e.VALUE_LENGTH=49152]="VALUE_LENGTH",e[e.BRANCH_LENGTH=16256]="BRANCH_LENGTH",e[e.JUMP_TABLE=127]="JUMP_TABLE"}(l||(l={})),function(e){e[e.EntityStart=0]="EntityStart",e[e.NumericStart=1]="NumericStart",e[e.NumericDecimal=2]="NumericDecimal",e[e.NumericHex=3]="NumericHex",e[e.NamedEntity=4]="NamedEntity"}(c||(c={})),function(e){e[e.Legacy=0]="Legacy",e[e.Strict=1]="Strict",e[e.Attribute=2]="Attribute"}(u||(u={}));class il{constructor(e,t,n){this.decodeTree=e,this.emitCodePoint=t,this.errors=n,this.state=c.EntityStart,this.consumed=1,this.result=0,this.treeIndex=0,this.excess=1,this.decodeMode=u.Strict}startEntity(e){this.decodeMode=e,this.state=c.EntityStart,this.result=0,this.treeIndex=0,this.excess=1,this.consumed=1}write(e,t){switch(this.state){case c.EntityStart:if(e.charCodeAt(t)===o.NUM)return this.state=c.NumericStart,this.consumed+=1,this.stateNumericStart(e,t+1);return this.state=c.NamedEntity,this.stateNamedEntity(e,t);case c.NumericStart:return this.stateNumericStart(e,t);case c.NumericDecimal:return this.stateNumericDecimal(e,t);case c.NumericHex:return this.stateNumericHex(e,t);case c.NamedEntity:return this.stateNamedEntity(e,t)}}stateNumericStart(e,t){return t>=e.length?-1:(32|e.charCodeAt(t))===o.LOWER_X?(this.state=c.NumericHex,this.consumed+=1,this.stateNumericHex(e,t+1)):(this.state=c.NumericDecimal,this.stateNumericDecimal(e,t))}addToNumericResult(e,t,n,r){if(t!==n){let a=n-t;this.result=this.result*Math.pow(r,a)+Number.parseInt(e.substr(t,a),r),this.consumed+=a}}stateNumericHex(e,t){let n=t;for(;t<e.length;){var r;let a=e.charCodeAt(t);if(!io(a)&&(!((r=a)>=o.UPPER_A)||!(r<=o.UPPER_F))&&(!(r>=o.LOWER_A)||!(r<=o.LOWER_F)))return this.addToNumericResult(e,n,t,16),this.emitNumericEntity(a,3);t+=1}return this.addToNumericResult(e,n,t,16),-1}stateNumericDecimal(e,t){let n=t;for(;t<e.length;){let r=e.charCodeAt(t);if(!io(r))return this.addToNumericResult(e,n,t,10),this.emitNumericEntity(r,2);t+=1}return this.addToNumericResult(e,n,t,10),-1}emitNumericEntity(e,t){var n,r,a;if(this.consumed<=t)return null===(n=this.errors)||void 0===n||n.absenceOfDigitsInNumericCharacterReference(this.consumed),0;if(e===o.SEMI)this.consumed+=1;else if(this.decodeMode===u.Strict)return 0;return this.emitCodePoint((r=this.result)>=55296&&r<=57343||r>1114111?65533:null!==(a=is.get(r))&&void 0!==a?a:r,this.consumed),this.errors&&(e!==o.SEMI&&this.errors.missingSemicolonAfterCharacterReference(),this.errors.validateNumericCharacterReference(this.result)),this.consumed}stateNamedEntity(e,t){let{decodeTree:n}=this,r=n[this.treeIndex],a=(r&l.VALUE_LENGTH)>>14;for(;t<e.length;t++,this.excess++){let i=e.charCodeAt(t);if(this.treeIndex=function(e,t,n,r){let a=(t&l.BRANCH_LENGTH)>>7,i=t&l.JUMP_TABLE;if(0===a)return 0!==i&&r===i?n:-1;if(i){let t=r-i;return t<0||t>=a?-1:e[n+t]-1}let s=n,o=s+a-1;for(;s<=o;){let t=s+o>>>1,n=e[t];if(n<r)s=t+1;else{if(!(n>r))return e[t+a];o=t-1}}return -1}(n,r,this.treeIndex+Math.max(1,a),i),this.treeIndex<0)return 0===this.result||this.decodeMode===u.Attribute&&(0===a||function(e){var t;return e===o.EQUALS||(t=e)>=o.UPPER_A&&t<=o.UPPER_Z||t>=o.LOWER_A&&t<=o.LOWER_Z||io(t)}(i))?0:this.emitNotTerminatedNamedEntity();if(0!=(a=((r=n[this.treeIndex])&l.VALUE_LENGTH)>>14)){if(i===o.SEMI)return this.emitNamedEntityData(this.treeIndex,a,this.consumed+this.excess);this.decodeMode!==u.Strict&&(this.result=this.treeIndex,this.consumed+=this.excess,this.excess=0)}}return -1}emitNotTerminatedNamedEntity(){var e;let{result:t,decodeTree:n}=this,r=(n[t]&l.VALUE_LENGTH)>>14;return this.emitNamedEntityData(t,r,this.consumed),null===(e=this.errors)||void 0===e||e.missingSemicolonAfterCharacterReference(),this.consumed}emitNamedEntityData(e,t,n){let{decodeTree:r}=this;return this.emitCodePoint(1===t?r[e]&~l.VALUE_LENGTH:r[e+1],n),3===t&&this.emitCodePoint(r[e+2],n),n}end(){var e;switch(this.state){case c.NamedEntity:return 0!==this.result&&(this.decodeMode!==u.Attribute||this.result===this.treeIndex)?this.emitNotTerminatedNamedEntity():0;case c.NumericDecimal:return this.emitNumericEntity(0,2);case c.NumericHex:return this.emitNumericEntity(0,3);case c.NumericStart:return null===(e=this.errors)||void 0===e||e.absenceOfDigitsInNumericCharacterReference(this.consumed),0;case c.EntityStart:return 0}}}(function(e){e.HTML="http://www.w3.org/1999/xhtml",e.MATHML="http://www.w3.org/1998/Math/MathML",e.SVG="http://www.w3.org/2000/svg",e.XLINK="http://www.w3.org/1999/xlink",e.XML="http://www.w3.org/XML/1998/namespace",e.XMLNS="http://www.w3.org/2000/xmlns/"})(h||(h={})),function(e){e.TYPE="type",e.ACTION="action",e.ENCODING="encoding",e.PROMPT="prompt",e.NAME="name",e.COLOR="color",e.FACE="face",e.SIZE="size"}(d||(d={})),function(e){e.NO_QUIRKS="no-quirks",e.QUIRKS="quirks",e.LIMITED_QUIRKS="limited-quirks"}(p||(p={})),function(e){e.A="a",e.ADDRESS="address",e.ANNOTATION_XML="annotation-xml",e.APPLET="applet",e.AREA="area",e.ARTICLE="article",e.ASIDE="aside",e.B="b",e.BASE="base",e.BASEFONT="basefont",e.BGSOUND="bgsound",e.BIG="big",e.BLOCKQUOTE="blockquote",e.BODY="body",e.BR="br",e.BUTTON="button",e.CAPTION="caption",e.CENTER="center",e.CODE="code",e.COL="col",e.COLGROUP="colgroup",e.DD="dd",e.DESC="desc",e.DETAILS="details",e.DIALOG="dialog",e.DIR="dir",e.DIV="div",e.DL="dl",e.DT="dt",e.EM="em",e.EMBED="embed",e.FIELDSET="fieldset",e.FIGCAPTION="figcaption",e.FIGURE="figure",e.FONT="font",e.FOOTER="footer",e.FOREIGN_OBJECT="foreignObject",e.FORM="form",e.FRAME="frame",e.FRAMESET="frameset",e.H1="h1",e.H2="h2",e.H3="h3",e.H4="h4",e.H5="h5",e.H6="h6",e.HEAD="head",e.HEADER="header",e.HGROUP="hgroup",e.HR="hr",e.HTML="html",e.I="i",e.IMG="img",e.IMAGE="image",e.INPUT="input",e.IFRAME="iframe",e.KEYGEN="keygen",e.LABEL="label",e.LI="li",e.LINK="link",e.LISTING="listing",e.MAIN="main",e.MALIGNMARK="malignmark",e.MARQUEE="marquee",e.MATH="math",e.MENU="menu",e.META="meta",e.MGLYPH="mglyph",e.MI="mi",e.MO="mo",e.MN="mn",e.MS="ms",e.MTEXT="mtext",e.NAV="nav",e.NOBR="nobr",e.NOFRAMES="noframes",e.NOEMBED="noembed",e.NOSCRIPT="noscript",e.OBJECT="object",e.OL="ol",e.OPTGROUP="optgroup",e.OPTION="option",e.P="p",e.PARAM="param",e.PLAINTEXT="plaintext",e.PRE="pre",e.RB="rb",e.RP="rp",e.RT="rt",e.RTC="rtc",e.RUBY="ruby",e.S="s",e.SCRIPT="script",e.SEARCH="search",e.SECTION="section",e.SELECT="select",e.SOURCE="source",e.SMALL="small",e.SPAN="span",e.STRIKE="strike",e.STRONG="strong",e.STYLE="style",e.SUB="sub",e.SUMMARY="summary",e.SUP="sup",e.TABLE="table",e.TBODY="tbody",e.TEMPLATE="template",e.TEXTAREA="textarea",e.TFOOT="tfoot",e.TD="td",e.TH="th",e.THEAD="thead",e.TITLE="title",e.TR="tr",e.TRACK="track",e.TT="tt",e.U="u",e.UL="ul",e.SVG="svg",e.VAR="var",e.WBR="wbr",e.XMP="xmp"}(m||(m={})),function(e){e[e.UNKNOWN=0]="UNKNOWN",e[e.A=1]="A",e[e.ADDRESS=2]="ADDRESS",e[e.ANNOTATION_XML=3]="ANNOTATION_XML",e[e.APPLET=4]="APPLET",e[e.AREA=5]="AREA",e[e.ARTICLE=6]="ARTICLE",e[e.ASIDE=7]="ASIDE",e[e.B=8]="B",e[e.BASE=9]="BASE",e[e.BASEFONT=10]="BASEFONT",e[e.BGSOUND=11]="BGSOUND",e[e.BIG=12]="BIG",e[e.BLOCKQUOTE=13]="BLOCKQUOTE",e[e.BODY=14]="BODY",e[e.BR=15]="BR",e[e.BUTTON=16]="BUTTON",e[e.CAPTION=17]="CAPTION",e[e.CENTER=18]="CENTER",e[e.CODE=19]="CODE",e[e.COL=20]="COL",e[e.COLGROUP=21]="COLGROUP",e[e.DD=22]="DD",e[e.DESC=23]="DESC",e[e.DETAILS=24]="DETAILS",e[e.DIALOG=25]="DIALOG",e[e.DIR=26]="DIR",e[e.DIV=27]="DIV",e[e.DL=28]="DL",e[e.DT=29]="DT",e[e.EM=30]="EM",e[e.EMBED=31]="EMBED",e[e.FIELDSET=32]="FIELDSET",e[e.FIGCAPTION=33]="FIGCAPTION",e[e.FIGURE=34]="FIGURE",e[e.FONT=35]="FONT",e[e.FOOTER=36]="FOOTER",e[e.FOREIGN_OBJECT=37]="FOREIGN_OBJECT",e[e.FORM=38]="FORM",e[e.FRAME=39]="FRAME",e[e.FRAMESET=40]="FRAMESET",e[e.H1=41]="H1",e[e.H2=42]="H2",e[e.H3=43]="H3",e[e.H4=44]="H4",e[e.H5=45]="H5",e[e.H6=46]="H6",e[e.HEAD=47]="HEAD",e[e.HEADER=48]="HEADER",e[e.HGROUP=49]="HGROUP",e[e.HR=50]="HR",e[e.HTML=51]="HTML",e[e.I=52]="I",e[e.IMG=53]="IMG",e[e.IMAGE=54]="IMAGE",e[e.INPUT=55]="INPUT",e[e.IFRAME=56]="IFRAME",e[e.KEYGEN=57]="KEYGEN",e[e.LABEL=58]="LABEL",e[e.LI=59]="LI",e[e.LINK=60]="LINK",e[e.LISTING=61]="LISTING",e[e.MAIN=62]="MAIN",e[e.MALIGNMARK=63]="MALIGNMARK",e[e.MARQUEE=64]="MARQUEE",e[e.MATH=65]="MATH",e[e.MENU=66]="MENU",e[e.META=67]="META",e[e.MGLYPH=68]="MGLYPH",e[e.MI=69]="MI",e[e.MO=70]="MO",e[e.MN=71]="MN",e[e.MS=72]="MS",e[e.MTEXT=73]="MTEXT",e[e.NAV=74]="NAV",e[e.NOBR=75]="NOBR",e[e.NOFRAMES=76]="NOFRAMES",e[e.NOEMBED=77]="NOEMBED",e[e.NOSCRIPT=78]="NOSCRIPT",e[e.OBJECT=79]="OBJECT",e[e.OL=80]="OL",e[e.OPTGROUP=81]="OPTGROUP",e[e.OPTION=82]="OPTION",e[e.P=83]="P",e[e.PARAM=84]="PARAM",e[e.PLAINTEXT=85]="PLAINTEXT",e[e.PRE=86]="PRE",e[e.RB=87]="RB",e[e.RP=88]="RP",e[e.RT=89]="RT",e[e.RTC=90]="RTC",e[e.RUBY=91]="RUBY",e[e.S=92]="S",e[e.SCRIPT=93]="SCRIPT",e[e.SEARCH=94]="SEARCH",e[e.SECTION=95]="SECTION",e[e.SELECT=96]="SELECT",e[e.SOURCE=97]="SOURCE",e[e.SMALL=98]="SMALL",e[e.SPAN=99]="SPAN",e[e.STRIKE=100]="STRIKE",e[e.STRONG=101]="STRONG",e[e.STYLE=102]="STYLE",e[e.SUB=103]="SUB",e[e.SUMMARY=104]="SUMMARY",e[e.SUP=105]="SUP",e[e.TABLE=106]="TABLE",e[e.TBODY=107]="TBODY",e[e.TEMPLATE=108]="TEMPLATE",e[e.TEXTAREA=109]="TEXTAREA",e[e.TFOOT=110]="TFOOT",e[e.TD=111]="TD",e[e.TH=112]="TH",e[e.THEAD=113]="THEAD",e[e.TITLE=114]="TITLE",e[e.TR=115]="TR",e[e.TRACK=116]="TRACK",e[e.TT=117]="TT",e[e.U=118]="U",e[e.UL=119]="UL",e[e.SVG=120]="SVG",e[e.VAR=121]="VAR",e[e.WBR=122]="WBR",e[e.XMP=123]="XMP"}(f||(f={}));let ic=new Map([[m.A,f.A],[m.ADDRESS,f.ADDRESS],[m.ANNOTATION_XML,f.ANNOTATION_XML],[m.APPLET,f.APPLET],[m.AREA,f.AREA],[m.ARTICLE,f.ARTICLE],[m.ASIDE,f.ASIDE],[m.B,f.B],[m.BASE,f.BASE],[m.BASEFONT,f.BASEFONT],[m.BGSOUND,f.BGSOUND],[m.BIG,f.BIG],[m.BLOCKQUOTE,f.BLOCKQUOTE],[m.BODY,f.BODY],[m.BR,f.BR],[m.BUTTON,f.BUTTON],[m.CAPTION,f.CAPTION],[m.CENTER,f.CENTER],[m.CODE,f.CODE],[m.COL,f.COL],[m.COLGROUP,f.COLGROUP],[m.DD,f.DD],[m.DESC,f.DESC],[m.DETAILS,f.DETAILS],[m.DIALOG,f.DIALOG],[m.DIR,f.DIR],[m.DIV,f.DIV],[m.DL,f.DL],[m.DT,f.DT],[m.EM,f.EM],[m.EMBED,f.EMBED],[m.FIELDSET,f.FIELDSET],[m.FIGCAPTION,f.FIGCAPTION],[m.FIGURE,f.FIGURE],[m.FONT,f.FONT],[m.FOOTER,f.FOOTER],[m.FOREIGN_OBJECT,f.FOREIGN_OBJECT],[m.FORM,f.FORM],[m.FRAME,f.FRAME],[m.FRAMESET,f.FRAMESET],[m.H1,f.H1],[m.H2,f.H2],[m.H3,f.H3],[m.H4,f.H4],[m.H5,f.H5],[m.H6,f.H6],[m.HEAD,f.HEAD],[m.HEADER,f.HEADER],[m.HGROUP,f.HGROUP],[m.HR,f.HR],[m.HTML,f.HTML],[m.I,f.I],[m.IMG,f.IMG],[m.IMAGE,f.IMAGE],[m.INPUT,f.INPUT],[m.IFRAME,f.IFRAME],[m.KEYGEN,f.KEYGEN],[m.LABEL,f.LABEL],[m.LI,f.LI],[m.LINK,f.LINK],[m.LISTING,f.LISTING],[m.MAIN,f.MAIN],[m.MALIGNMARK,f.MALIGNMARK],[m.MARQUEE,f.MARQUEE],[m.MATH,f.MATH],[m.MENU,f.MENU],[m.META,f.META],[m.MGLYPH,f.MGLYPH],[m.MI,f.MI],[m.MO,f.MO],[m.MN,f.MN],[m.MS,f.MS],[m.MTEXT,f.MTEXT],[m.NAV,f.NAV],[m.NOBR,f.NOBR],[m.NOFRAMES,f.NOFRAMES],[m.NOEMBED,f.NOEMBED],[m.NOSCRIPT,f.NOSCRIPT],[m.OBJECT,f.OBJECT],[m.OL,f.OL],[m.OPTGROUP,f.OPTGROUP],[m.OPTION,f.OPTION],[m.P,f.P],[m.PARAM,f.PARAM],[m.PLAINTEXT,f.PLAINTEXT],[m.PRE,f.PRE],[m.RB,f.RB],[m.RP,f.RP],[m.RT,f.RT],[m.RTC,f.RTC],[m.RUBY,f.RUBY],[m.S,f.S],[m.SCRIPT,f.SCRIPT],[m.SEARCH,f.SEARCH],[m.SECTION,f.SECTION],[m.SELECT,f.SELECT],[m.SOURCE,f.SOURCE],[m.SMALL,f.SMALL],[m.SPAN,f.SPAN],[m.STRIKE,f.STRIKE],[m.STRONG,f.STRONG],[m.STYLE,f.STYLE],[m.SUB,f.SUB],[m.SUMMARY,f.SUMMARY],[m.SUP,f.SUP],[m.TABLE,f.TABLE],[m.TBODY,f.TBODY],[m.TEMPLATE,f.TEMPLATE],[m.TEXTAREA,f.TEXTAREA],[m.TFOOT,f.TFOOT],[m.TD,f.TD],[m.TH,f.TH],[m.THEAD,f.THEAD],[m.TITLE,f.TITLE],[m.TR,f.TR],[m.TRACK,f.TRACK],[m.TT,f.TT],[m.U,f.U],[m.UL,f.UL],[m.SVG,f.SVG],[m.VAR,f.VAR],[m.WBR,f.WBR],[m.XMP,f.XMP]]);function iu(e){var t;return null!==(t=ic.get(e))&&void 0!==t?t:f.UNKNOWN}let ih=f,id={[h.HTML]:new Set([ih.ADDRESS,ih.APPLET,ih.AREA,ih.ARTICLE,ih.ASIDE,ih.BASE,ih.BASEFONT,ih.BGSOUND,ih.BLOCKQUOTE,ih.BODY,ih.BR,ih.BUTTON,ih.CAPTION,ih.CENTER,ih.COL,ih.COLGROUP,ih.DD,ih.DETAILS,ih.DIR,ih.DIV,ih.DL,ih.DT,ih.EMBED,ih.FIELDSET,ih.FIGCAPTION,ih.FIGURE,ih.FOOTER,ih.FORM,ih.FRAME,ih.FRAMESET,ih.H1,ih.H2,ih.H3,ih.H4,ih.H5,ih.H6,ih.HEAD,ih.HEADER,ih.HGROUP,ih.HR,ih.HTML,ih.IFRAME,ih.IMG,ih.INPUT,ih.LI,ih.LINK,ih.LISTING,ih.MAIN,ih.MARQUEE,ih.MENU,ih.META,ih.NAV,ih.NOEMBED,ih.NOFRAMES,ih.NOSCRIPT,ih.OBJECT,ih.OL,ih.P,ih.PARAM,ih.PLAINTEXT,ih.PRE,ih.SCRIPT,ih.SECTION,ih.SELECT,ih.SOURCE,ih.STYLE,ih.SUMMARY,ih.TABLE,ih.TBODY,ih.TD,ih.TEMPLATE,ih.TEXTAREA,ih.TFOOT,ih.TH,ih.THEAD,ih.TITLE,ih.TR,ih.TRACK,ih.UL,ih.WBR,ih.XMP]),[h.MATHML]:new Set([ih.MI,ih.MO,ih.MN,ih.MS,ih.MTEXT,ih.ANNOTATION_XML]),[h.SVG]:new Set([ih.TITLE,ih.FOREIGN_OBJECT,ih.DESC]),[h.XLINK]:new Set,[h.XML]:new Set,[h.XMLNS]:new Set},ip=new Set([ih.H1,ih.H2,ih.H3,ih.H4,ih.H5,ih.H6]);m.STYLE,m.SCRIPT,m.XMP,m.IFRAME,m.NOEMBED,m.NOFRAMES,m.PLAINTEXT,function(e){e[e.DATA=0]="DATA",e[e.RCDATA=1]="RCDATA",e[e.RAWTEXT=2]="RAWTEXT",e[e.SCRIPT_DATA=3]="SCRIPT_DATA",e[e.PLAINTEXT=4]="PLAINTEXT",e[e.TAG_OPEN=5]="TAG_OPEN",e[e.END_TAG_OPEN=6]="END_TAG_OPEN",e[e.TAG_NAME=7]="TAG_NAME",e[e.RCDATA_LESS_THAN_SIGN=8]="RCDATA_LESS_THAN_SIGN",e[e.RCDATA_END_TAG_OPEN=9]="RCDATA_END_TAG_OPEN",e[e.RCDATA_END_TAG_NAME=10]="RCDATA_END_TAG_NAME",e[e.RAWTEXT_LESS_THAN_SIGN=11]="RAWTEXT_LESS_THAN_SIGN",e[e.RAWTEXT_END_TAG_OPEN=12]="RAWTEXT_END_TAG_OPEN",e[e.RAWTEXT_END_TAG_NAME=13]="RAWTEXT_END_TAG_NAME",e[e.SCRIPT_DATA_LESS_THAN_SIGN=14]="SCRIPT_DATA_LESS_THAN_SIGN",e[e.SCRIPT_DATA_END_TAG_OPEN=15]="SCRIPT_DATA_END_TAG_OPEN",e[e.SCRIPT_DATA_END_TAG_NAME=16]="SCRIPT_DATA_END_TAG_NAME",e[e.SCRIPT_DATA_ESCAPE_START=17]="SCRIPT_DATA_ESCAPE_START",e[e.SCRIPT_DATA_ESCAPE_START_DASH=18]="SCRIPT_DATA_ESCAPE_START_DASH",e[e.SCRIPT_DATA_ESCAPED=19]="SCRIPT_DATA_ESCAPED",e[e.SCRIPT_DATA_ESCAPED_DASH=20]="SCRIPT_DATA_ESCAPED_DASH",e[e.SCRIPT_DATA_ESCAPED_DASH_DASH=21]="SCRIPT_DATA_ESCAPED_DASH_DASH",e[e.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN=22]="SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN",e[e.SCRIPT_DATA_ESCAPED_END_TAG_OPEN=23]="SCRIPT_DATA_ESCAPED_END_TAG_OPEN",e[e.SCRIPT_DATA_ESCAPED_END_TAG_NAME=24]="SCRIPT_DATA_ESCAPED_END_TAG_NAME",e[e.SCRIPT_DATA_DOUBLE_ESCAPE_START=25]="SCRIPT_DATA_DOUBLE_ESCAPE_START",e[e.SCRIPT_DATA_DOUBLE_ESCAPED=26]="SCRIPT_DATA_DOUBLE_ESCAPED",e[e.SCRIPT_DATA_DOUBLE_ESCAPED_DASH=27]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH",e[e.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH=28]="SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH",e[e.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN=29]="SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN",e[e.SCRIPT_DATA_DOUBLE_ESCAPE_END=30]="SCRIPT_DATA_DOUBLE_ESCAPE_END",e[e.BEFORE_ATTRIBUTE_NAME=31]="BEFORE_ATTRIBUTE_NAME",e[e.ATTRIBUTE_NAME=32]="ATTRIBUTE_NAME",e[e.AFTER_ATTRIBUTE_NAME=33]="AFTER_ATTRIBUTE_NAME",e[e.BEFORE_ATTRIBUTE_VALUE=34]="BEFORE_ATTRIBUTE_VALUE",e[e.ATTRIBUTE_VALUE_DOUBLE_QUOTED=35]="ATTRIBUTE_VALUE_DOUBLE_QUOTED",e[e.ATTRIBUTE_VALUE_SINGLE_QUOTED=36]="ATTRIBUTE_VALUE_SINGLE_QUOTED",e[e.ATTRIBUTE_VALUE_UNQUOTED=37]="ATTRIBUTE_VALUE_UNQUOTED",e[e.AFTER_ATTRIBUTE_VALUE_QUOTED=38]="AFTER_ATTRIBUTE_VALUE_QUOTED",e[e.SELF_CLOSING_START_TAG=39]="SELF_CLOSING_START_TAG",e[e.BOGUS_COMMENT=40]="BOGUS_COMMENT",e[e.MARKUP_DECLARATION_OPEN=41]="MARKUP_DECLARATION_OPEN",e[e.COMMENT_START=42]="COMMENT_START",e[e.COMMENT_START_DASH=43]="COMMENT_START_DASH",e[e.COMMENT=44]="COMMENT",e[e.COMMENT_LESS_THAN_SIGN=45]="COMMENT_LESS_THAN_SIGN",e[e.COMMENT_LESS_THAN_SIGN_BANG=46]="COMMENT_LESS_THAN_SIGN_BANG",e[e.COMMENT_LESS_THAN_SIGN_BANG_DASH=47]="COMMENT_LESS_THAN_SIGN_BANG_DASH",e[e.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH=48]="COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH",e[e.COMMENT_END_DASH=49]="COMMENT_END_DASH",e[e.COMMENT_END=50]="COMMENT_END",e[e.COMMENT_END_BANG=51]="COMMENT_END_BANG",e[e.DOCTYPE=52]="DOCTYPE",e[e.BEFORE_DOCTYPE_NAME=53]="BEFORE_DOCTYPE_NAME",e[e.DOCTYPE_NAME=54]="DOCTYPE_NAME",e[e.AFTER_DOCTYPE_NAME=55]="AFTER_DOCTYPE_NAME",e[e.AFTER_DOCTYPE_PUBLIC_KEYWORD=56]="AFTER_DOCTYPE_PUBLIC_KEYWORD",e[e.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER=57]="BEFORE_DOCTYPE_PUBLIC_IDENTIFIER",e[e.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED=58]="DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED",e[e.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED=59]="DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED",e[e.AFTER_DOCTYPE_PUBLIC_IDENTIFIER=60]="AFTER_DOCTYPE_PUBLIC_IDENTIFIER",e[e.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS=61]="BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS",e[e.AFTER_DOCTYPE_SYSTEM_KEYWORD=62]="AFTER_DOCTYPE_SYSTEM_KEYWORD",e[e.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER=63]="BEFORE_DOCTYPE_SYSTEM_IDENTIFIER",e[e.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED=64]="DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED",e[e.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED=65]="DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED",e[e.AFTER_DOCTYPE_SYSTEM_IDENTIFIER=66]="AFTER_DOCTYPE_SYSTEM_IDENTIFIER",e[e.BOGUS_DOCTYPE=67]="BOGUS_DOCTYPE",e[e.CDATA_SECTION=68]="CDATA_SECTION",e[e.CDATA_SECTION_BRACKET=69]="CDATA_SECTION_BRACKET",e[e.CDATA_SECTION_END=70]="CDATA_SECTION_END",e[e.CHARACTER_REFERENCE=71]="CHARACTER_REFERENCE",e[e.AMBIGUOUS_AMPERSAND=72]="AMBIGUOUS_AMPERSAND"}(E||(E={}));let im={DATA:E.DATA,RCDATA:E.RCDATA,RAWTEXT:E.RAWTEXT,SCRIPT_DATA:E.SCRIPT_DATA,PLAINTEXT:E.PLAINTEXT,CDATA_SECTION:E.CDATA_SECTION};function iE(e){return e>=r.LATIN_CAPITAL_A&&e<=r.LATIN_CAPITAL_Z}function iT(e){return e>=r.LATIN_SMALL_A&&e<=r.LATIN_SMALL_Z||iE(e)}function ig(e){return iT(e)||e>=r.DIGIT_0&&e<=r.DIGIT_9}function iA(e){return e===r.SPACE||e===r.LINE_FEED||e===r.TABULATION||e===r.FORM_FEED}function i_(e){return iA(e)||e===r.SOLIDUS||e===r.GREATER_THAN_SIGN}class ik{constructor(e,t){this.options=e,this.handler=t,this.paused=!1,this.inLoop=!1,this.inForeignNode=!1,this.lastStartTagName="",this.active=!1,this.state=E.DATA,this.returnState=E.DATA,this.entityStartPos=0,this.consumedAfterSnapshot=-1,this.currentCharacterToken=null,this.currentToken=null,this.currentAttr={name:"",value:""},this.preprocessor=new ir(t),this.currentLocation=this.getCurrentLocation(-1),this.entityDecoder=new il(ii,(e,t)=>{this.preprocessor.pos=this.entityStartPos+t-1,this._flushCodePointConsumedAsCharacterReference(e)},t.onParseError?{missingSemicolonAfterCharacterReference:()=>{this._err(a.missingSemicolonAfterCharacterReference,1)},absenceOfDigitsInNumericCharacterReference:e=>{this._err(a.absenceOfDigitsInNumericCharacterReference,this.entityStartPos-this.preprocessor.pos+e)},validateNumericCharacterReference:e=>{let t=function(e){return e===r.NULL?a.nullCharacterReference:e>1114111?a.characterReferenceOutsideUnicodeRange:a7(e)?a.surrogateCharacterReference:it(e)?a.noncharacterCharacterReference:ie(e)||e===r.CARRIAGE_RETURN?a.controlCharacterReference:null}(e);t&&this._err(t,1)}}:void 0)}_err(e,t=0){var n,r;null===(r=(n=this.handler).onParseError)||void 0===r||r.call(n,this.preprocessor.getError(e,t))}getCurrentLocation(e){return this.options.sourceCodeLocationInfo?{startLine:this.preprocessor.line,startCol:this.preprocessor.col-e,startOffset:this.preprocessor.offset-e,endLine:-1,endCol:-1,endOffset:-1}:null}_runParsingLoop(){if(!this.inLoop){for(this.inLoop=!0;this.active&&!this.paused;){this.consumedAfterSnapshot=0;let e=this._consume();this._ensureHibernation()||this._callState(e)}this.inLoop=!1}}pause(){this.paused=!0}resume(e){if(!this.paused)throw Error("Parser was already resumed");this.paused=!1,this.inLoop||(this._runParsingLoop(),this.paused||null==e||e())}write(e,t,n){this.active=!0,this.preprocessor.write(e,t),this._runParsingLoop(),this.paused||null==n||n()}insertHtmlAtCurrentPos(e){this.active=!0,this.preprocessor.insertHtmlAtCurrentPos(e),this._runParsingLoop()}_ensureHibernation(){return!!this.preprocessor.endOfChunkHit&&(this.preprocessor.retreat(this.consumedAfterSnapshot),this.consumedAfterSnapshot=0,this.active=!1,!0)}_consume(){return this.consumedAfterSnapshot++,this.preprocessor.advance()}_advanceBy(e){this.consumedAfterSnapshot+=e;for(let t=0;t<e;t++)this.preprocessor.advance()}_consumeSequenceIfMatch(e,t){return!!this.preprocessor.startsWith(e,t)&&(this._advanceBy(e.length-1),!0)}_createStartTagToken(){this.currentToken={type:i.START_TAG,tagName:"",tagID:f.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(1)}}_createEndTagToken(){this.currentToken={type:i.END_TAG,tagName:"",tagID:f.UNKNOWN,selfClosing:!1,ackSelfClosing:!1,attrs:[],location:this.getCurrentLocation(2)}}_createCommentToken(e){this.currentToken={type:i.COMMENT,data:"",location:this.getCurrentLocation(e)}}_createDoctypeToken(e){this.currentToken={type:i.DOCTYPE,name:e,forceQuirks:!1,publicId:null,systemId:null,location:this.currentLocation}}_createCharacterToken(e,t){this.currentCharacterToken={type:e,chars:t,location:this.currentLocation}}_createAttr(e){this.currentAttr={name:e,value:""},this.currentLocation=this.getCurrentLocation(0)}_leaveAttrName(){var e,t;let n=this.currentToken;null===ia(n,this.currentAttr.name)?(n.attrs.push(this.currentAttr),n.location&&this.currentLocation&&((null!==(e=(t=n.location).attrs)&&void 0!==e?e:t.attrs=Object.create(null))[this.currentAttr.name]=this.currentLocation,this._leaveAttrValue())):this._err(a.duplicateAttribute)}_leaveAttrValue(){this.currentLocation&&(this.currentLocation.endLine=this.preprocessor.line,this.currentLocation.endCol=this.preprocessor.col,this.currentLocation.endOffset=this.preprocessor.offset)}prepareToken(e){this._emitCurrentCharacterToken(e.location),this.currentToken=null,e.location&&(e.location.endLine=this.preprocessor.line,e.location.endCol=this.preprocessor.col+1,e.location.endOffset=this.preprocessor.offset+1),this.currentLocation=this.getCurrentLocation(-1)}emitCurrentTagToken(){let e=this.currentToken;this.prepareToken(e),e.tagID=iu(e.tagName),e.type===i.START_TAG?(this.lastStartTagName=e.tagName,this.handler.onStartTag(e)):(e.attrs.length>0&&this._err(a.endTagWithAttributes),e.selfClosing&&this._err(a.endTagWithTrailingSolidus),this.handler.onEndTag(e)),this.preprocessor.dropParsedChunk()}emitCurrentComment(e){this.prepareToken(e),this.handler.onComment(e),this.preprocessor.dropParsedChunk()}emitCurrentDoctype(e){this.prepareToken(e),this.handler.onDoctype(e),this.preprocessor.dropParsedChunk()}_emitCurrentCharacterToken(e){if(this.currentCharacterToken){switch(e&&this.currentCharacterToken.location&&(this.currentCharacterToken.location.endLine=e.startLine,this.currentCharacterToken.location.endCol=e.startCol,this.currentCharacterToken.location.endOffset=e.startOffset),this.currentCharacterToken.type){case i.CHARACTER:this.handler.onCharacter(this.currentCharacterToken);break;case i.NULL_CHARACTER:this.handler.onNullCharacter(this.currentCharacterToken);break;case i.WHITESPACE_CHARACTER:this.handler.onWhitespaceCharacter(this.currentCharacterToken)}this.currentCharacterToken=null}}_emitEOFToken(){let e=this.getCurrentLocation(0);e&&(e.endLine=e.startLine,e.endCol=e.startCol,e.endOffset=e.startOffset),this._emitCurrentCharacterToken(e),this.handler.onEof({type:i.EOF,location:e}),this.active=!1}_appendCharToCurrentCharacterToken(e,t){if(this.currentCharacterToken){if(this.currentCharacterToken.type===e){this.currentCharacterToken.chars+=t;return}this.currentLocation=this.getCurrentLocation(0),this._emitCurrentCharacterToken(this.currentLocation),this.preprocessor.dropParsedChunk()}this._createCharacterToken(e,t)}_emitCodePoint(e){let t=iA(e)?i.WHITESPACE_CHARACTER:e===r.NULL?i.NULL_CHARACTER:i.CHARACTER;this._appendCharToCurrentCharacterToken(t,String.fromCodePoint(e))}_emitChars(e){this._appendCharToCurrentCharacterToken(i.CHARACTER,e)}_startCharacterReference(){this.returnState=this.state,this.state=E.CHARACTER_REFERENCE,this.entityStartPos=this.preprocessor.pos,this.entityDecoder.startEntity(this._isCharacterReferenceInAttribute()?u.Attribute:u.Legacy)}_isCharacterReferenceInAttribute(){return this.returnState===E.ATTRIBUTE_VALUE_DOUBLE_QUOTED||this.returnState===E.ATTRIBUTE_VALUE_SINGLE_QUOTED||this.returnState===E.ATTRIBUTE_VALUE_UNQUOTED}_flushCodePointConsumedAsCharacterReference(e){this._isCharacterReferenceInAttribute()?this.currentAttr.value+=String.fromCodePoint(e):this._emitCodePoint(e)}_callState(e){switch(this.state){case E.DATA:this._stateData(e);break;case E.RCDATA:this._stateRcdata(e);break;case E.RAWTEXT:this._stateRawtext(e);break;case E.SCRIPT_DATA:this._stateScriptData(e);break;case E.PLAINTEXT:this._statePlaintext(e);break;case E.TAG_OPEN:this._stateTagOpen(e);break;case E.END_TAG_OPEN:this._stateEndTagOpen(e);break;case E.TAG_NAME:this._stateTagName(e);break;case E.RCDATA_LESS_THAN_SIGN:this._stateRcdataLessThanSign(e);break;case E.RCDATA_END_TAG_OPEN:this._stateRcdataEndTagOpen(e);break;case E.RCDATA_END_TAG_NAME:this._stateRcdataEndTagName(e);break;case E.RAWTEXT_LESS_THAN_SIGN:this._stateRawtextLessThanSign(e);break;case E.RAWTEXT_END_TAG_OPEN:this._stateRawtextEndTagOpen(e);break;case E.RAWTEXT_END_TAG_NAME:this._stateRawtextEndTagName(e);break;case E.SCRIPT_DATA_LESS_THAN_SIGN:this._stateScriptDataLessThanSign(e);break;case E.SCRIPT_DATA_END_TAG_OPEN:this._stateScriptDataEndTagOpen(e);break;case E.SCRIPT_DATA_END_TAG_NAME:this._stateScriptDataEndTagName(e);break;case E.SCRIPT_DATA_ESCAPE_START:this._stateScriptDataEscapeStart(e);break;case E.SCRIPT_DATA_ESCAPE_START_DASH:this._stateScriptDataEscapeStartDash(e);break;case E.SCRIPT_DATA_ESCAPED:this._stateScriptDataEscaped(e);break;case E.SCRIPT_DATA_ESCAPED_DASH:this._stateScriptDataEscapedDash(e);break;case E.SCRIPT_DATA_ESCAPED_DASH_DASH:this._stateScriptDataEscapedDashDash(e);break;case E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataEscapedLessThanSign(e);break;case E.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:this._stateScriptDataEscapedEndTagOpen(e);break;case E.SCRIPT_DATA_ESCAPED_END_TAG_NAME:this._stateScriptDataEscapedEndTagName(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPE_START:this._stateScriptDataDoubleEscapeStart(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPED:this._stateScriptDataDoubleEscaped(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH:this._stateScriptDataDoubleEscapedDash(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH:this._stateScriptDataDoubleEscapedDashDash(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN:this._stateScriptDataDoubleEscapedLessThanSign(e);break;case E.SCRIPT_DATA_DOUBLE_ESCAPE_END:this._stateScriptDataDoubleEscapeEnd(e);break;case E.BEFORE_ATTRIBUTE_NAME:this._stateBeforeAttributeName(e);break;case E.ATTRIBUTE_NAME:this._stateAttributeName(e);break;case E.AFTER_ATTRIBUTE_NAME:this._stateAfterAttributeName(e);break;case E.BEFORE_ATTRIBUTE_VALUE:this._stateBeforeAttributeValue(e);break;case E.ATTRIBUTE_VALUE_DOUBLE_QUOTED:this._stateAttributeValueDoubleQuoted(e);break;case E.ATTRIBUTE_VALUE_SINGLE_QUOTED:this._stateAttributeValueSingleQuoted(e);break;case E.ATTRIBUTE_VALUE_UNQUOTED:this._stateAttributeValueUnquoted(e);break;case E.AFTER_ATTRIBUTE_VALUE_QUOTED:this._stateAfterAttributeValueQuoted(e);break;case E.SELF_CLOSING_START_TAG:this._stateSelfClosingStartTag(e);break;case E.BOGUS_COMMENT:this._stateBogusComment(e);break;case E.MARKUP_DECLARATION_OPEN:this._stateMarkupDeclarationOpen(e);break;case E.COMMENT_START:this._stateCommentStart(e);break;case E.COMMENT_START_DASH:this._stateCommentStartDash(e);break;case E.COMMENT:this._stateComment(e);break;case E.COMMENT_LESS_THAN_SIGN:this._stateCommentLessThanSign(e);break;case E.COMMENT_LESS_THAN_SIGN_BANG:this._stateCommentLessThanSignBang(e);break;case E.COMMENT_LESS_THAN_SIGN_BANG_DASH:this._stateCommentLessThanSignBangDash(e);break;case E.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:this._stateCommentLessThanSignBangDashDash(e);break;case E.COMMENT_END_DASH:this._stateCommentEndDash(e);break;case E.COMMENT_END:this._stateCommentEnd(e);break;case E.COMMENT_END_BANG:this._stateCommentEndBang(e);break;case E.DOCTYPE:this._stateDoctype(e);break;case E.BEFORE_DOCTYPE_NAME:this._stateBeforeDoctypeName(e);break;case E.DOCTYPE_NAME:this._stateDoctypeName(e);break;case E.AFTER_DOCTYPE_NAME:this._stateAfterDoctypeName(e);break;case E.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._stateAfterDoctypePublicKeyword(e);break;case E.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER:this._stateBeforeDoctypePublicIdentifier(e);break;case E.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypePublicIdentifierDoubleQuoted(e);break;case E.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypePublicIdentifierSingleQuoted(e);break;case E.AFTER_DOCTYPE_PUBLIC_IDENTIFIER:this._stateAfterDoctypePublicIdentifier(e);break;case E.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS:this._stateBetweenDoctypePublicAndSystemIdentifiers(e);break;case E.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._stateAfterDoctypeSystemKeyword(e);break;case E.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER:this._stateBeforeDoctypeSystemIdentifier(e);break;case E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED:this._stateDoctypeSystemIdentifierDoubleQuoted(e);break;case E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED:this._stateDoctypeSystemIdentifierSingleQuoted(e);break;case E.AFTER_DOCTYPE_SYSTEM_IDENTIFIER:this._stateAfterDoctypeSystemIdentifier(e);break;case E.BOGUS_DOCTYPE:this._stateBogusDoctype(e);break;case E.CDATA_SECTION:this._stateCdataSection(e);break;case E.CDATA_SECTION_BRACKET:this._stateCdataSectionBracket(e);break;case E.CDATA_SECTION_END:this._stateCdataSectionEnd(e);break;case E.CHARACTER_REFERENCE:this._stateCharacterReference();break;case E.AMBIGUOUS_AMPERSAND:this._stateAmbiguousAmpersand(e);break;default:throw Error("Unknown state")}}_stateData(e){switch(e){case r.LESS_THAN_SIGN:this.state=E.TAG_OPEN;break;case r.AMPERSAND:this._startCharacterReference();break;case r.NULL:this._err(a.unexpectedNullCharacter),this._emitCodePoint(e);break;case r.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRcdata(e){switch(e){case r.AMPERSAND:this._startCharacterReference();break;case r.LESS_THAN_SIGN:this.state=E.RCDATA_LESS_THAN_SIGN;break;case r.NULL:this._err(a.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateRawtext(e){switch(e){case r.LESS_THAN_SIGN:this.state=E.RAWTEXT_LESS_THAN_SIGN;break;case r.NULL:this._err(a.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptData(e){switch(e){case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_LESS_THAN_SIGN;break;case r.NULL:this._err(a.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_statePlaintext(e){switch(e){case r.NULL:this._err(a.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateTagOpen(e){if(iT(e))this._createStartTagToken(),this.state=E.TAG_NAME,this._stateTagName(e);else switch(e){case r.EXCLAMATION_MARK:this.state=E.MARKUP_DECLARATION_OPEN;break;case r.SOLIDUS:this.state=E.END_TAG_OPEN;break;case r.QUESTION_MARK:this._err(a.unexpectedQuestionMarkInsteadOfTagName),this._createCommentToken(1),this.state=E.BOGUS_COMMENT,this._stateBogusComment(e);break;case r.EOF:this._err(a.eofBeforeTagName),this._emitChars("<"),this._emitEOFToken();break;default:this._err(a.invalidFirstCharacterOfTagName),this._emitChars("<"),this.state=E.DATA,this._stateData(e)}}_stateEndTagOpen(e){if(iT(e))this._createEndTagToken(),this.state=E.TAG_NAME,this._stateTagName(e);else switch(e){case r.GREATER_THAN_SIGN:this._err(a.missingEndTagName),this.state=E.DATA;break;case r.EOF:this._err(a.eofBeforeTagName),this._emitChars("</"),this._emitEOFToken();break;default:this._err(a.invalidFirstCharacterOfTagName),this._createCommentToken(2),this.state=E.BOGUS_COMMENT,this._stateBogusComment(e)}}_stateTagName(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.BEFORE_ATTRIBUTE_NAME;break;case r.SOLIDUS:this.state=E.SELF_CLOSING_START_TAG;break;case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentTagToken();break;case r.NULL:this._err(a.unexpectedNullCharacter),t.tagName+="�";break;case r.EOF:this._err(a.eofInTag),this._emitEOFToken();break;default:t.tagName+=String.fromCodePoint(iE(e)?e+32:e)}}_stateRcdataLessThanSign(e){e===r.SOLIDUS?this.state=E.RCDATA_END_TAG_OPEN:(this._emitChars("<"),this.state=E.RCDATA,this._stateRcdata(e))}_stateRcdataEndTagOpen(e){iT(e)?(this.state=E.RCDATA_END_TAG_NAME,this._stateRcdataEndTagName(e)):(this._emitChars("</"),this.state=E.RCDATA,this._stateRcdata(e))}handleSpecialEndTag(e){if(!this.preprocessor.startsWith(this.lastStartTagName,!1))return!this._ensureHibernation();switch(this._createEndTagToken(),this.currentToken.tagName=this.lastStartTagName,this.preprocessor.peek(this.lastStartTagName.length)){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:return this._advanceBy(this.lastStartTagName.length),this.state=E.BEFORE_ATTRIBUTE_NAME,!1;case r.SOLIDUS:return this._advanceBy(this.lastStartTagName.length),this.state=E.SELF_CLOSING_START_TAG,!1;case r.GREATER_THAN_SIGN:return this._advanceBy(this.lastStartTagName.length),this.emitCurrentTagToken(),this.state=E.DATA,!1;default:return!this._ensureHibernation()}}_stateRcdataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=E.RCDATA,this._stateRcdata(e))}_stateRawtextLessThanSign(e){e===r.SOLIDUS?this.state=E.RAWTEXT_END_TAG_OPEN:(this._emitChars("<"),this.state=E.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagOpen(e){iT(e)?(this.state=E.RAWTEXT_END_TAG_NAME,this._stateRawtextEndTagName(e)):(this._emitChars("</"),this.state=E.RAWTEXT,this._stateRawtext(e))}_stateRawtextEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=E.RAWTEXT,this._stateRawtext(e))}_stateScriptDataLessThanSign(e){switch(e){case r.SOLIDUS:this.state=E.SCRIPT_DATA_END_TAG_OPEN;break;case r.EXCLAMATION_MARK:this.state=E.SCRIPT_DATA_ESCAPE_START,this._emitChars("<!");break;default:this._emitChars("<"),this.state=E.SCRIPT_DATA,this._stateScriptData(e)}}_stateScriptDataEndTagOpen(e){iT(e)?(this.state=E.SCRIPT_DATA_END_TAG_NAME,this._stateScriptDataEndTagName(e)):(this._emitChars("</"),this.state=E.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=E.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStart(e){e===r.HYPHEN_MINUS?(this.state=E.SCRIPT_DATA_ESCAPE_START_DASH,this._emitChars("-")):(this.state=E.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscapeStartDash(e){e===r.HYPHEN_MINUS?(this.state=E.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-")):(this.state=E.SCRIPT_DATA,this._stateScriptData(e))}_stateScriptDataEscaped(e){switch(e){case r.HYPHEN_MINUS:this.state=E.SCRIPT_DATA_ESCAPED_DASH,this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case r.NULL:this._err(a.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataEscapedDash(e){switch(e){case r.HYPHEN_MINUS:this.state=E.SCRIPT_DATA_ESCAPED_DASH_DASH,this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case r.NULL:this._err(a.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_ESCAPED,this._emitChars("�");break;case r.EOF:this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=E.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedDashDash(e){switch(e){case r.HYPHEN_MINUS:this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_ESCAPED_LESS_THAN_SIGN;break;case r.GREATER_THAN_SIGN:this.state=E.SCRIPT_DATA,this._emitChars(">");break;case r.NULL:this._err(a.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_ESCAPED,this._emitChars("�");break;case r.EOF:this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=E.SCRIPT_DATA_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataEscapedLessThanSign(e){e===r.SOLIDUS?this.state=E.SCRIPT_DATA_ESCAPED_END_TAG_OPEN:iT(e)?(this._emitChars("<"),this.state=E.SCRIPT_DATA_DOUBLE_ESCAPE_START,this._stateScriptDataDoubleEscapeStart(e)):(this._emitChars("<"),this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagOpen(e){iT(e)?(this.state=E.SCRIPT_DATA_ESCAPED_END_TAG_NAME,this._stateScriptDataEscapedEndTagName(e)):(this._emitChars("</"),this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataEscapedEndTagName(e){this.handleSpecialEndTag(e)&&(this._emitChars("</"),this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscapeStart(e){if(this.preprocessor.startsWith(a9.SCRIPT,!1)&&i_(this.preprocessor.peek(a9.SCRIPT.length))){this._emitCodePoint(e);for(let e=0;e<a9.SCRIPT.length;e++)this._emitCodePoint(this._consume());this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED}else this._ensureHibernation()||(this.state=E.SCRIPT_DATA_ESCAPED,this._stateScriptDataEscaped(e))}_stateScriptDataDoubleEscaped(e){switch(e){case r.HYPHEN_MINUS:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH,this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case r.NULL:this._err(a.unexpectedNullCharacter),this._emitChars("�");break;case r.EOF:this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDash(e){switch(e){case r.HYPHEN_MINUS:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_DASH_DASH,this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case r.NULL:this._err(a.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars("�");break;case r.EOF:this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedDashDash(e){switch(e){case r.HYPHEN_MINUS:this._emitChars("-");break;case r.LESS_THAN_SIGN:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED_LESS_THAN_SIGN,this._emitChars("<");break;case r.GREATER_THAN_SIGN:this.state=E.SCRIPT_DATA,this._emitChars(">");break;case r.NULL:this._err(a.unexpectedNullCharacter),this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitChars("�");break;case r.EOF:this._err(a.eofInScriptHtmlCommentLikeText),this._emitEOFToken();break;default:this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._emitCodePoint(e)}}_stateScriptDataDoubleEscapedLessThanSign(e){e===r.SOLIDUS?(this.state=E.SCRIPT_DATA_DOUBLE_ESCAPE_END,this._emitChars("/")):(this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateScriptDataDoubleEscapeEnd(e){if(this.preprocessor.startsWith(a9.SCRIPT,!1)&&i_(this.preprocessor.peek(a9.SCRIPT.length))){this._emitCodePoint(e);for(let e=0;e<a9.SCRIPT.length;e++)this._emitCodePoint(this._consume());this.state=E.SCRIPT_DATA_ESCAPED}else this._ensureHibernation()||(this.state=E.SCRIPT_DATA_DOUBLE_ESCAPED,this._stateScriptDataDoubleEscaped(e))}_stateBeforeAttributeName(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.SOLIDUS:case r.GREATER_THAN_SIGN:case r.EOF:this.state=E.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case r.EQUALS_SIGN:this._err(a.unexpectedEqualsSignBeforeAttributeName),this._createAttr("="),this.state=E.ATTRIBUTE_NAME;break;default:this._createAttr(""),this.state=E.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateAttributeName(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:case r.SOLIDUS:case r.GREATER_THAN_SIGN:case r.EOF:this._leaveAttrName(),this.state=E.AFTER_ATTRIBUTE_NAME,this._stateAfterAttributeName(e);break;case r.EQUALS_SIGN:this._leaveAttrName(),this.state=E.BEFORE_ATTRIBUTE_VALUE;break;case r.QUOTATION_MARK:case r.APOSTROPHE:case r.LESS_THAN_SIGN:this._err(a.unexpectedCharacterInAttributeName),this.currentAttr.name+=String.fromCodePoint(e);break;case r.NULL:this._err(a.unexpectedNullCharacter),this.currentAttr.name+="�";break;default:this.currentAttr.name+=String.fromCodePoint(iE(e)?e+32:e)}}_stateAfterAttributeName(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.SOLIDUS:this.state=E.SELF_CLOSING_START_TAG;break;case r.EQUALS_SIGN:this.state=E.BEFORE_ATTRIBUTE_VALUE;break;case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentTagToken();break;case r.EOF:this._err(a.eofInTag),this._emitEOFToken();break;default:this._createAttr(""),this.state=E.ATTRIBUTE_NAME,this._stateAttributeName(e)}}_stateBeforeAttributeValue(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.QUOTATION_MARK:this.state=E.ATTRIBUTE_VALUE_DOUBLE_QUOTED;break;case r.APOSTROPHE:this.state=E.ATTRIBUTE_VALUE_SINGLE_QUOTED;break;case r.GREATER_THAN_SIGN:this._err(a.missingAttributeValue),this.state=E.DATA,this.emitCurrentTagToken();break;default:this.state=E.ATTRIBUTE_VALUE_UNQUOTED,this._stateAttributeValueUnquoted(e)}}_stateAttributeValueDoubleQuoted(e){switch(e){case r.QUOTATION_MARK:this.state=E.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case r.AMPERSAND:this._startCharacterReference();break;case r.NULL:this._err(a.unexpectedNullCharacter),this.currentAttr.value+="�";break;case r.EOF:this._err(a.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueSingleQuoted(e){switch(e){case r.APOSTROPHE:this.state=E.AFTER_ATTRIBUTE_VALUE_QUOTED;break;case r.AMPERSAND:this._startCharacterReference();break;case r.NULL:this._err(a.unexpectedNullCharacter),this.currentAttr.value+="�";break;case r.EOF:this._err(a.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAttributeValueUnquoted(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this._leaveAttrValue(),this.state=E.BEFORE_ATTRIBUTE_NAME;break;case r.AMPERSAND:this._startCharacterReference();break;case r.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=E.DATA,this.emitCurrentTagToken();break;case r.NULL:this._err(a.unexpectedNullCharacter),this.currentAttr.value+="�";break;case r.QUOTATION_MARK:case r.APOSTROPHE:case r.LESS_THAN_SIGN:case r.EQUALS_SIGN:case r.GRAVE_ACCENT:this._err(a.unexpectedCharacterInUnquotedAttributeValue),this.currentAttr.value+=String.fromCodePoint(e);break;case r.EOF:this._err(a.eofInTag),this._emitEOFToken();break;default:this.currentAttr.value+=String.fromCodePoint(e)}}_stateAfterAttributeValueQuoted(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this._leaveAttrValue(),this.state=E.BEFORE_ATTRIBUTE_NAME;break;case r.SOLIDUS:this._leaveAttrValue(),this.state=E.SELF_CLOSING_START_TAG;break;case r.GREATER_THAN_SIGN:this._leaveAttrValue(),this.state=E.DATA,this.emitCurrentTagToken();break;case r.EOF:this._err(a.eofInTag),this._emitEOFToken();break;default:this._err(a.missingWhitespaceBetweenAttributes),this.state=E.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateSelfClosingStartTag(e){switch(e){case r.GREATER_THAN_SIGN:this.currentToken.selfClosing=!0,this.state=E.DATA,this.emitCurrentTagToken();break;case r.EOF:this._err(a.eofInTag),this._emitEOFToken();break;default:this._err(a.unexpectedSolidusInTag),this.state=E.BEFORE_ATTRIBUTE_NAME,this._stateBeforeAttributeName(e)}}_stateBogusComment(e){let t=this.currentToken;switch(e){case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentComment(t);break;case r.EOF:this.emitCurrentComment(t),this._emitEOFToken();break;case r.NULL:this._err(a.unexpectedNullCharacter),t.data+="�";break;default:t.data+=String.fromCodePoint(e)}}_stateMarkupDeclarationOpen(e){this._consumeSequenceIfMatch(a9.DASH_DASH,!0)?(this._createCommentToken(a9.DASH_DASH.length+1),this.state=E.COMMENT_START):this._consumeSequenceIfMatch(a9.DOCTYPE,!1)?(this.currentLocation=this.getCurrentLocation(a9.DOCTYPE.length+1),this.state=E.DOCTYPE):this._consumeSequenceIfMatch(a9.CDATA_START,!0)?this.inForeignNode?this.state=E.CDATA_SECTION:(this._err(a.cdataInHtmlContent),this._createCommentToken(a9.CDATA_START.length+1),this.currentToken.data="[CDATA[",this.state=E.BOGUS_COMMENT):this._ensureHibernation()||(this._err(a.incorrectlyOpenedComment),this._createCommentToken(2),this.state=E.BOGUS_COMMENT,this._stateBogusComment(e))}_stateCommentStart(e){switch(e){case r.HYPHEN_MINUS:this.state=E.COMMENT_START_DASH;break;case r.GREATER_THAN_SIGN:{this._err(a.abruptClosingOfEmptyComment),this.state=E.DATA;let e=this.currentToken;this.emitCurrentComment(e);break}default:this.state=E.COMMENT,this._stateComment(e)}}_stateCommentStartDash(e){let t=this.currentToken;switch(e){case r.HYPHEN_MINUS:this.state=E.COMMENT_END;break;case r.GREATER_THAN_SIGN:this._err(a.abruptClosingOfEmptyComment),this.state=E.DATA,this.emitCurrentComment(t);break;case r.EOF:this._err(a.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=E.COMMENT,this._stateComment(e)}}_stateComment(e){let t=this.currentToken;switch(e){case r.HYPHEN_MINUS:this.state=E.COMMENT_END_DASH;break;case r.LESS_THAN_SIGN:t.data+="<",this.state=E.COMMENT_LESS_THAN_SIGN;break;case r.NULL:this._err(a.unexpectedNullCharacter),t.data+="�";break;case r.EOF:this._err(a.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+=String.fromCodePoint(e)}}_stateCommentLessThanSign(e){let t=this.currentToken;switch(e){case r.EXCLAMATION_MARK:t.data+="!",this.state=E.COMMENT_LESS_THAN_SIGN_BANG;break;case r.LESS_THAN_SIGN:t.data+="<";break;default:this.state=E.COMMENT,this._stateComment(e)}}_stateCommentLessThanSignBang(e){e===r.HYPHEN_MINUS?this.state=E.COMMENT_LESS_THAN_SIGN_BANG_DASH:(this.state=E.COMMENT,this._stateComment(e))}_stateCommentLessThanSignBangDash(e){e===r.HYPHEN_MINUS?this.state=E.COMMENT_LESS_THAN_SIGN_BANG_DASH_DASH:(this.state=E.COMMENT_END_DASH,this._stateCommentEndDash(e))}_stateCommentLessThanSignBangDashDash(e){e!==r.GREATER_THAN_SIGN&&e!==r.EOF&&this._err(a.nestedComment),this.state=E.COMMENT_END,this._stateCommentEnd(e)}_stateCommentEndDash(e){let t=this.currentToken;switch(e){case r.HYPHEN_MINUS:this.state=E.COMMENT_END;break;case r.EOF:this._err(a.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="-",this.state=E.COMMENT,this._stateComment(e)}}_stateCommentEnd(e){let t=this.currentToken;switch(e){case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentComment(t);break;case r.EXCLAMATION_MARK:this.state=E.COMMENT_END_BANG;break;case r.HYPHEN_MINUS:t.data+="-";break;case r.EOF:this._err(a.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--",this.state=E.COMMENT,this._stateComment(e)}}_stateCommentEndBang(e){let t=this.currentToken;switch(e){case r.HYPHEN_MINUS:t.data+="--!",this.state=E.COMMENT_END_DASH;break;case r.GREATER_THAN_SIGN:this._err(a.incorrectlyClosedComment),this.state=E.DATA,this.emitCurrentComment(t);break;case r.EOF:this._err(a.eofInComment),this.emitCurrentComment(t),this._emitEOFToken();break;default:t.data+="--!",this.state=E.COMMENT,this._stateComment(e)}}_stateDoctype(e){switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.BEFORE_DOCTYPE_NAME;break;case r.GREATER_THAN_SIGN:this.state=E.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e);break;case r.EOF:{this._err(a.eofInDoctype),this._createDoctypeToken(null);let e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._err(a.missingWhitespaceBeforeDoctypeName),this.state=E.BEFORE_DOCTYPE_NAME,this._stateBeforeDoctypeName(e)}}_stateBeforeDoctypeName(e){if(iE(e))this._createDoctypeToken(String.fromCharCode(e+32)),this.state=E.DOCTYPE_NAME;else switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.NULL:this._err(a.unexpectedNullCharacter),this._createDoctypeToken("�"),this.state=E.DOCTYPE_NAME;break;case r.GREATER_THAN_SIGN:{this._err(a.missingDoctypeName),this._createDoctypeToken(null);let e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this.state=E.DATA;break}case r.EOF:{this._err(a.eofInDoctype),this._createDoctypeToken(null);let e=this.currentToken;e.forceQuirks=!0,this.emitCurrentDoctype(e),this._emitEOFToken();break}default:this._createDoctypeToken(String.fromCodePoint(e)),this.state=E.DOCTYPE_NAME}}_stateDoctypeName(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.AFTER_DOCTYPE_NAME;break;case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.NULL:this._err(a.unexpectedNullCharacter),t.name+="�";break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.name+=String.fromCodePoint(iE(e)?e+32:e)}}_stateAfterDoctypeName(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._consumeSequenceIfMatch(a9.PUBLIC,!1)?this.state=E.AFTER_DOCTYPE_PUBLIC_KEYWORD:this._consumeSequenceIfMatch(a9.SYSTEM,!1)?this.state=E.AFTER_DOCTYPE_SYSTEM_KEYWORD:this._ensureHibernation()||(this._err(a.invalidCharacterSequenceAfterDoctypeName),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e))}}_stateAfterDoctypePublicKeyword(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.BEFORE_DOCTYPE_PUBLIC_IDENTIFIER;break;case r.QUOTATION_MARK:this._err(a.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:this._err(a.missingWhitespaceAfterDoctypePublicKeyword),t.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case r.GREATER_THAN_SIGN:this._err(a.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(a.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypePublicIdentifier(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.QUOTATION_MARK:t.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:t.publicId="",this.state=E.DOCTYPE_PUBLIC_IDENTIFIER_SINGLE_QUOTED;break;case r.GREATER_THAN_SIGN:this._err(a.missingDoctypePublicIdentifier),t.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(a.missingQuoteBeforeDoctypePublicIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypePublicIdentifierDoubleQuoted(e){let t=this.currentToken;switch(e){case r.QUOTATION_MARK:this.state=E.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case r.NULL:this._err(a.unexpectedNullCharacter),t.publicId+="�";break;case r.GREATER_THAN_SIGN:this._err(a.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateDoctypePublicIdentifierSingleQuoted(e){let t=this.currentToken;switch(e){case r.APOSTROPHE:this.state=E.AFTER_DOCTYPE_PUBLIC_IDENTIFIER;break;case r.NULL:this._err(a.unexpectedNullCharacter),t.publicId+="�";break;case r.GREATER_THAN_SIGN:this._err(a.abruptDoctypePublicIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.publicId+=String.fromCodePoint(e)}}_stateAfterDoctypePublicIdentifier(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.BETWEEN_DOCTYPE_PUBLIC_AND_SYSTEM_IDENTIFIERS;break;case r.GREATER_THAN_SIGN:this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.QUOTATION_MARK:this._err(a.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:this._err(a.missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers),t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(a.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBetweenDoctypePublicAndSystemIdentifiers(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.QUOTATION_MARK:t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(a.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateAfterDoctypeSystemKeyword(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:this.state=E.BEFORE_DOCTYPE_SYSTEM_IDENTIFIER;break;case r.QUOTATION_MARK:this._err(a.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:this._err(a.missingWhitespaceAfterDoctypeSystemKeyword),t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case r.GREATER_THAN_SIGN:this._err(a.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(a.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBeforeDoctypeSystemIdentifier(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.QUOTATION_MARK:t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_DOUBLE_QUOTED;break;case r.APOSTROPHE:t.systemId="",this.state=E.DOCTYPE_SYSTEM_IDENTIFIER_SINGLE_QUOTED;break;case r.GREATER_THAN_SIGN:this._err(a.missingDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.DATA,this.emitCurrentDoctype(t);break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(a.missingQuoteBeforeDoctypeSystemIdentifier),t.forceQuirks=!0,this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateDoctypeSystemIdentifierDoubleQuoted(e){let t=this.currentToken;switch(e){case r.QUOTATION_MARK:this.state=E.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case r.NULL:this._err(a.unexpectedNullCharacter),t.systemId+="�";break;case r.GREATER_THAN_SIGN:this._err(a.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateDoctypeSystemIdentifierSingleQuoted(e){let t=this.currentToken;switch(e){case r.APOSTROPHE:this.state=E.AFTER_DOCTYPE_SYSTEM_IDENTIFIER;break;case r.NULL:this._err(a.unexpectedNullCharacter),t.systemId+="�";break;case r.GREATER_THAN_SIGN:this._err(a.abruptDoctypeSystemIdentifier),t.forceQuirks=!0,this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:t.systemId+=String.fromCodePoint(e)}}_stateAfterDoctypeSystemIdentifier(e){let t=this.currentToken;switch(e){case r.SPACE:case r.LINE_FEED:case r.TABULATION:case r.FORM_FEED:break;case r.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.EOF:this._err(a.eofInDoctype),t.forceQuirks=!0,this.emitCurrentDoctype(t),this._emitEOFToken();break;default:this._err(a.unexpectedCharacterAfterDoctypeSystemIdentifier),this.state=E.BOGUS_DOCTYPE,this._stateBogusDoctype(e)}}_stateBogusDoctype(e){let t=this.currentToken;switch(e){case r.GREATER_THAN_SIGN:this.emitCurrentDoctype(t),this.state=E.DATA;break;case r.NULL:this._err(a.unexpectedNullCharacter);break;case r.EOF:this.emitCurrentDoctype(t),this._emitEOFToken()}}_stateCdataSection(e){switch(e){case r.RIGHT_SQUARE_BRACKET:this.state=E.CDATA_SECTION_BRACKET;break;case r.EOF:this._err(a.eofInCdata),this._emitEOFToken();break;default:this._emitCodePoint(e)}}_stateCdataSectionBracket(e){e===r.RIGHT_SQUARE_BRACKET?this.state=E.CDATA_SECTION_END:(this._emitChars("]"),this.state=E.CDATA_SECTION,this._stateCdataSection(e))}_stateCdataSectionEnd(e){switch(e){case r.GREATER_THAN_SIGN:this.state=E.DATA;break;case r.RIGHT_SQUARE_BRACKET:this._emitChars("]");break;default:this._emitChars("]]"),this.state=E.CDATA_SECTION,this._stateCdataSection(e)}}_stateCharacterReference(){let e=this.entityDecoder.write(this.preprocessor.html,this.preprocessor.pos);if(e<0){if(this.preprocessor.lastChunkWritten)e=this.entityDecoder.end();else{this.active=!1,this.preprocessor.pos=this.preprocessor.html.length-1,this.consumedAfterSnapshot=0,this.preprocessor.endOfChunkHit=!0;return}}0===e?(this.preprocessor.pos=this.entityStartPos,this._flushCodePointConsumedAsCharacterReference(r.AMPERSAND),this.state=!this._isCharacterReferenceInAttribute()&&ig(this.preprocessor.peek(1))?E.AMBIGUOUS_AMPERSAND:this.returnState):this.state=this.returnState}_stateAmbiguousAmpersand(e){ig(e)?this._flushCodePointConsumedAsCharacterReference(e):(e===r.SEMICOLON&&this._err(a.unknownNamedCharacterReference),this.state=this.returnState,this._callState(e))}}let iS=new Set([f.DD,f.DT,f.LI,f.OPTGROUP,f.OPTION,f.P,f.RB,f.RP,f.RT,f.RTC]),ib=new Set([...iS,f.CAPTION,f.COLGROUP,f.TBODY,f.TD,f.TFOOT,f.TH,f.THEAD,f.TR]),iy=new Set([f.APPLET,f.CAPTION,f.HTML,f.MARQUEE,f.OBJECT,f.TABLE,f.TD,f.TEMPLATE,f.TH]),iN=new Set([...iy,f.OL,f.UL]),iC=new Set([...iy,f.BUTTON]),iI=new Set([f.ANNOTATION_XML,f.MI,f.MN,f.MO,f.MS,f.MTEXT]),iD=new Set([f.DESC,f.FOREIGN_OBJECT,f.TITLE]),iO=new Set([f.TR,f.TEMPLATE,f.HTML]),iL=new Set([f.TBODY,f.TFOOT,f.THEAD,f.TEMPLATE,f.HTML]),ix=new Set([f.TABLE,f.TEMPLATE,f.HTML]),iR=new Set([f.TD,f.TH]);class iv{get currentTmplContentOrNode(){return this._isInTemplate()?this.treeAdapter.getTemplateContent(this.current):this.current}constructor(e,t,n){this.treeAdapter=t,this.handler=n,this.items=[],this.tagIDs=[],this.stackTop=-1,this.tmplCount=0,this.currentTagId=f.UNKNOWN,this.current=e}_indexOf(e){return this.items.lastIndexOf(e,this.stackTop)}_isInTemplate(){return this.currentTagId===f.TEMPLATE&&this.treeAdapter.getNamespaceURI(this.current)===h.HTML}_updateCurrentElement(){this.current=this.items[this.stackTop],this.currentTagId=this.tagIDs[this.stackTop]}push(e,t){this.stackTop++,this.items[this.stackTop]=e,this.current=e,this.tagIDs[this.stackTop]=t,this.currentTagId=t,this._isInTemplate()&&this.tmplCount++,this.handler.onItemPush(e,t,!0)}pop(){let e=this.current;this.tmplCount>0&&this._isInTemplate()&&this.tmplCount--,this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!0)}replace(e,t){let n=this._indexOf(e);this.items[n]=t,n===this.stackTop&&(this.current=t)}insertAfter(e,t,n){let r=this._indexOf(e)+1;this.items.splice(r,0,t),this.tagIDs.splice(r,0,n),this.stackTop++,r===this.stackTop&&this._updateCurrentElement(),this.current&&void 0!==this.currentTagId&&this.handler.onItemPush(this.current,this.currentTagId,r===this.stackTop)}popUntilTagNamePopped(e){let t=this.stackTop+1;do t=this.tagIDs.lastIndexOf(e,t-1);while(t>0&&this.treeAdapter.getNamespaceURI(this.items[t])!==h.HTML);this.shortenToLength(Math.max(t,0))}shortenToLength(e){for(;this.stackTop>=e;){let t=this.current;this.tmplCount>0&&this._isInTemplate()&&(this.tmplCount-=1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(t,this.stackTop<e)}}popUntilElementPopped(e){let t=this._indexOf(e);this.shortenToLength(Math.max(t,0))}popUntilPopped(e,t){let n=this._indexOfTagNames(e,t);this.shortenToLength(Math.max(n,0))}popUntilNumberedHeaderPopped(){this.popUntilPopped(ip,h.HTML)}popUntilTableCellPopped(){this.popUntilPopped(iR,h.HTML)}popAllUpToHtmlElement(){this.tmplCount=0,this.shortenToLength(1)}_indexOfTagNames(e,t){for(let n=this.stackTop;n>=0;n--)if(e.has(this.tagIDs[n])&&this.treeAdapter.getNamespaceURI(this.items[n])===t)return n;return -1}clearBackTo(e,t){let n=this._indexOfTagNames(e,t);this.shortenToLength(n+1)}clearBackToTableContext(){this.clearBackTo(ix,h.HTML)}clearBackToTableBodyContext(){this.clearBackTo(iL,h.HTML)}clearBackToTableRowContext(){this.clearBackTo(iO,h.HTML)}remove(e){let t=this._indexOf(e);t>=0&&(t===this.stackTop?this.pop():(this.items.splice(t,1),this.tagIDs.splice(t,1),this.stackTop--,this._updateCurrentElement(),this.handler.onItemPop(e,!1)))}tryPeekProperlyNestedBodyElement(){return this.stackTop>=1&&this.tagIDs[1]===f.BODY?this.items[1]:null}contains(e){return this._indexOf(e)>-1}getCommonAncestor(e){let t=this._indexOf(e)-1;return t>=0?this.items[t]:null}isRootHtmlElementCurrent(){return 0===this.stackTop&&this.tagIDs[0]===f.HTML}hasInDynamicScope(e,t){for(let n=this.stackTop;n>=0;n--){let r=this.tagIDs[n];switch(this.treeAdapter.getNamespaceURI(this.items[n])){case h.HTML:if(r===e)return!0;if(t.has(r))return!1;break;case h.SVG:if(iD.has(r))return!1;break;case h.MATHML:if(iI.has(r))return!1}}return!0}hasInScope(e){return this.hasInDynamicScope(e,iy)}hasInListItemScope(e){return this.hasInDynamicScope(e,iN)}hasInButtonScope(e){return this.hasInDynamicScope(e,iC)}hasNumberedHeaderInScope(){for(let e=this.stackTop;e>=0;e--){let t=this.tagIDs[e];switch(this.treeAdapter.getNamespaceURI(this.items[e])){case h.HTML:if(ip.has(t))return!0;if(iy.has(t))return!1;break;case h.SVG:if(iD.has(t))return!1;break;case h.MATHML:if(iI.has(t))return!1}}return!0}hasInTableScope(e){for(let t=this.stackTop;t>=0;t--)if(this.treeAdapter.getNamespaceURI(this.items[t])===h.HTML)switch(this.tagIDs[t]){case e:return!0;case f.TABLE:case f.HTML:return!1}return!0}hasTableBodyContextInTableScope(){for(let e=this.stackTop;e>=0;e--)if(this.treeAdapter.getNamespaceURI(this.items[e])===h.HTML)switch(this.tagIDs[e]){case f.TBODY:case f.THEAD:case f.TFOOT:return!0;case f.TABLE:case f.HTML:return!1}return!0}hasInSelectScope(e){for(let t=this.stackTop;t>=0;t--)if(this.treeAdapter.getNamespaceURI(this.items[t])===h.HTML)switch(this.tagIDs[t]){case e:return!0;case f.OPTION:case f.OPTGROUP:break;default:return!1}return!0}generateImpliedEndTags(){for(;void 0!==this.currentTagId&&iS.has(this.currentTagId);)this.pop()}generateImpliedEndTagsThoroughly(){for(;void 0!==this.currentTagId&&ib.has(this.currentTagId);)this.pop()}generateImpliedEndTagsWithExclusion(e){for(;void 0!==this.currentTagId&&this.currentTagId!==e&&ib.has(this.currentTagId);)this.pop()}}!function(e){e[e.Marker=0]="Marker",e[e.Element=1]="Element"}(T||(T={}));let iP={type:T.Marker};class iM{constructor(e){this.treeAdapter=e,this.entries=[],this.bookmark=null}_getNoahArkConditionCandidates(e,t){let n=[],r=t.length,a=this.treeAdapter.getTagName(e),i=this.treeAdapter.getNamespaceURI(e);for(let e=0;e<this.entries.length;e++){let t=this.entries[e];if(t.type===T.Marker)break;let{element:s}=t;if(this.treeAdapter.getTagName(s)===a&&this.treeAdapter.getNamespaceURI(s)===i){let t=this.treeAdapter.getAttrList(s);t.length===r&&n.push({idx:e,attrs:t})}}return n}_ensureNoahArkCondition(e){if(this.entries.length<3)return;let t=this.treeAdapter.getAttrList(e),n=this._getNoahArkConditionCandidates(e,t);if(n.length<3)return;let r=new Map(t.map(e=>[e.name,e.value])),a=0;for(let e=0;e<n.length;e++){let t=n[e];t.attrs.every(e=>r.get(e.name)===e.value)&&(a+=1)>=3&&this.entries.splice(t.idx,1)}}insertMarker(){this.entries.unshift(iP)}pushElement(e,t){this._ensureNoahArkCondition(e),this.entries.unshift({type:T.Element,element:e,token:t})}insertElementAfterBookmark(e,t){let n=this.entries.indexOf(this.bookmark);this.entries.splice(n,0,{type:T.Element,element:e,token:t})}removeEntry(e){let t=this.entries.indexOf(e);-1!==t&&this.entries.splice(t,1)}clearToLastMarker(){let e=this.entries.indexOf(iP);-1===e?this.entries.length=0:this.entries.splice(0,e+1)}getElementEntryInScopeWithTagName(e){let t=this.entries.find(t=>t.type===T.Marker||this.treeAdapter.getTagName(t.element)===e);return t&&t.type===T.Element?t:null}getElementEntry(e){return this.entries.find(t=>t.type===T.Element&&t.element===e)}}let iw={createDocument:()=>({nodeName:"#document",mode:p.NO_QUIRKS,childNodes:[]}),createDocumentFragment:()=>({nodeName:"#document-fragment",childNodes:[]}),createElement:(e,t,n)=>({nodeName:e,tagName:e,attrs:n,namespaceURI:t,childNodes:[],parentNode:null}),createCommentNode:e=>({nodeName:"#comment",data:e,parentNode:null}),createTextNode:e=>({nodeName:"#text",value:e,parentNode:null}),appendChild(e,t){e.childNodes.push(t),t.parentNode=e},insertBefore(e,t,n){let r=e.childNodes.indexOf(n);e.childNodes.splice(r,0,t),t.parentNode=e},setTemplateContent(e,t){e.content=t},getTemplateContent:e=>e.content,setDocumentType(e,t,n,r){let a=e.childNodes.find(e=>"#documentType"===e.nodeName);a?(a.name=t,a.publicId=n,a.systemId=r):iw.appendChild(e,{nodeName:"#documentType",name:t,publicId:n,systemId:r,parentNode:null})},setDocumentMode(e,t){e.mode=t},getDocumentMode:e=>e.mode,detachNode(e){if(e.parentNode){let t=e.parentNode.childNodes.indexOf(e);e.parentNode.childNodes.splice(t,1),e.parentNode=null}},insertText(e,t){if(e.childNodes.length>0){let n=e.childNodes[e.childNodes.length-1];if(iw.isTextNode(n)){n.value+=t;return}}iw.appendChild(e,iw.createTextNode(t))},insertTextBefore(e,t,n){let r=e.childNodes[e.childNodes.indexOf(n)-1];r&&iw.isTextNode(r)?r.value+=t:iw.insertBefore(e,iw.createTextNode(t),n)},adoptAttributes(e,t){let n=new Set(e.attrs.map(e=>e.name));for(let r=0;r<t.length;r++)n.has(t[r].name)||e.attrs.push(t[r])},getFirstChild:e=>e.childNodes[0],getChildNodes:e=>e.childNodes,getParentNode:e=>e.parentNode,getAttrList:e=>e.attrs,getTagName:e=>e.tagName,getNamespaceURI:e=>e.namespaceURI,getTextNodeContent:e=>e.value,getCommentNodeContent:e=>e.data,getDocumentTypeNodeName:e=>e.name,getDocumentTypeNodePublicId:e=>e.publicId,getDocumentTypeNodeSystemId:e=>e.systemId,isTextNode:e=>"#text"===e.nodeName,isCommentNode:e=>"#comment"===e.nodeName,isDocumentTypeNode:e=>"#documentType"===e.nodeName,isElementNode:e=>Object.prototype.hasOwnProperty.call(e,"tagName"),setNodeSourceCodeLocation(e,t){e.sourceCodeLocation=t},getNodeSourceCodeLocation:e=>e.sourceCodeLocation,updateNodeSourceCodeLocation(e,t){e.sourceCodeLocation={...e.sourceCodeLocation,...t}}},iB="html",iF=["+//silmaril//dtd html pro v0r11 19970101//","-//as//dtd html 3.0 aswedit + extensions//","-//advasoft ltd//dtd html 3.0 aswedit + extensions//","-//ietf//dtd html 2.0 level 1//","-//ietf//dtd html 2.0 level 2//","-//ietf//dtd html 2.0 strict level 1//","-//ietf//dtd html 2.0 strict level 2//","-//ietf//dtd html 2.0 strict//","-//ietf//dtd html 2.0//","-//ietf//dtd html 2.1e//","-//ietf//dtd html 3.0//","-//ietf//dtd html 3.2 final//","-//ietf//dtd html 3.2//","-//ietf//dtd html 3//","-//ietf//dtd html level 0//","-//ietf//dtd html level 1//","-//ietf//dtd html level 2//","-//ietf//dtd html level 3//","-//ietf//dtd html strict level 0//","-//ietf//dtd html strict level 1//","-//ietf//dtd html strict level 2//","-//ietf//dtd html strict level 3//","-//ietf//dtd html strict//","-//ietf//dtd html//","-//metrius//dtd metrius presentational//","-//microsoft//dtd internet explorer 2.0 html strict//","-//microsoft//dtd internet explorer 2.0 html//","-//microsoft//dtd internet explorer 2.0 tables//","-//microsoft//dtd internet explorer 3.0 html strict//","-//microsoft//dtd internet explorer 3.0 html//","-//microsoft//dtd internet explorer 3.0 tables//","-//netscape comm. corp.//dtd html//","-//netscape comm. corp.//dtd strict html//","-//o'reilly and associates//dtd html 2.0//","-//o'reilly and associates//dtd html extended 1.0//","-//o'reilly and associates//dtd html extended relaxed 1.0//","-//sq//dtd html 2.0 hotmetal + extensions//","-//softquad software//dtd hotmetal pro 6.0::19990601::extensions to html 4.0//","-//softquad//dtd hotmetal pro 4.0::19971010::extensions to html 4.0//","-//spyglass//dtd html 2.0 extended//","-//sun microsystems corp.//dtd hotjava html//","-//sun microsystems corp.//dtd hotjava strict html//","-//w3c//dtd html 3 1995-03-24//","-//w3c//dtd html 3.2 draft//","-//w3c//dtd html 3.2 final//","-//w3c//dtd html 3.2//","-//w3c//dtd html 3.2s draft//","-//w3c//dtd html 4.0 frameset//","-//w3c//dtd html 4.0 transitional//","-//w3c//dtd html experimental 19960712//","-//w3c//dtd html experimental 970421//","-//w3c//dtd w3 html//","-//w3o//dtd w3 html 3.0//","-//webtechs//dtd mozilla html 2.0//","-//webtechs//dtd mozilla html//"],iU=[...iF,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"],iH=new Set(["-//w3o//dtd w3 html strict 3.0//en//","-/w3c/dtd html 4.0 transitional/en","html"]),iG=["-//w3c//dtd xhtml 1.0 frameset//","-//w3c//dtd xhtml 1.0 transitional//"],iq=[...iG,"-//w3c//dtd html 4.01 frameset//","-//w3c//dtd html 4.01 transitional//"];function iz(e,t){return t.some(t=>e.startsWith(t))}let ij={TEXT_HTML:"text/html",APPLICATION_XML:"application/xhtml+xml"},iY=new Map(["attributeName","attributeType","baseFrequency","baseProfile","calcMode","clipPathUnits","diffuseConstant","edgeMode","filterUnits","glyphRef","gradientTransform","gradientUnits","kernelMatrix","kernelUnitLength","keyPoints","keySplines","keyTimes","lengthAdjust","limitingConeAngle","markerHeight","markerUnits","markerWidth","maskContentUnits","maskUnits","numOctaves","pathLength","patternContentUnits","patternTransform","patternUnits","pointsAtX","pointsAtY","pointsAtZ","preserveAlpha","preserveAspectRatio","primitiveUnits","refX","refY","repeatCount","repeatDur","requiredExtensions","requiredFeatures","specularConstant","specularExponent","spreadMethod","startOffset","stdDeviation","stitchTiles","surfaceScale","systemLanguage","tableValues","targetX","targetY","textLength","viewBox","viewTarget","xChannelSelector","yChannelSelector","zoomAndPan"].map(e=>[e.toLowerCase(),e])),iV=new Map([["xlink:actuate",{prefix:"xlink",name:"actuate",namespace:h.XLINK}],["xlink:arcrole",{prefix:"xlink",name:"arcrole",namespace:h.XLINK}],["xlink:href",{prefix:"xlink",name:"href",namespace:h.XLINK}],["xlink:role",{prefix:"xlink",name:"role",namespace:h.XLINK}],["xlink:show",{prefix:"xlink",name:"show",namespace:h.XLINK}],["xlink:title",{prefix:"xlink",name:"title",namespace:h.XLINK}],["xlink:type",{prefix:"xlink",name:"type",namespace:h.XLINK}],["xml:lang",{prefix:"xml",name:"lang",namespace:h.XML}],["xml:space",{prefix:"xml",name:"space",namespace:h.XML}],["xmlns",{prefix:"",name:"xmlns",namespace:h.XMLNS}],["xmlns:xlink",{prefix:"xmlns",name:"xlink",namespace:h.XMLNS}]]),iW=new Map(["altGlyph","altGlyphDef","altGlyphItem","animateColor","animateMotion","animateTransform","clipPath","feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence","foreignObject","glyphRef","linearGradient","radialGradient","textPath"].map(e=>[e.toLowerCase(),e])),iQ=new Set([f.B,f.BIG,f.BLOCKQUOTE,f.BODY,f.BR,f.CENTER,f.CODE,f.DD,f.DIV,f.DL,f.DT,f.EM,f.EMBED,f.H1,f.H2,f.H3,f.H4,f.H5,f.H6,f.HEAD,f.HR,f.I,f.IMG,f.LI,f.LISTING,f.MENU,f.META,f.NOBR,f.OL,f.P,f.PRE,f.RUBY,f.S,f.SMALL,f.SPAN,f.STRONG,f.STRIKE,f.SUB,f.SUP,f.TABLE,f.TT,f.U,f.UL,f.VAR]);function iK(e){for(let t=0;t<e.attrs.length;t++)if("definitionurl"===e.attrs[t].name){e.attrs[t].name="definitionURL";break}}function iX(e){for(let t=0;t<e.attrs.length;t++){let n=iY.get(e.attrs[t].name);null!=n&&(e.attrs[t].name=n)}}function i$(e){for(let t=0;t<e.attrs.length;t++){let n=iV.get(e.attrs[t].name);n&&(e.attrs[t].prefix=n.prefix,e.attrs[t].name=n.name,e.attrs[t].namespace=n.namespace)}}!function(e){e[e.INITIAL=0]="INITIAL",e[e.BEFORE_HTML=1]="BEFORE_HTML",e[e.BEFORE_HEAD=2]="BEFORE_HEAD",e[e.IN_HEAD=3]="IN_HEAD",e[e.IN_HEAD_NO_SCRIPT=4]="IN_HEAD_NO_SCRIPT",e[e.AFTER_HEAD=5]="AFTER_HEAD",e[e.IN_BODY=6]="IN_BODY",e[e.TEXT=7]="TEXT",e[e.IN_TABLE=8]="IN_TABLE",e[e.IN_TABLE_TEXT=9]="IN_TABLE_TEXT",e[e.IN_CAPTION=10]="IN_CAPTION",e[e.IN_COLUMN_GROUP=11]="IN_COLUMN_GROUP",e[e.IN_TABLE_BODY=12]="IN_TABLE_BODY",e[e.IN_ROW=13]="IN_ROW",e[e.IN_CELL=14]="IN_CELL",e[e.IN_SELECT=15]="IN_SELECT",e[e.IN_SELECT_IN_TABLE=16]="IN_SELECT_IN_TABLE",e[e.IN_TEMPLATE=17]="IN_TEMPLATE",e[e.AFTER_BODY=18]="AFTER_BODY",e[e.IN_FRAMESET=19]="IN_FRAMESET",e[e.AFTER_FRAMESET=20]="AFTER_FRAMESET",e[e.AFTER_AFTER_BODY=21]="AFTER_AFTER_BODY",e[e.AFTER_AFTER_FRAMESET=22]="AFTER_AFTER_FRAMESET"}(g||(g={}));let iZ={startLine:-1,startCol:-1,startOffset:-1,endLine:-1,endCol:-1,endOffset:-1},iJ=new Set([f.TABLE,f.TBODY,f.TFOOT,f.THEAD,f.TR]),i0={scriptingEnabled:!0,sourceCodeLocationInfo:!1,treeAdapter:iw,onParseError:null};class i1{constructor(e,t,n=null,r=null){this.fragmentContext=n,this.scriptHandler=r,this.currentToken=null,this.stopped=!1,this.insertionMode=g.INITIAL,this.originalInsertionMode=g.INITIAL,this.headElement=null,this.formElement=null,this.currentNotInHTML=!1,this.tmplInsertionModeStack=[],this.pendingCharacterTokens=[],this.hasNonWhitespacePendingCharacterToken=!1,this.framesetOk=!0,this.skipNextNewLine=!1,this.fosterParentingEnabled=!1,this.options={...i0,...e},this.treeAdapter=this.options.treeAdapter,this.onParseError=this.options.onParseError,this.onParseError&&(this.options.sourceCodeLocationInfo=!0),this.document=null!=t?t:this.treeAdapter.createDocument(),this.tokenizer=new ik(this.options,this),this.activeFormattingElements=new iM(this.treeAdapter),this.fragmentContextID=n?iu(this.treeAdapter.getTagName(n)):f.UNKNOWN,this._setContextModes(null!=n?n:this.document,this.fragmentContextID),this.openElements=new iv(this.document,this.treeAdapter,this)}static parse(e,t){let n=new this(t);return n.tokenizer.write(e,!0),n.document}static getFragmentParser(e,t){let n={...i0,...t};null!=e||(e=n.treeAdapter.createElement(m.TEMPLATE,h.HTML,[]));let r=n.treeAdapter.createElement("documentmock",h.HTML,[]),a=new this(n,r,e);return a.fragmentContextID===f.TEMPLATE&&a.tmplInsertionModeStack.unshift(g.IN_TEMPLATE),a._initTokenizerForFragmentParsing(),a._insertFakeRootElement(),a._resetInsertionMode(),a._findFormInFragmentContext(),a}getFragment(){let e=this.treeAdapter.getFirstChild(this.document),t=this.treeAdapter.createDocumentFragment();return this._adoptNodes(e,t),t}_err(e,t,n){var r;if(!this.onParseError)return;let a=null!==(r=e.location)&&void 0!==r?r:iZ,i={code:t,startLine:a.startLine,startCol:a.startCol,startOffset:a.startOffset,endLine:n?a.startLine:a.endLine,endCol:n?a.startCol:a.endCol,endOffset:n?a.startOffset:a.endOffset};this.onParseError(i)}onItemPush(e,t,n){var r,a;null===(a=(r=this.treeAdapter).onItemPush)||void 0===a||a.call(r,e),n&&this.openElements.stackTop>0&&this._setContextModes(e,t)}onItemPop(e,t){var n,r;if(this.options.sourceCodeLocationInfo&&this._setEndLocation(e,this.currentToken),null===(r=(n=this.treeAdapter).onItemPop)||void 0===r||r.call(n,e,this.openElements.current),t){let e,t;0===this.openElements.stackTop&&this.fragmentContext?(e=this.fragmentContext,t=this.fragmentContextID):{current:e,currentTagId:t}=this.openElements,this._setContextModes(e,t)}}_setContextModes(e,t){let n=e===this.document||e&&this.treeAdapter.getNamespaceURI(e)===h.HTML;this.currentNotInHTML=!n,this.tokenizer.inForeignNode=!n&&void 0!==e&&void 0!==t&&!this._isIntegrationPoint(t,e)}_switchToTextParsing(e,t){this._insertElement(e,h.HTML),this.tokenizer.state=t,this.originalInsertionMode=this.insertionMode,this.insertionMode=g.TEXT}switchToPlaintextParsing(){this.insertionMode=g.TEXT,this.originalInsertionMode=g.IN_BODY,this.tokenizer.state=im.PLAINTEXT}_getAdjustedCurrentElement(){return 0===this.openElements.stackTop&&this.fragmentContext?this.fragmentContext:this.openElements.current}_findFormInFragmentContext(){let e=this.fragmentContext;for(;e;){if(this.treeAdapter.getTagName(e)===m.FORM){this.formElement=e;break}e=this.treeAdapter.getParentNode(e)}}_initTokenizerForFragmentParsing(){if(this.fragmentContext&&this.treeAdapter.getNamespaceURI(this.fragmentContext)===h.HTML)switch(this.fragmentContextID){case f.TITLE:case f.TEXTAREA:this.tokenizer.state=im.RCDATA;break;case f.STYLE:case f.XMP:case f.IFRAME:case f.NOEMBED:case f.NOFRAMES:case f.NOSCRIPT:this.tokenizer.state=im.RAWTEXT;break;case f.SCRIPT:this.tokenizer.state=im.SCRIPT_DATA;break;case f.PLAINTEXT:this.tokenizer.state=im.PLAINTEXT}}_setDocumentType(e){let t=e.name||"",n=e.publicId||"",r=e.systemId||"";if(this.treeAdapter.setDocumentType(this.document,t,n,r),e.location){let t=this.treeAdapter.getChildNodes(this.document).find(e=>this.treeAdapter.isDocumentTypeNode(e));t&&this.treeAdapter.setNodeSourceCodeLocation(t,e.location)}}_attachElementToTree(e,t){if(this.options.sourceCodeLocationInfo){let n=t&&{...t,startTag:t};this.treeAdapter.setNodeSourceCodeLocation(e,n)}if(this._shouldFosterParentOnInsertion())this._fosterParentElement(e);else{let t=this.openElements.currentTmplContentOrNode;this.treeAdapter.appendChild(null!=t?t:this.document,e)}}_appendElement(e,t){let n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location)}_insertElement(e,t){let n=this.treeAdapter.createElement(e.tagName,t,e.attrs);this._attachElementToTree(n,e.location),this.openElements.push(n,e.tagID)}_insertFakeElement(e,t){let n=this.treeAdapter.createElement(e,h.HTML,[]);this._attachElementToTree(n,null),this.openElements.push(n,t)}_insertTemplate(e){let t=this.treeAdapter.createElement(e.tagName,h.HTML,e.attrs),n=this.treeAdapter.createDocumentFragment();this.treeAdapter.setTemplateContent(t,n),this._attachElementToTree(t,e.location),this.openElements.push(t,e.tagID),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,null)}_insertFakeRootElement(){let e=this.treeAdapter.createElement(m.HTML,h.HTML,[]);this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(e,null),this.treeAdapter.appendChild(this.openElements.current,e),this.openElements.push(e,f.HTML)}_appendCommentNode(e,t){let n=this.treeAdapter.createCommentNode(e.data);this.treeAdapter.appendChild(t,n),this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(n,e.location)}_insertCharacters(e){let t,n;if(this._shouldFosterParentOnInsertion()?({parent:t,beforeElement:n}=this._findFosterParentingLocation(),n?this.treeAdapter.insertTextBefore(t,e.chars,n):this.treeAdapter.insertText(t,e.chars)):(t=this.openElements.currentTmplContentOrNode,this.treeAdapter.insertText(t,e.chars)),!e.location)return;let r=this.treeAdapter.getChildNodes(t),a=n?r.lastIndexOf(n):r.length,i=r[a-1];if(this.treeAdapter.getNodeSourceCodeLocation(i)){let{endLine:t,endCol:n,endOffset:r}=e.location;this.treeAdapter.updateNodeSourceCodeLocation(i,{endLine:t,endCol:n,endOffset:r})}else this.options.sourceCodeLocationInfo&&this.treeAdapter.setNodeSourceCodeLocation(i,e.location)}_adoptNodes(e,t){for(let n=this.treeAdapter.getFirstChild(e);n;n=this.treeAdapter.getFirstChild(e))this.treeAdapter.detachNode(n),this.treeAdapter.appendChild(t,n)}_setEndLocation(e,t){if(this.treeAdapter.getNodeSourceCodeLocation(e)&&t.location){let n=t.location,r=this.treeAdapter.getTagName(e),a=t.type===i.END_TAG&&r===t.tagName?{endTag:{...n},endLine:n.endLine,endCol:n.endCol,endOffset:n.endOffset}:{endLine:n.startLine,endCol:n.startCol,endOffset:n.startOffset};this.treeAdapter.updateNodeSourceCodeLocation(e,a)}}shouldProcessStartTagTokenInForeignContent(e){let t,n;return!!this.currentNotInHTML&&(0===this.openElements.stackTop&&this.fragmentContext?(t=this.fragmentContext,n=this.fragmentContextID):{current:t,currentTagId:n}=this.openElements,(e.tagID!==f.SVG||this.treeAdapter.getTagName(t)!==m.ANNOTATION_XML||this.treeAdapter.getNamespaceURI(t)!==h.MATHML)&&(this.tokenizer.inForeignNode||(e.tagID===f.MGLYPH||e.tagID===f.MALIGNMARK)&&void 0!==n&&!this._isIntegrationPoint(n,t,h.HTML)))}_processToken(e){switch(e.type){case i.CHARACTER:this.onCharacter(e);break;case i.NULL_CHARACTER:this.onNullCharacter(e);break;case i.COMMENT:this.onComment(e);break;case i.DOCTYPE:this.onDoctype(e);break;case i.START_TAG:this._processStartTag(e);break;case i.END_TAG:this.onEndTag(e);break;case i.EOF:this.onEof(e);break;case i.WHITESPACE_CHARACTER:this.onWhitespaceCharacter(e)}}_isIntegrationPoint(e,t,n){let r=this.treeAdapter.getNamespaceURI(t),a=this.treeAdapter.getAttrList(t);return(!n||n===h.HTML)&&function(e,t,n){if(t===h.MATHML&&e===f.ANNOTATION_XML){for(let e=0;e<n.length;e++)if(n[e].name===d.ENCODING){let t=n[e].value.toLowerCase();return t===ij.TEXT_HTML||t===ij.APPLICATION_XML}}return t===h.SVG&&(e===f.FOREIGN_OBJECT||e===f.DESC||e===f.TITLE)}(e,r,a)||(!n||n===h.MATHML)&&r===h.MATHML&&(e===f.MI||e===f.MO||e===f.MN||e===f.MS||e===f.MTEXT)}_reconstructActiveFormattingElements(){let e=this.activeFormattingElements.entries.length;if(e){let t=this.activeFormattingElements.entries.findIndex(e=>e.type===T.Marker||this.openElements.contains(e.element)),n=-1===t?e-1:t-1;for(let e=n;e>=0;e--){let t=this.activeFormattingElements.entries[e];this._insertElement(t.token,this.treeAdapter.getNamespaceURI(t.element)),t.element=this.openElements.current}}}_closeTableCell(){this.openElements.generateImpliedEndTags(),this.openElements.popUntilTableCellPopped(),this.activeFormattingElements.clearToLastMarker(),this.insertionMode=g.IN_ROW}_closePElement(){this.openElements.generateImpliedEndTagsWithExclusion(f.P),this.openElements.popUntilTagNamePopped(f.P)}_resetInsertionMode(){for(let e=this.openElements.stackTop;e>=0;e--)switch(0===e&&this.fragmentContext?this.fragmentContextID:this.openElements.tagIDs[e]){case f.TR:this.insertionMode=g.IN_ROW;return;case f.TBODY:case f.THEAD:case f.TFOOT:this.insertionMode=g.IN_TABLE_BODY;return;case f.CAPTION:this.insertionMode=g.IN_CAPTION;return;case f.COLGROUP:this.insertionMode=g.IN_COLUMN_GROUP;return;case f.TABLE:this.insertionMode=g.IN_TABLE;return;case f.BODY:this.insertionMode=g.IN_BODY;return;case f.FRAMESET:this.insertionMode=g.IN_FRAMESET;return;case f.SELECT:this._resetInsertionModeForSelect(e);return;case f.TEMPLATE:this.insertionMode=this.tmplInsertionModeStack[0];return;case f.HTML:this.insertionMode=this.headElement?g.AFTER_HEAD:g.BEFORE_HEAD;return;case f.TD:case f.TH:if(e>0){this.insertionMode=g.IN_CELL;return}break;case f.HEAD:if(e>0){this.insertionMode=g.IN_HEAD;return}}this.insertionMode=g.IN_BODY}_resetInsertionModeForSelect(e){if(e>0)for(let t=e-1;t>0;t--){let e=this.openElements.tagIDs[t];if(e===f.TEMPLATE)break;if(e===f.TABLE){this.insertionMode=g.IN_SELECT_IN_TABLE;return}}this.insertionMode=g.IN_SELECT}_isElementCausesFosterParenting(e){return iJ.has(e)}_shouldFosterParentOnInsertion(){return this.fosterParentingEnabled&&void 0!==this.openElements.currentTagId&&this._isElementCausesFosterParenting(this.openElements.currentTagId)}_findFosterParentingLocation(){for(let e=this.openElements.stackTop;e>=0;e--){let t=this.openElements.items[e];switch(this.openElements.tagIDs[e]){case f.TEMPLATE:if(this.treeAdapter.getNamespaceURI(t)===h.HTML)return{parent:this.treeAdapter.getTemplateContent(t),beforeElement:null};break;case f.TABLE:{let n=this.treeAdapter.getParentNode(t);if(n)return{parent:n,beforeElement:t};return{parent:this.openElements.items[e-1],beforeElement:null}}}}return{parent:this.openElements.items[0],beforeElement:null}}_fosterParentElement(e){let t=this._findFosterParentingLocation();t.beforeElement?this.treeAdapter.insertBefore(t.parent,e,t.beforeElement):this.treeAdapter.appendChild(t.parent,e)}_isSpecialElement(e,t){return id[this.treeAdapter.getNamespaceURI(e)].has(t)}onCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode){this._insertCharacters(e),this.framesetOk=!1;return}switch(this.insertionMode){case g.INITIAL:i4(this,e);break;case g.BEFORE_HTML:i6(this,e);break;case g.BEFORE_HEAD:i8(this,e);break;case g.IN_HEAD:se(this,e);break;case g.IN_HEAD_NO_SCRIPT:st(this,e);break;case g.AFTER_HEAD:sn(this,e);break;case g.IN_BODY:case g.IN_CAPTION:case g.IN_CELL:case g.IN_TEMPLATE:si(this,e);break;case g.TEXT:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:this._insertCharacters(e);break;case g.IN_TABLE:case g.IN_TABLE_BODY:case g.IN_ROW:sm(this,e);break;case g.IN_TABLE_TEXT:sA(this,e);break;case g.IN_COLUMN_GROUP:sb(this,e);break;case g.AFTER_BODY:sR(this,e);break;case g.AFTER_AFTER_BODY:sv(this,e)}}onNullCharacter(e){if(this.skipNextNewLine=!1,this.tokenizer.inForeignNode){e.chars="�",this._insertCharacters(e);return}switch(this.insertionMode){case g.INITIAL:i4(this,e);break;case g.BEFORE_HTML:i6(this,e);break;case g.BEFORE_HEAD:i8(this,e);break;case g.IN_HEAD:se(this,e);break;case g.IN_HEAD_NO_SCRIPT:st(this,e);break;case g.AFTER_HEAD:sn(this,e);break;case g.TEXT:this._insertCharacters(e);break;case g.IN_TABLE:case g.IN_TABLE_BODY:case g.IN_ROW:sm(this,e);break;case g.IN_COLUMN_GROUP:sb(this,e);break;case g.AFTER_BODY:sR(this,e);break;case g.AFTER_AFTER_BODY:sv(this,e)}}onComment(e){if(this.skipNextNewLine=!1,this.currentNotInHTML){i3(this,e);return}switch(this.insertionMode){case g.INITIAL:case g.BEFORE_HTML:case g.BEFORE_HEAD:case g.IN_HEAD:case g.IN_HEAD_NO_SCRIPT:case g.AFTER_HEAD:case g.IN_BODY:case g.IN_TABLE:case g.IN_CAPTION:case g.IN_COLUMN_GROUP:case g.IN_TABLE_BODY:case g.IN_ROW:case g.IN_CELL:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:case g.IN_TEMPLATE:case g.IN_FRAMESET:case g.AFTER_FRAMESET:i3(this,e);break;case g.IN_TABLE_TEXT:s_(this,e);break;case g.AFTER_BODY:(function(e,t){e._appendCommentNode(t,e.openElements.items[0])})(this,e);break;case g.AFTER_AFTER_BODY:case g.AFTER_AFTER_FRAMESET:(function(e,t){e._appendCommentNode(t,e.document)})(this,e)}}onDoctype(e){switch(this.skipNextNewLine=!1,this.insertionMode){case g.INITIAL:(function(e,t){e._setDocumentType(t);let n=t.forceQuirks?p.QUIRKS:function(e){if(e.name!==iB)return p.QUIRKS;let{systemId:t}=e;if(t&&"http://www.ibm.com/data/dtd/v11/ibmxhtml1-transitional.dtd"===t.toLowerCase())return p.QUIRKS;let{publicId:n}=e;if(null!==n){if(n=n.toLowerCase(),iH.has(n))return p.QUIRKS;let e=null===t?iU:iF;if(iz(n,e))return p.QUIRKS;if(iz(n,e=null===t?iG:iq))return p.LIMITED_QUIRKS}return p.NO_QUIRKS}(t);t.name===iB&&null===t.publicId&&(null===t.systemId||"about:legacy-compat"===t.systemId)||e._err(t,a.nonConformingDoctype),e.treeAdapter.setDocumentMode(e.document,n),e.insertionMode=g.BEFORE_HTML})(this,e);break;case g.BEFORE_HEAD:case g.IN_HEAD:case g.IN_HEAD_NO_SCRIPT:case g.AFTER_HEAD:this._err(e,a.misplacedDoctype);break;case g.IN_TABLE_TEXT:s_(this,e)}}onStartTag(e){this.skipNextNewLine=!1,this.currentToken=e,this._processStartTag(e),e.selfClosing&&!e.ackSelfClosing&&this._err(e,a.nonVoidHtmlElementStartTagWithTrailingSolidus)}_processStartTag(e){this.shouldProcessStartTagTokenInForeignContent(e)?function(e,t){if(function(e){let t=e.tagID;return t===f.FONT&&e.attrs.some(({name:e})=>e===d.COLOR||e===d.SIZE||e===d.FACE)||iQ.has(t)}(t))sP(e),e._startTagOutsideForeignContent(t);else{let n=e._getAdjustedCurrentElement(),r=e.treeAdapter.getNamespaceURI(n);r===h.MATHML?iK(t):r===h.SVG&&(function(e){let t=iW.get(e.tagName);null!=t&&(e.tagName=t,e.tagID=iu(e.tagName))}(t),iX(t)),i$(t),t.selfClosing?e._appendElement(t,r):e._insertElement(t,r),t.ackSelfClosing=!0}}(this,e):this._startTagOutsideForeignContent(e)}_startTagOutsideForeignContent(e){switch(this.insertionMode){case g.INITIAL:i4(this,e);break;case g.BEFORE_HTML:e.tagID===f.HTML?(this._insertElement(e,h.HTML),this.insertionMode=g.BEFORE_HEAD):i6(this,e);break;case g.BEFORE_HEAD:(function(e,t){switch(t.tagID){case f.HTML:su(e,t);break;case f.HEAD:e._insertElement(t,h.HTML),e.headElement=e.openElements.current,e.insertionMode=g.IN_HEAD;break;default:i8(e,t)}})(this,e);break;case g.IN_HEAD:i9(this,e);break;case g.IN_HEAD_NO_SCRIPT:(function(e,t){switch(t.tagID){case f.HTML:su(e,t);break;case f.BASEFONT:case f.BGSOUND:case f.HEAD:case f.LINK:case f.META:case f.NOFRAMES:case f.STYLE:i9(e,t);break;case f.NOSCRIPT:e._err(t,a.nestedNoscriptInHead);break;default:st(e,t)}})(this,e);break;case g.AFTER_HEAD:(function(e,t){switch(t.tagID){case f.HTML:su(e,t);break;case f.BODY:e._insertElement(t,h.HTML),e.framesetOk=!1,e.insertionMode=g.IN_BODY;break;case f.FRAMESET:e._insertElement(t,h.HTML),e.insertionMode=g.IN_FRAMESET;break;case f.BASE:case f.BASEFONT:case f.BGSOUND:case f.LINK:case f.META:case f.NOFRAMES:case f.SCRIPT:case f.STYLE:case f.TEMPLATE:case f.TITLE:e._err(t,a.abandonedHeadElementChild),e.openElements.push(e.headElement,f.HEAD),i9(e,t),e.openElements.remove(e.headElement);break;case f.HEAD:e._err(t,a.misplacedStartTagForHeadElement);break;default:sn(e,t)}})(this,e);break;case g.IN_BODY:su(this,e);break;case g.IN_TABLE:sf(this,e);break;case g.IN_TABLE_TEXT:s_(this,e);break;case g.IN_CAPTION:(function(e,t){let n=t.tagID;sk.has(n)?e.openElements.hasInTableScope(f.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(f.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=g.IN_TABLE,sf(e,t)):su(e,t)})(this,e);break;case g.IN_COLUMN_GROUP:sS(this,e);break;case g.IN_TABLE_BODY:sy(this,e);break;case g.IN_ROW:sC(this,e);break;case g.IN_CELL:(function(e,t){let n=t.tagID;sk.has(n)?(e.openElements.hasInTableScope(f.TD)||e.openElements.hasInTableScope(f.TH))&&(e._closeTableCell(),sC(e,t)):su(e,t)})(this,e);break;case g.IN_SELECT:sD(this,e);break;case g.IN_SELECT_IN_TABLE:(function(e,t){let n=t.tagID;n===f.CAPTION||n===f.TABLE||n===f.TBODY||n===f.TFOOT||n===f.THEAD||n===f.TR||n===f.TD||n===f.TH?(e.openElements.popUntilTagNamePopped(f.SELECT),e._resetInsertionMode(),e._processStartTag(t)):sD(e,t)})(this,e);break;case g.IN_TEMPLATE:(function(e,t){switch(t.tagID){case f.BASE:case f.BASEFONT:case f.BGSOUND:case f.LINK:case f.META:case f.NOFRAMES:case f.SCRIPT:case f.STYLE:case f.TEMPLATE:case f.TITLE:i9(e,t);break;case f.CAPTION:case f.COLGROUP:case f.TBODY:case f.TFOOT:case f.THEAD:e.tmplInsertionModeStack[0]=g.IN_TABLE,e.insertionMode=g.IN_TABLE,sf(e,t);break;case f.COL:e.tmplInsertionModeStack[0]=g.IN_COLUMN_GROUP,e.insertionMode=g.IN_COLUMN_GROUP,sS(e,t);break;case f.TR:e.tmplInsertionModeStack[0]=g.IN_TABLE_BODY,e.insertionMode=g.IN_TABLE_BODY,sy(e,t);break;case f.TD:case f.TH:e.tmplInsertionModeStack[0]=g.IN_ROW,e.insertionMode=g.IN_ROW,sC(e,t);break;default:e.tmplInsertionModeStack[0]=g.IN_BODY,e.insertionMode=g.IN_BODY,su(e,t)}})(this,e);break;case g.AFTER_BODY:e.tagID===f.HTML?su(this,e):sR(this,e);break;case g.IN_FRAMESET:(function(e,t){switch(t.tagID){case f.HTML:su(e,t);break;case f.FRAMESET:e._insertElement(t,h.HTML);break;case f.FRAME:e._appendElement(t,h.HTML),t.ackSelfClosing=!0;break;case f.NOFRAMES:i9(e,t)}})(this,e);break;case g.AFTER_FRAMESET:(function(e,t){switch(t.tagID){case f.HTML:su(e,t);break;case f.NOFRAMES:i9(e,t)}})(this,e);break;case g.AFTER_AFTER_BODY:e.tagID===f.HTML?su(this,e):sv(this,e);break;case g.AFTER_AFTER_FRAMESET:(function(e,t){switch(t.tagID){case f.HTML:su(e,t);break;case f.NOFRAMES:i9(e,t)}})(this,e)}}onEndTag(e){this.skipNextNewLine=!1,this.currentToken=e,this.currentNotInHTML?function(e,t){if(t.tagID===f.P||t.tagID===f.BR){sP(e),e._endTagOutsideForeignContent(t);return}for(let n=e.openElements.stackTop;n>0;n--){let r=e.openElements.items[n];if(e.treeAdapter.getNamespaceURI(r)===h.HTML){e._endTagOutsideForeignContent(t);break}let a=e.treeAdapter.getTagName(r);if(a.toLowerCase()===t.tagName){t.tagName=a,e.openElements.shortenToLength(n);break}}}(this,e):this._endTagOutsideForeignContent(e)}_endTagOutsideForeignContent(e){var t;switch(this.insertionMode){case g.INITIAL:i4(this,e);break;case g.BEFORE_HTML:(function(e,t){let n=t.tagID;(n===f.HTML||n===f.HEAD||n===f.BODY||n===f.BR)&&i6(e,t)})(this,e);break;case g.BEFORE_HEAD:(function(e,t){let n=t.tagID;n===f.HEAD||n===f.BODY||n===f.HTML||n===f.BR?i8(e,t):e._err(t,a.endTagWithoutMatchingOpenElement)})(this,e);break;case g.IN_HEAD:(function(e,t){switch(t.tagID){case f.HEAD:e.openElements.pop(),e.insertionMode=g.AFTER_HEAD;break;case f.BODY:case f.BR:case f.HTML:se(e,t);break;case f.TEMPLATE:i7(e,t);break;default:e._err(t,a.endTagWithoutMatchingOpenElement)}})(this,e);break;case g.IN_HEAD_NO_SCRIPT:(function(e,t){switch(t.tagID){case f.NOSCRIPT:e.openElements.pop(),e.insertionMode=g.IN_HEAD;break;case f.BR:st(e,t);break;default:e._err(t,a.endTagWithoutMatchingOpenElement)}})(this,e);break;case g.AFTER_HEAD:(function(e,t){switch(t.tagID){case f.BODY:case f.HTML:case f.BR:sn(e,t);break;case f.TEMPLATE:i7(e,t);break;default:e._err(t,a.endTagWithoutMatchingOpenElement)}})(this,e);break;case g.IN_BODY:sd(this,e);break;case g.TEXT:e.tagID===f.SCRIPT&&(null===(t=this.scriptHandler)||void 0===t||t.call(this,this.openElements.current)),this.openElements.pop(),this.insertionMode=this.originalInsertionMode;break;case g.IN_TABLE:sE(this,e);break;case g.IN_TABLE_TEXT:s_(this,e);break;case g.IN_CAPTION:(function(e,t){let n=t.tagID;switch(n){case f.CAPTION:case f.TABLE:e.openElements.hasInTableScope(f.CAPTION)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(f.CAPTION),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=g.IN_TABLE,n===f.TABLE&&sE(e,t));break;case f.BODY:case f.COL:case f.COLGROUP:case f.HTML:case f.TBODY:case f.TD:case f.TFOOT:case f.TH:case f.THEAD:case f.TR:break;default:sd(e,t)}})(this,e);break;case g.IN_COLUMN_GROUP:(function(e,t){switch(t.tagID){case f.COLGROUP:e.openElements.currentTagId===f.COLGROUP&&(e.openElements.pop(),e.insertionMode=g.IN_TABLE);break;case f.TEMPLATE:i7(e,t);break;case f.COL:break;default:sb(e,t)}})(this,e);break;case g.IN_TABLE_BODY:sN(this,e);break;case g.IN_ROW:sI(this,e);break;case g.IN_CELL:(function(e,t){let n=t.tagID;switch(n){case f.TD:case f.TH:e.openElements.hasInTableScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker(),e.insertionMode=g.IN_ROW);break;case f.TABLE:case f.TBODY:case f.TFOOT:case f.THEAD:case f.TR:e.openElements.hasInTableScope(n)&&(e._closeTableCell(),sI(e,t));break;case f.BODY:case f.CAPTION:case f.COL:case f.COLGROUP:case f.HTML:break;default:sd(e,t)}})(this,e);break;case g.IN_SELECT:sO(this,e);break;case g.IN_SELECT_IN_TABLE:(function(e,t){let n=t.tagID;n===f.CAPTION||n===f.TABLE||n===f.TBODY||n===f.TFOOT||n===f.THEAD||n===f.TR||n===f.TD||n===f.TH?e.openElements.hasInTableScope(n)&&(e.openElements.popUntilTagNamePopped(f.SELECT),e._resetInsertionMode(),e.onEndTag(t)):sO(e,t)})(this,e);break;case g.IN_TEMPLATE:e.tagID===f.TEMPLATE&&i7(this,e);break;case g.AFTER_BODY:sx(this,e);break;case g.IN_FRAMESET:e.tagID!==f.FRAMESET||this.openElements.isRootHtmlElementCurrent()||(this.openElements.pop(),this.fragmentContext||this.openElements.currentTagId===f.FRAMESET||(this.insertionMode=g.AFTER_FRAMESET));break;case g.AFTER_FRAMESET:e.tagID===f.HTML&&(this.insertionMode=g.AFTER_AFTER_FRAMESET);break;case g.AFTER_AFTER_BODY:sv(this,e)}}onEof(e){switch(this.insertionMode){case g.INITIAL:i4(this,e);break;case g.BEFORE_HTML:i6(this,e);break;case g.BEFORE_HEAD:i8(this,e);break;case g.IN_HEAD:se(this,e);break;case g.IN_HEAD_NO_SCRIPT:st(this,e);break;case g.AFTER_HEAD:sn(this,e);break;case g.IN_BODY:case g.IN_TABLE:case g.IN_CAPTION:case g.IN_COLUMN_GROUP:case g.IN_TABLE_BODY:case g.IN_ROW:case g.IN_CELL:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:sp(this,e);break;case g.TEXT:this._err(e,a.eofInElementThatCanContainOnlyText),this.openElements.pop(),this.insertionMode=this.originalInsertionMode,this.onEof(e);break;case g.IN_TABLE_TEXT:s_(this,e);break;case g.IN_TEMPLATE:sL(this,e);break;case g.AFTER_BODY:case g.IN_FRAMESET:case g.AFTER_FRAMESET:case g.AFTER_AFTER_BODY:case g.AFTER_AFTER_FRAMESET:i5(this,e)}}onWhitespaceCharacter(e){if(this.skipNextNewLine&&(this.skipNextNewLine=!1,e.chars.charCodeAt(0)===r.LINE_FEED)){if(1===e.chars.length)return;e.chars=e.chars.substr(1)}if(this.tokenizer.inForeignNode){this._insertCharacters(e);return}switch(this.insertionMode){case g.IN_HEAD:case g.IN_HEAD_NO_SCRIPT:case g.AFTER_HEAD:case g.TEXT:case g.IN_COLUMN_GROUP:case g.IN_SELECT:case g.IN_SELECT_IN_TABLE:case g.IN_FRAMESET:case g.AFTER_FRAMESET:this._insertCharacters(e);break;case g.IN_BODY:case g.IN_CAPTION:case g.IN_CELL:case g.IN_TEMPLATE:case g.AFTER_BODY:case g.AFTER_AFTER_BODY:case g.AFTER_AFTER_FRAMESET:sa(this,e);break;case g.IN_TABLE:case g.IN_TABLE_BODY:case g.IN_ROW:sm(this,e);break;case g.IN_TABLE_TEXT:sg(this,e)}}}function i2(e,t){for(let n=0;n<8;n++){let n=function(e,t){let n=e.activeFormattingElements.getElementEntryInScopeWithTagName(t.tagName);return n?e.openElements.contains(n.element)?e.openElements.hasInScope(t.tagID)||(n=null):(e.activeFormattingElements.removeEntry(n),n=null):sh(e,t),n}(e,t);if(!n)break;let r=function(e,t){let n=null,r=e.openElements.stackTop;for(;r>=0;r--){let a=e.openElements.items[r];if(a===t.element)break;e._isSpecialElement(a,e.openElements.tagIDs[r])&&(n=a)}return n||(e.openElements.shortenToLength(Math.max(r,0)),e.activeFormattingElements.removeEntry(t)),n}(e,n);if(!r)break;e.activeFormattingElements.bookmark=n;let a=function(e,t,n){let r=t,a=e.openElements.getCommonAncestor(t);for(let i=0,s=a;s!==n;i++,s=a){a=e.openElements.getCommonAncestor(s);let n=e.activeFormattingElements.getElementEntry(s),o=n&&i>=3;!n||o?(o&&e.activeFormattingElements.removeEntry(n),e.openElements.remove(s)):(s=function(e,t){let n=e.treeAdapter.getNamespaceURI(t.element),r=e.treeAdapter.createElement(t.token.tagName,n,t.token.attrs);return e.openElements.replace(t.element,r),t.element=r,r}(e,n),r===t&&(e.activeFormattingElements.bookmark=n),e.treeAdapter.detachNode(r),e.treeAdapter.appendChild(s,r),r=s)}return r}(e,r,n.element),i=e.openElements.getCommonAncestor(n.element);e.treeAdapter.detachNode(a),i&&function(e,t,n){let r=iu(e.treeAdapter.getTagName(t));if(e._isElementCausesFosterParenting(r))e._fosterParentElement(n);else{let a=e.treeAdapter.getNamespaceURI(t);r===f.TEMPLATE&&a===h.HTML&&(t=e.treeAdapter.getTemplateContent(t)),e.treeAdapter.appendChild(t,n)}}(e,i,a),function(e,t,n){let r=e.treeAdapter.getNamespaceURI(n.element),{token:a}=n,i=e.treeAdapter.createElement(a.tagName,r,a.attrs);e._adoptNodes(t,i),e.treeAdapter.appendChild(t,i),e.activeFormattingElements.insertElementAfterBookmark(i,a),e.activeFormattingElements.removeEntry(n),e.openElements.remove(n.element),e.openElements.insertAfter(t,i,a.tagID)}(e,r,n)}}function i3(e,t){e._appendCommentNode(t,e.openElements.currentTmplContentOrNode)}function i5(e,t){if(e.stopped=!0,t.location){let n=e.fragmentContext?0:2;for(let r=e.openElements.stackTop;r>=n;r--)e._setEndLocation(e.openElements.items[r],t);if(!e.fragmentContext&&e.openElements.stackTop>=0){let n=e.openElements.items[0],r=e.treeAdapter.getNodeSourceCodeLocation(n);if(r&&!r.endTag&&(e._setEndLocation(n,t),e.openElements.stackTop>=1)){let n=e.openElements.items[1],r=e.treeAdapter.getNodeSourceCodeLocation(n);r&&!r.endTag&&e._setEndLocation(n,t)}}}}function i4(e,t){e._err(t,a.missingDoctype,!0),e.treeAdapter.setDocumentMode(e.document,p.QUIRKS),e.insertionMode=g.BEFORE_HTML,e._processToken(t)}function i6(e,t){e._insertFakeRootElement(),e.insertionMode=g.BEFORE_HEAD,e._processToken(t)}function i8(e,t){e._insertFakeElement(m.HEAD,f.HEAD),e.headElement=e.openElements.current,e.insertionMode=g.IN_HEAD,e._processToken(t)}function i9(e,t){switch(t.tagID){case f.HTML:su(e,t);break;case f.BASE:case f.BASEFONT:case f.BGSOUND:case f.LINK:case f.META:e._appendElement(t,h.HTML),t.ackSelfClosing=!0;break;case f.TITLE:e._switchToTextParsing(t,im.RCDATA);break;case f.NOSCRIPT:e.options.scriptingEnabled?e._switchToTextParsing(t,im.RAWTEXT):(e._insertElement(t,h.HTML),e.insertionMode=g.IN_HEAD_NO_SCRIPT);break;case f.NOFRAMES:case f.STYLE:e._switchToTextParsing(t,im.RAWTEXT);break;case f.SCRIPT:e._switchToTextParsing(t,im.SCRIPT_DATA);break;case f.TEMPLATE:e._insertTemplate(t),e.activeFormattingElements.insertMarker(),e.framesetOk=!1,e.insertionMode=g.IN_TEMPLATE,e.tmplInsertionModeStack.unshift(g.IN_TEMPLATE);break;case f.HEAD:e._err(t,a.misplacedStartTagForHeadElement);break;default:se(e,t)}}function i7(e,t){e.openElements.tmplCount>0?(e.openElements.generateImpliedEndTagsThoroughly(),e.openElements.currentTagId!==f.TEMPLATE&&e._err(t,a.closingOfElementWithOpenChildElements),e.openElements.popUntilTagNamePopped(f.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode()):e._err(t,a.endTagWithoutMatchingOpenElement)}function se(e,t){e.openElements.pop(),e.insertionMode=g.AFTER_HEAD,e._processToken(t)}function st(e,t){let n=t.type===i.EOF?a.openElementsLeftAfterEof:a.disallowedContentInNoscriptInHead;e._err(t,n),e.openElements.pop(),e.insertionMode=g.IN_HEAD,e._processToken(t)}function sn(e,t){e._insertFakeElement(m.BODY,f.BODY),e.insertionMode=g.IN_BODY,sr(e,t)}function sr(e,t){switch(t.type){case i.CHARACTER:si(e,t);break;case i.WHITESPACE_CHARACTER:sa(e,t);break;case i.COMMENT:i3(e,t);break;case i.START_TAG:su(e,t);break;case i.END_TAG:sd(e,t);break;case i.EOF:sp(e,t)}}function sa(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t)}function si(e,t){e._reconstructActiveFormattingElements(),e._insertCharacters(t),e.framesetOk=!1}function ss(e,t){e._reconstructActiveFormattingElements(),e._appendElement(t,h.HTML),e.framesetOk=!1,t.ackSelfClosing=!0}function so(e){let t=ia(e,d.TYPE);return null!=t&&"hidden"===t.toLowerCase()}function sl(e,t){e._switchToTextParsing(t,im.RAWTEXT)}function sc(e,t){e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML)}function su(e,t){switch(t.tagID){case f.I:case f.S:case f.B:case f.U:case f.EM:case f.TT:case f.BIG:case f.CODE:case f.FONT:case f.SMALL:case f.STRIKE:case f.STRONG:e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t);break;case f.A:!function(e,t){let n=e.activeFormattingElements.getElementEntryInScopeWithTagName(m.A);n&&(i2(e,t),e.openElements.remove(n.element),e.activeFormattingElements.removeEntry(n)),e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t)}(e,t);break;case f.H1:case f.H2:case f.H3:case f.H4:case f.H5:case f.H6:e.openElements.hasInButtonScope(f.P)&&e._closePElement(),void 0!==e.openElements.currentTagId&&ip.has(e.openElements.currentTagId)&&e.openElements.pop(),e._insertElement(t,h.HTML);break;case f.P:case f.DL:case f.OL:case f.UL:case f.DIV:case f.DIR:case f.NAV:case f.MAIN:case f.MENU:case f.ASIDE:case f.CENTER:case f.FIGURE:case f.FOOTER:case f.HEADER:case f.HGROUP:case f.DIALOG:case f.DETAILS:case f.ADDRESS:case f.ARTICLE:case f.SEARCH:case f.SECTION:case f.SUMMARY:case f.FIELDSET:case f.BLOCKQUOTE:case f.FIGCAPTION:e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,h.HTML);break;case f.LI:case f.DD:case f.DT:!function(e,t){e.framesetOk=!1;let n=t.tagID;for(let t=e.openElements.stackTop;t>=0;t--){let r=e.openElements.tagIDs[t];if(n===f.LI&&r===f.LI||(n===f.DD||n===f.DT)&&(r===f.DD||r===f.DT)){e.openElements.generateImpliedEndTagsWithExclusion(r),e.openElements.popUntilTagNamePopped(r);break}if(r!==f.ADDRESS&&r!==f.DIV&&r!==f.P&&e._isSpecialElement(e.openElements.items[t],r))break}e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,h.HTML)}(e,t);break;case f.BR:case f.IMG:case f.WBR:case f.AREA:case f.EMBED:case f.KEYGEN:ss(e,t);break;case f.HR:e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._appendElement(t,h.HTML),e.framesetOk=!1,t.ackSelfClosing=!0;break;case f.RB:case f.RTC:e.openElements.hasInScope(f.RUBY)&&e.openElements.generateImpliedEndTags(),e._insertElement(t,h.HTML);break;case f.RT:case f.RP:e.openElements.hasInScope(f.RUBY)&&e.openElements.generateImpliedEndTagsWithExclusion(f.RTC),e._insertElement(t,h.HTML);break;case f.PRE:case f.LISTING:e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,h.HTML),e.skipNextNewLine=!0,e.framesetOk=!1;break;case f.XMP:e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._reconstructActiveFormattingElements(),e.framesetOk=!1,e._switchToTextParsing(t,im.RAWTEXT);break;case f.SVG:e._reconstructActiveFormattingElements(),iX(t),i$(t),t.selfClosing?e._appendElement(t,h.SVG):e._insertElement(t,h.SVG),t.ackSelfClosing=!0;break;case f.HTML:0===e.openElements.tmplCount&&e.treeAdapter.adoptAttributes(e.openElements.items[0],t.attrs);break;case f.BASE:case f.LINK:case f.META:case f.STYLE:case f.TITLE:case f.SCRIPT:case f.BGSOUND:case f.BASEFONT:case f.TEMPLATE:i9(e,t);break;case f.BODY:!function(e,t){let n=e.openElements.tryPeekProperlyNestedBodyElement();n&&0===e.openElements.tmplCount&&(e.framesetOk=!1,e.treeAdapter.adoptAttributes(n,t.attrs))}(e,t);break;case f.FORM:!function(e,t){let n=e.openElements.tmplCount>0;e.formElement&&!n||(e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,h.HTML),n||(e.formElement=e.openElements.current))}(e,t);break;case f.NOBR:e._reconstructActiveFormattingElements(),e.openElements.hasInScope(f.NOBR)&&(i2(e,t),e._reconstructActiveFormattingElements()),e._insertElement(t,h.HTML),e.activeFormattingElements.pushElement(e.openElements.current,t);break;case f.MATH:e._reconstructActiveFormattingElements(),iK(t),i$(t),t.selfClosing?e._appendElement(t,h.MATHML):e._insertElement(t,h.MATHML),t.ackSelfClosing=!0;break;case f.TABLE:e.treeAdapter.getDocumentMode(e.document)!==p.QUIRKS&&e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,h.HTML),e.framesetOk=!1,e.insertionMode=g.IN_TABLE;break;case f.INPUT:e._reconstructActiveFormattingElements(),e._appendElement(t,h.HTML),so(t)||(e.framesetOk=!1),t.ackSelfClosing=!0;break;case f.PARAM:case f.TRACK:case f.SOURCE:e._appendElement(t,h.HTML),t.ackSelfClosing=!0;break;case f.IMAGE:t.tagName=m.IMG,t.tagID=f.IMG,ss(e,t);break;case f.BUTTON:e.openElements.hasInScope(f.BUTTON)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(f.BUTTON)),e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML),e.framesetOk=!1;break;case f.APPLET:case f.OBJECT:case f.MARQUEE:e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML),e.activeFormattingElements.insertMarker(),e.framesetOk=!1;break;case f.IFRAME:e.framesetOk=!1,e._switchToTextParsing(t,im.RAWTEXT);break;case f.SELECT:e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML),e.framesetOk=!1,e.insertionMode=e.insertionMode===g.IN_TABLE||e.insertionMode===g.IN_CAPTION||e.insertionMode===g.IN_TABLE_BODY||e.insertionMode===g.IN_ROW||e.insertionMode===g.IN_CELL?g.IN_SELECT_IN_TABLE:g.IN_SELECT;break;case f.OPTION:case f.OPTGROUP:e.openElements.currentTagId===f.OPTION&&e.openElements.pop(),e._reconstructActiveFormattingElements(),e._insertElement(t,h.HTML);break;case f.NOEMBED:case f.NOFRAMES:sl(e,t);break;case f.FRAMESET:!function(e,t){let n=e.openElements.tryPeekProperlyNestedBodyElement();e.framesetOk&&n&&(e.treeAdapter.detachNode(n),e.openElements.popAllUpToHtmlElement(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_FRAMESET)}(e,t);break;case f.TEXTAREA:e._insertElement(t,h.HTML),e.skipNextNewLine=!0,e.tokenizer.state=im.RCDATA,e.originalInsertionMode=e.insertionMode,e.framesetOk=!1,e.insertionMode=g.TEXT;break;case f.NOSCRIPT:e.options.scriptingEnabled?sl(e,t):sc(e,t);break;case f.PLAINTEXT:e.openElements.hasInButtonScope(f.P)&&e._closePElement(),e._insertElement(t,h.HTML),e.tokenizer.state=im.PLAINTEXT;break;case f.COL:case f.TH:case f.TD:case f.TR:case f.HEAD:case f.FRAME:case f.TBODY:case f.TFOOT:case f.THEAD:case f.CAPTION:case f.COLGROUP:break;default:sc(e,t)}}function sh(e,t){let n=t.tagName,r=t.tagID;for(let t=e.openElements.stackTop;t>0;t--){let a=e.openElements.items[t],i=e.openElements.tagIDs[t];if(r===i&&(r!==f.UNKNOWN||e.treeAdapter.getTagName(a)===n)){e.openElements.generateImpliedEndTagsWithExclusion(r),e.openElements.stackTop>=t&&e.openElements.shortenToLength(t);break}if(e._isSpecialElement(a,i))break}}function sd(e,t){switch(t.tagID){case f.A:case f.B:case f.I:case f.S:case f.U:case f.EM:case f.TT:case f.BIG:case f.CODE:case f.FONT:case f.NOBR:case f.SMALL:case f.STRIKE:case f.STRONG:i2(e,t);break;case f.P:e.openElements.hasInButtonScope(f.P)||e._insertFakeElement(m.P,f.P),e._closePElement();break;case f.DL:case f.UL:case f.OL:case f.DIR:case f.DIV:case f.NAV:case f.PRE:case f.MAIN:case f.MENU:case f.ASIDE:case f.BUTTON:case f.CENTER:case f.FIGURE:case f.FOOTER:case f.HEADER:case f.HGROUP:case f.DIALOG:case f.ADDRESS:case f.ARTICLE:case f.DETAILS:case f.SEARCH:case f.SECTION:case f.SUMMARY:case f.LISTING:case f.FIELDSET:case f.BLOCKQUOTE:case f.FIGCAPTION:!function(e,t){let n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n))}(e,t);break;case f.LI:e.openElements.hasInListItemScope(f.LI)&&(e.openElements.generateImpliedEndTagsWithExclusion(f.LI),e.openElements.popUntilTagNamePopped(f.LI));break;case f.DD:case f.DT:!function(e,t){let n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTagsWithExclusion(n),e.openElements.popUntilTagNamePopped(n))}(e,t);break;case f.H1:case f.H2:case f.H3:case f.H4:case f.H5:case f.H6:e.openElements.hasNumberedHeaderInScope()&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilNumberedHeaderPopped());break;case f.BR:e._reconstructActiveFormattingElements(),e._insertFakeElement(m.BR,f.BR),e.openElements.pop(),e.framesetOk=!1;break;case f.BODY:!function(e,t){if(e.openElements.hasInScope(f.BODY)&&(e.insertionMode=g.AFTER_BODY,e.options.sourceCodeLocationInfo)){let n=e.openElements.tryPeekProperlyNestedBodyElement();n&&e._setEndLocation(n,t)}}(e,t);break;case f.HTML:e.openElements.hasInScope(f.BODY)&&(e.insertionMode=g.AFTER_BODY,sx(e,t));break;case f.FORM:!function(e){let t=e.openElements.tmplCount>0,{formElement:n}=e;t||(e.formElement=null),(n||t)&&e.openElements.hasInScope(f.FORM)&&(e.openElements.generateImpliedEndTags(),t?e.openElements.popUntilTagNamePopped(f.FORM):n&&e.openElements.remove(n))}(e);break;case f.APPLET:case f.OBJECT:case f.MARQUEE:!function(e,t){let n=t.tagID;e.openElements.hasInScope(n)&&(e.openElements.generateImpliedEndTags(),e.openElements.popUntilTagNamePopped(n),e.activeFormattingElements.clearToLastMarker())}(e,t);break;case f.TEMPLATE:i7(e,t);break;default:sh(e,t)}}function sp(e,t){e.tmplInsertionModeStack.length>0?sL(e,t):i5(e,t)}function sm(e,t){if(void 0!==e.openElements.currentTagId&&iJ.has(e.openElements.currentTagId))switch(e.pendingCharacterTokens.length=0,e.hasNonWhitespacePendingCharacterToken=!1,e.originalInsertionMode=e.insertionMode,e.insertionMode=g.IN_TABLE_TEXT,t.type){case i.CHARACTER:sA(e,t);break;case i.WHITESPACE_CHARACTER:sg(e,t)}else sT(e,t)}function sf(e,t){switch(t.tagID){case f.TD:case f.TH:case f.TR:e.openElements.clearBackToTableContext(),e._insertFakeElement(m.TBODY,f.TBODY),e.insertionMode=g.IN_TABLE_BODY,sy(e,t);break;case f.STYLE:case f.SCRIPT:case f.TEMPLATE:i9(e,t);break;case f.COL:e.openElements.clearBackToTableContext(),e._insertFakeElement(m.COLGROUP,f.COLGROUP),e.insertionMode=g.IN_COLUMN_GROUP,sS(e,t);break;case f.FORM:e.formElement||0!==e.openElements.tmplCount||(e._insertElement(t,h.HTML),e.formElement=e.openElements.current,e.openElements.pop());break;case f.TABLE:e.openElements.hasInTableScope(f.TABLE)&&(e.openElements.popUntilTagNamePopped(f.TABLE),e._resetInsertionMode(),e._processStartTag(t));break;case f.TBODY:case f.TFOOT:case f.THEAD:e.openElements.clearBackToTableContext(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_TABLE_BODY;break;case f.INPUT:so(t)?e._appendElement(t,h.HTML):sT(e,t),t.ackSelfClosing=!0;break;case f.CAPTION:e.openElements.clearBackToTableContext(),e.activeFormattingElements.insertMarker(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_CAPTION;break;case f.COLGROUP:e.openElements.clearBackToTableContext(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_COLUMN_GROUP;break;default:sT(e,t)}}function sE(e,t){switch(t.tagID){case f.TABLE:e.openElements.hasInTableScope(f.TABLE)&&(e.openElements.popUntilTagNamePopped(f.TABLE),e._resetInsertionMode());break;case f.TEMPLATE:i7(e,t);break;case f.BODY:case f.CAPTION:case f.COL:case f.COLGROUP:case f.HTML:case f.TBODY:case f.TD:case f.TFOOT:case f.TH:case f.THEAD:case f.TR:break;default:sT(e,t)}}function sT(e,t){let n=e.fosterParentingEnabled;e.fosterParentingEnabled=!0,sr(e,t),e.fosterParentingEnabled=n}function sg(e,t){e.pendingCharacterTokens.push(t)}function sA(e,t){e.pendingCharacterTokens.push(t),e.hasNonWhitespacePendingCharacterToken=!0}function s_(e,t){let n=0;if(e.hasNonWhitespacePendingCharacterToken)for(;n<e.pendingCharacterTokens.length;n++)sT(e,e.pendingCharacterTokens[n]);else for(;n<e.pendingCharacterTokens.length;n++)e._insertCharacters(e.pendingCharacterTokens[n]);e.insertionMode=e.originalInsertionMode,e._processToken(t)}let sk=new Set([f.CAPTION,f.COL,f.COLGROUP,f.TBODY,f.TD,f.TFOOT,f.TH,f.THEAD,f.TR]);function sS(e,t){switch(t.tagID){case f.HTML:su(e,t);break;case f.COL:e._appendElement(t,h.HTML),t.ackSelfClosing=!0;break;case f.TEMPLATE:i9(e,t);break;default:sb(e,t)}}function sb(e,t){e.openElements.currentTagId===f.COLGROUP&&(e.openElements.pop(),e.insertionMode=g.IN_TABLE,e._processToken(t))}function sy(e,t){switch(t.tagID){case f.TR:e.openElements.clearBackToTableBodyContext(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_ROW;break;case f.TH:case f.TD:e.openElements.clearBackToTableBodyContext(),e._insertFakeElement(m.TR,f.TR),e.insertionMode=g.IN_ROW,sC(e,t);break;case f.CAPTION:case f.COL:case f.COLGROUP:case f.TBODY:case f.TFOOT:case f.THEAD:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE,sf(e,t));break;default:sf(e,t)}}function sN(e,t){let n=t.tagID;switch(t.tagID){case f.TBODY:case f.TFOOT:case f.THEAD:e.openElements.hasInTableScope(n)&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE);break;case f.TABLE:e.openElements.hasTableBodyContextInTableScope()&&(e.openElements.clearBackToTableBodyContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE,sE(e,t));break;case f.BODY:case f.CAPTION:case f.COL:case f.COLGROUP:case f.HTML:case f.TD:case f.TH:case f.TR:break;default:sE(e,t)}}function sC(e,t){switch(t.tagID){case f.TH:case f.TD:e.openElements.clearBackToTableRowContext(),e._insertElement(t,h.HTML),e.insertionMode=g.IN_CELL,e.activeFormattingElements.insertMarker();break;case f.CAPTION:case f.COL:case f.COLGROUP:case f.TBODY:case f.TFOOT:case f.THEAD:case f.TR:e.openElements.hasInTableScope(f.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY,sy(e,t));break;default:sf(e,t)}}function sI(e,t){switch(t.tagID){case f.TR:e.openElements.hasInTableScope(f.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY);break;case f.TABLE:e.openElements.hasInTableScope(f.TR)&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY,sN(e,t));break;case f.TBODY:case f.TFOOT:case f.THEAD:(e.openElements.hasInTableScope(t.tagID)||e.openElements.hasInTableScope(f.TR))&&(e.openElements.clearBackToTableRowContext(),e.openElements.pop(),e.insertionMode=g.IN_TABLE_BODY,sN(e,t));break;case f.BODY:case f.CAPTION:case f.COL:case f.COLGROUP:case f.HTML:case f.TD:case f.TH:break;default:sE(e,t)}}function sD(e,t){switch(t.tagID){case f.HTML:su(e,t);break;case f.OPTION:e.openElements.currentTagId===f.OPTION&&e.openElements.pop(),e._insertElement(t,h.HTML);break;case f.OPTGROUP:e.openElements.currentTagId===f.OPTION&&e.openElements.pop(),e.openElements.currentTagId===f.OPTGROUP&&e.openElements.pop(),e._insertElement(t,h.HTML);break;case f.HR:e.openElements.currentTagId===f.OPTION&&e.openElements.pop(),e.openElements.currentTagId===f.OPTGROUP&&e.openElements.pop(),e._appendElement(t,h.HTML),t.ackSelfClosing=!0;break;case f.INPUT:case f.KEYGEN:case f.TEXTAREA:case f.SELECT:e.openElements.hasInSelectScope(f.SELECT)&&(e.openElements.popUntilTagNamePopped(f.SELECT),e._resetInsertionMode(),t.tagID!==f.SELECT&&e._processStartTag(t));break;case f.SCRIPT:case f.TEMPLATE:i9(e,t)}}function sO(e,t){switch(t.tagID){case f.OPTGROUP:e.openElements.stackTop>0&&e.openElements.currentTagId===f.OPTION&&e.openElements.tagIDs[e.openElements.stackTop-1]===f.OPTGROUP&&e.openElements.pop(),e.openElements.currentTagId===f.OPTGROUP&&e.openElements.pop();break;case f.OPTION:e.openElements.currentTagId===f.OPTION&&e.openElements.pop();break;case f.SELECT:e.openElements.hasInSelectScope(f.SELECT)&&(e.openElements.popUntilTagNamePopped(f.SELECT),e._resetInsertionMode());break;case f.TEMPLATE:i7(e,t)}}function sL(e,t){e.openElements.tmplCount>0?(e.openElements.popUntilTagNamePopped(f.TEMPLATE),e.activeFormattingElements.clearToLastMarker(),e.tmplInsertionModeStack.shift(),e._resetInsertionMode(),e.onEof(t)):i5(e,t)}function sx(e,t){var n;if(t.tagID===f.HTML){if(e.fragmentContext||(e.insertionMode=g.AFTER_AFTER_BODY),e.options.sourceCodeLocationInfo&&e.openElements.tagIDs[0]===f.HTML){e._setEndLocation(e.openElements.items[0],t);let r=e.openElements.items[1];!r||(null===(n=e.treeAdapter.getNodeSourceCodeLocation(r))||void 0===n?void 0:n.endTag)||e._setEndLocation(r,t)}}else sR(e,t)}function sR(e,t){e.insertionMode=g.IN_BODY,sr(e,t)}function sv(e,t){e.insertionMode=g.IN_BODY,sr(e,t)}function sP(e){for(;e.treeAdapter.getNamespaceURI(e.openElements.current)!==h.HTML&&void 0!==e.openElements.currentTagId&&!e._isIntegrationPoint(e.openElements.currentTagId,e.openElements.current);)e.openElements.pop()}null==String.prototype.codePointAt||((e,t)=>e.codePointAt(t)),m.AREA,m.BASE,m.BASEFONT,m.BGSOUND,m.BR,m.COL,m.EMBED,m.FRAME,m.HR,m.IMG,m.INPUT,m.KEYGEN,m.LINK,m.META,m.PARAM,m.SOURCE,m.TRACK,m.WBR;let sM=/<(\/?)(iframe|noembed|noframes|plaintext|script|style|textarea|title|xmp)(?=[\t\n\f\r />])/gi,sw=new Set(["mdxFlowExpression","mdxJsxFlowElement","mdxJsxTextElement","mdxTextExpression","mdxjsEsm"]),sB={sourceCodeLocationInfo:!0,scriptingEnabled:!1};function sF(e,t){let n=function(e){let t="root"===e.type?e.children[0]:e;return!!(t&&("doctype"===t.type||"element"===t.type&&"html"===t.tagName.toLowerCase()))}(e),r=a1("type",{handlers:{root:sH,element:sG,text:sq,comment:sj,doctype:sz,raw:sY},unknown:sV}),a={parser:n?new i1(sB):i1.getFragmentParser(void 0,sB),handle(e){r(e,a)},stitches:!1,options:t||{}};r(e,a),sW(a,eg());let i=function(e,t){let n=t||{};return aT({file:n.file||void 0,location:!1,schema:"svg"===n.space?es:ei,verbose:n.verbose||!1},e)}(n?a.parser.document:a.parser.getFragment(),{file:a.options.file});return(a.stitches&&n_(i,"comment",function(e,t,n){if(e.value.stitch&&n&&void 0!==t)return n.children[t]=e.value.stitch,t}),"root"===i.type&&1===i.children.length&&i.children[0].type===e.type)?i.children[0]:i}function sU(e,t){let n=-1;if(e)for(;++n<e.length;)t.handle(e[n])}function sH(e,t){sU(e.children,t)}function sG(e,t){(function(e,t){let n=e.tagName.toLowerCase();if(t.parser.tokenizer.state===im.PLAINTEXT)return;sW(t,eg(e));let r=t.parser.openElements.current,a="namespaceURI"in r?r.namespaceURI:am.html;a===am.html&&"svg"===n&&(a=am.svg);let s=a3({...e,children:[]},"svg"===({space:a===am.svg?"svg":"html"}).space?aQ:aW),o={type:i.START_TAG,tagName:n,tagID:iu(n),selfClosing:!1,ackSelfClosing:!1,attrs:"attrs"in s?s.attrs:[],location:sK(e)};t.parser.currentToken=o,t.parser._processToken(t.parser.currentToken),t.parser.tokenizer.lastStartTagName=n})(e,t),sU(e.children,t),function(e,t){let n=e.tagName.toLowerCase();if(!t.parser.tokenizer.inForeignNode&&a6.includes(n)||t.parser.tokenizer.state===im.PLAINTEXT)return;sW(t,eT(e));let r={type:i.END_TAG,tagName:n,tagID:iu(n),selfClosing:!1,ackSelfClosing:!1,attrs:[],location:sK(e)};t.parser.currentToken=r,t.parser._processToken(t.parser.currentToken),n===t.parser.tokenizer.lastStartTagName&&(t.parser.tokenizer.state===im.RCDATA||t.parser.tokenizer.state===im.RAWTEXT||t.parser.tokenizer.state===im.SCRIPT_DATA)&&(t.parser.tokenizer.state=im.DATA)}(e,t)}function sq(e,t){t.parser.tokenizer.state>4&&(t.parser.tokenizer.state=0);let n={type:i.CHARACTER,chars:e.value,location:sK(e)};sW(t,eg(e)),t.parser.currentToken=n,t.parser._processToken(t.parser.currentToken)}function sz(e,t){let n={type:i.DOCTYPE,name:"html",forceQuirks:!1,publicId:"",systemId:"",location:sK(e)};sW(t,eg(e)),t.parser.currentToken=n,t.parser._processToken(t.parser.currentToken)}function sj(e,t){let n=e.value,r={type:i.COMMENT,data:n,location:sK(e)};sW(t,eg(e)),t.parser.currentToken=r,t.parser._processToken(t.parser.currentToken)}function sY(e,t){if(t.parser.tokenizer.preprocessor.html="",t.parser.tokenizer.preprocessor.pos=-1,t.parser.tokenizer.preprocessor.lastGapPos=-2,t.parser.tokenizer.preprocessor.gapStack=[],t.parser.tokenizer.preprocessor.skipNextNewLine=!1,t.parser.tokenizer.preprocessor.lastChunkWritten=!1,t.parser.tokenizer.preprocessor.endOfChunkHit=!1,t.parser.tokenizer.preprocessor.isEol=!1,sQ(t,eg(e)),t.parser.tokenizer.write(t.options.tagfilter?e.value.replace(sM,"&lt;$1$2"):e.value,!1),t.parser.tokenizer._runParsingLoop(),72===t.parser.tokenizer.state||78===t.parser.tokenizer.state){t.parser.tokenizer.preprocessor.lastChunkWritten=!0;let e=t.parser.tokenizer._consume();t.parser.tokenizer._callState(e)}}function sV(e,t){if(t.options.passThrough&&t.options.passThrough.includes(e.type))!function(e,t){t.stitches=!0;let n="children"in e?nh({...e,children:[]}):nh(e);if("children"in e&&"children"in n){let r=sF({type:"root",children:e.children},t.options);n.children=r.children}sj({type:"comment",value:{stitch:n}},t)}(e,t);else{let t="";throw sw.has(e.type)&&(t=". It looks like you are using MDX nodes with `hast-util-raw` (or `rehype-raw`). If you use this because you are using remark or rehype plugins that inject `'html'` nodes, then please raise an issue with that plugin, as its a bad and slow idea. If you use this because you are using markdown syntax, then you have to configure this utility (or plugin) to pass through these nodes (see `passThrough` in docs), but you can also migrate to use the MDX syntax"),Error("Cannot compile `"+e.type+"` node"+t)}}function sW(e,t){sQ(e,t);let n=e.parser.tokenizer.currentCharacterToken;n&&n.location&&(n.location.endLine=e.parser.tokenizer.preprocessor.line,n.location.endCol=e.parser.tokenizer.preprocessor.col+1,n.location.endOffset=e.parser.tokenizer.preprocessor.offset+1,e.parser.currentToken=n,e.parser._processToken(e.parser.currentToken)),e.parser.tokenizer.paused=!1,e.parser.tokenizer.inLoop=!1,e.parser.tokenizer.active=!1,e.parser.tokenizer.returnState=im.DATA,e.parser.tokenizer.charRefCode=-1,e.parser.tokenizer.consumedAfterSnapshot=-1,e.parser.tokenizer.currentLocation=null,e.parser.tokenizer.currentCharacterToken=null,e.parser.tokenizer.currentToken=null,e.parser.tokenizer.currentAttr={name:"",value:""}}function sQ(e,t){if(t&&void 0!==t.offset){let n={startLine:t.line,startCol:t.column,startOffset:t.offset,endLine:-1,endCol:-1,endOffset:-1};e.parser.tokenizer.preprocessor.lineStartPos=-t.column+1,e.parser.tokenizer.preprocessor.droppedBufferSize=t.offset,e.parser.tokenizer.preprocessor.line=t.line,e.parser.tokenizer.currentLocation=n}}function sK(e){let t=eg(e)||{line:void 0,column:void 0,offset:void 0},n=eT(e)||{line:void 0,column:void 0,offset:void 0};return{startLine:t.line,startCol:t.column,startOffset:t.offset,endLine:n.line,endCol:n.column,endOffset:n.offset}}function sX(e){return function(t,n){return sF(t,{...e,file:n})}}var s$=n(68570);let sZ=(0,s$.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDInteractive.tsx#default`),sJ=(0,s$.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/NSAIDContentSimple.tsx#default`);var s0=n(76373);let s1=(0,s$.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/ArticleInteractions.tsx#default`),s2=(0,s$.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/ReadingProgress.tsx#default`),s3=(0,s$.createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/TableOfContents.tsx#default`);async function s5(){let e=["nsaid-menstrual-pain-professional-guide","natural-physical-therapy-guide","menstrual-pain-management-guide","period-pain-relief-guide","understanding-menstrual-pain"],t=[];for(let n of["en","zh"])for(let r of e)t.push({locale:n,slug:r});return t}async function s4({params:{locale:e,slug:t}}){let n=(0,C.vX)(t,e);if(!n)return{title:"Article Not Found | periodhub.health",description:"The requested article could not be found."};let r="zh"===e&&n.title_zh||n.title,a="zh"===e&&n.seo_description_zh||n.seo_description,i=process.env.NEXT_PUBLIC_BASE_URL||"https://period-hub.com",s=`${i}/${e}/articles/${t}`;return{title:`${r} | Period Hub`,description:a,keywords:("zh"===e?n.tags_zh:n.tags)?.join(", "),authors:[{name:n.author}],openGraph:{title:r,description:a,url:s,siteName:"Period Hub",images:[{url:n.featured_image||`${i}/og-image.jpg`,width:1200,height:630,alt:r}],type:"article",publishedTime:n.date,modifiedTime:n.date,locale:e,authors:[n.author]},twitter:{card:"summary_large_image",title:r,description:a,images:[n.featured_image||`${i}/og-image.jpg`]},alternates:{canonical:s,languages:{"zh-CN":`${i}/zh/articles/${t}`,"en-US":`${i}/en/articles/${t}`}}}}async function s6({params:{locale:e,slug:t}}){(0,y.t)(e);let n=(0,C.vX)(t,e),r=(0,C.eA)(t,e,3);n||(0,b.notFound)();let a="zh"===e&&n.title_zh||n.title,i="zh"===e&&n.summary_zh||n.summary,s="zh"===e&&n.category_zh||n.category,o="zh"===e&&n.reading_time_zh||n.reading_time,l="nsaid-menstrual-pain-professional-guide"===t,c=process.env.NEXT_PUBLIC_BASE_URL||"https://period-hub.com",u=`${c}/${e}/articles/${t}`;return(0,S.jsxs)("div",{className:"min-h-screen bg-neutral-50",children:[S.jsx(s0.Z,{type:"medicalWebPage",data:{title:a,description:i||"",url:u,image:n.featured_image,author:n.author,datePublished:n.date,dateModified:n.date,locale:e,keywords:"zh"===e?n.tags_zh:n.tags}}),S.jsx(s2,{locale:e}),l&&S.jsx(sZ,{locale:e}),(0,S.jsxs)("div",{className:"space-y-6 sm:space-y-8",children:[S.jsx("div",{className:"container-custom",children:(0,S.jsxs)(N.default,{href:`/${e}/articles`,className:"inline-flex items-center text-primary-600 hover:text-primary-700 font-medium mobile-touch-target",children:[S.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"zh"===e?"返回文章列表":"Back to Articles"]})}),S.jsx("header",{className:"bg-white border-b border-gray-100",children:S.jsx("div",{className:"container-custom py-6 sm:py-8",children:(0,S.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,S.jsxs)("div",{className:"flex flex-wrap items-center gap-2 sm:gap-4 text-xs sm:text-sm text-neutral-600 mb-4",children:[S.jsx("span",{className:"bg-primary-100 text-primary-700 px-2 sm:px-3 py-1 rounded-full font-medium",children:s}),(0,S.jsxs)("time",{dateTime:n.date,className:"flex items-center gap-1",children:[S.jsx("svg",{className:"w-3 h-3 sm:w-4 sm:h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})}),new Date(n.date).toLocaleDateString("zh"===e?"zh-CN":"en-US")]}),o&&(0,S.jsxs)("span",{className:"flex items-center gap-1",children:[S.jsx("svg",{className:"w-3 h-3 sm:w-4 sm:h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})}),o]})]}),S.jsx("h1",{className:"text-xl sm:text-2xl md:text-3xl lg:text-4xl font-bold text-neutral-800 mb-3 sm:mb-4 leading-tight",children:a}),i&&(0,S.jsxs)("div",{className:"bg-blue-50 border-l-4 border-blue-400 p-4 sm:p-6 mb-6 rounded-r-lg",children:[S.jsx("h2",{className:"text-sm sm:text-base font-semibold text-blue-800 mb-2",children:"zh"===e?"文章摘要":"Article Summary"}),S.jsx("p",{className:"text-sm sm:text-base text-blue-700 leading-relaxed",children:i})]}),S.jsx("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-6",children:(0,S.jsxs)("div",{className:"flex items-center gap-3",children:[S.jsx("div",{className:"w-8 h-8 sm:w-10 sm:h-10 bg-primary-100 rounded-full flex items-center justify-center",children:S.jsx("span",{className:"text-primary-600 font-semibold text-sm sm:text-base",children:n.author?.charAt(0)||"P"})}),(0,S.jsxs)("div",{children:[S.jsx("p",{className:"font-medium text-sm sm:text-base text-neutral-800",children:n.author||"Period Health Team"}),S.jsx("p",{className:"text-xs sm:text-sm text-neutral-600",children:"zh"===e?"健康专家":"Health Expert"})]})]})})]})})}),S.jsx("main",{className:"container-custom",children:S.jsx("div",{className:"max-w-6xl mx-auto",children:(0,S.jsxs)("div",{className:"lg:grid lg:grid-cols-4 lg:gap-8",children:[(0,S.jsxs)("div",{className:"lg:col-span-3",children:[S.jsx("div",{className:"lg:hidden mb-6",children:S.jsx(s3,{locale:e})}),S.jsx("div",{className:"bg-white rounded-lg shadow-sm p-4 sm:p-6 lg:p-8 mb-6",children:S.jsx("div",{className:"prose prose-sm sm:prose-base lg:prose-lg max-w-none prose-primary prose-headings:text-neutral-800 prose-p:text-neutral-700 prose-li:text-neutral-700",children:l?S.jsx(sJ,{content:n.content}):S.jsx(n6,{remarkPlugins:[ao],rehypePlugins:[sX],components:{table:({children:e})=>S.jsx("div",{className:"overflow-x-auto my-6",children:S.jsx("table",{className:"min-w-full border-collapse border border-gray-300 bg-white rounded-lg shadow-sm",children:e})}),thead:({children:e})=>S.jsx("thead",{className:"bg-primary-50",children:e}),th:({children:e})=>S.jsx("th",{className:"border border-gray-300 px-3 sm:px-4 py-2 sm:py-3 bg-primary-100 font-semibold text-left text-primary-800 text-sm sm:text-base",children:e}),td:({children:e})=>S.jsx("td",{className:"border border-gray-300 px-3 sm:px-4 py-2 sm:py-3 text-neutral-700 text-sm sm:text-base",children:e}),tr:({children:e})=>S.jsx("tr",{className:"even:bg-gray-50 hover:bg-primary-25",children:e})},children:n.content})})}),S.jsx(s1,{articleId:t,articleTitle:a,locale:e,className:"mb-6"})]}),S.jsx("div",{className:"hidden lg:block lg:col-span-1",children:(0,S.jsxs)("div",{className:"sticky top-6 space-y-6",children:[S.jsx(s3,{locale:e}),(0,S.jsxs)("div",{className:"bg-white border border-gray-200 rounded-lg p-4",children:[S.jsx("h3",{className:"font-semibold text-gray-800 mb-3 text-sm",children:"zh"===e?"快速操作":"Quick Actions"}),S.jsx("div",{className:"space-y-2",children:(0,S.jsxs)(N.default,{href:`/${e}/articles`,className:"w-full text-left px-3 py-2 text-sm text-gray-600 hover:bg-gray-50 rounded flex items-center gap-2",children:[S.jsx("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})}),"zh"===e?"更多文章":"More Articles"]})})]})]})})]})})}),S.jsx("section",{className:"container-custom",children:S.jsx("div",{className:"max-w-4xl mx-auto",children:S.jsx("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4 sm:p-6",children:(0,S.jsxs)("div",{className:"flex items-start gap-3",children:[S.jsx("div",{className:"flex-shrink-0",children:S.jsx("svg",{className:"w-5 h-5 sm:w-6 sm:h-6 text-red-600 mt-0.5",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,S.jsxs)("div",{children:[S.jsx("h4",{className:"font-bold text-red-800 mb-2 text-sm sm:text-base",children:"zh"===e?"⚠️ 医疗免责声明":"⚠️ Medical Disclaimer"}),S.jsx("p",{className:"text-xs sm:text-sm text-red-700 leading-relaxed",children:"zh"===e?"本文内容仅供教育和信息目的，不能替代专业医疗建议、诊断或治疗。如有任何健康问题或疑虑，请咨询合格的医疗专业人员。在做出任何健康相关决定之前，请务必寻求医生的建议。":"This content is for educational and informational purposes only and should not replace professional medical advice, diagnosis, or treatment. If you have any health concerns or questions, please consult with a qualified healthcare professional. Always seek medical advice before making any health-related decisions."})]})]})})})}),S.jsx("section",{className:"bg-gradient-to-br from-gray-50 to-gray-100 py-8 sm:py-12",children:S.jsx("div",{className:"container-custom",children:(0,S.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,S.jsxs)("div",{className:"text-center mb-6 sm:mb-8",children:[S.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-neutral-800 mb-2",children:"zh"===e?"\uD83D\uDCDA 相关文章推荐":"\uD83D\uDCDA Related Articles"}),S.jsx("p",{className:"text-sm sm:text-base text-neutral-600",children:"zh"===e?"继续探索更多专业健康内容":"Continue exploring more professional health content"})]}),r.length>0?S.jsx("div",{className:"grid gap-4 sm:grid-cols-2 lg:grid-cols-3",children:r.map(t=>{let n="zh"===e&&t.title_zh||t.title,r="zh"===e&&t.summary_zh||t.summary,a="zh"===e&&t.category_zh||t.category;return(0,S.jsxs)(N.default,{href:`/${e}/articles/${t.slug}`,className:"group block bg-white p-4 sm:p-6 rounded-xl shadow-sm hover:shadow-lg transition-all duration-200 border border-gray-100 hover:border-primary-200",children:[S.jsx("div",{className:"flex items-center mb-3",children:S.jsx("span",{className:"bg-primary-100 text-primary-700 px-2 py-1 rounded-full text-xs font-medium",children:a})}),S.jsx("h3",{className:"text-base sm:text-lg font-semibold text-neutral-800 mb-2 group-hover:text-primary-600 transition-colors line-clamp-2",children:n}),S.jsx("p",{className:"text-neutral-600 text-sm line-clamp-3 leading-relaxed mb-3",children:r}),(0,S.jsxs)("div",{className:"flex items-center text-primary-600 text-sm font-medium",children:[S.jsx("span",{children:"zh"===e?"阅读全文":"Read More"}),S.jsx("svg",{className:"w-4 h-4 ml-1 group-hover:translate-x-1 transition-transform",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]},t.slug)})}):(0,S.jsxs)("div",{className:"bg-white p-6 sm:p-8 rounded-xl shadow-sm border border-gray-100 text-center",children:[S.jsx("div",{className:"w-16 h-16 bg-primary-100 rounded-full flex items-center justify-center mx-auto mb-4",children:S.jsx("svg",{className:"w-8 h-8 text-primary-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"})})}),S.jsx("h3",{className:"text-lg font-semibold text-primary-600 mb-2",children:"zh"===e?"更多文章即将发布":"More Articles Coming Soon"}),S.jsx("p",{className:"text-neutral-600 text-sm sm:text-base",children:"zh"===e?"我们正在准备更多高质量的健康内容，敬请期待。":"We are preparing more high-quality health content. Stay tuned."}),(0,S.jsxs)(N.default,{href:`/${e}/articles`,className:"inline-flex items-center mt-4 px-4 py-2 bg-primary-600 text-white rounded-lg hover:bg-primary-700 transition-colors text-sm font-medium",children:["zh"===e?"浏览所有文章":"Browse All Articles",S.jsx("svg",{className:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:S.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]})})})]})]})}},76373:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});let r=(0,n(68570).createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/StructuredData.tsx#default`)},52095:(e,t,n)=>{"use strict";n.d(t,{eA:()=>d,vX:()=>h,zC:()=>u});var r=n(92048),a=n.n(r),i=n(55315),s=n.n(i),o=n(3673),l=n.n(o);let c=s().join(process.cwd(),"content/articles");function u(e="en"){try{let t="zh"===e?s().join(c,"zh"):s().join(c,"en");if(!a().existsSync(t))return[];return a().readdirSync(t).filter(e=>e.endsWith(".md")).map(t=>{let n=t.replace(/\.md$/,"");return h(n,e)}).filter(e=>null!==e).sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())}catch(e){return[]}}function h(e,t="en"){try{let n="zh"===t?s().join(c,"zh"):s().join(c,"en"),r=s().join(n,`${e}.md`);if(!a().existsSync(r))return null;let i=a().readFileSync(r,"utf8"),{data:o,content:u}=l()(i);return{slug:e,title:o.title||"",title_zh:o.title_zh,date:o.date||"",summary:o.summary||"",summary_zh:o.summary_zh,tags:o.tags||[],tags_zh:o.tags_zh,category:o.category||"",category_zh:o.category_zh,author:o.author||"",featured_image:o.featured_image||"",reading_time:o.reading_time||"",reading_time_zh:o.reading_time_zh,content:u,seo_title:o.seo_title,seo_title_zh:o.seo_title_zh,seo_description:o.seo_description,seo_description_zh:o.seo_description_zh,canonical_url:o.canonical_url,schema_type:o.schema_type}}catch(e){return null}}function d(e,t="en",n=3){let r=h(e,t);return r?u(t).filter(t=>t.slug!==e).map(e=>{let n=0;("zh"===t?r.category_zh:r.category)===("zh"===t?e.category_zh:e.category)&&(n+=3);let a="zh"===t?r.tags_zh:r.tags,i="zh"===t?e.tags_zh:e.tags;return{article:e,score:n+=(a?.filter(e=>i?.includes(e))||[]).length}}).sort((e,t)=>t.score-e.score).slice(0,n).map(e=>e.article):[]}},19675:e=>{"use strict";var t=Object.prototype.hasOwnProperty,n=Object.prototype.toString,r=Object.defineProperty,a=Object.getOwnPropertyDescriptor,i=function(e){return"function"==typeof Array.isArray?Array.isArray(e):"[object Array]"===n.call(e)},s=function(e){if(!e||"[object Object]"!==n.call(e))return!1;var r,a=t.call(e,"constructor"),i=e.constructor&&e.constructor.prototype&&t.call(e.constructor.prototype,"isPrototypeOf");if(e.constructor&&!a&&!i)return!1;for(r in e);return void 0===r||t.call(e,r)},o=function(e,t){r&&"__proto__"===t.name?r(e,t.name,{enumerable:!0,configurable:!0,value:t.newValue,writable:!0}):e[t.name]=t.newValue},l=function(e,n){if("__proto__"===n){if(!t.call(e,n))return;if(a)return a(e,n).value}return e[n]};e.exports=function e(){var t,n,r,a,c,u,h=arguments[0],d=1,p=arguments.length,m=!1;for("boolean"==typeof h&&(m=h,h=arguments[1]||{},d=2),(null==h||"object"!=typeof h&&"function"!=typeof h)&&(h={});d<p;++d)if(t=arguments[d],null!=t)for(n in t)r=l(h,n),h!==(a=l(t,n))&&(m&&a&&(s(a)||(c=i(a)))?(c?(c=!1,u=r&&i(r)?r:[]):u=r&&s(r)?r:{},o(h,{name:n,newValue:e(m,u,a)})):void 0!==a&&o(h,{name:n,newValue:a}));return h}},47220:e=>{var t=/\/\*[^*]*\*+([^/*][^*]*\*+)*\//g,n=/\n/g,r=/^\s*/,a=/^(\*?[-#/*\\\w]+(\[[0-9a-z_-]+\])?)\s*/,i=/^:\s*/,s=/^((?:'(?:\\'|.)*?'|"(?:\\"|.)*?"|\([^)]*?\)|[^};])+)/,o=/^[;\s]*/,l=/^\s+|\s+$/g;function c(e){return e?e.replace(l,""):""}e.exports=function(e,l){if("string"!=typeof e)throw TypeError("First argument must be a string");if(!e)return[];l=l||{};var u=1,h=1;function d(e){var t=e.match(n);t&&(u+=t.length);var r=e.lastIndexOf("\n");h=~r?e.length-r:h+e.length}function p(){var e={line:u,column:h};return function(t){return t.position=new m(e),T(r),t}}function m(e){this.start=e,this.end={line:u,column:h},this.source=l.source}m.prototype.content=e;var f=[];function E(t){var n=Error(l.source+":"+u+":"+h+": "+t);if(n.reason=t,n.filename=l.source,n.line=u,n.column=h,n.source=e,l.silent)f.push(n);else throw n}function T(t){var n=t.exec(e);if(n){var r=n[0];return d(r),e=e.slice(r.length),n}}function g(e){var t;for(e=e||[];t=A();)!1!==t&&e.push(t);return e}function A(){var t=p();if("/"==e.charAt(0)&&"*"==e.charAt(1)){for(var n=2;""!=e.charAt(n)&&("*"!=e.charAt(n)||"/"!=e.charAt(n+1));)++n;if(n+=2,""===e.charAt(n-1))return E("End of comment missing");var r=e.slice(2,n-2);return h+=2,d(r),e=e.slice(n),h+=2,t({type:"comment",comment:r})}}return T(r),function(){var e,n=[];for(g(n);e=function(){var e=p(),n=T(a);if(n){if(A(),!T(i))return E("property missing ':'");var r=T(s),l=e({type:"declaration",property:c(n[0].replace(t,"")),value:r?c(r[0].replace(t,"")):""});return T(o),l}}();)!1!==e&&(n.push(e),g(n));return n}()}},94576:function(e,t,n){"use strict";var r=(this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}})(n(25654)),a=n(32301);function i(e,t){var n={};return e&&"string"==typeof e&&(0,r.default)(e,function(e,r){e&&r&&(n[(0,a.camelCase)(e,t)]=r)}),n}i.default=i,e.exports=i},32301:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.camelCase=void 0;var n=/^--[a-zA-Z0-9_-]+$/,r=/-([a-z])/g,a=/^[^-]+$/,i=/^-(webkit|moz|ms|o|khtml)-/,s=/^-(ms)-/,o=function(e,t){return t.toUpperCase()},l=function(e,t){return"".concat(t,"-")};t.camelCase=function(e,t){var c;return(void 0===t&&(t={}),!(c=e)||a.test(c)||n.test(c))?e:(e=e.toLowerCase(),(e=t.reactCompat?e.replace(s,l):e.replace(i,l)).replace(r,o))}},25654:function(e,t,n){"use strict";var r=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.default=function(e,t){var n=null;if(!e||"string"!=typeof e)return n;var r=(0,a.default)(e),i="function"==typeof t;return r.forEach(function(e){if("declaration"===e.type){var r=e.property,a=e.value;i?t(r,a,e):a&&((n=n||{})[r]=a)}}),n};var a=r(n(47220))}};var t=require("../../../../webpack-runtime.js");t.C(e);var n=e=>t(t.s=e),r=t.X(0,[8948,1386,6621,6929,4358,2810,6544],()=>n(83579));module.exports=r})();