(()=>{var e={};e.id=6622,e.ids=[6622],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},35104:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>m,originalPathname:()=>h,pages:()=>o,routeModule:()=>x,tree:()=>c}),t(98207),t(75110),t(21149),t(35866);var l=t(23191),a=t(88716),r=t(37922),i=t.n(r),n=t(95231),d={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>n[e]);t.d(s,d);let c=["",{children:["[locale]",{children:["health-guide",{children:["lifestyle",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,98207)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/lifestyle/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,75110)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,21149)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/lifestyle/page.tsx"],h="/[locale]/health-guide/lifestyle/page",m={require:t,loadChunk:()=>Promise.resolve()},x=new l.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/health-guide/lifestyle/page",pathname:"/[locale]/health-guide/lifestyle",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},83604:(e,s,t)=>{Promise.resolve().then(t.t.bind(t,79404,23))},98207:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>c,generateMetadata:()=>n,generateStaticParams:()=>d});var l=t(19510),a=t(40055),r=t(57371),i=t(69526);async function n({params:{locale:e}}){return{title:"zh"===e?"生活方式管理 - 痛经健康指南":"Lifestyle Management - Health Guide",description:"zh"===e?"通过饮食、运动和日常习惯改善经期健康，建立长期有效的痛经管理策略。":"Improve menstrual health through diet, exercise, and daily habits, establishing long-term effective menstrual pain management strategies."}}async function d(){return i.k.map(e=>({locale:e}))}function c({params:{locale:e}}){return(0,a.t)(e),(0,l.jsxs)("div",{className:"space-y-12",children:[(0,l.jsxs)("nav",{className:"text-sm text-neutral-600",children:[l.jsx(r.default,{href:`/${e}`,className:"hover:text-primary-600",children:"zh"===e?"首页":"Home"}),l.jsx("span",{className:"mx-2",children:"›"}),l.jsx(r.default,{href:`/${e}/health-guide`,className:"hover:text-primary-600",children:"zh"===e?"痛经健康指南":"Health Guide"}),l.jsx("span",{className:"mx-2",children:"›"}),l.jsx("span",{className:"text-neutral-800",children:"zh"===e?"生活方式管理":"Lifestyle Management"})]}),(0,l.jsxs)("header",{className:"text-center",children:[l.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-primary-700 mb-4",children:"zh"===e?"生活方式管理":"Lifestyle Management"}),l.jsx("p",{className:"text-lg text-neutral-600 max-w-3xl mx-auto",children:"zh"===e?"通过科学的饮食、运动和日常习惯改善经期健康，建立长期有效的痛经管理策略。":"Improve menstrual health through scientific diet, exercise, and daily habits, establishing long-term effective menstrual pain management strategies."})]}),(0,l.jsxs)("section",{className:"bg-gradient-to-br from-green-50 to-neutral-50 p-6 md:p-8 rounded-xl",children:[l.jsx("h2",{className:"text-2xl font-semibold text-neutral-800 mb-6",children:"zh"===e?"营养与饮食":"Nutrition and Diet"}),(0,l.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"bg-white p-6 rounded-lg",children:[l.jsx("h3",{className:"text-lg font-semibold text-green-600 mb-4",children:"zh"===e?"推荐食物":"Recommended Foods"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{children:[l.jsx("h4",{className:"font-medium text-neutral-800 mb-1",children:"zh"===e?"抗炎食物":"Anti-inflammatory Foods"}),l.jsx("p",{className:"text-sm text-neutral-600",children:"zh"===e?"深海鱼类、坚果、橄榄油、绿叶蔬菜":"Deep-sea fish, nuts, olive oil, leafy greens"})]}),(0,l.jsxs)("div",{children:[l.jsx("h4",{className:"font-medium text-neutral-800 mb-1",children:"zh"===e?"富含镁的食物":"Magnesium-rich Foods"}),l.jsx("p",{className:"text-sm text-neutral-600",children:"zh"===e?"黑巧克力、香蕉、菠菜、杏仁":"Dark chocolate, bananas, spinach, almonds"})]}),(0,l.jsxs)("div",{children:[l.jsx("h4",{className:"font-medium text-neutral-800 mb-1",children:"zh"===e?"富含铁的食物":"Iron-rich Foods"}),l.jsx("p",{className:"text-sm text-neutral-600",children:"zh"===e?"瘦肉、豆类、深绿色蔬菜":"Lean meat, legumes, dark green vegetables"})]})]})]}),(0,l.jsxs)("div",{className:"bg-white p-6 rounded-lg",children:[l.jsx("h3",{className:"text-lg font-semibold text-red-600 mb-4",children:"zh"===e?"避免食物":"Foods to Avoid"}),(0,l.jsxs)("div",{className:"space-y-3",children:[(0,l.jsxs)("div",{children:[l.jsx("h4",{className:"font-medium text-neutral-800 mb-1",children:"zh"===e?"高糖食物":"High-sugar Foods"}),l.jsx("p",{className:"text-sm text-neutral-600",children:"zh"===e?"糖果、甜饮料、精制糖":"Candy, sweet drinks, refined sugar"})]}),(0,l.jsxs)("div",{children:[l.jsx("h4",{className:"font-medium text-neutral-800 mb-1",children:"zh"===e?"高盐食物":"High-sodium Foods"}),l.jsx("p",{className:"text-sm text-neutral-600",children:"zh"===e?"加工食品、腌制食品":"Processed foods, pickled foods"})]}),(0,l.jsxs)("div",{children:[l.jsx("h4",{className:"font-medium text-neutral-800 mb-1",children:"zh"===e?"咖啡因":"Caffeine"}),l.jsx("p",{className:"text-sm text-neutral-600",children:"zh"===e?"过量咖啡、浓茶":"Excessive coffee, strong tea"})]})]})]})]})]}),(0,l.jsxs)("section",{children:[l.jsx("h2",{className:"text-2xl font-semibold text-neutral-800 mb-6",children:"zh"===e?"运动与锻炼":"Exercise and Physical Activity"}),(0,l.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,l.jsxs)("div",{className:"card",children:[l.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mb-4",children:l.jsx("svg",{className:"w-6 h-6 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M13 10V3L4 14h7v7l9-11h-7z"})})}),l.jsx("h3",{className:"text-lg font-semibold text-neutral-800 mb-3",children:"zh"===e?"有氧运动":"Aerobic Exercise"}),l.jsx("p",{className:"text-neutral-600 mb-4 text-sm",children:"zh"===e?"促进血液循环，释放内啡肽，自然缓解疼痛。":"Promotes blood circulation, releases endorphins, naturally relieves pain."}),(0,l.jsxs)("ul",{className:"text-sm text-neutral-600 space-y-1",children:[(0,l.jsxs)("li",{children:["• ","zh"===e?"快走 20-30分钟":"Brisk walking 20-30 minutes"]}),(0,l.jsxs)("li",{children:["• ","zh"===e?"游泳":"Swimming"]}),(0,l.jsxs)("li",{children:["• ","zh"===e?"骑自行车":"Cycling"]}),(0,l.jsxs)("li",{children:["• ","zh"===e?"舞蹈":"Dancing"]})]})]}),(0,l.jsxs)("div",{className:"card",children:[l.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mb-4",children:l.jsx("svg",{className:"w-6 h-6 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"})})}),l.jsx("h3",{className:"text-lg font-semibold text-neutral-800 mb-3",children:"zh"===e?"瑜伽与伸展":"Yoga and Stretching"}),l.jsx("p",{className:"text-neutral-600 mb-4 text-sm",children:"zh"===e?"放松肌肉，减轻压力，改善柔韧性。":"Relaxes muscles, reduces stress, improves flexibility."}),(0,l.jsxs)("ul",{className:"text-sm text-neutral-600 space-y-1",children:[(0,l.jsxs)("li",{children:["• ","zh"===e?"猫牛式":"Cat-Cow pose"]}),(0,l.jsxs)("li",{children:["• ","zh"===e?"儿童式":"Child's pose"]}),(0,l.jsxs)("li",{children:["• ","zh"===e?"扭转式":"Twisting poses"]}),(0,l.jsxs)("li",{children:["• ","zh"===e?"腿部伸展":"Leg stretches"]})]})]}),(0,l.jsxs)("div",{className:"card",children:[l.jsx("div",{className:"w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mb-4",children:l.jsx("svg",{className:"w-6 h-6 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"})})}),l.jsx("h3",{className:"text-lg font-semibold text-neutral-800 mb-3",children:"zh"===e?"正念练习":"Mindfulness Practice"}),l.jsx("p",{className:"text-neutral-600 mb-4 text-sm",children:"zh"===e?"减轻压力，改善疼痛感知，提高整体健康。":"Reduces stress, improves pain perception, enhances overall well-being."}),(0,l.jsxs)("ul",{className:"text-sm text-neutral-600 space-y-1",children:[(0,l.jsxs)("li",{children:["• ","zh"===e?"冥想 10-15分钟":"Meditation 10-15 minutes"]}),(0,l.jsxs)("li",{children:["• ","zh"===e?"深呼吸练习":"Deep breathing exercises"]}),(0,l.jsxs)("li",{children:["• ","zh"===e?"渐进性肌肉放松":"Progressive muscle relaxation"]}),(0,l.jsxs)("li",{children:["• ","zh"===e?"正念行走":"Mindful walking"]})]})]})]})]}),(0,l.jsxs)("section",{className:"bg-gradient-to-br from-blue-50 to-neutral-50 p-6 md:p-8 rounded-xl",children:[l.jsx("h2",{className:"text-2xl font-semibold text-neutral-800 mb-6",children:"zh"===e?"睡眠与压力管理":"Sleep and Stress Management"}),(0,l.jsxs)("div",{className:"grid md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"bg-white p-6 rounded-lg",children:[l.jsx("h3",{className:"text-lg font-semibold text-blue-600 mb-4",children:"zh"===e?"优质睡眠":"Quality Sleep"}),(0,l.jsxs)("ul",{className:"space-y-2 text-sm text-neutral-600",children:[(0,l.jsxs)("li",{className:"flex items-start",children:[l.jsx("span",{className:"text-blue-500 mr-2",children:"•"}),"zh"===e?"保持规律的睡眠时间（7-9小时）":"Maintain regular sleep schedule (7-9 hours)"]}),(0,l.jsxs)("li",{className:"flex items-start",children:[l.jsx("span",{className:"text-blue-500 mr-2",children:"•"}),"zh"===e?"创造舒适的睡眠环境":"Create comfortable sleep environment"]}),(0,l.jsxs)("li",{className:"flex items-start",children:[l.jsx("span",{className:"text-blue-500 mr-2",children:"•"}),"zh"===e?"睡前避免电子设备":"Avoid electronic devices before bed"]}),(0,l.jsxs)("li",{className:"flex items-start",children:[l.jsx("span",{className:"text-blue-500 mr-2",children:"•"}),"zh"===e?"建立睡前放松仪式":"Establish bedtime relaxation routine"]})]})]}),(0,l.jsxs)("div",{className:"bg-white p-6 rounded-lg",children:[l.jsx("h3",{className:"text-lg font-semibold text-purple-600 mb-4",children:"zh"===e?"压力管理":"Stress Management"}),(0,l.jsxs)("ul",{className:"space-y-2 text-sm text-neutral-600",children:[(0,l.jsxs)("li",{className:"flex items-start",children:[l.jsx("span",{className:"text-purple-500 mr-2",children:"•"}),"zh"===e?"识别和避免压力源":"Identify and avoid stress triggers"]}),(0,l.jsxs)("li",{className:"flex items-start",children:[l.jsx("span",{className:"text-purple-500 mr-2",children:"•"}),"zh"===e?"学习放松技巧":"Learn relaxation techniques"]}),(0,l.jsxs)("li",{className:"flex items-start",children:[l.jsx("span",{className:"text-purple-500 mr-2",children:"•"}),"zh"===e?"保持社交联系":"Maintain social connections"]}),(0,l.jsxs)("li",{className:"flex items-start",children:[l.jsx("span",{className:"text-purple-500 mr-2",children:"•"}),"zh"===e?"寻求专业帮助（如需要）":"Seek professional help (if needed)"]})]})]})]})]}),(0,l.jsxs)("section",{children:[l.jsx("h2",{className:"text-2xl font-semibold text-neutral-800 mb-6",children:"zh"===e?"日常习惯建议":"Daily Habit Recommendations"}),(0,l.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-4",children:[(0,l.jsxs)("div",{className:"card text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3",children:l.jsx("svg",{className:"w-8 h-8 text-yellow-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"})})}),l.jsx("h3",{className:"font-semibold text-neutral-800 mb-2",children:"zh"===e?"晨间例行":"Morning Routine"}),l.jsx("p",{className:"text-sm text-neutral-600",children:"zh"===e?"温水、轻度伸展、营养早餐":"Warm water, light stretching, nutritious breakfast"})]}),(0,l.jsxs)("div",{className:"card text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3",children:l.jsx("svg",{className:"w-8 h-8 text-blue-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z"})})}),l.jsx("h3",{className:"font-semibold text-neutral-800 mb-2",children:"zh"===e?"水分补充":"Hydration"}),l.jsx("p",{className:"text-sm text-neutral-600",children:"zh"===e?"每天8-10杯水，草药茶":"8-10 glasses of water daily, herbal teas"})]}),(0,l.jsxs)("div",{className:"card text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3",children:l.jsx("svg",{className:"w-8 h-8 text-green-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"})})}),l.jsx("h3",{className:"font-semibold text-neutral-800 mb-2",children:"zh"===e?"规律作息":"Regular Schedule"}),l.jsx("p",{className:"text-sm text-neutral-600",children:"zh"===e?"固定用餐、运动、睡眠时间":"Fixed meal, exercise, and sleep times"})]}),(0,l.jsxs)("div",{className:"card text-center",children:[l.jsx("div",{className:"w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3",children:l.jsx("svg",{className:"w-8 h-8 text-purple-600",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12l2 2 4-4M7.835 4.697a3.42 3.42 0 001.946-.806 3.42 3.42 0 014.438 0 3.42 3.42 0 001.946.806 3.42 3.42 0 013.138 3.138 3.42 3.42 0 00.806 1.946 3.42 3.42 0 010 4.438 3.42 3.42 0 00-.806 1.946 3.42 3.42 0 01-3.138 3.138 3.42 3.42 0 00-1.946.806 3.42 3.42 0 01-4.438 0 3.42 3.42 0 00-1.946-.806 3.42 3.42 0 01-3.138-3.138 3.42 3.42 0 00-.806-1.946 3.42 3.42 0 010-4.438 3.42 3.42 0 00.806-1.946 3.42 3.42 0 013.138-3.138z"})})}),l.jsx("h3",{className:"font-semibold text-neutral-800 mb-2",children:"zh"===e?"自我关爱":"Self-care"}),l.jsx("p",{className:"text-sm text-neutral-600",children:"zh"===e?"记录症状、奖励进步":"Track symptoms, reward progress"})]})]})]}),(0,l.jsxs)("section",{className:"bg-accent-50 p-6 md:p-8 rounded-xl",children:[l.jsx("h2",{className:"text-2xl font-semibold text-neutral-800 mb-4",children:"zh"===e?"实施建议":"Implementation Tips"}),(0,l.jsxs)("div",{className:"bg-white p-6 rounded-lg",children:[l.jsx("h3",{className:"font-semibold text-neutral-800 mb-3",children:"zh"===e?"循序渐进的改变":"Gradual Changes"}),l.jsx("p",{className:"text-neutral-700 mb-4",children:"zh"===e?"不要试图一次性改变所有习惯。选择1-2个最容易实施的改变开始，建立信心后再逐步添加其他习惯。":"Don't try to change all habits at once. Start with 1-2 easiest changes to implement, build confidence, then gradually add other habits."}),(0,l.jsxs)("div",{className:"grid md:grid-cols-3 gap-4 text-sm",children:[(0,l.jsxs)("div",{className:"text-center",children:[l.jsx("div",{className:"font-semibold text-primary-600 mb-1",children:"zh"===e?"第1-2周":"Week 1-2"}),l.jsx("p",{className:"text-neutral-600",children:"zh"===e?"建立一个新习惯":"Establish one new habit"})]}),(0,l.jsxs)("div",{className:"text-center",children:[l.jsx("div",{className:"font-semibold text-secondary-600 mb-1",children:"zh"===e?"第3-4周":"Week 3-4"}),l.jsx("p",{className:"text-neutral-600",children:"zh"===e?"巩固并添加第二个":"Consolidate and add second"})]}),(0,l.jsxs)("div",{className:"text-center",children:[l.jsx("div",{className:"font-semibold text-accent-600 mb-1",children:"zh"===e?"第5周+":"Week 5+"}),l.jsx("p",{className:"text-neutral-600",children:"zh"===e?"继续扩展":"Continue expanding"})]})]})]})]}),(0,l.jsxs)("section",{className:"flex justify-between items-center pt-8 border-t border-neutral-200",children:[(0,l.jsxs)(r.default,{href:`/${e}/health-guide/relief-methods`,className:"text-primary-600 hover:text-primary-700 font-medium inline-flex items-center",children:[l.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"zh"===e?"上一章：A-Z缓解方法":"Previous: A-Z Relief Methods"]}),(0,l.jsxs)(r.default,{href:`/${e}/health-guide/medical-care`,className:"text-primary-600 hover:text-primary-700 font-medium inline-flex items-center",children:["zh"===e?"下一章：何时寻求帮助":"Next: When to Seek Help",l.jsx("svg",{className:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:l.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]})}},57371:(e,s,t)=>{"use strict";t.d(s,{default:()=>a.a});var l=t(670),a=t.n(l)},670:(e,s,t)=>{"use strict";let{createProxy:l}=t(68570);e.exports=l("/Users/<USER>/Downloads/periodhub-health_副本01版/node_modules/next/dist/client/link.js")}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),l=s.X(0,[8948,1386,6621,6929,2810,6544],()=>t(35104));module.exports=l})();