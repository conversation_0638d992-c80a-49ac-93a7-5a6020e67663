(()=>{var e={};e.id=4399,e.ids=[4399],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},71891:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>r.a,__next_app__:()=>c,originalPathname:()=>m,pages:()=>h,routeModule:()=>p,tree:()=>d}),i(47136),i(75110),i(21149),i(35866);var a=i(23191),s=i(88716),n=i(37922),r=i.n(n),l=i(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);i.d(t,o);let d=["",{children:["[locale]",{children:["health-guide",{children:["relief-methods",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,47136)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/relief-methods/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,75110)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,21149)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],h=["/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/health-guide/relief-methods/page.tsx"],m="/[locale]/health-guide/relief-methods/page",c={require:i,loadChunk:()=>Promise.resolve()},p=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/[locale]/health-guide/relief-methods/page",pathname:"/[locale]/health-guide/relief-methods",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},83604:(e,t,i)=>{Promise.resolve().then(i.t.bind(i,79404,23))},47136:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>o,generateMetadata:()=>r,generateStaticParams:()=>l});var a=i(19510),s=i(57371),n=i(69526);async function r({params:{locale:e}}){return{title:"zh"===e?"A-Z缓解方法 - 痛经健康指南":"A-Z Relief Methods - Health Guide",description:"zh"===e?"从A到Z的全面缓解方法，包括即时和长期策略，帮助您找到最适合的痛经管理方案。":"Comprehensive relief methods from A to Z, including immediate and long-term strategies to help you find the most suitable menstrual pain management plan."}}async function l(){return n.k.map(e=>({locale:e}))}function o({params:{locale:e}}){return(0,a.jsxs)("div",{className:"space-y-12",children:[(0,a.jsxs)("nav",{className:"text-sm text-neutral-600",children:[a.jsx(s.default,{href:`/${e}`,className:"hover:text-primary-600",children:"zh"===e?"首页":"Home"}),a.jsx("span",{className:"mx-2",children:"›"}),a.jsx(s.default,{href:`/${e}/health-guide`,className:"hover:text-primary-600",children:"zh"===e?"痛经健康指南":"Health Guide"}),a.jsx("span",{className:"mx-2",children:"›"}),a.jsx("span",{className:"text-neutral-800",children:"zh"===e?"A-Z缓解方法":"A-Z Relief Methods"})]}),(0,a.jsxs)("header",{className:"text-center",children:[a.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-primary-700 mb-4",children:"zh"===e?"A-Z缓解方法":"A-Z Relief Methods"}),a.jsx("p",{className:"text-lg text-neutral-600 max-w-3xl mx-auto",children:"zh"===e?"从A到Z的全面缓解方法，包括即时和长期策略，帮助您找到最适合的痛经管理方案。":"Comprehensive relief methods from A to Z, including immediate and long-term strategies to help you find the most suitable menstrual pain management plan."})]}),(0,a.jsxs)("section",{className:"bg-gradient-to-br from-primary-50 to-neutral-50 p-6 md:p-8 rounded-xl",children:[a.jsx("h2",{className:"text-2xl font-semibold text-neutral-800 mb-4",children:"zh"===e?"如何使用这个指南":"How to Use This Guide"}),a.jsx("p",{className:"text-neutral-700 leading-relaxed mb-4",children:"zh"===e?"这个A-Z指南包含了各种经过验证的痛经缓解方法。每个人的身体反应不同，建议您尝试多种方法，找到最适合自己的组合。记住，持续性和耐心是成功的关键。":"This A-Z guide contains various proven menstrual pain relief methods. Since everyone's body responds differently, we recommend trying multiple approaches to find the combination that works best for you. Remember, consistency and patience are key to success."}),(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg",children:[a.jsx("h3",{className:"font-semibold text-neutral-800 mb-2",children:"zh"===e?"使用建议：":"Usage Tips:"}),(0,a.jsxs)("ul",{className:"list-disc list-inside text-neutral-600 space-y-1 text-sm",children:[a.jsx("li",{children:"zh"===e?"从简单易行的方法开始":"Start with simple and easy methods"}),a.jsx("li",{children:"zh"===e?"记录哪些方法对您有效":"Record which methods work for you"}),a.jsx("li",{children:"zh"===e?"结合多种方法以获得最佳效果":"Combine multiple methods for best results"}),a.jsx("li",{children:"zh"===e?"如有疑问，请咨询医疗专业人士":"Consult healthcare professionals if in doubt"})]})]})]}),(0,a.jsxs)("section",{children:[a.jsx("h2",{className:"text-2xl font-semibold text-neutral-800 mb-8 text-center",children:"zh"===e?"缓解方法大全":"Complete Relief Methods"}),a.jsx("div",{className:"space-y-8",children:[{letter:"A",methods:[{name:"zh"===e?"穴位按摩 (Acupressure)":"Acupressure",description:"zh"===e?"通过按压特定穴位来缓解疼痛":"Relieve pain by pressing specific acupoints"},{name:"zh"===e?"芳香疗法 (Aromatherapy)":"Aromatherapy",description:"zh"===e?"使用精油进行放松和疼痛缓解":"Use essential oils for relaxation and pain relief"}]},{letter:"B",methods:[{name:"zh"===e?"呼吸练习 (Breathing Exercises)":"Breathing Exercises",description:"zh"===e?"深呼吸技巧帮助放松和减轻疼痛":"Deep breathing techniques help relax and reduce pain"},{name:"zh"===e?"八段锦 (Baduanjin)":"Baduanjin",description:"zh"===e?"传统中医气功练习":"Traditional Chinese qigong practice"}]},{letter:"C",methods:[{name:"zh"===e?"冷敷 (Cold Therapy)":"Cold Therapy",description:"zh"===e?"适用于炎症性疼痛":"Suitable for inflammatory pain"},{name:"zh"===e?"认知行为疗法 (CBT)":"Cognitive Behavioral Therapy",description:"zh"===e?"改变对疼痛的认知和反应":"Change cognition and response to pain"}]},{letter:"D",methods:[{name:"zh"===e?"饮食调整 (Diet Modification)":"Diet Modification",description:"zh"===e?"抗炎饮食和营养补充":"Anti-inflammatory diet and nutritional supplements"},{name:"zh"===e?"舞蹈疗法 (Dance Therapy)":"Dance Therapy",description:"zh"===e?"通过舞蹈运动缓解疼痛":"Relieve pain through dance movement"}]},{letter:"E",methods:[{name:"zh"===e?"运动 (Exercise)":"Exercise",description:"zh"===e?"适度运动促进血液循环":"Moderate exercise promotes blood circulation"},{name:"zh"===e?"精油按摩 (Essential Oil Massage)":"Essential Oil Massage",description:"zh"===e?"结合按摩和芳香疗法":"Combine massage and aromatherapy"}]},{letter:"F",methods:[{name:"zh"===e?"足部反射疗法 (Foot Reflexology)":"Foot Reflexology",description:"zh"===e?"通过足部按摩缓解全身疼痛":"Relieve whole body pain through foot massage"},{name:"zh"===e?"纤维补充 (Fiber Supplements)":"Fiber Supplements",description:"zh"===e?"改善肠道健康，减少炎症":"Improve gut health and reduce inflammation"}]},{letter:"G",methods:[{name:"zh"===e?"生姜疗法 (Ginger Therapy)":"Ginger Therapy",description:"zh"===e?"天然抗炎和止痛效果":"Natural anti-inflammatory and pain relief effects"},{name:"zh"===e?"引导冥想 (Guided Meditation)":"Guided Meditation",description:"zh"===e?"通过冥想减轻疼痛感知":"Reduce pain perception through meditation"}]},{letter:"H",methods:[{name:"zh"===e?"热疗 (Heat Therapy)":"Heat Therapy",description:"zh"===e?"使用热敷缓解肌肉紧张":"Use heat to relieve muscle tension"},{name:"zh"===e?"草药茶 (Herbal Tea)":"Herbal Tea",description:"zh"===e?"洋甘菊、薄荷等舒缓茶饮":"Soothing teas like chamomile and mint"}]},{letter:"I",methods:[{name:"zh"===e?"冰敷疗法 (Ice Therapy)":"Ice Therapy",description:"zh"===e?"减少炎症和麻痹疼痛":"Reduce inflammation and numb pain"},{name:"zh"===e?"意象疗法 (Imagery Therapy)":"Imagery Therapy",description:"zh"===e?"通过想象缓解疼痛":"Relieve pain through visualization"}]},{letter:"J",methods:[{name:"zh"===e?"日记记录 (Journaling)":"Journaling",description:"zh"===e?"记录疼痛模式和触发因素":"Track pain patterns and triggers"},{name:"zh"===e?"慢跑 (Jogging)":"Jogging",description:"zh"===e?"轻度有氧运动促进血液循环":"Light aerobic exercise promotes circulation"}]},{letter:"K",methods:[{name:"zh"===e?"膝胸位 (Knee-to-Chest Position)":"Knee-to-Chest Position",description:"zh"===e?"缓解下腹部疼痛的体位":"Position to relieve lower abdominal pain"},{name:"zh"===e?"昆达里尼瑜伽 (Kundalini Yoga)":"Kundalini Yoga",description:"zh"===e?"特殊的瑜伽练习形式":"Special form of yoga practice"}]},{letter:"L",methods:[{name:"zh"===e?"薰衣草精油 (Lavender Oil)":"Lavender Oil",description:"zh"===e?"放松和镇静效果":"Relaxing and calming effects"},{name:"zh"===e?"生活方式调整 (Lifestyle Changes)":"Lifestyle Changes",description:"zh"===e?"改善整体健康状况":"Improve overall health condition"}]},{letter:"M",methods:[{name:"zh"===e?"按摩疗法 (Massage Therapy)":"Massage Therapy",description:"zh"===e?"专业按摩缓解肌肉紧张":"Professional massage to relieve muscle tension"},{name:"zh"===e?"正念冥想 (Mindfulness Meditation)":"Mindfulness Meditation",description:"zh"===e?"专注当下，减轻疼痛感知":"Focus on present moment, reduce pain perception"}]},{letter:"N",methods:[{name:"zh"===e?"营养补充 (Nutritional Supplements)":"Nutritional Supplements",description:"zh"===e?"镁、维生素B等营养素":"Nutrients like magnesium and vitamin B"},{name:"zh"===e?"自然疗法 (Natural Remedies)":"Natural Remedies",description:"zh"===e?"草药和天然治疗方法":"Herbal and natural treatment methods"}]},{letter:"O",methods:[{name:"zh"===e?"Omega-3脂肪酸 (Omega-3 Fatty Acids)":"Omega-3 Fatty Acids",description:"zh"===e?"抗炎和疼痛缓解效果":"Anti-inflammatory and pain relief effects"},{name:"zh"===e?"有机食品 (Organic Foods)":"Organic Foods",description:"zh"===e?"减少化学物质摄入":"Reduce chemical intake"}]},{letter:"P",methods:[{name:"zh"===e?"渐进性肌肉放松 (Progressive Muscle Relaxation)":"Progressive Muscle Relaxation",description:"zh"===e?"系统性放松肌肉群":"Systematically relax muscle groups"},{name:"zh"===e?"普拉提 (Pilates)":"Pilates",description:"zh"===e?"核心力量和柔韧性训练":"Core strength and flexibility training"}]},{letter:"Q",methods:[{name:"zh"===e?"气功 (Qigong)":"Qigong",description:"zh"===e?"中医传统运动疗法":"Traditional Chinese movement therapy"},{name:"zh"===e?"安静休息 (Quiet Rest)":"Quiet Rest",description:"zh"===e?"在安静环境中休息恢复":"Rest and recover in quiet environment"}]},{letter:"R",methods:[{name:"zh"===e?"放松技巧 (Relaxation Techniques)":"Relaxation Techniques",description:"zh"===e?"各种放松身心的方法":"Various methods to relax body and mind"},{name:"zh"===e?"反射疗法 (Reflexology)":"Reflexology",description:"zh"===e?"通过反射点缓解疼痛":"Relieve pain through reflex points"}]},{letter:"S",methods:[{name:"zh"===e?"拉伸运动 (Stretching)":"Stretching",description:"zh"===e?"温和的拉伸缓解肌肉紧张":"Gentle stretching to relieve muscle tension"},{name:"zh"===e?"睡眠优化 (Sleep Optimization)":"Sleep Optimization",description:"zh"===e?"改善睡眠质量促进恢复":"Improve sleep quality to promote recovery"}]},{letter:"T",methods:[{name:"zh"===e?"太极 (Tai Chi)":"Tai Chi",description:"zh"===e?"缓慢流畅的运动练习":"Slow and flowing movement practice"},{name:"zh"===e?"茶疗 (Tea Therapy)":"Tea Therapy",description:"zh"===e?"药用茶饮缓解症状":"Medicinal teas to relieve symptoms"}]},{letter:"U",methods:[{name:"zh"===e?"超声波疗法 (Ultrasound Therapy)":"Ultrasound Therapy",description:"zh"===e?"深层组织加热治疗":"Deep tissue heating treatment"},{name:"zh"===e?"理解教育 (Understanding Education)":"Understanding Education",description:"zh"===e?"了解痛经机制减少焦虑":"Understanding pain mechanisms reduces anxiety"}]},{letter:"V",methods:[{name:"zh"===e?"可视化技巧 (Visualization)":"Visualization",description:"zh"===e?"心理意象缓解疼痛":"Mental imagery for pain relief"},{name:"zh"===e?"维生素疗法 (Vitamin Therapy)":"Vitamin Therapy",description:"zh"===e?"补充必需维生素":"Supplement essential vitamins"}]},{letter:"W",methods:[{name:"zh"===e?"温水浴 (Warm Bath)":"Warm Bath",description:"zh"===e?"温水浸泡放松肌肉":"Warm water soaking to relax muscles"},{name:"zh"===e?"步行 (Walking)":"Walking",description:"zh"===e?"轻度运动促进血液循环":"Light exercise to promote circulation"}]},{letter:"X",methods:[{name:"zh"===e?"X光检查 (X-ray Examination)":"X-ray Examination",description:"zh"===e?"排除器质性病变":"Rule out organic lesions"},{name:"zh"===e?"木糖醇 (Xylitol)":"Xylitol",description:"zh"===e?"天然甜味剂，减少炎症":"Natural sweetener, reduce inflammation"}]},{letter:"Y",methods:[{name:"zh"===e?"瑜伽 (Yoga)":"Yoga",description:"zh"===e?"身心结合的练习方法":"Mind-body integrated practice"},{name:"zh"===e?"阴瑜伽 (Yin Yoga)":"Yin Yoga",description:"zh"===e?"深度放松的瑜伽形式":"Deep relaxation form of yoga"}]},{letter:"Z",methods:[{name:"zh"===e?"锌补充 (Zinc Supplements)":"Zinc Supplements",description:"zh"===e?"支持免疫系统和愈合":"Support immune system and healing"},{name:"zh"===e?"禅修 (Zen Meditation)":"Zen Meditation",description:"zh"===e?"深度冥想练习":"Deep meditation practice"}]}].map(t=>(0,a.jsxs)("div",{className:"card",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[a.jsx("div",{className:"w-12 h-12 bg-primary-600 text-white rounded-full flex items-center justify-center text-xl font-bold mr-4",children:t.letter}),a.jsx("h3",{className:"text-xl font-semibold text-neutral-800",children:"zh"===e?`${t.letter} 开头的方法`:`Methods Starting with ${t.letter}`})]}),a.jsx("div",{className:"grid md:grid-cols-2 gap-4",children:t.methods.map((e,t)=>(0,a.jsxs)("div",{className:"bg-neutral-50 p-4 rounded-lg",children:[a.jsx("h4",{className:"font-semibold text-primary-600 mb-2",children:e.name}),a.jsx("p",{className:"text-neutral-600 text-sm",children:e.description})]},t))})]},t.letter))})]}),(0,a.jsxs)("section",{className:"bg-secondary-50 p-6 md:p-8 rounded-xl",children:[a.jsx("h2",{className:"text-2xl font-semibold text-neutral-800 mb-6",children:"zh"===e?"快速参考":"Quick Reference"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg",children:[a.jsx("h3",{className:"font-semibold text-green-600 mb-2",children:"zh"===e?"即时缓解":"Immediate Relief"}),(0,a.jsxs)("ul",{className:"text-sm text-neutral-600 space-y-1",children:[(0,a.jsxs)("li",{children:["• ","zh"===e?"热敷":"Heat therapy"]}),(0,a.jsxs)("li",{children:["• ","zh"===e?"深呼吸":"Deep breathing"]}),(0,a.jsxs)("li",{children:["• ","zh"===e?"穴位按摩":"Acupressure"]}),(0,a.jsxs)("li",{children:["• ","zh"===e?"温水浴":"Warm bath"]})]})]}),(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg",children:[a.jsx("h3",{className:"font-semibold text-blue-600 mb-2",children:"zh"===e?"中期管理":"Medium-term Management"}),(0,a.jsxs)("ul",{className:"text-sm text-neutral-600 space-y-1",children:[(0,a.jsxs)("li",{children:["• ","zh"===e?"规律运动":"Regular exercise"]}),(0,a.jsxs)("li",{children:["• ","zh"===e?"瑜伽练习":"Yoga practice"]}),(0,a.jsxs)("li",{children:["• ","zh"===e?"饮食调整":"Diet modification"]}),(0,a.jsxs)("li",{children:["• ","zh"===e?"压力管理":"Stress management"]})]})]}),(0,a.jsxs)("div",{className:"bg-white p-4 rounded-lg",children:[a.jsx("h3",{className:"font-semibold text-purple-600 mb-2",children:"zh"===e?"长期预防":"Long-term Prevention"}),(0,a.jsxs)("ul",{className:"text-sm text-neutral-600 space-y-1",children:[(0,a.jsxs)("li",{children:["• ","zh"===e?"生活方式改变":"Lifestyle changes"]}),(0,a.jsxs)("li",{children:["• ","zh"===e?"营养补充":"Nutritional supplements"]}),(0,a.jsxs)("li",{children:["• ","zh"===e?"中医调理":"TCM conditioning"]}),(0,a.jsxs)("li",{children:["• ","zh"===e?"定期检查":"Regular check-ups"]})]})]})]})]}),(0,a.jsxs)("section",{className:"bg-accent-50 p-6 md:p-8 rounded-xl",children:[a.jsx("h2",{className:"text-2xl font-semibold text-neutral-800 mb-4",children:"zh"===e?"相关资源":"Related Resources"}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4",children:[(0,a.jsxs)(s.default,{href:`/${e}/scenario-solutions`,className:"bg-white p-4 rounded-lg hover:shadow-md transition-shadow",children:[a.jsx("h3",{className:"font-semibold text-primary-600 mb-2",children:"zh"===e?"情景解决方案":"Scenario Solutions"}),a.jsx("p",{className:"text-neutral-600 text-sm",children:"zh"===e?"针对特定情况的专业解决方案":"Professional solutions for specific situations"})]}),(0,a.jsxs)(s.default,{href:`/${e}/interactive-tools`,className:"bg-white p-4 rounded-lg hover:shadow-md transition-shadow",children:[a.jsx("h3",{className:"font-semibold text-primary-600 mb-2",children:"zh"===e?"互动工具":"Interactive Tools"}),a.jsx("p",{className:"text-neutral-600 text-sm",children:"zh"===e?"个性化评估和追踪工具":"Personalized assessment and tracking tools"})]})]})]}),(0,a.jsxs)("section",{className:"flex justify-between items-center pt-8 border-t border-neutral-200",children:[(0,a.jsxs)(s.default,{href:`/${e}/health-guide/understanding-pain`,className:"text-primary-600 hover:text-primary-700 font-medium inline-flex items-center",children:[a.jsx("svg",{className:"w-4 h-4 mr-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),"zh"===e?"上一章：理解痛经":"Previous: Understanding Pain"]}),(0,a.jsxs)(s.default,{href:`/${e}/health-guide/lifestyle`,className:"text-primary-600 hover:text-primary-700 font-medium inline-flex items-center",children:["zh"===e?"下一章：生活方式管理":"Next: Lifestyle Management",a.jsx("svg",{className:"w-4 h-4 ml-2",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]})]})}},57371:(e,t,i)=>{"use strict";i.d(t,{default:()=>s.a});var a=i(670),s=i.n(a)},670:(e,t,i)=>{"use strict";let{createProxy:a}=i(68570);e.exports=a("/Users/<USER>/Downloads/periodhub-health_副本01版/node_modules/next/dist/client/link.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),a=t.X(0,[8948,1386,6621,6929,2810,6544],()=>i(71891));module.exports=a})();