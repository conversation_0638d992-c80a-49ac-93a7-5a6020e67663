(()=>{var e={};e.id=157,e.ids=[157],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},15129:(e,t,i)=>{"use strict";i.r(t),i.d(t,{GlobalError:()=>o.a,__next_app__:()=>u,originalPathname:()=>m,pages:()=>d,routeModule:()=>p,tree:()=>c}),i(73077),i(75110),i(21149),i(35866);var s=i(23191),a=i(88716),n=i(37922),o=i.n(n),r=i(95231),l={};for(let e in r)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>r[e]);i.d(t,l);let c=["",{children:["[locale]",{children:["interactive-tools",{children:["[tool]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(i.bind(i,73077)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/[tool]/page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(i.bind(i,75110)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(i.bind(i,21149)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(i.t.bind(i,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(i.bind(i,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/[tool]/page.tsx"],m="/[locale]/interactive-tools/[tool]/page",u={require:i,loadChunk:()=>Promise.resolve()},p=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/[locale]/interactive-tools/[tool]/page",pathname:"/[locale]/interactive-tools/[tool]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},28637:(e,t,i)=>{Promise.resolve().then(i.bind(i,99381)),Promise.resolve().then(i.bind(i,5308)),Promise.resolve().then(i.bind(i,89124)),Promise.resolve().then(i.bind(i,86520)),Promise.resolve().then(i.t.bind(i,79404,23)),Promise.resolve().then(i.bind(i,933)),Promise.resolve().then(i.bind(i,46618))},99381:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>L});var s=i(10326),a=i(17577),n=i(98415),o=i(76557);let r=(0,o.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]);var l=i(48998),c=i(67427);let d=(0,o.Z)("Leaf",[["path",{d:"M11 20A7 7 0 0 1 9.8 6.1C15.5 5 17 4.48 19 2c1 2 2 4.18 2 8 0 5.5-4.78 10-10 10Z",key:"nnexq3"}],["path",{d:"M2 21c0-3 1.85-5.36 5.08-6C9.5 14.52 12 13 13 12",key:"mt58a7"}]]);var m=i(66697),u=i(87888),p=i(94893),h=i(54659),g=i(77636);let y=(0,o.Z)("Utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"1ogz0v"}]]),x=(0,o.Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]),b=(0,o.Z)("Package",[["path",{d:"m7.5 4.27 9 5.15",key:"1c824w"}],["path",{d:"M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z",key:"hh9hay"}],["path",{d:"m3.3 7 8.7 5 8.7-5",key:"g66t2b"}],["path",{d:"M12 22V12",key:"d0xqtd"}]]),f=(0,o.Z)("BookOpen",[["path",{d:"M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z",key:"vv98re"}],["path",{d:"M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z",key:"1cyq3y"}]]);var v=i(36283),w=i(24230);let j=(0,o.Z)("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),N=(0,o.Z)("Users",[["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}],["path",{d:"M22 21v-2a4 4 0 0 0-3-3.87",key:"kshegd"}],["path",{d:"M16 3.13a4 4 0 0 1 0 7.75",key:"1da9ce"}]]);var D=i(43810),k=i(86333);let C={zh:[{id:"energy_level",type:"single",category:"basic",weight:2,title:"您平时的精力状态如何？",description:"选择最符合您日常状态的选项",validation:{required:!0},options:[{value:"energetic",label:"精力充沛，很少感到疲劳",weight:3,constitutionType:"balanced"},{value:"moderate",label:"精力一般，偶尔感到疲劳",weight:2,constitutionType:"qi_deficiency"},{value:"tired",label:"经常感到疲劳，精力不足",weight:3,constitutionType:"qi_deficiency"},{value:"exhausted",label:"总是感到疲惫不堪",weight:4,constitutionType:"yang_deficiency"}]},{id:"cold_tolerance",type:"single",category:"temperature",weight:2,title:"您对寒冷的耐受性如何？",description:"比较您与同龄人的耐寒能力",validation:{required:!0},options:[{value:"very_tolerant",label:"很耐寒，手脚总是温暖",weight:3,constitutionType:"balanced"},{value:"normal",label:"一般，与大多数人差不多",weight:2,constitutionType:"balanced"},{value:"sensitive",label:"比较怕冷，手脚容易凉",weight:3,constitutionType:"yang_deficiency"},{value:"very_sensitive",label:"非常怕冷，即使夏天也手脚冰凉",weight:4,constitutionType:"yang_deficiency"}]},{id:"digestive_health",type:"single",category:"digestion",weight:2,title:"您的消化功能如何？",description:"选择最符合您消化状况的描述",validation:{required:!0},options:[{value:"excellent",label:"消化很好，食欲正常，很少腹胀",weight:3,constitutionType:"balanced"},{value:"good",label:"消化一般，偶尔腹胀或消化不良",weight:2,constitutionType:"qi_deficiency"},{value:"poor",label:"经常腹胀、消化不良，食欲不振",weight:3,constitutionType:"phlegm_dampness"},{value:"very_poor",label:"消化很差，经常腹泻或便秘",weight:4,constitutionType:"phlegm_dampness"}]},{id:"sleep_quality",type:"single",category:"sleep",weight:2,title:"您的睡眠质量如何？",description:"评估您的整体睡眠状况",validation:{required:!0},options:[{value:"excellent",label:"睡眠很好，容易入睡，睡得深沉",weight:3,constitutionType:"balanced"},{value:"light_sleep",label:"睡眠较浅，容易醒，多梦",weight:3,constitutionType:"yin_deficiency"},{value:"insomnia",label:"经常失眠，难以入睡",weight:4,constitutionType:"qi_stagnation"},{value:"drowsy",label:"总是感到困倦，睡不够",weight:3,constitutionType:"phlegm_dampness"}]},{id:"emotional_state",type:"single",category:"emotion",weight:2,title:"您的情绪状态通常如何？",description:"选择最符合您情绪特点的描述",validation:{required:!0},options:[{value:"stable",label:"情绪稳定，心情愉快",weight:3,constitutionType:"balanced"},{value:"anxious",label:"容易焦虑，心情烦躁",weight:3,constitutionType:"qi_stagnation"},{value:"depressed",label:"经常感到抑郁，情绪低落",weight:4,constitutionType:"qi_stagnation"},{value:"irritable",label:"容易发脾气，情绪波动大",weight:3,constitutionType:"damp_heat"}]},{id:"menstrual_pattern",type:"single",category:"menstruation",weight:3,title:"您的月经特点是？",description:"选择最符合您月经情况的描述",validation:{required:!0},options:[{value:"regular_normal",label:"周期规律，量适中，颜色正常",weight:3,constitutionType:"balanced"},{value:"light_delayed",label:"量少，周期延后，颜色淡",weight:3,constitutionType:"qi_deficiency"},{value:"heavy_early",label:"量多，周期提前，颜色深红",weight:3,constitutionType:"damp_heat"},{value:"clots_dark",label:"有血块，颜色暗红或紫黑",weight:4,constitutionType:"blood_stasis"},{value:"irregular",label:"周期不规律，时多时少",weight:3,constitutionType:"qi_stagnation"}]},{id:"body_type",type:"single",category:"physical",weight:2,title:"您的体型特点是？",description:"选择最符合您体型的描述",validation:{required:!0},options:[{value:"normal",label:"体型匀称，不胖不瘦",weight:3,constitutionType:"balanced"},{value:"thin",label:"偏瘦，不容易长胖",weight:3,constitutionType:"yin_deficiency"},{value:"overweight",label:"偏胖，容易水肿",weight:3,constitutionType:"phlegm_dampness"},{value:"muscular",label:"体格健壮，肌肉结实",weight:2,constitutionType:"balanced"}]},{id:"skin_condition",type:"single",category:"appearance",weight:1,title:"您的皮肤状态如何？",description:"选择最符合您皮肤特点的描述",validation:{required:!0},options:[{value:"healthy",label:"皮肤润泽，有光泽，很少长痘",weight:3,constitutionType:"balanced"},{value:"dry",label:"皮肤干燥，缺乏光泽",weight:3,constitutionType:"yin_deficiency"},{value:"oily_acne",label:"皮肤油腻，容易长痘",weight:3,constitutionType:"damp_heat"},{value:"dull",label:"皮肤暗沉，色斑较多",weight:3,constitutionType:"blood_stasis"}]},{id:"menstrual_pain_severity",type:"scale",category:"menstrual",weight:3,title:"您经期疼痛的程度如何？",description:"请在滑块上选择您的疼痛程度（0=无痛，10=剧烈疼痛）",validation:{required:!0,min:0,max:10},options:Array.from({length:11},(e,t)=>({value:t,label:0===t?"无痛":t<=3?"轻微":t<=6?"中等":t<=8?"严重":"剧烈",weight:t<=2?3:t<=4?2:t<=7?3:4,constitutionType:t<=2?"balanced":t<=4?"qi_deficiency":t<=7?"blood_stasis":"qi_stagnation"}))},{id:"pain_nature",type:"single",category:"menstrual",weight:2,title:"您的经期疼痛性质主要是？",description:"选择最符合您疼痛感受的描述",validation:{required:!0},options:[{value:"cramping",label:"绞痛，一阵一阵的收缩感",weight:3,constitutionType:"qi_stagnation"},{value:"dull_ache",label:"钝痛，持续的隐隐作痛",weight:3,constitutionType:"qi_deficiency"},{value:"sharp_pain",label:"刺痛，像针扎一样",weight:4,constitutionType:"blood_stasis"},{value:"cold_pain",label:"冷痛，遇冷加重，喜温喜按",weight:3,constitutionType:"yang_deficiency"}]},{id:"menstrual_symptoms",type:"multiple",category:"menstrual",weight:2,title:"您在经期还有哪些伴随症状？",description:"可以选择多个症状",validation:{required:!1},options:[{value:"bloating",label:"腹胀",weight:2,constitutionType:"phlegm_dampness"},{value:"nausea",label:"恶心呕吐",weight:3,constitutionType:"damp_heat"},{value:"headache",label:"头痛",weight:2,constitutionType:"qi_stagnation"},{value:"mood_swings",label:"情绪波动大",weight:3,constitutionType:"qi_stagnation"},{value:"fatigue",label:"极度疲劳",weight:2,constitutionType:"qi_deficiency"},{value:"back_pain",label:"腰痛",weight:2,constitutionType:"yang_deficiency"},{value:"breast_tenderness",label:"乳房胀痛",weight:2,constitutionType:"qi_stagnation"},{value:"none",label:"以上都没有",weight:1,constitutionType:"balanced"}]}],en:[{id:"energy_level",type:"single",category:"basic",weight:2,title:"How is your usual energy level?",description:"Choose the option that best describes your daily state",validation:{required:!0},options:[{value:"energetic",label:"Energetic, rarely feel tired",weight:3,constitutionType:"balanced"},{value:"moderate",label:"Moderate energy, occasionally feel tired",weight:2,constitutionType:"qi_deficiency"},{value:"tired",label:"Often feel tired, lack of energy",weight:3,constitutionType:"qi_deficiency"},{value:"exhausted",label:"Always feel exhausted",weight:4,constitutionType:"yang_deficiency"}]},{id:"cold_tolerance",type:"single",category:"temperature",weight:2,title:"How is your tolerance to cold?",description:"Compare your cold tolerance with people of your age",validation:{required:!0},options:[{value:"very_tolerant",label:"Very cold-tolerant, hands and feet always warm",weight:3,constitutionType:"balanced"},{value:"normal",label:"Normal, similar to most people",weight:2,constitutionType:"balanced"},{value:"sensitive",label:"Quite sensitive to cold, hands and feet easily get cold",weight:3,constitutionType:"yang_deficiency"},{value:"very_sensitive",label:"Very sensitive to cold, hands and feet cold even in summer",weight:4,constitutionType:"yang_deficiency"}]},{id:"digestive_health",type:"single",category:"digestion",weight:2,title:"How is your digestive function?",description:"Choose the description that best fits your digestive condition",validation:{required:!0},options:[{value:"excellent",label:"Excellent digestion, normal appetite, rarely bloated",weight:3,constitutionType:"balanced"},{value:"good",label:"Fair digestion, occasional bloating or indigestion",weight:2,constitutionType:"qi_deficiency"},{value:"poor",label:"Often bloated, indigestion, poor appetite",weight:3,constitutionType:"phlegm_dampness"},{value:"very_poor",label:"Very poor digestion, often diarrhea or constipation",weight:4,constitutionType:"phlegm_dampness"}]},{id:"sleep_quality",type:"single",category:"sleep",weight:2,title:"How is your sleep quality?",description:"Assess your overall sleep condition",validation:{required:!0},options:[{value:"excellent",label:"Excellent sleep, fall asleep easily, sleep deeply",weight:3,constitutionType:"balanced"},{value:"light_sleep",label:"Light sleep, wake up easily, many dreams",weight:3,constitutionType:"yin_deficiency"},{value:"insomnia",label:"Often insomnia, difficult to fall asleep",weight:4,constitutionType:"qi_stagnation"},{value:"drowsy",label:"Always feel drowsy, never get enough sleep",weight:3,constitutionType:"phlegm_dampness"}]},{id:"emotional_state",type:"single",category:"emotion",weight:2,title:"How is your emotional state usually?",description:"Choose the description that best fits your emotional characteristics",validation:{required:!0},options:[{value:"stable",label:"Emotionally stable, happy mood",weight:3,constitutionType:"balanced"},{value:"anxious",label:"Easily anxious, irritable mood",weight:3,constitutionType:"qi_stagnation"},{value:"depressed",label:"Often feel depressed, low mood",weight:4,constitutionType:"qi_stagnation"},{value:"irritable",label:"Easily lose temper, large mood swings",weight:3,constitutionType:"damp_heat"}]},{id:"menstrual_pattern",type:"single",category:"menstruation",weight:3,title:"What are your menstrual characteristics?",description:"Choose the description that best fits your menstrual condition",validation:{required:!0},options:[{value:"regular_normal",label:"Regular cycle, moderate flow, normal color",weight:3,constitutionType:"balanced"},{value:"light_delayed",label:"Light flow, delayed cycle, pale color",weight:3,constitutionType:"qi_deficiency"},{value:"heavy_early",label:"Heavy flow, early cycle, dark red color",weight:3,constitutionType:"damp_heat"},{value:"clots_dark",label:"Blood clots, dark red or purple-black color",weight:4,constitutionType:"blood_stasis"},{value:"irregular",label:"Irregular cycle, variable flow",weight:3,constitutionType:"qi_stagnation"}]},{id:"body_type",type:"single",category:"physical",weight:2,title:"What are your body type characteristics?",description:"Choose the description that best fits your body type",validation:{required:!0},options:[{value:"normal",label:"Well-proportioned, neither fat nor thin",weight:3,constitutionType:"balanced"},{value:"thin",label:"Lean, not easy to gain weight",weight:3,constitutionType:"yin_deficiency"},{value:"overweight",label:"Overweight, prone to edema",weight:3,constitutionType:"phlegm_dampness"},{value:"muscular",label:"Strong build, firm muscles",weight:2,constitutionType:"balanced"}]},{id:"skin_condition",type:"single",category:"appearance",weight:1,title:"How is your skin condition?",description:"Choose the description that best fits your skin characteristics",validation:{required:!0},options:[{value:"healthy",label:"Moist skin, glossy, rarely get acne",weight:3,constitutionType:"balanced"},{value:"dry",label:"Dry skin, lack of luster",weight:3,constitutionType:"yin_deficiency"},{value:"oily_acne",label:"Oily skin, prone to acne",weight:3,constitutionType:"damp_heat"},{value:"dull",label:"Dull skin, many dark spots",weight:3,constitutionType:"blood_stasis"}]},{id:"menstrual_pain_severity",type:"scale",category:"menstrual",weight:3,title:"How severe is your menstrual pain?",description:"Please select your pain level on the slider (0=No pain, 10=Severe pain)",validation:{required:!0,min:0,max:10},options:Array.from({length:11},(e,t)=>({value:t,label:0===t?"No pain":t<=3?"Mild":t<=6?"Moderate":t<=8?"Severe":"Extreme",weight:t<=2?3:t<=4?2:t<=7?3:4,constitutionType:t<=2?"balanced":t<=4?"qi_deficiency":t<=7?"blood_stasis":"qi_stagnation"}))},{id:"pain_nature",type:"single",category:"menstrual",weight:2,title:"What is the nature of your menstrual pain?",description:"Choose the description that best matches your pain sensation",validation:{required:!0},options:[{value:"cramping",label:"Cramping, wave-like contractions",weight:3,constitutionType:"qi_stagnation"},{value:"dull_ache",label:"Dull ache, continuous mild pain",weight:3,constitutionType:"qi_deficiency"},{value:"sharp_pain",label:"Sharp pain, like needle pricks",weight:4,constitutionType:"blood_stasis"},{value:"cold_pain",label:"Cold pain, worsens with cold, improves with warmth",weight:3,constitutionType:"yang_deficiency"}]},{id:"menstrual_symptoms",type:"multiple",category:"menstrual",weight:2,title:"What other symptoms do you experience during menstruation?",description:"You can select multiple symptoms",validation:{required:!1},options:[{value:"bloating",label:"Bloating",weight:2,constitutionType:"phlegm_dampness"},{value:"nausea",label:"Nausea and vomiting",weight:3,constitutionType:"damp_heat"},{value:"headache",label:"Headache",weight:2,constitutionType:"qi_stagnation"},{value:"mood_swings",label:"Severe mood swings",weight:3,constitutionType:"qi_stagnation"},{value:"fatigue",label:"Extreme fatigue",weight:2,constitutionType:"qi_deficiency"},{value:"back_pain",label:"Back pain",weight:2,constitutionType:"yang_deficiency"},{value:"breast_tenderness",label:"Breast tenderness",weight:2,constitutionType:"qi_stagnation"},{value:"none",label:"None of the above",weight:1,constitutionType:"balanced"}]}]},S={zh:{balanced:{acupoints:{primaryPoints:[{name:"足三里",location:"膝盖下3寸，胫骨外侧1横指",function:"调理脾胃，增强体质",method:"顺时针按揉3-5分钟"},{name:"关元",location:"肚脐下3寸",function:"培元固本，调理气血",method:"温和按压2-3分钟"}],supportingPoints:[{name:"百会",location:"头顶正中",function:"提神醒脑，调节情绪",method:"轻柔按压1-2分钟"}],massageTechnique:"温和按摩，以酸胀感为度",frequency:"每日1-2次",duration:"每次10-15分钟"},diet:{beneficial:["五谷杂粮","新鲜蔬果","优质蛋白","适量坚果"],avoid:["过度油腻","过度辛辣","过度生冷"],principles:["饮食均衡","定时定量","细嚼慢咽"],sampleMeals:["小米粥配青菜","蒸蛋羹","清炖鸡汤","时令水果"]},lifestyle:{exercise:["太极拳","八段锦","慢跑","瑜伽"],sleep:["规律作息","晚上11点前入睡","保证7-8小时睡眠"],emotional:["保持心情愉快","适度社交","培养兴趣爱好"],seasonal:["春季养肝","夏季养心","秋季养肺","冬季养肾"]},moxibustion:{points:["足三里","关元"],timing:"每周2-3次",duration:"每穴15-20分钟",frequency:"保健为主",precautions:["注意防烫","孕期禁用","饭后1小时进行"]}},qi_deficiency:{acupoints:{primaryPoints:[{name:"气海",location:"肚脐下1.5寸",function:"补气益气，增强体力",method:"顺时针按揉5-8分钟"},{name:"足三里",location:"膝盖下3寸，胫骨外侧1横指",function:"健脾益气，增强消化",method:"重点按揉5-10分钟"},{name:"脾俞",location:"第11胸椎棘突下旁开1.5寸",function:"健脾益气，助消化",method:"按压配合艾灸效果更佳"}],supportingPoints:[{name:"百会",location:"头顶正中",function:"升阳举陷，提神益气",method:"轻柔提拉按压"},{name:"太冲",location:"足背第1、2跖骨间",function:"疏肝理气，调和气血",method:"按压2-3分钟"}],massageTechnique:"温和持续按压，避免过度用力",frequency:"每日2次，早晚各一次",duration:"每次15-20分钟"},diet:{beneficial:["黄芪","党参","山药","大枣","桂圆","小米","南瓜","胡萝卜"],avoid:["生冷食物","过度油腻","难消化食物","过量生萝卜"],principles:["温补脾胃","少食多餐","细嚼慢咽","避免过饱"],sampleMeals:["黄芪炖鸡汤","山药小米粥","红枣桂圆茶","蒸蛋羹"]},lifestyle:{exercise:["八段锦","太极拳","散步","轻度瑜伽"],sleep:["早睡早起","午休30分钟","避免熬夜"],emotional:["保持乐观","避免过度思虑","适度放松"],seasonal:["春夏养阳","秋冬进补","避免过度劳累"]},moxibustion:{points:["气海","关元","足三里","脾俞"],timing:"每日或隔日",duration:"每穴20-30分钟",frequency:"连续调理2-3个月",precautions:["温度适中","避免烫伤","经期减量"]}},yang_deficiency:{acupoints:{primaryPoints:[{name:"命门",location:"第2腰椎棘突下",function:"温补肾阳，强腰健肾",method:"温热按压配合艾灸"},{name:"肾俞",location:"第2腰椎棘突下旁开1.5寸",function:"补肾壮阳，强腰膝",method:"双手同时按压"},{name:"关元",location:"肚脐下3寸",function:"温补下焦，固本培元",method:"顺时针按揉配合艾灸"}],supportingPoints:[{name:"涌泉",location:"足底前1/3凹陷处",function:"温补肾阳，引火归元",method:"睡前按摩至发热"}],massageTechnique:"温热按摩，配合艾灸效果更佳",frequency:"每日2次",duration:"每次20-30分钟"},diet:{beneficial:["羊肉","韭菜","生姜","肉桂","核桃","栗子","黑豆","枸杞"],avoid:["生冷食物","寒性水果","冰饮","苦寒药物"],principles:["温补阳气","忌食生冷","适当进补","温热为主"],sampleMeals:["当归生姜羊肉汤","韭菜炒蛋","核桃粥","枸杞茶"]},lifestyle:{exercise:["慢跑","太极拳","八段锦","适度力量训练"],sleep:["保暖睡眠","避免夜间受凉","充足睡眠"],emotional:["保持积极心态","避免过度忧虑"],seasonal:["春夏养阳","秋冬重点保暖","避免贪凉"]},moxibustion:{points:["命门","肾俞","关元","足三里"],timing:"每日艾灸",duration:"每穴30-40分钟",frequency:"长期调理",precautions:["注意保暖","避免受风","经期谨慎使用"]}},yin_deficiency:{acupoints:{primaryPoints:[{name:"太溪",location:"内踝后方，跟腱前凹陷处",function:"滋阴补肾，清虚热",method:"轻柔按揉3-5分钟"},{name:"三阴交",location:"内踝上3寸，胫骨内侧缘后方",function:"滋阴养血，调经止痛",method:"按压至酸胀感"},{name:"肾俞",location:"第2腰椎棘突下旁开1.5寸",function:"补肾滋阴，强腰膝",method:"轻柔按压，避免过重"}],supportingPoints:[{name:"神门",location:"腕横纹尺侧端，尺侧腕屈肌腱桡侧凹陷处",function:"宁心安神，改善睡眠",method:"睡前按压2-3分钟"}],massageTechnique:"轻柔按摩，避免过度刺激",frequency:"每日1-2次",duration:"每次10-15分钟"},diet:{beneficial:["银耳","百合","枸杞","黑芝麻","蜂蜜","梨","葡萄","鸭肉"],avoid:["辛辣食物","煎炸食品","温燥食物","过量咖啡"],principles:["滋阴润燥","清淡饮食","多饮水","少食辛辣"],sampleMeals:["银耳莲子汤","百合粥","蜂蜜柠檬水","清蒸鱼"]},lifestyle:{exercise:["瑜伽","太极拳","游泳","散步"],sleep:["规律作息","创造安静睡眠环境","睡前放松"],emotional:["保持心境平和","学会释放压力","冥想练习"],seasonal:["秋冬滋阴","避免过度出汗","注意补水"]},moxibustion:{points:["太溪","三阴交"],timing:"隔日进行",duration:"每穴15-20分钟",frequency:"温和调理",precautions:["温度不宜过高","时间不宜过长","注意补水"]}},phlegm_dampness:{acupoints:{primaryPoints:[{name:"丰隆",location:"外踝上8寸，胫骨前缘外侧1.5寸",function:"化痰除湿，健脾和胃",method:"重点按揉5-8分钟"},{name:"阴陵泉",location:"胫骨内侧髁下方凹陷处",function:"健脾利湿，消肿",method:"按压至酸胀感明显"},{name:"中脘",location:"肚脐上4寸",function:"健脾和胃，化湿消痰",method:"顺时针按揉"}],supportingPoints:[{name:"天枢",location:"肚脐旁开2寸",function:"调理肠胃，消除腹胀",method:"双侧同时按揉"}],massageTechnique:"稍重按压，以促进气血运行",frequency:"每日2-3次",duration:"每次15-20分钟"},diet:{beneficial:["薏米","冬瓜","白萝卜","陈皮","山楂","荷叶","绿豆"],avoid:["甜腻食物","油炸食品","肥肉","奶制品过量"],principles:["清淡饮食","少油少盐","控制甜食","多食化湿食物"],sampleMeals:["薏米红豆汤","冬瓜汤","山楂茶","清蒸蔬菜"]},lifestyle:{exercise:["快走","慢跑","游泳","有氧运动"],sleep:["避免午睡过长","保持规律作息"],emotional:["保持积极心态","避免过度思虑"],seasonal:["春夏祛湿","秋冬温补","避免潮湿环境"]},moxibustion:{points:["丰隆","阴陵泉","中脘"],timing:"每日或隔日",duration:"每穴20-25分钟",frequency:"坚持调理",precautions:["配合运动","控制饮食","保持环境干燥"]}},damp_heat:{acupoints:{primaryPoints:[{name:"曲池",location:"肘横纹外侧端，屈肘时肘横纹头",function:"清热解毒，祛湿热",method:"按压至酸胀感"},{name:"阴陵泉",location:"胫骨内侧髁下方凹陷处",function:"清热利湿，健脾",method:"重点按揉"},{name:"大椎",location:"第7颈椎棘突下",function:"清热解表，调节免疫",method:"轻柔按压"}],supportingPoints:[{name:"合谷",location:"手背第1、2掌骨间",function:"清热解毒，调理面部",method:"按压2-3分钟"}],massageTechnique:"适中力度，以清热为主",frequency:"每日1-2次",duration:"每次10-15分钟"},diet:{beneficial:["绿豆","苦瓜","黄瓜","西瓜","薏米","茯苓","莲子心"],avoid:["辛辣食物","油炸食品","烧烤","酒类","甜腻食物"],principles:["清热利湿","清淡饮食","多饮水","少食肥甘"],sampleMeals:["绿豆汤","苦瓜炒蛋","薏米粥","莲子心茶"]},lifestyle:{exercise:["游泳","瑜伽","太极拳","避免剧烈运动"],sleep:["保持凉爽睡眠环境","规律作息"],emotional:["保持心境平和","避免急躁情绪"],seasonal:["夏季重点清热","避免暴晒","保持环境通风"]},moxibustion:{points:["阴陵泉"],timing:"谨慎使用",duration:"时间较短",frequency:"以按摩为主",precautions:["避免过热","以清热为主","可用刮痧代替"]}},blood_stasis:{acupoints:{primaryPoints:[{name:"血海",location:"髌骨内上缘上2寸",function:"活血化瘀，调经止痛",method:"按揉至局部发热"},{name:"三阴交",location:"内踝上3寸，胫骨内侧缘后方",function:"活血调经，化瘀止痛",method:"重点按压"},{name:"膈俞",location:"第7胸椎棘突下旁开1.5寸",function:"活血化瘀，宽胸理气",method:"按压配合艾灸"}],supportingPoints:[{name:"太冲",location:"足背第1、2跖骨间",function:"疏肝理气，活血化瘀",method:"按压至酸胀感"}],massageTechnique:"适度用力，以活血为主",frequency:"每日2次",duration:"每次15-20分钟"},diet:{beneficial:["山楂","红花","当归","川芎","红糖","黑木耳","洋葱"],avoid:["生冷食物","油腻食物","过咸食物"],principles:["活血化瘀","温通经络","适当温补"],sampleMeals:["山楂茶","当归炖鸡","黑木耳炒菜","红糖姜茶"]},lifestyle:{exercise:["慢跑","太极拳","瑜伽","适度有氧运动"],sleep:["保持规律作息","避免熬夜"],emotional:["保持心情愉快","避免情绪郁结"],seasonal:["春季疏肝","注意保暖","避免受寒"]},moxibustion:{points:["血海","三阴交","膈俞"],timing:"每日或隔日",duration:"每穴20-25分钟",frequency:"经期前后重点调理",precautions:["经期谨慎使用","注意温度","配合运动"]}},qi_stagnation:{acupoints:{primaryPoints:[{name:"太冲",location:"足背第1、2跖骨间",function:"疏肝解郁，调畅气机",method:"按压至酸胀感明显"},{name:"期门",location:"第6肋间隙，乳头直下",function:"疏肝理气，宽胸解郁",method:"轻柔按揉"},{name:"神门",location:"腕横纹尺侧端",function:"宁心安神，调节情绪",method:"睡前重点按压"}],supportingPoints:[{name:"印堂",location:"两眉头连线中点",function:"宁心安神，开窍醒脑",method:"轻柔按压"}],massageTechnique:"轻柔舒缓，以疏通为主",frequency:"每日2-3次",duration:"每次10-15分钟"},diet:{beneficial:["玫瑰花","柠檬","橙子","佛手","香橼","薄荷","茉莉花"],avoid:["过于油腻","难消化食物","过量咖啡因"],principles:["疏肝理气","清淡饮食","适量芳香类食物"],sampleMeals:["玫瑰花茶","柠檬蜂蜜水","薄荷茶","清淡蔬菜"]},lifestyle:{exercise:["瑜伽","太极拳","散步","深呼吸练习"],sleep:["规律作息","睡前放松","创造安静环境"],emotional:["学会释放压力","培养兴趣爱好","适当社交"],seasonal:["春季重点疏肝","保持心情愉快","避免情绪波动"]},moxibustion:{points:["太冲","神门"],timing:"情绪不佳时",duration:"每穴15-20分钟",frequency:"按需调理",precautions:["温和施灸","配合情绪调节","避免过度刺激"]}},special_diathesis:{acupoints:{primaryPoints:[{name:"风池",location:"枕骨下，胸锁乳突肌与斜方肌间凹陷处",function:"祛风解表，增强抵抗力",method:"轻柔按压"},{name:"足三里",location:"膝盖下3寸，胫骨外侧1横指",function:"调理脾胃，增强体质",method:"温和按揉"}],supportingPoints:[{name:"迎香",location:"鼻翼外缘中点旁",function:"通鼻窍，防过敏",method:"轻柔按揉"}],massageTechnique:"温和按摩，避免过度刺激",frequency:"每日1次",duration:"每次10分钟"},diet:{beneficial:["益生菌食品","新鲜蔬果","优质蛋白","抗过敏食物"],avoid:["已知过敏原","添加剂多的食品","刺激性食物"],principles:["避免过敏原","增强免疫力","营养均衡"],sampleMeals:["酸奶","新鲜水果","清淡蔬菜","白肉类"]},lifestyle:{exercise:["适度运动","避免过敏环境","增强体质"],sleep:["保持充足睡眠","避免过敏原"],emotional:["保持积极心态","学会应对过敏"],seasonal:["根据季节调整","预防过敏发作"]},moxibustion:{points:["足三里"],timing:"谨慎使用",duration:"时间较短",frequency:"个体化调理",precautions:["避免过敏反应","个体化方案","医生指导下进行"]}}},en:{balanced:{acupoints:{primaryPoints:[{name:"Zusanli (ST36)",location:"3 cun below the knee, 1 finger-width lateral to the tibia",function:"Regulate spleen and stomach, strengthen constitution",method:"Massage clockwise for 3-5 minutes"},{name:"Guanyuan (CV4)",location:"3 cun below the navel",function:"Strengthen vitality, regulate qi and blood",method:"Gentle pressure for 2-3 minutes"}],supportingPoints:[{name:"Baihui (GV20)",location:"Top center of the head",function:"Refresh mind, regulate emotions",method:"Gentle pressure for 1-2 minutes"}],massageTechnique:"Gentle massage until feeling soreness",frequency:"1-2 times daily",duration:"10-15 minutes each session"},diet:{beneficial:["Whole grains","Fresh vegetables and fruits","Quality protein","Moderate nuts"],avoid:["Excessive greasy food","Excessive spicy food","Excessive cold food"],principles:["Balanced diet","Regular meals","Chew slowly"],sampleMeals:["Millet porridge with vegetables","Steamed egg custard","Clear chicken soup","Seasonal fruits"]},lifestyle:{exercise:["Tai Chi","Qigong","Jogging","Yoga"],sleep:["Regular schedule","Sleep before 11 PM","Ensure 7-8 hours of sleep"],emotional:["Maintain happy mood","Moderate socializing","Cultivate hobbies"],seasonal:["Nourish liver in spring","Nourish heart in summer","Nourish lungs in autumn","Nourish kidneys in winter"]},moxibustion:{points:["Zusanli","Guanyuan"],timing:"2-3 times per week",duration:"15-20 minutes per point",frequency:"Mainly for health maintenance",precautions:["Prevent burns","Avoid during pregnancy","Perform 1 hour after meals"]}},qi_deficiency:{acupoints:{primaryPoints:[{name:"Qihai (CV6)",location:"1.5 cun below the navel",function:"Tonify qi, enhance physical strength",method:"Massage clockwise for 5-8 minutes"},{name:"Zusanli (ST36)",location:"3 cun below the knee, 1 finger-width lateral to the tibia",function:"Strengthen spleen and qi, enhance digestion",method:"Focus massage for 5-10 minutes"},{name:"Pishu (BL20)",location:"1.5 cun lateral to the 11th thoracic vertebra",function:"Strengthen spleen and qi, aid digestion",method:"Pressure combined with moxibustion works better"}],supportingPoints:[{name:"Baihui (GV20)",location:"Top center of the head",function:"Lift yang qi, refresh and tonify qi",method:"Gentle lifting pressure"},{name:"Taichong (LR3)",location:"Between 1st and 2nd metatarsals on foot dorsum",function:"Soothe liver qi, harmonize qi and blood",method:"Press for 2-3 minutes"}],massageTechnique:"Gentle sustained pressure, avoid excessive force",frequency:"2 times daily, morning and evening",duration:"15-20 minutes each session"},diet:{beneficial:["Astragalus","Codonopsis","Chinese yam","Red dates","Longan","Millet","Pumpkin","Carrot"],avoid:["Cold raw foods","Excessive greasy food","Hard-to-digest foods","Excessive raw radish"],principles:["Warm and tonify spleen-stomach","Small frequent meals","Chew slowly","Avoid overeating"],sampleMeals:["Astragalus chicken soup","Chinese yam millet porridge","Red date longan tea","Steamed egg custard"]},lifestyle:{exercise:["Qigong","Tai Chi","Walking","Light yoga"],sleep:["Early to bed and early to rise","30-minute afternoon nap","Avoid staying up late"],emotional:["Stay optimistic","Avoid overthinking","Moderate relaxation"],seasonal:["Nourish yang in spring-summer","Tonify in autumn-winter","Avoid overexertion"]},moxibustion:{points:["Qihai","Guanyuan","Zusanli","Pishu"],timing:"Daily or every other day",duration:"20-30 minutes per point",frequency:"Continuous treatment for 2-3 months",precautions:["Moderate temperature","Avoid burns","Reduce during menstruation"]}},yang_deficiency:{acupoints:{primaryPoints:[{name:"Mingmen (GV4)",location:"Below the 2nd lumbar vertebra",function:"Warm and tonify kidney yang, strengthen lower back and kidneys",method:"Warm pressure combined with moxibustion"},{name:"Shenshu (BL23)",location:"1.5 cun lateral to the 2nd lumbar vertebra",function:"Tonify kidneys and strengthen yang, strengthen lower back and knees",method:"Press with both hands simultaneously"},{name:"Guanyuan (CV4)",location:"3 cun below the navel",function:"Warm and tonify lower jiao, strengthen foundation",method:"Clockwise massage combined with moxibustion"}],supportingPoints:[{name:"Yongquan (KD1)",location:"Depression in the front 1/3 of the sole",function:"Warm and tonify kidney yang, guide fire back to source",method:"Massage before sleep until warm"}],massageTechnique:"Warm massage, better effect when combined with moxibustion",frequency:"2 times daily",duration:"20-30 minutes each session"},diet:{beneficial:["Mutton","Chinese chives","Ginger","Cinnamon","Walnuts","Chestnuts","Black beans","Goji berries"],avoid:["Cold raw foods","Cold-natured fruits","Ice drinks","Bitter cold medicines"],principles:["Warm and tonify yang qi","Avoid cold foods","Appropriate tonification","Focus on warm foods"],sampleMeals:["Angelica ginger mutton soup","Stir-fried eggs with chives","Walnut porridge","Goji berry tea"]},lifestyle:{exercise:["Jogging","Tai Chi","Qigong","Moderate strength training"],sleep:["Keep warm while sleeping","Avoid catching cold at night","Adequate sleep"],emotional:["Maintain positive attitude","Avoid excessive worry"],seasonal:["Nourish yang in spring-summer","Focus on keeping warm in autumn-winter","Avoid seeking coolness"]},moxibustion:{points:["Mingmen","Shenshu","Guanyuan","Zusanli"],timing:"Daily moxibustion",duration:"30-40 minutes per point",frequency:"Long-term treatment",precautions:["Keep warm","Avoid wind exposure","Use cautiously during menstruation"]}},yin_deficiency:{acupoints:{primaryPoints:[{name:"Taixi (KD3)",location:"Depression behind the medial malleolus, in front of the Achilles tendon",function:"Nourish yin and tonify kidneys, clear deficiency heat",method:"Gentle massage for 3-5 minutes"},{name:"Sanyinjiao (SP6)",location:"3 cun above the medial malleolus, behind the medial border of the tibia",function:"Nourish yin and blood, regulate menstruation and relieve pain",method:"Press until feeling soreness"},{name:"Shenshu (BL23)",location:"1.5 cun lateral to the 2nd lumbar vertebra",function:"Tonify kidneys and nourish yin, strengthen lower back and knees",method:"Gentle pressure, avoid excessive force"}],supportingPoints:[{name:"Shenmen (HE7)",location:"Ulnar end of the wrist crease, in the depression on the radial side of the flexor carpi ulnaris tendon",function:"Calm the mind and spirit, improve sleep",method:"Press for 2-3 minutes before sleep"}],massageTechnique:"Gentle massage, avoid excessive stimulation",frequency:"1-2 times daily",duration:"10-15 minutes each session"},diet:{beneficial:["White fungus","Lily bulb","Goji berries","Black sesame","Honey","Pears","Grapes","Duck meat"],avoid:["Spicy foods","Fried foods","Warm-dry foods","Excessive coffee"],principles:["Nourish yin and moisten dryness","Light diet","Drink plenty of water","Reduce spicy foods"],sampleMeals:["White fungus lotus seed soup","Lily porridge","Honey lemon water","Steamed fish"]},lifestyle:{exercise:["Yoga","Tai Chi","Swimming","Walking"],sleep:["Regular schedule","Create quiet sleep environment","Relax before sleep"],emotional:["Maintain peaceful mind","Learn to release stress","Meditation practice"],seasonal:["Nourish yin in autumn-winter","Avoid excessive sweating","Pay attention to hydration"]},moxibustion:{points:["Taixi","Sanyinjiao"],timing:"Every other day",duration:"15-20 minutes per point",frequency:"Gentle treatment",precautions:["Temperature should not be too high","Duration should not be too long","Pay attention to hydration"]}},phlegm_dampness:{acupoints:{primaryPoints:[{name:"Fenglong (ST40)",location:"8 cun above the lateral malleolus, 1.5 cun lateral to the anterior border of the tibia",function:"Transform phlegm and eliminate dampness, strengthen spleen and harmonize stomach",method:"Focus massage for 5-8 minutes"},{name:"Yinlingquan (SP9)",location:"Depression below the medial condyle of the tibia",function:"Strengthen spleen and drain dampness, reduce swelling",method:"Press until obvious soreness"},{name:"Zhongwan (CV12)",location:"4 cun above the navel",function:"Strengthen spleen and harmonize stomach, transform dampness and eliminate phlegm",method:"Clockwise massage"}],supportingPoints:[{name:"Tianshu (ST25)",location:"2 cun lateral to the navel",function:"Regulate intestines and stomach, eliminate abdominal distension",method:"Massage both sides simultaneously"}],massageTechnique:"Slightly stronger pressure to promote qi and blood circulation",frequency:"2-3 times daily",duration:"15-20 minutes each session"},diet:{beneficial:["Job's tears","Winter melon","White radish","Tangerine peel","Hawthorn","Lotus leaf","Mung beans"],avoid:["Sweet greasy foods","Fried foods","Fatty meat","Excessive dairy products"],principles:["Light diet","Low oil and salt","Control sweets","Eat more dampness-transforming foods"],sampleMeals:["Job's tears and red bean soup","Winter melon soup","Hawthorn tea","Steamed vegetables"]},lifestyle:{exercise:["Brisk walking","Jogging","Swimming","Aerobic exercise"],sleep:["Avoid excessive afternoon naps","Maintain regular schedule"],emotional:["Maintain positive attitude","Avoid overthinking"],seasonal:["Eliminate dampness in spring-summer","Warm tonification in autumn-winter","Avoid humid environments"]},moxibustion:{points:["Fenglong","Yinlingquan","Zhongwan"],timing:"Daily or every other day",duration:"20-25 minutes per point",frequency:"Persist in treatment",precautions:["Combine with exercise","Control diet","Keep environment dry"]}},damp_heat:{acupoints:{primaryPoints:[{name:"Quchi (LI11)",location:"Lateral end of the elbow crease when elbow is flexed",function:"Clear heat and detoxify, eliminate damp-heat",method:"Press until feeling soreness"},{name:"Yinlingquan (SP9)",location:"Depression below the medial condyle of the tibia",function:"Clear heat and drain dampness, strengthen spleen",method:"Focus massage"},{name:"Dazhui (GV14)",location:"Below the 7th cervical vertebra",function:"Clear heat and release exterior, regulate immunity",method:"Gentle pressure"}],supportingPoints:[{name:"Hegu (LI4)",location:"Between the 1st and 2nd metacarpals on the back of hand",function:"Clear heat and detoxify, regulate facial area",method:"Press for 2-3 minutes"}],massageTechnique:"Moderate force, focus on clearing heat",frequency:"1-2 times daily",duration:"10-15 minutes each session"},diet:{beneficial:["Mung beans","Bitter melon","Cucumber","Watermelon","Job's tears","Poria","Lotus seed heart"],avoid:["Spicy foods","Fried foods","Barbecue","Alcohol","Sweet greasy foods"],principles:["Clear heat and drain dampness","Light diet","Drink plenty of water","Reduce rich foods"],sampleMeals:["Mung bean soup","Stir-fried bitter melon with eggs","Job's tears porridge","Lotus seed heart tea"]},lifestyle:{exercise:["Swimming","Yoga","Tai Chi","Avoid vigorous exercise"],sleep:["Keep cool sleep environment","Regular schedule"],emotional:["Maintain peaceful mind","Avoid irritable emotions"],seasonal:["Focus on clearing heat in summer","Avoid sun exposure","Keep environment ventilated"]},moxibustion:{points:["Yinlingquan"],timing:"Use cautiously",duration:"Shorter duration",frequency:"Focus on massage",precautions:["Avoid overheating","Focus on clearing heat","Can use scraping instead"]}},blood_stasis:{acupoints:{primaryPoints:[{name:"Xuehai (SP10)",location:"2 cun above the medial superior border of the patella",function:"Invigorate blood and resolve stasis, regulate menstruation and relieve pain",method:"Massage until local warmth"},{name:"Sanyinjiao (SP6)",location:"3 cun above the medial malleolus, behind the medial border of the tibia",function:"Invigorate blood and regulate menstruation, resolve stasis and relieve pain",method:"Focus pressure"},{name:"Geshu (BL17)",location:"1.5 cun lateral to the 7th thoracic vertebra",function:"Invigorate blood and resolve stasis, expand chest and regulate qi",method:"Pressure combined with moxibustion"}],supportingPoints:[{name:"Taichong (LR3)",location:"Between the 1st and 2nd metatarsals on foot dorsum",function:"Soothe liver and regulate qi, invigorate blood and resolve stasis",method:"Press until feeling soreness"}],massageTechnique:"Moderate force, focus on invigorating blood",frequency:"2 times daily",duration:"15-20 minutes each session"},diet:{beneficial:["Hawthorn","Safflower","Angelica","Chuanxiong","Brown sugar","Black fungus","Onions"],avoid:["Cold raw foods","Greasy foods","Excessively salty foods"],principles:["Invigorate blood and resolve stasis","Warm and unblock meridians","Appropriate warm tonification"],sampleMeals:["Hawthorn tea","Angelica stewed chicken","Stir-fried black fungus","Brown sugar ginger tea"]},lifestyle:{exercise:["Jogging","Tai Chi","Yoga","Moderate aerobic exercise"],sleep:["Maintain regular schedule","Avoid staying up late"],emotional:["Keep happy mood","Avoid emotional stagnation"],seasonal:["Soothe liver in spring","Pay attention to keeping warm","Avoid catching cold"]},moxibustion:{points:["Xuehai","Sanyinjiao","Geshu"],timing:"Daily or every other day",duration:"20-25 minutes per point",frequency:"Focus treatment before and after menstruation",precautions:["Use cautiously during menstruation","Pay attention to temperature","Combine with exercise"]}},qi_stagnation:{acupoints:{primaryPoints:[{name:"Taichong (LR3)",location:"Between the 1st and 2nd metatarsals on foot dorsum",function:"Soothe liver and relieve depression, regulate qi movement",method:"Press until obvious soreness"},{name:"Qimen (LR14)",location:"6th intercostal space, directly below the nipple",function:"Soothe liver and regulate qi, expand chest and relieve depression",method:"Gentle massage"},{name:"Shenmen (HE7)",location:"Ulnar end of the wrist crease",function:"Calm the mind and spirit, regulate emotions",method:"Focus pressure before sleep"}],supportingPoints:[{name:"Yintang (EX-HN3)",location:"Midpoint between the eyebrows",function:"Calm the mind and spirit, open orifices and awaken brain",method:"Gentle pressure"}],massageTechnique:"Gentle and soothing, focus on unblocking",frequency:"2-3 times daily",duration:"10-15 minutes each session"},diet:{beneficial:["Rose flowers","Lemon","Orange","Buddha's hand","Citron","Mint","Jasmine flowers"],avoid:["Excessively greasy food","Hard-to-digest foods","Excessive caffeine"],principles:["Soothe liver and regulate qi","Light diet","Moderate aromatic foods"],sampleMeals:["Rose flower tea","Lemon honey water","Mint tea","Light vegetables"]},lifestyle:{exercise:["Yoga","Tai Chi","Walking","Deep breathing exercises"],sleep:["Regular schedule","Relax before sleep","Create quiet environment"],emotional:["Learn to release stress","Cultivate hobbies","Appropriate socializing"],seasonal:["Focus on soothing liver in spring","Keep happy mood","Avoid emotional fluctuations"]},moxibustion:{points:["Taichong","Shenmen"],timing:"When emotions are poor",duration:"15-20 minutes per point",frequency:"Treatment as needed",precautions:["Gentle moxibustion","Combine with emotional regulation","Avoid excessive stimulation"]}},special_diathesis:{acupoints:{primaryPoints:[{name:"Fengchi (GB20)",location:"Depression between the sternocleidomastoid and trapezius muscles below the occiput",function:"Dispel wind and release exterior, enhance resistance",method:"Gentle pressure"},{name:"Zusanli (ST36)",location:"3 cun below the knee, 1 finger-width lateral to the tibia",function:"Regulate spleen and stomach, strengthen constitution",method:"Gentle massage"}],supportingPoints:[{name:"Yingxiang (LI20)",location:"Beside the midpoint of the lateral border of the nostril",function:"Unblock nasal orifices, prevent allergies",method:"Gentle massage"}],massageTechnique:"Gentle massage, avoid excessive stimulation",frequency:"1 time daily",duration:"10 minutes each session"},diet:{beneficial:["Probiotic foods","Fresh vegetables and fruits","Quality protein","Anti-allergic foods"],avoid:["Known allergens","Foods with many additives","Irritating foods"],principles:["Avoid allergens","Enhance immunity","Balanced nutrition"],sampleMeals:["Yogurt","Fresh fruits","Light vegetables","White meat"]},lifestyle:{exercise:["Moderate exercise","Avoid allergic environments","Strengthen constitution"],sleep:["Maintain adequate sleep","Avoid allergens"],emotional:["Maintain positive attitude","Learn to cope with allergies"],seasonal:["Adjust according to seasons","Prevent allergic episodes"]},moxibustion:{points:["Zusanli"],timing:"Use cautiously",duration:"Shorter duration",frequency:"Individualized treatment",precautions:["Avoid allergic reactions","Individualized plan","Under medical guidance"]}}}};var _=i(44702),T=i(29152),q=i(7326);let P={zh:{balanced:{name:"平和质",description:"体质平和，身心健康，是最理想的体质状态。",characteristics:["精力充沛，不易疲劳","睡眠良好，情绪稳定","消化功能正常","对环境适应能力强"],commonSymptoms:["很少生病","恢复能力强","抵抗力好"],menstrualFeatures:["月经周期规律（28-30天）","经量适中","颜色正常红色","痛经轻微或无痛经"]},qi_deficiency:{name:"气虚质",description:"元气不足，以疲乏、气短、自汗等气虚表现为主要特征。",characteristics:["容易疲劳，精神不振","说话声音低，不爱说话","容易出汗，活动后更明显","抵抗力差，容易感冒"],commonSymptoms:["气短懒言","容易疲劳","自汗","食欲不振"],menstrualFeatures:["月经量少，颜色淡","周期可能延后","经期疲劳加重","可能有轻度痛经"]},yang_deficiency:{name:"阳虚质",description:"阳气不足，以畏寒怕冷、手足不温等虚寒表现为主要特征。",characteristics:["畏寒怕冷，手足不温","喜热饮食，不耐寒邪","精神不振，睡眠偏多","大便溏薄，小便清长"],commonSymptoms:["畏寒肢冷","精神萎靡","腰膝酸软","性功能减退"],menstrualFeatures:["月经量少，颜色淡","周期延后","经期腹痛喜温喜按","经前或经期腰酸明显"]},yin_deficiency:{name:"阴虚质",description:"阴液亏少，以口燥咽干、手足心热等虚热表现为主要特征。",characteristics:["手足心热，口咽干燥","喜冷饮，不耐暑热","大便干燥，小便短赤","睡眠差，性情急躁"],commonSymptoms:["五心烦热","口干咽燥","盗汗","失眠多梦"],menstrualFeatures:["月经量少或正常","周期可能提前","经色鲜红","经前烦躁，失眠"]},phlegm_dampness:{name:"痰湿质",description:"痰湿凝聚，以形体肥胖、腹部肥满、口黏苔腻等痰湿表现为主要特征。",characteristics:["形体肥胖，腹部肥满松软","面部皮肤油脂较多","容易困倦，身重不爽","口黏腻或甜，喜食肥甘甜腻"],commonSymptoms:["身重困倦","胸闷痰多","口黏腻","大便正常或不实"],menstrualFeatures:["月经量多或正常","经色淡红","质地粘稠","经前胸闷、水肿"]},damp_heat:{name:"湿热质",description:"湿热内蕴，以面垢油腻、口苦、苔黄腻等湿热表现为主要特征。",characteristics:["面垢油腻，易生痤疮","口苦口干，身重困倦","大便黏滞不畅或燥结","小便短黄，男易阴囊潮湿"],commonSymptoms:["面部油腻","口苦口干","身重困倦","大便黏滞"],menstrualFeatures:["月经量多，颜色深红","周期可能提前","经前烦躁易怒","痛经较重，喜冷恶热"]},blood_stasis:{name:"血瘀质",description:"血行不畅，以肤色晦黯、舌质紫黯等血瘀表现为主要特征。",characteristics:["肤色晦黯，色素沉着","容易出现瘀斑","口唇黯淡，舌下络脉紫黯","性情急躁，健忘"],commonSymptoms:["肤色晦黯","易生色斑","疼痛如针刺","健忘"],menstrualFeatures:["月经有血块","经色暗红或紫黑","痛经明显，拒按","经前乳房胀痛"]},qi_stagnation:{name:"气郁质",description:"气机郁滞，以神情抑郁、忧虑脆弱等气郁表现为主要特征。",characteristics:["神情抑郁，情感脆弱","烦闷不乐，容易紧张","多愁善感，忧虑不安","对精神刺激适应能力较差"],commonSymptoms:["情绪抑郁","胸胁胀满","善太息","咽中如有异物"],menstrualFeatures:["月经不规律","经前情绪波动大","乳房胀痛明显","痛经程度与情绪相关"]},special_diathesis:{name:"特禀质",description:"先天失常，以生理缺陷、过敏反应等为主要特征。",characteristics:["先天禀赋不足","容易过敏","适应能力差","遗传性疾病家族史"],commonSymptoms:["过敏性疾病","遗传性疾病","胎传性疾病"],menstrualFeatures:["月经异常多样","可能伴随过敏症状","对环境变化敏感"]}},en:{balanced:{name:"Balanced Constitution",description:"A harmonious constitution with balanced body and mind, representing the ideal health state.",characteristics:["Energetic and not easily fatigued","Good sleep and stable emotions","Normal digestive function","Strong adaptability to environment"],commonSymptoms:["Rarely gets sick","Strong recovery ability","Good resistance"],menstrualFeatures:["Regular menstrual cycle (28-30 days)","Moderate flow","Normal red color","Mild or no menstrual pain"]},qi_deficiency:{name:"Qi Deficiency Constitution",description:"Insufficient vital energy, characterized by fatigue, shortness of breath, and spontaneous sweating.",characteristics:["Easily fatigued and low spirits","Low voice, reluctant to speak","Prone to sweating, especially after activity","Poor resistance, easily catches cold"],commonSymptoms:["Shortness of breath and reluctance to speak","Easy fatigue","Spontaneous sweating","Poor appetite"],menstrualFeatures:["Scanty menstruation with pale color","Cycle may be delayed","Increased fatigue during menstruation","May have mild menstrual pain"]},yang_deficiency:{name:"Yang Deficiency Constitution",description:"Insufficient yang qi, characterized by aversion to cold, cold limbs, and other cold manifestations.",characteristics:["Aversion to cold, cold hands and feet","Prefers warm food and drinks, intolerant to cold","Low spirits, tends to sleep more","Loose stools, clear and long urine"],commonSymptoms:["Aversion to cold and cold limbs","Mental fatigue","Sore and weak lower back and knees","Decreased sexual function"],menstrualFeatures:["Scanty menstruation with pale color","Delayed cycle","Abdominal pain during menstruation, relieved by warmth and pressure","Obvious lower back pain before or during menstruation"]},yin_deficiency:{name:"Yin Deficiency Constitution",description:"Insufficient yin fluid, characterized by dry mouth and throat, hot palms and soles.",characteristics:["Hot palms and soles, dry mouth and throat","Prefers cold drinks, intolerant to heat","Dry stools, short and yellow urine","Poor sleep, irritable temperament"],commonSymptoms:["Five-center heat (palms, soles, chest)","Dry mouth and throat","Night sweats","Insomnia and vivid dreams"],menstrualFeatures:["Scanty or normal menstruation","Cycle may be advanced","Bright red menstrual color","Irritability and insomnia before menstruation"]},phlegm_dampness:{name:"Phlegm-Dampness Constitution",description:"Accumulation of phlegm and dampness, characterized by obesity, abdominal fullness, and sticky mouth.",characteristics:["Obese body with soft and full abdomen","Oily facial skin","Easily drowsy, heavy body feeling","Sticky or sweet mouth, prefers fatty and sweet foods"],commonSymptoms:["Heavy body and drowsiness","Chest tightness and phlegm","Sticky mouth","Normal or loose stools"],menstrualFeatures:["Heavy or normal menstruation","Light red color","Thick consistency","Chest tightness and edema before menstruation"]},damp_heat:{name:"Damp-Heat Constitution",description:"Internal accumulation of damp-heat, characterized by oily face, bitter mouth, and yellow greasy tongue coating.",characteristics:["Oily face, prone to acne","Bitter and dry mouth, heavy body feeling","Sticky or dry stools","Short and yellow urine, men prone to scrotal dampness"],commonSymptoms:["Oily face","Bitter and dry mouth","Heavy body and drowsiness","Sticky stools"],menstrualFeatures:["Heavy menstruation with dark red color","Cycle may be advanced","Irritability before menstruation","Severe menstrual pain, prefers cold and dislikes heat"]},blood_stasis:{name:"Blood Stasis Constitution",description:"Poor blood circulation, characterized by dull complexion and purple tongue.",characteristics:["Dull complexion with pigmentation","Prone to bruising","Dark lips, purple sublingual vessels","Irritable temperament, forgetful"],commonSymptoms:["Dull complexion","Prone to dark spots","Needle-like pain","Forgetfulness"],menstrualFeatures:["Menstruation with blood clots","Dark red or purple-black color","Obvious menstrual pain, refuses pressure","Breast distension before menstruation"]},qi_stagnation:{name:"Qi Stagnation Constitution",description:"Stagnant qi movement, characterized by depression, anxiety, and emotional fragility.",characteristics:["Depressed mood, emotionally fragile","Restless and easily tense","Sentimental and anxious","Poor adaptability to mental stimulation"],commonSymptoms:["Emotional depression","Chest and hypochondriac distension","Frequent sighing","Feeling of foreign body in throat"],menstrualFeatures:["Irregular menstruation","Large emotional fluctuations before menstruation","Obvious breast distension","Menstrual pain related to emotions"]},special_diathesis:{name:"Special Constitution",description:"Congenital abnormalities, characterized by physiological defects and allergic reactions.",characteristics:["Congenital insufficiency","Prone to allergies","Poor adaptability","Family history of hereditary diseases"],commonSymptoms:["Allergic diseases","Hereditary diseases","Congenital diseases"],menstrualFeatures:["Various menstrual abnormalities","May be accompanied by allergic symptoms","Sensitive to environmental changes"]}}},A={zh:{balanced:[{name:"三阴交",description:"调理气血，缓解轻微经期不适"},{name:"血海",description:"活血调经，维持经期平衡"}],qi_deficiency:[{name:"气海",description:"补益元气，缓解疲劳型痛经"},{name:"足三里",description:"健脾益气，改善体质虚弱"},{name:"关元",description:"温补肾阳，增强体质"}],yang_deficiency:[{name:"关元",description:"温阳散寒，缓解冷痛"},{name:"神阙",description:"温中散寒，改善宫寒症状"},{name:"肾俞",description:"补肾壮阳，温暖下焦"}],yin_deficiency:[{name:"太溪",description:"滋阴补肾，缓解燥热症状"},{name:"三阴交",description:"滋阴养血，调理月经"},{name:"照海",description:"滋肾阴，清虚热"}],phlegm_dampness:[{name:"丰隆",description:"化痰除湿，缓解腹胀"},{name:"阴陵泉",description:"健脾利湿，消除水肿"},{name:"中脘",description:"健脾和胃，化湿消胀"}],damp_heat:[{name:"阴陵泉",description:"清热利湿，缓解湿热症状"},{name:"曲池",description:"清热解毒，凉血止痛"},{name:"太冲",description:"疏肝清热，调理情绪"}],blood_stasis:[{name:"血海",description:"活血化瘀，缓解刺痛"},{name:"膈俞",description:"活血化瘀，通络止痛"},{name:"次髎",description:"活血通络，缓解盆腔瘀血"}],qi_stagnation:[{name:"太冲",description:"疏肝理气，缓解绞痛"},{name:"期门",description:"疏肝解郁，调理情绪"},{name:"行间",description:"疏肝泄热，缓解烦躁"}],special_diathesis:[{name:"百会",description:"调节神经，缓解过敏症状"},{name:"风池",description:"疏风解表，调节免疫"},{name:"合谷",description:"调气止痛，增强抵抗力"}]},en:{balanced:[{name:"Sanyinjiao (SP6)",description:"Regulates qi and blood, relieves mild menstrual discomfort"},{name:"Xuehai (SP10)",description:"Activates blood circulation, maintains menstrual balance"}],qi_deficiency:[{name:"Qihai (CV6)",description:"Tonifies primordial qi, relieves fatigue-type dysmenorrhea"},{name:"Zusanli (ST36)",description:"Strengthens spleen and qi, improves weak constitution"},{name:"Guanyuan (CV4)",description:"Warms and tonifies kidney yang, strengthens constitution"}],yang_deficiency:[{name:"Guanyuan (CV4)",description:"Warms yang and disperses cold, relieves cold pain"},{name:"Shenque (CV8)",description:"Warms the center and disperses cold, improves uterine cold"},{name:"Shenshu (BL23)",description:"Tonifies kidney and strengthens yang, warms lower jiao"}],yin_deficiency:[{name:"Taixi (KI3)",description:"Nourishes yin and tonifies kidney, relieves heat symptoms"},{name:"Sanyinjiao (SP6)",description:"Nourishes yin and blood, regulates menstruation"},{name:"Zhaohai (KI6)",description:"Nourishes kidney yin, clears deficiency heat"}],phlegm_dampness:[{name:"Fenglong (ST40)",description:"Transforms phlegm and eliminates dampness, relieves bloating"},{name:"Yinlingquan (SP9)",description:"Strengthens spleen and drains dampness, reduces edema"},{name:"Zhongwan (CV12)",description:"Strengthens spleen and stomach, transforms dampness"}],damp_heat:[{name:"Yinlingquan (SP9)",description:"Clears heat and drains dampness, relieves damp-heat symptoms"},{name:"Quchi (LI11)",description:"Clears heat and detoxifies, cools blood and stops pain"},{name:"Taichong (LR3)",description:"Soothes liver and clears heat, regulates emotions"}],blood_stasis:[{name:"Xuehai (SP10)",description:"Activates blood and resolves stasis, relieves stabbing pain"},{name:"Geshu (BL17)",description:"Activates blood and resolves stasis, unblocks meridians"},{name:"Ciliao (BL32)",description:"Activates blood and unblocks meridians, relieves pelvic stasis"}],qi_stagnation:[{name:"Taichong (LR3)",description:"Soothes liver and regulates qi, relieves cramping pain"},{name:"Qimen (LR14)",description:"Soothes liver and relieves depression, regulates emotions"},{name:"Xingjian (LR2)",description:"Soothes liver and drains heat, relieves irritability"}],special_diathesis:[{name:"Baihui (GV20)",description:"Regulates nervous system, relieves allergic symptoms"},{name:"Fengchi (GB20)",description:"Expels wind and releases exterior, regulates immunity"},{name:"Hegu (LI4)",description:"Regulates qi and stops pain, strengthens resistance"}]}},E={zh:{balanced:["保持规律的作息时间","适量运动，如散步、瑜伽","经期注意保暖，避免受凉","保持心情愉快，避免过度紧张"],qi_deficiency:["充足睡眠，避免熬夜","选择温和的运动，避免剧烈活动","经期多休息，减少体力消耗","注意营养补充，多吃补气食物"],yang_deficiency:["注意保暖，特别是腹部和腰部","避免生冷食物，多喝温开水","适当进行温和的有氧运动","经期可用热水袋敷腹部"],yin_deficiency:["避免熬夜，保证充足睡眠","减少辛辣刺激性食物","多吃滋阴润燥的食物","保持情绪稳定，避免急躁"],phlegm_dampness:["控制体重，避免过度肥胖","减少甜腻食物的摄入","增加有氧运动，促进代谢","保持环境干燥，避免潮湿"],damp_heat:["饮食清淡，避免油腻食物","多吃清热利湿的食物","保持心情舒畅，避免急躁","注意个人卫生，保持清洁"],blood_stasis:["适当运动，促进血液循环","避免久坐不动","经期可进行轻柔按摩","保持情绪稳定，避免生气"],qi_stagnation:["学会情绪管理，保持心情舒畅","适当进行舒缓运动，如瑜伽","避免压力过大，学会放松","可以听音乐、冥想来缓解压力"],special_diathesis:["避免接触过敏原","增强体质，提高免疫力","注意环境卫生，减少刺激","必要时寻求专业医疗建议"]},en:{balanced:["Maintain regular sleep schedule","Moderate exercise like walking and yoga","Keep warm during menstruation, avoid cold","Stay positive and avoid excessive stress"],qi_deficiency:["Get adequate sleep, avoid staying up late","Choose gentle exercises, avoid intense activities","Rest more during menstruation, reduce physical exertion","Focus on nutrition, eat qi-tonifying foods"],yang_deficiency:["Keep warm, especially abdomen and lower back","Avoid cold foods, drink warm water","Engage in gentle aerobic exercises","Use heating pad on abdomen during menstruation"],yin_deficiency:["Avoid staying up late, ensure adequate sleep","Reduce spicy and irritating foods","Eat yin-nourishing and moistening foods","Maintain emotional stability, avoid irritability"],phlegm_dampness:["Control weight, avoid excessive obesity","Reduce intake of sweet and greasy foods","Increase aerobic exercise to boost metabolism","Keep environment dry, avoid humidity"],damp_heat:["Eat light diet, avoid greasy foods","Eat heat-clearing and dampness-draining foods","Stay calm and avoid irritability","Maintain personal hygiene and cleanliness"],blood_stasis:["Exercise appropriately to promote blood circulation","Avoid prolonged sitting","Gentle massage during menstruation","Maintain emotional stability, avoid anger"],qi_stagnation:["Learn emotional management, stay cheerful","Engage in soothing exercises like yoga","Avoid excessive stress, learn to relax","Listen to music or meditate to relieve stress"],special_diathesis:["Avoid contact with allergens","Strengthen constitution and boost immunity","Pay attention to environmental hygiene, reduce irritation","Seek professional medical advice when necessary"]}},M=(e,t,i)=>{let s={zh:[{title:"痛经的自然与物理疗法综合指南：15种科学验证的缓解方法",description:"详细介绍热敷、按摩、瑜伽等自然疗法，以及穴位按摩的具体操作方法，帮助您自然缓解痛经。",category:"自然疗法",link:"/zh/articles/natural-physical-therapy-comprehensive-guide"},{title:"痛经药物治疗专业指南：NSAIDs安全用药与剂量计算",description:"专业的痛经药物治疗指南，包括布洛芬、萘普生等NSAIDs的安全用药方法和剂量计算。",category:"药物治疗",link:"/zh/articles/nsaid-menstrual-pain-professional-guide"}],en:[{title:"Comprehensive Guide to Natural and Physical Therapies for Menstrual Pain",description:"Detailed introduction to natural therapies such as heat therapy, massage, yoga, and specific acupoint massage techniques to naturally relieve menstrual pain.",category:"Natural Therapy",link:"/en/articles/natural-physical-therapy-comprehensive-guide"},{title:"Professional Guide to Menstrual Pain Medication: Safe Use of NSAIDs and Dosage Calculation",description:"Professional guide to menstrual pain medication, including safe use and dosage calculation of NSAIDs like ibuprofen and naproxen.",category:"Medical Treatment",link:"/en/articles/nsaid-menstrual-pain-professional-guide"}]},a=[...s[i]||s.zh],n={zh:{qi_deficiency:[{title:"气虚体质痛经调理：补气养血的中医方案",description:"针对气虚体质的痛经特点，提供补气养血的中医调理方案，包括食疗、穴位按摩等。",category:"体质调理",link:"/zh/interactive-tools/constitution-test"}],yang_deficiency:[{title:"阳虚体质痛经调理：温阳散寒的调理方法",description:"专门针对阳虚体质的痛经调理，重点介绍温阳散寒的方法和注意事项。",category:"体质调理",link:"/zh/interactive-tools/constitution-test"}],blood_stasis:[{title:"血瘀体质痛经调理：活血化瘀的有效方法",description:"针对血瘀体质的痛经特点，提供活血化瘀的调理方案和生活指导。",category:"体质调理",link:"/zh/interactive-tools/constitution-test"}]},en:{qi_deficiency:[{title:"Qi Deficiency Constitution Menstrual Pain Management: TCM Solutions for Qi and Blood Tonification",description:"Targeted TCM solutions for qi deficiency constitution menstrual pain, including dietary therapy and acupoint massage.",category:"Constitution Care",link:"/en/interactive-tools/constitution-test"}],yang_deficiency:[{title:"Yang Deficiency Constitution Menstrual Pain Management: Warming Yang and Dispersing Cold",description:"Specialized care for yang deficiency constitution menstrual pain, focusing on warming yang and dispersing cold methods.",category:"Constitution Care",link:"/en/interactive-tools/constitution-test"}],blood_stasis:[{title:"Blood Stasis Constitution Menstrual Pain Management: Effective Blood Circulation Methods",description:"Targeted solutions for blood stasis constitution menstrual pain, providing blood circulation and stasis resolution guidance.",category:"Constitution Care",link:"/en/interactive-tools/constitution-test"}]}}[i]?.[e];return n&&a.push(...n),a.slice(0,3)},z={zh:[{scenario:"与伴侣沟通",templates:[{title:"温和告知",content:"亲爱的，我今天经期疼痛比较严重，可能需要多休息一下。如果我看起来不太舒服，请不要担心，这是正常的生理反应。",tone:"intimate"},{title:"寻求理解",content:"我现在有些痛经，可能情绪会有些波动，不是因为你做错了什么。能给我一些时间和空间吗？",tone:"intimate"},{title:"请求帮助",content:"我现在肚子很痛，能帮我准备一杯热水吗？或者陪我安静地待一会儿就好。",tone:"intimate"}]},{scenario:"与朋友沟通",templates:[{title:"约会改期",content:"不好意思，我今天身体不太舒服（经期疼痛），可能没办法保持最佳状态。我们能改到下次吗？",tone:"casual"},{title:"聚会参与",content:"我会参加聚会，但可能需要早点回家休息。如果我看起来有点疲惫，请理解一下～",tone:"casual"},{title:"寻求支持",content:"姐妹，我现在痛经痛得厉害，你有什么好的缓解方法吗？或者就是想找人聊聊。",tone:"casual"}]},{scenario:"与同事/领导沟通",templates:[{title:"请假申请",content:"您好，我今天身体不适，可能需要请假半天/一天。我会尽快处理紧急工作，其他事务明天补上。",tone:"formal"},{title:"工作调整",content:"不好意思，我今天身体有些不适，可能工作效率会受影响。如果有紧急事务，请优先安排。",tone:"formal"},{title:"会议参与",content:"我可能需要在会议中途短暂离开一下，不是对会议内容不感兴趣，而是身体原因。",tone:"formal"}]}],en:[{scenario:"Communicating with Partner",templates:[{title:"Gentle Notification",content:"Honey, I'm experiencing severe menstrual cramps today and might need some extra rest. If I seem uncomfortable, please don't worry - it's a normal physiological response.",tone:"intimate"},{title:"Seeking Understanding",content:"I'm having period pain right now and my emotions might be a bit up and down. It's not because you did anything wrong. Could you give me some time and space?",tone:"intimate"},{title:"Asking for Help",content:"I'm having really bad cramps right now. Could you help me get some hot water? Or just stay with me quietly for a while.",tone:"intimate"}]},{scenario:"Communicating with Friends",templates:[{title:"Rescheduling Dates",content:"Sorry, I'm not feeling well today (period pain) and might not be at my best. Could we reschedule for another time?",tone:"casual"},{title:"Party Participation",content:"I'll join the party, but I might need to head home early to rest. Please understand if I seem a bit tired~",tone:"casual"},{title:"Seeking Support",content:"Girl, I'm having terrible period cramps right now. Do you have any good relief methods? Or I just want someone to talk to.",tone:"casual"}]},{scenario:"Communicating with Colleagues/Boss",templates:[{title:"Leave Request",content:"Hello, I'm not feeling well today and may need to take half a day/full day off. I'll handle urgent work as soon as possible and catch up on other tasks tomorrow.",tone:"formal"},{title:"Work Adjustment",content:"Sorry, I'm feeling a bit unwell today and my work efficiency might be affected. Please prioritize urgent matters if any.",tone:"formal"},{title:"Meeting Participation",content:"I might need to step out briefly during the meeting. It's not because I'm not interested in the content, but due to health reasons.",tone:"formal"}]}]},Z={zh:{balanced:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"小包纸巾",reason:"日常清洁需要",priority:"medium"}]},{category:"舒缓用品",items:[{name:"暖宝宝",reason:"温热缓解轻微不适",priority:"medium"},{name:"保温杯",reason:"随时补充温水",priority:"medium"},{name:"薄荷糖",reason:"提神醒脑，缓解疲劳",priority:"low"}]}],qi_deficiency:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"能量小零食",reason:"及时补充体力",priority:"high"}]},{category:"补气用品",items:[{name:"红枣茶包",reason:"补气养血，缓解疲劳",priority:"high"},{name:"暖宝宝",reason:"温暖身体，提升阳气",priority:"high"},{name:"小毯子",reason:"保暖休息，避免受凉",priority:"medium"}]},{category:"应急药品",items:[{name:"维生素B群",reason:"支持神经系统，缓解疲劳",priority:"medium"},{name:"葡萄糖片",reason:"快速补充能量",priority:"low"}]}],yang_deficiency:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"保温杯",reason:"随时饮用热水",priority:"high"}]},{category:"温阳用品",items:[{name:"暖宝宝",reason:"持续温暖，驱散寒气",priority:"high"},{name:"暖宫贴",reason:"专门温暖腹部",priority:"high"},{name:"生姜茶包",reason:"温中散寒，暖胃驱寒",priority:"high"}]},{category:"保暖用品",items:[{name:"薄外套",reason:"随时增添衣物保暖",priority:"medium"},{name:"暖手宝",reason:"温暖手部，促进循环",priority:"medium"},{name:"保暖袜",reason:"足部保暖，防止寒从脚起",priority:"low"}]}],yin_deficiency:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"保湿喷雾",reason:"缓解干燥，滋润肌肤",priority:"medium"}]},{category:"滋阴用品",items:[{name:"蜂蜜柠檬茶",reason:"滋阴润燥，缓解内热",priority:"high"},{name:"润喉糖",reason:"滋润咽喉，缓解干燥",priority:"medium"},{name:"保湿面膜",reason:"滋润肌肤，缓解干燥",priority:"low"}]},{category:"镇静用品",items:[{name:"薰衣草精油",reason:"舒缓情绪，帮助放松",priority:"medium"},{name:"眼罩",reason:"遮光休息，缓解疲劳",priority:"low"}]}],phlegm_dampness:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"干爽粉",reason:"保持身体干爽",priority:"medium"}]},{category:"化湿用品",items:[{name:"陈皮茶包",reason:"健脾化湿，消除胀气",priority:"high"},{name:"薄荷茶",reason:"清香化湿，提神醒脑",priority:"medium"},{name:"除湿贴",reason:"局部除湿，保持干爽",priority:"low"}]},{category:"消胀用品",items:[{name:"消化酶片",reason:"帮助消化，减少胀气",priority:"medium"},{name:"按摩球",reason:"促进循环，消除水肿",priority:"low"}]}],damp_heat:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"抗菌洗手液",reason:"清洁杀菌，预防感染",priority:"medium"}]},{category:"清热用品",items:[{name:"菊花茶包",reason:"清热解毒，降火消炎",priority:"high"},{name:"绿茶包",reason:"清热利湿，抗氧化",priority:"medium"},{name:"清凉贴",reason:"局部降温，缓解热感",priority:"low"}]},{category:"清洁用品",items:[{name:"私处清洁湿巾",reason:"专用清洁，预防炎症",priority:"medium"},{name:"漱口水",reason:"口腔清洁，去除异味",priority:"low"}]}],blood_stasis:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"止痛药",reason:"缓解刺痛，改善循环",priority:"high"}]},{category:"活血用品",items:[{name:"红花茶包",reason:"活血化瘀，缓解疼痛",priority:"high"},{name:"暖宝宝",reason:"温热促循环，缓解瘀滞",priority:"high"},{name:"按摩膏",reason:"局部按摩，促进血液循环",priority:"medium"}]},{category:"舒缓用品",items:[{name:"热敷袋",reason:"深度热敷，缓解深层疼痛",priority:"medium"},{name:"按摩球",reason:"穴位按摩，疏通经络",priority:"low"}]}],qi_stagnation:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"止痛药",reason:"缓解绞痛，舒缓情绪",priority:"high"}]},{category:"疏肝用品",items:[{name:"玫瑰花茶包",reason:"疏肝解郁，调节情绪",priority:"high"},{name:"柠檬精油",reason:"芳香疏肝，提升心情",priority:"medium"},{name:"暖宝宝",reason:"温暖腹部，缓解痉挛",priority:"high"}]},{category:"情绪调节",items:[{name:"舒缓音乐",reason:"放松心情，缓解压力",priority:"medium"},{name:"减压玩具",reason:"转移注意力，释放压力",priority:"low"}]}],special_diathesis:[{category:"基础必需品",items:[{name:"卫生巾/棉条",reason:"基本生理需求",priority:"high"},{name:"湿纸巾",reason:"保持清洁卫生",priority:"high"},{name:"抗过敏药",reason:"预防过敏反应",priority:"high"}]},{category:"防护用品",items:[{name:"口罩",reason:"过滤空气，减少过敏原",priority:"high"},{name:"免洗洗手液",reason:"随时清洁，减少接触",priority:"medium"},{name:"防过敏贴",reason:"皮肤保护，预防接触性过敏",priority:"medium"}]},{category:"应急药品",items:[{name:"抗组胺药",reason:"快速缓解过敏症状",priority:"high"},{name:"肾上腺素笔",reason:"严重过敏时的救命药物",priority:"medium"},{name:"舒缓喷雾",reason:"缓解皮肤过敏不适",priority:"low"}]}]},en:{balanced:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Small tissue packs",reason:"Daily cleaning needs",priority:"medium"}]},{category:"Comfort Items",items:[{name:"Heat pads",reason:"Warm relief for mild discomfort",priority:"medium"},{name:"Thermos bottle",reason:"Stay hydrated with warm water",priority:"medium"},{name:"Mint candies",reason:"Refresh and relieve fatigue",priority:"low"}]}],qi_deficiency:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Energy snacks",reason:"Timely energy replenishment",priority:"high"}]},{category:"Qi-Tonifying Items",items:[{name:"Jujube tea bags",reason:"Tonify qi and blood, relieve fatigue",priority:"high"},{name:"Heat pads",reason:"Warm body, boost yang qi",priority:"high"},{name:"Small blanket",reason:"Keep warm and rest, avoid catching cold",priority:"medium"}]},{category:"Emergency Supplements",items:[{name:"Vitamin B complex",reason:"Support nervous system, relieve fatigue",priority:"medium"},{name:"Glucose tablets",reason:"Quick energy boost",priority:"low"}]}],yang_deficiency:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Thermos bottle",reason:"Drink hot water anytime",priority:"high"}]},{category:"Yang-Warming Items",items:[{name:"Heat pads",reason:"Continuous warmth, dispel cold",priority:"high"},{name:"Abdominal heat patches",reason:"Specifically warm abdomen",priority:"high"},{name:"Ginger tea bags",reason:"Warm center, dispel cold from stomach",priority:"high"}]},{category:"Warming Items",items:[{name:"Light jacket",reason:"Add layers for warmth anytime",priority:"medium"},{name:"Hand warmers",reason:"Warm hands, promote circulation",priority:"medium"},{name:"Warm socks",reason:"Keep feet warm, prevent cold from feet",priority:"low"}]}],yin_deficiency:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Moisturizing spray",reason:"Relieve dryness, moisturize skin",priority:"medium"}]},{category:"Yin-Nourishing Items",items:[{name:"Honey lemon tea",reason:"Nourish yin, moisten dryness, relieve internal heat",priority:"high"},{name:"Throat lozenges",reason:"Moisten throat, relieve dryness",priority:"medium"},{name:"Moisturizing face mask",reason:"Moisturize skin, relieve dryness",priority:"low"}]},{category:"Calming Items",items:[{name:"Lavender essential oil",reason:"Soothe emotions, help relaxation",priority:"medium"},{name:"Eye mask",reason:"Block light for rest, relieve fatigue",priority:"low"}]}],phlegm_dampness:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Drying powder",reason:"Keep body dry",priority:"medium"}]},{category:"Dampness-Resolving Items",items:[{name:"Tangerine peel tea bags",reason:"Strengthen spleen, resolve dampness, eliminate bloating",priority:"high"},{name:"Mint tea",reason:"Fragrant dampness resolution, refresh mind",priority:"medium"},{name:"Moisture-absorbing patches",reason:"Local moisture removal, stay dry",priority:"low"}]},{category:"Anti-Bloating Items",items:[{name:"Digestive enzyme tablets",reason:"Aid digestion, reduce bloating",priority:"medium"},{name:"Massage ball",reason:"Promote circulation, eliminate edema",priority:"low"}]}],damp_heat:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Antibacterial hand sanitizer",reason:"Clean and sterilize, prevent infection",priority:"medium"}]},{category:"Heat-Clearing Items",items:[{name:"Chrysanthemum tea bags",reason:"Clear heat, detoxify, reduce inflammation",priority:"high"},{name:"Green tea bags",reason:"Clear heat, drain dampness, antioxidant",priority:"medium"},{name:"Cooling patches",reason:"Local cooling, relieve heat sensation",priority:"low"}]},{category:"Cleansing Items",items:[{name:"Intimate cleansing wipes",reason:"Specialized cleaning, prevent inflammation",priority:"medium"},{name:"Mouthwash",reason:"Oral hygiene, remove odors",priority:"low"}]}],blood_stasis:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Pain relievers",reason:"Relieve stabbing pain, improve circulation",priority:"high"}]},{category:"Blood-Activating Items",items:[{name:"Safflower tea bags",reason:"Activate blood, resolve stasis, relieve pain",priority:"high"},{name:"Heat pads",reason:"Warm heat promotes circulation, relieves stasis",priority:"high"},{name:"Massage balm",reason:"Local massage, promote blood circulation",priority:"medium"}]},{category:"Soothing Items",items:[{name:"Hot compress bags",reason:"Deep heat therapy, relieve deep pain",priority:"medium"},{name:"Massage ball",reason:"Acupoint massage, unblock meridians",priority:"low"}]}],qi_stagnation:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Pain relievers",reason:"Relieve cramping pain, soothe emotions",priority:"high"}]},{category:"Liver-Soothing Items",items:[{name:"Rose tea bags",reason:"Soothe liver, relieve depression, regulate emotions",priority:"high"},{name:"Lemon essential oil",reason:"Aromatic liver soothing, uplift mood",priority:"medium"},{name:"Heat pads",reason:"Warm abdomen, relieve spasms",priority:"high"}]},{category:"Mood Regulation",items:[{name:"Soothing music",reason:"Relax mood, relieve stress",priority:"medium"},{name:"Stress relief toys",reason:"Divert attention, release stress",priority:"low"}]}],special_diathesis:[{category:"Basic Essentials",items:[{name:"Sanitary pads/tampons",reason:"Basic physiological needs",priority:"high"},{name:"Wet wipes",reason:"Maintain cleanliness and hygiene",priority:"high"},{name:"Anti-allergy medication",reason:"Prevent allergic reactions",priority:"high"}]},{category:"Protective Items",items:[{name:"Face masks",reason:"Filter air, reduce allergens",priority:"high"},{name:"Hand sanitizer",reason:"Clean anytime, reduce contact",priority:"medium"},{name:"Anti-allergy patches",reason:"Skin protection, prevent contact allergies",priority:"medium"}]},{category:"Emergency Medications",items:[{name:"Antihistamines",reason:"Quickly relieve allergy symptoms",priority:"high"},{name:"Epinephrine pen",reason:"Life-saving medication for severe allergies",priority:"medium"},{name:"Soothing spray",reason:"Relieve skin allergy discomfort",priority:"low"}]}]}},B={zh:{balanced:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["保持良好坐姿，每小时起身活动5分钟","办公桌常备温水杯，保持充足水分","适当调节空调温度，避免过冷","工作间隙可做简单的颈肩放松操"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["选择舒适的鞋子，减少足部疲劳","公共交通上可听轻音乐放松心情","避免长时间低头看手机","提前准备好保暖外套"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["选择舒适宽松的衣物","聚会时适量饮食，避免过饱","主动选择温热的饮品","必要时可提前告知亲近朋友"]}],qi_deficiency:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["工作强度适中，避免过度劳累","午休时间尽量小憩15-20分钟","常备红枣茶或桂圆茶补气","重要会议前可按压足三里穴提神"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["避免早高峰拥挤，可适当错峰出行","通勤包里备好小零食补充能量","选择有座位的交通方式","疲劳时可按压合谷穴缓解"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["聚会时间不宜过长，适时休息","选择营养丰富、易消化的食物","避免过于激烈的娱乐活动","可以坐着参与，减少站立时间"]}],yang_deficiency:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["办公室常备小毯子或暖宝宝","选择温热的午餐，避免生冷食物","座位尽量远离空调出风口","工作间隙可做暖身小运动"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["出门前检查保暖措施是否充足","随身携带保温杯装热水","避免在寒冷环境中久待","可在包里放暖手宝"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["选择温暖的聚会场所","避免冰镇饮料和生冷食物","可以带一件薄外套备用","聚会后注意保暖回家"]}],yin_deficiency:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["保持办公环境适度湿润","多喝温开水，少喝咖啡","避免长时间对着电脑屏幕","中午可以闭目养神片刻"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["避免在烈日下长时间等车","可以听舒缓音乐平静心情","通勤时间可做深呼吸练习","保持心情平和，避免急躁"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["避免过于嘈杂的聚会环境","选择清淡的食物，少吃辛辣","聚会时间适中，不宜过晚","保持情绪稳定，避免过度兴奋"]}],phlegm_dampness:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["保持办公环境通风干燥","午餐选择清淡少油的食物","工作间隙可做简单伸展运动","避免久坐，定时起身活动"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["选择透气性好的衣物","避免在潮湿环境中久留","可以做一些简单的活动筋骨","保持心情愉快，避免沉闷"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["避免过量饮食，特别是甜腻食物","选择有氧活动类型的聚会","多与朋友交流，保持活跃","聚会后可以散步消食"]}],damp_heat:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["保持办公环境清洁干爽","多喝绿茶或菊花茶清热","避免辛辣刺激的外卖食物","工作压力大时可做放松练习"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["选择吸汗透气的衣物","避免在闷热环境中久待","保持心情平静，避免烦躁","可以听清淡的音乐舒缓情绪"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["选择清爽的聚会环境","避免油腻、辛辣、酒精类食物","聚会时间不宜过长","保持情绪稳定，避免激动"]}],blood_stasis:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["避免长时间保持同一姿势","定时做颈肩和腰部活动","工作间隙可按摩手部穴位","保持心情舒畅，避免郁闷"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["在车上可做简单的踝关节运动","避免紧身衣物限制血液循环","可以听欢快的音乐调节心情","到站后可以快走几分钟"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["选择活跃一些的聚会活动","避免久坐不动的聚会形式","多与朋友交流，保持心情愉快","可以参与一些轻松的运动"]}],qi_stagnation:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["工作压力大时及时调节情绪","可以在办公室放一些绿植","午休时可以到户外走走","与同事保持良好的沟通"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["通勤时可以听喜欢的音乐","避免在拥挤时段出行","可以做深呼吸缓解压力","保持积极乐观的心态"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["多参与轻松愉快的聚会","与朋友分享心情，释放压力","选择开阔明亮的聚会场所","避免过于严肃的话题"]}],special_diathesis:[{scenario:"办公场景",icon:"\uD83D\uDCBC",tips:["注意办公环境的过敏原","保持办公用品的清洁","避免使用刺激性的清洁用品","工作压力大时注意调节"]},{scenario:"通勤路上",icon:"\uD83D\uDE87",tips:["避免接触可能的过敏原","在空气质量差时戴口罩","选择相对清洁的交通工具","随身携带必要的应急药物"]},{scenario:"社交聚会",icon:"\uD83D\uDC65",tips:["提前了解聚会环境和食物","避免接触已知的过敏原","必要时提前告知朋友注意事项","随身携带抗过敏药物"]}]},en:{balanced:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Maintain good posture, stand and move for 5 minutes every hour","Keep a water bottle at your desk for adequate hydration","Adjust air conditioning temperature appropriately, avoid overcooling","Do simple neck and shoulder relaxation exercises during breaks"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Choose comfortable shoes to reduce foot fatigue","Listen to light music on public transport to relax","Avoid looking down at phone for extended periods","Prepare warm outerwear in advance"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Choose comfortable and loose-fitting clothing","Eat moderately at gatherings, avoid overeating","Actively choose warm beverages","Inform close friends in advance if necessary"]}],qi_deficiency:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Moderate work intensity, avoid overexertion","Take 15-20 minute naps during lunch break","Keep jujube tea or longan tea for qi tonification","Press Zusanli acupoint before important meetings for energy"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Avoid rush hour crowds, consider off-peak travel","Pack small snacks in commute bag for energy","Choose transportation with seating when possible","Press Hegu acupoint when feeling fatigued"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Keep gathering time moderate, rest when needed","Choose nutritious, easily digestible foods","Avoid overly vigorous entertainment activities","Participate while seated, reduce standing time"]}],yang_deficiency:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Keep small blankets or heating pads in office","Choose warm lunch, avoid cold foods","Sit away from air conditioning vents","Do warming exercises during work breaks"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Check warmth measures before leaving home","Carry thermos with hot water","Avoid prolonged stays in cold environments","Keep hand warmers in bag"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Choose warm gathering venues","Avoid iced drinks and cold foods","Bring a light jacket as backup","Stay warm when heading home after gatherings"]}],yin_deficiency:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Maintain moderate humidity in office environment","Drink more warm water, less coffee","Avoid prolonged computer screen exposure","Close eyes and rest briefly at noon"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Avoid waiting in direct sunlight for extended periods","Listen to soothing music to calm mood","Practice deep breathing during commute","Stay calm and avoid irritability"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Avoid overly noisy gathering environments","Choose light foods, eat less spicy food","Keep gathering time moderate, not too late","Maintain emotional stability, avoid overexcitement"]}],phlegm_dampness:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Keep office environment ventilated and dry","Choose light, low-oil foods for lunch","Do simple stretching exercises during breaks","Avoid prolonged sitting, stand and move regularly"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Choose breathable clothing","Avoid prolonged stays in humid environments","Do simple joint movements","Stay cheerful, avoid feeling stuffy"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Avoid overeating, especially sweet and greasy foods","Choose aerobic activity-type gatherings","Communicate actively with friends, stay active","Take a walk after gatherings to aid digestion"]}],damp_heat:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Keep office environment clean and dry","Drink more green tea or chrysanthemum tea for heat clearing","Avoid spicy and irritating takeout foods","Do relaxation exercises when work stress is high"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Choose sweat-wicking, breathable clothing","Avoid prolonged stays in stuffy environments","Stay calm, avoid irritability","Listen to light music to soothe emotions"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Choose refreshing gathering environments","Avoid greasy, spicy, and alcoholic foods","Keep gathering time moderate","Maintain emotional stability, avoid excitement"]}],blood_stasis:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Avoid maintaining same posture for long periods","Do regular neck, shoulder, and waist movements","Massage hand acupoints during work breaks","Stay cheerful, avoid feeling depressed"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Do simple ankle exercises while on transport","Avoid tight clothing that restricts blood circulation","Listen to upbeat music to regulate mood","Walk briskly for a few minutes after getting off"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Choose more active gathering activities","Avoid sedentary gathering formats","Communicate more with friends, stay happy","Participate in some light exercises"]}],qi_stagnation:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Regulate emotions promptly when work stress is high","Place some green plants in the office","Go outdoors for a walk during lunch break","Maintain good communication with colleagues"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Listen to favorite music during commute","Avoid traveling during crowded times","Do deep breathing to relieve stress","Maintain positive and optimistic attitude"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Participate more in relaxed and pleasant gatherings","Share feelings with friends, release stress","Choose open and bright gathering venues","Avoid overly serious topics"]}],special_diathesis:[{scenario:"Office Environment",icon:"\uD83D\uDCBC",tips:["Pay attention to allergens in office environment","Keep office supplies clean","Avoid using irritating cleaning products","Pay attention to regulation when work stress is high"]},{scenario:"Commuting",icon:"\uD83D\uDE87",tips:["Avoid contact with potential allergens","Wear mask when air quality is poor","Choose relatively clean transportation","Carry necessary emergency medications"]},{scenario:"Social Gatherings",icon:"\uD83D\uDC65",tips:["Learn about gathering environment and food in advance","Avoid contact with known allergens","Inform friends of precautions if necessary","Carry anti-allergy medications"]}]}};function L({locale:e}){let{t}=(0,n.A0)("constitutionTest"),[i,o]=(0,a.useState)({}),{currentSession:L,currentQuestion:F,currentQuestionIndex:I,totalQuestions:R,progress:G,isComplete:H,result:O,isLoading:W,error:$,startTest:U,answerQuestion:K,goToPreviousQuestion:V,goToNextQuestion:Y,completeTest:Q,resetTest:J}=function(){let[e,t]=(0,a.useState)(null),[i,s]=(0,a.useState)(0),[n,o]=(0,a.useState)(null),[r,l]=(0,a.useState)(!1),[c,d]=(0,a.useState)(null),m=e?C[e.locale]||C.zh:[],u=m[i]||null,p=i>=m.length,h=m.length>0?Math.min(i/m.length*100,100):0,g=(0,a.useCallback)(e=>{t({id:`constitution_test_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,answers:[],startedAt:new Date().toISOString(),locale:e}),s(0),o(null),d(null)},[]),y=(0,a.useCallback)(i=>{e&&t(e=>{if(!e)return e;let t=e.answers.findIndex(e=>e.questionId===i.questionId),s=[...e.answers];return t>=0?s[t]=i:s.push(i),{...e,answers:s}})},[e]),x=(0,a.useCallback)(e=>{e>=0&&e<m.length&&s(e)},[m.length]),b=(0,a.useCallback)(()=>{i>0&&s(e=>e-1)},[i]),f=(0,a.useCallback)(()=>{i<m.length-1&&s(e=>e+1)},[i,m.length]),v=(0,a.useCallback)(e=>{let t={balanced:0,qi_deficiency:0,yang_deficiency:0,yin_deficiency:0,phlegm_dampness:0,damp_heat:0,blood_stasis:0,qi_stagnation:0,special_diathesis:0};return e.forEach(e=>{let i=m.find(t=>t.id===e.questionId);i&&e.selectedValues.forEach(e=>{let s=i.options.find(t=>t.value===e);s&&(t[s.constitutionType]+=s.weight*i.weight)})}),t},[m]),w=(0,a.useCallback)(()=>{if(!e||e.answers.length!==m.length)return d(e?.locale==="en"?"Test incomplete, please answer all questions":"测试未完成，请回答所有问题"),null;l(!0);try{let i=v(e.answers),s=Object.entries(i).sort(([,e],[,t])=>t-e),a=s[0][0],n=s[1][1]>0?s[1][0]:void 0,r=Object.values(i).reduce((e,t)=>e+t,0),l=r>0?Math.round(i[a]/r*100):0,c={primaryType:a,secondaryType:n,scores:i,confidence:l,recommendations:S[e.locale]?.[a]||S.zh[a],sessionId:e.id,completedAt:new Date().toISOString()};return t(e=>e?{...e,completedAt:c.completedAt}:null),o(c),d(null),c}catch(t){return d(e?.locale==="en"?"Error calculating results, please try again":"计算结果时出错，请重试"),null}finally{l(!1)}},[e,m.length,v]),j=(0,a.useCallback)(()=>{t(null),s(0),o(null),d(null),l(!1)},[]);return{currentSession:e,currentQuestionIndex:i,currentQuestion:u,isComplete:p,progress:h,totalQuestions:m.length,startTest:g,answerQuestion:y,goToQuestion:x,goToPreviousQuestion:b,goToNextQuestion:f,completeTest:w,resetTest:j,result:n,isLoading:r,error:c}}(),{notifications:X,removeNotification:ee,addSuccessNotification:et,addErrorNotification:ei}=(0,_.z)(),es=(e,t)=>{let i=String(t);o(t=>({...t,[e]:i})),K({questionId:e,selectedValues:[i],timestamp:new Date().toISOString()})},ea=(e,t)=>{let s;let a=Array.isArray(i[e])?i[e]:i[e]?[i[e]]:[],n="none"===t;if(a.includes("none"),n)s=a.includes("none")?[]:["none"];else{let e=a.filter(e=>"none"!==e);s=e.includes(t)?e.filter(e=>e!==t):[...e,t]}o(t=>({...t,[e]:s})),K({questionId:e,selectedValues:s,timestamp:new Date().toISOString()})},en=()=>F?i[F.id]:void 0,eo=e=>e.some(e=>"menstrual_pain_severity"===e.questionId&&e.selectedValues.some(e=>"no_pain"!==e));if(!L)return(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[s.jsx(T.Z,{notifications:X,onRemove:ee}),(0,s.jsxs)("div",{className:"text-center mb-8 animate-fade-in",children:[s.jsx("div",{className:"w-24 h-24 mx-auto mb-6 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center shadow-lg",children:s.jsx(r,{className:"w-12 h-12 text-purple-600"})}),s.jsx("h1",{className:"text-4xl font-bold bg-gradient-to-r from-purple-600 to-purple-800 bg-clip-text text-transparent mb-4",children:t("title")}),s.jsx("p",{className:"text-xl text-gray-600 max-w-2xl mx-auto leading-relaxed",children:t("subtitle")})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-purple-100",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(l.Z,{className:"w-6 h-6 text-purple-600"})}),s.jsx("h3",{className:"font-semibold text-gray-800 mb-2",children:t("features.quick.title")}),s.jsx("p",{className:"text-sm text-gray-600 leading-relaxed",children:t("features.quick.description")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-purple-100",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-pink-100 to-pink-200 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(c.Z,{className:"w-6 h-6 text-pink-600"})}),s.jsx("h3",{className:"font-semibold text-gray-800 mb-2",children:t("features.professional.title")}),s.jsx("p",{className:"text-sm text-gray-600 leading-relaxed",children:t("features.professional.description")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-purple-100",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-green-100 to-green-200 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(d,{className:"w-6 h-6 text-green-600"})}),s.jsx("h3",{className:"font-semibold text-gray-800 mb-2",children:t("features.personalized.title")}),s.jsx("p",{className:"text-sm text-gray-600 leading-relaxed",children:t("features.personalized.description")})]}),(0,s.jsxs)("div",{className:"text-center p-6 bg-white rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:-translate-y-1 border border-purple-100",children:[s.jsx("div",{className:"w-12 h-12 bg-gradient-to-br from-purple-100 to-purple-200 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(m.Z,{className:"w-6 h-6 text-purple-600"})}),s.jsx("h3",{className:"font-semibold text-gray-800 mb-2",children:t("features.practical.title")}),s.jsx("p",{className:"text-sm text-gray-600 leading-relaxed",children:t("features.practical.description")})]})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 border-l-4 border-purple-500 p-6 mb-8 rounded-r-lg shadow-sm",children:[(0,s.jsxs)("h3",{className:"font-semibold text-purple-800 mb-3 flex items-center",children:[s.jsx(u.Z,{className:"w-5 h-5 mr-2"}),t("instructions.title")]}),(0,s.jsxs)("ul",{className:"text-purple-700 space-y-2",children:[(0,s.jsxs)("li",{className:"flex items-start",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"}),t("instructions.item1")]}),(0,s.jsxs)("li",{className:"flex items-start",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"}),t("instructions.item2")]}),(0,s.jsxs)("li",{className:"flex items-start",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"}),t("instructions.item3")]}),(0,s.jsxs)("li",{className:"flex items-start",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"}),t("instructions.item4")]})]})]}),s.jsx("div",{className:"text-center",children:(0,s.jsxs)("button",{onClick:()=>{U(e),o({})},className:"inline-flex items-center justify-center bg-gradient-to-r from-purple-600 to-purple-700 text-white text-lg px-10 py-4 rounded-xl font-semibold hover:from-purple-700 hover:to-purple-800 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-xl shadow-lg",children:[s.jsx(p.Z,{className:"w-6 h-6 mr-3 flex-shrink-0"}),s.jsx("span",{children:t("navigation.startTest")})]})})]});if(O){var er,el;let i=P[e]?.[O.primaryType]||P.zh[O.primaryType];return(0,s.jsxs)("div",{className:"max-w-6xl mx-auto",children:[s.jsx(T.Z,{notifications:X,onRemove:ee}),(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx("div",{className:"w-20 h-20 mx-auto mb-6 bg-green-100 rounded-full flex items-center justify-center",children:s.jsx(h.Z,{className:"w-10 h-10 text-green-600"})}),s.jsx("h1",{className:"text-3xl font-bold text-neutral-800 mb-2",children:t("result.title")}),s.jsx("p",{className:"text-lg text-neutral-600",children:t("result.subtitle")})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-blue-50 p-8 rounded-xl mb-8",children:[(0,s.jsxs)("div",{className:"text-center mb-6",children:[s.jsx("h2",{className:"text-2xl font-bold text-green-700 mb-2",children:i.name}),s.jsx("p",{className:"text-lg text-neutral-700 mb-4",children:i.description}),(0,s.jsxs)("div",{className:"inline-flex items-center bg-white px-4 py-2 rounded-full",children:[s.jsx("span",{className:"text-sm text-neutral-600 mr-2",children:t("result.match")}),(0,s.jsxs)("span",{className:"font-semibold text-green-600",children:[O.confidence,"%"]})]})]}),(0,s.jsxs)("div",{className:"grid md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"font-semibold text-neutral-800 mb-3 flex items-center",children:[s.jsx(r,{className:"w-5 h-5 mr-2 text-blue-600"}),t("result.constitutionFeatures")]}),s.jsx("ul",{className:"space-y-1",children:i.characteristics.map((e,t)=>(0,s.jsxs)("li",{className:"text-sm text-neutral-700",children:["• ",e]},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"font-semibold text-neutral-800 mb-3 flex items-center",children:[s.jsx(u.Z,{className:"w-5 h-5 mr-2 text-orange-600"}),t("result.commonSymptoms")]}),s.jsx("ul",{className:"space-y-1",children:i.commonSymptoms.map((e,t)=>(0,s.jsxs)("li",{className:"text-sm text-neutral-700",children:["• ",e]},t))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("h3",{className:"font-semibold text-neutral-800 mb-3 flex items-center",children:[s.jsx(c.Z,{className:"w-5 h-5 mr-2 text-red-600"}),t("result.menstrualFeatures")]}),s.jsx("ul",{className:"space-y-1",children:i.menstrualFeatures.map((e,t)=>(0,s.jsxs)("li",{className:"text-sm text-neutral-700",children:["• ",e]},t))})]})]})]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-8 mb-8",children:[(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("h3",{className:"text-xl font-semibold text-neutral-800 mb-4 flex items-center",children:[s.jsx(g.Z,{className:"w-6 h-6 mr-2 text-green-600"}),t("recommendations.acupoints.title")]}),(0,s.jsxs)("div",{className:"mb-6",children:[s.jsx("h4",{className:"font-medium text-neutral-700 mb-3",children:t("recommendations.acupoints.primaryAcupoints")}),s.jsx("div",{className:"space-y-3",children:O.recommendations.acupoints.primaryPoints.map((e,i)=>(0,s.jsxs)("div",{className:"bg-green-50 p-3 rounded-lg",children:[s.jsx("h5",{className:"font-medium text-green-800",children:e.name}),(0,s.jsxs)("p",{className:"text-sm text-green-700 mb-1",children:[t("recommendations.acupoints.location"),e.location]}),(0,s.jsxs)("p",{className:"text-sm text-green-700 mb-1",children:[t("recommendations.acupoints.function"),e.function]}),(0,s.jsxs)("p",{className:"text-sm text-green-600",children:[t("recommendations.acupoints.method"),e.method]})]},i))})]}),(0,s.jsxs)("div",{className:"bg-neutral-50 p-4 rounded-lg",children:[s.jsx("h4",{className:"font-medium text-neutral-700 mb-2",children:t("recommendations.acupoints.guidelines")}),(0,s.jsxs)("p",{className:"text-sm text-neutral-600 mb-1",children:[s.jsx("strong",{children:t("recommendations.acupoints.technique")}),O.recommendations.acupoints.massageTechnique]}),(0,s.jsxs)("p",{className:"text-sm text-neutral-600 mb-1",children:[s.jsx("strong",{children:t("recommendations.acupoints.frequency")}),O.recommendations.acupoints.frequency]}),(0,s.jsxs)("p",{className:"text-sm text-neutral-600",children:[s.jsx("strong",{children:t("recommendations.acupoints.duration")}),O.recommendations.acupoints.duration]})]})]}),(0,s.jsxs)("div",{className:"card",children:[(0,s.jsxs)("h3",{className:"text-xl font-semibold text-neutral-800 mb-4 flex items-center",children:[s.jsx(y,{className:"w-6 h-6 mr-2 text-orange-600"}),t("recommendations.dietary.title")]}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"font-medium text-green-700 mb-2",children:t("recommendations.dietary.beneficialFoods")}),s.jsx("div",{className:"flex flex-wrap gap-2",children:O.recommendations.diet.beneficial.map((e,t)=>s.jsx("span",{className:"bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm",children:e},t))})]}),(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"font-medium text-red-700 mb-2",children:t("recommendations.dietary.foodsToAvoid")}),s.jsx("div",{className:"flex flex-wrap gap-2",children:O.recommendations.diet.avoid.map((e,t)=>s.jsx("span",{className:"bg-red-100 text-red-800 px-3 py-1 rounded-full text-sm",children:e},t))})]}),(0,s.jsxs)("div",{children:[s.jsx("h4",{className:"font-medium text-neutral-700 mb-2",children:t("recommendations.dietary.dietaryPrinciples")}),s.jsx("ul",{className:"space-y-1",children:O.recommendations.diet.principles.map((e,t)=>(0,s.jsxs)("li",{className:"text-sm text-neutral-600",children:["• ",e]},t))})]})]})]})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-br from-green-50 to-emerald-50 p-8 rounded-xl mb-8",children:[(0,s.jsxs)("h3",{className:"text-2xl font-semibold text-emerald-800 mb-6 flex items-center",children:[s.jsx(g.Z,{className:"w-7 h-7 mr-3 text-green-600"}),t("recommendations.lifestyle.title")]}),s.jsx("p",{className:"text-emerald-700 mb-6",children:t("recommendations.lifestyle.description")}),s.jsx("div",{className:"grid lg:grid-cols-3 gap-6",children:B[e]?.[O.primaryType]?.map((e,t)=>s.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-green-200",children:[s.jsxs("h4",{className:"font-semibold text-emerald-800 mb-4 flex items-center",children:[s.jsx("span",{className:"text-2xl mr-3",children:e.icon}),e.scenario]}),s.jsx("ul",{className:"space-y-3",children:e.tips.map((e,t)=>s.jsxs("li",{className:"text-sm text-emerald-700 flex items-start",children:[s.jsx("span",{className:"w-2 h-2 bg-emerald-400 rounded-full mt-2 mr-3 flex-shrink-0"}),e]},t))})]},t))||[]}),s.jsx("div",{className:"mt-6 p-4 bg-green-100 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-green-800",children:[s.jsx("strong",{children:t("recommendations.lifestyle.reminder")}),t("recommendations.lifestyle.reminderText")]})})]}),eo(L?.answers||[])&&(0,s.jsxs)("div",{className:"bg-gradient-to-br from-pink-50 to-purple-50 p-8 rounded-xl mb-8",children:[(0,s.jsxs)("h3",{className:"text-2xl font-semibold text-purple-800 mb-6 flex items-center",children:[s.jsx(c.Z,{className:"w-7 h-7 mr-3 text-pink-600"}),t("recommendations.menstrualPain.title")]}),(0,s.jsxs)("div",{className:"grid lg:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,s.jsxs)("h4",{className:"font-semibold text-purple-700 mb-4 flex items-center",children:[s.jsx(g.Z,{className:"w-5 h-5 mr-2"}),t("recommendations.menstrualPain.acupointTherapy")]}),(er=O.primaryType,(A[e]||A.zh)[er]||[]).map((e,t)=>(0,s.jsxs)("div",{className:"mb-3 p-3 bg-purple-50 rounded-lg",children:[s.jsx("h5",{className:"font-medium text-purple-800",children:e.name}),s.jsx("p",{className:"text-sm text-purple-700",children:e.description})]},t))]}),(0,s.jsxs)("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[(0,s.jsxs)("h4",{className:"font-semibold text-purple-700 mb-4 flex items-center",children:[s.jsx(x,{className:"w-5 h-5 mr-2"}),t("recommendations.menstrualPain.lifestyleAdjustments")]}),s.jsx("ul",{className:"space-y-2",children:(el=O.primaryType,(E[e]||E.zh)[el]||[]).map((e,t)=>(0,s.jsxs)("li",{className:"text-sm text-purple-700 flex items-start",children:[s.jsx("span",{className:"w-2 h-2 bg-purple-400 rounded-full mt-2 mr-3 flex-shrink-0"}),e]},t))})]})]})]}),eo(L?.answers||[])&&(0,s.jsxs)("div",{className:"bg-gradient-to-br from-orange-50 to-amber-50 p-8 rounded-xl mb-8",children:[(0,s.jsxs)("h3",{className:"text-2xl font-semibold text-orange-800 mb-6 flex items-center",children:[s.jsx(b,{className:"w-7 h-7 mr-3 text-orange-600"}),t("emergencyKit.title")]}),s.jsx("p",{className:"text-orange-700 mb-6",children:t("emergencyKit.description")}),s.jsx("div",{className:"space-y-6",children:Z[e]?.[O.primaryType]?.map((e,i)=>s.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm border border-orange-200",children:[s.jsxs("h4",{className:"font-semibold text-orange-800 mb-4 flex items-center",children:[s.jsx("span",{className:"w-3 h-3 bg-orange-500 rounded-full mr-3"}),e.category]}),s.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-4",children:e.items.map((e,i)=>s.jsxs("div",{className:`p-4 rounded-lg border-2 ${"high"===e.priority?"border-red-200 bg-red-50":"medium"===e.priority?"border-yellow-200 bg-yellow-50":"border-green-200 bg-green-50"}`,children:[s.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("h5",{className:"font-medium text-orange-800",children:e.name}),s.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${"high"===e.priority?"bg-red-100 text-red-700":"medium"===e.priority?"bg-yellow-100 text-yellow-700":"bg-green-100 text-green-700"}`,children:"high"===e.priority?t("emergencyKit.priority.high"):"medium"===e.priority?t("emergencyKit.priority.medium"):t("emergencyKit.priority.low")})]}),s.jsx("p",{className:"text-sm text-orange-600",children:e.reason})]},i))})]},i))||[]}),s.jsx("div",{className:"mt-6 p-4 bg-orange-100 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-orange-800",children:[s.jsx("strong",{children:t("emergencyKit.packingTips")}),t("emergencyKit.packingAdvice")]})})]}),(0,s.jsxs)("div",{className:"bg-white p-8 rounded-xl shadow-sm mb-8",children:[(0,s.jsxs)("h3",{className:"text-2xl font-semibold text-neutral-800 mb-6 flex items-center",children:[s.jsx(f,{className:"w-7 h-7 mr-3 text-blue-600"}),t("articles.title")]}),s.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:M(O.primaryType,L?.answers||[],e).map((e,i)=>(0,s.jsxs)("div",{className:"border border-neutral-200 rounded-lg p-4 hover:shadow-md transition-shadow",children:[(0,s.jsxs)("div",{className:"flex items-center mb-3",children:[s.jsx("div",{className:"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mr-3",children:s.jsx(v.Z,{className:"w-5 h-5 text-blue-600"})}),s.jsx("span",{className:"text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded-full",children:e.category})]}),s.jsx("h4",{className:"font-semibold text-neutral-800 mb-2 line-clamp-2",children:e.title}),s.jsx("p",{className:"text-sm text-neutral-600 mb-3 line-clamp-3",children:e.description}),(0,s.jsxs)("a",{href:e.link,className:"inline-flex items-center text-blue-600 hover:text-blue-800 text-sm font-medium",children:[t("articles.readMore"),s.jsx(w.Z,{className:"w-4 h-4 ml-1"})]})]},i))})]}),eo(L?.answers||[])&&(0,s.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 to-indigo-50 p-8 rounded-xl mb-8",children:[(0,s.jsxs)("h3",{className:"text-2xl font-semibold text-indigo-800 mb-6 flex items-center",children:[s.jsx(j,{className:"w-7 h-7 mr-3 text-blue-600"}),t("communication.title")]}),s.jsx("p",{className:"text-indigo-700 mb-6",children:t("communication.description")}),s.jsx("div",{className:"grid lg:grid-cols-3 gap-6",children:z[e]?.map((e,i)=>s.jsxs("div",{className:"bg-white p-6 rounded-lg shadow-sm",children:[s.jsxs("h4",{className:"font-semibold text-indigo-800 mb-4 flex items-center",children:[s.jsx(N,{className:"w-5 h-5 mr-2"}),e.scenario]}),s.jsx("div",{className:"space-y-4",children:e.templates.map((e,i)=>s.jsxs("div",{className:"border border-indigo-200 rounded-lg p-4",children:[s.jsxs("div",{className:"flex items-center justify-between mb-2",children:[s.jsx("h5",{className:"font-medium text-indigo-700",children:e.title}),s.jsx("span",{className:`text-xs px-2 py-1 rounded-full ${"intimate"===e.tone?"bg-pink-100 text-pink-700":"casual"===e.tone?"bg-green-100 text-green-700":"bg-blue-100 text-blue-700"}`,children:"intimate"===e.tone?t("communication.styles.intimate"):"casual"===e.tone?t("communication.styles.casual"):t("communication.styles.formal")})]}),s.jsxs("p",{className:"text-sm text-indigo-600 mb-3 leading-relaxed",children:['"',e.content,'"']}),s.jsxs("button",{onClick:()=>{navigator.clipboard.writeText(e.content)},className:"flex items-center text-xs text-indigo-600 hover:text-indigo-800 transition-colors",children:[s.jsx(D.Z,{className:"w-3 h-3 mr-1"}),t("communication.copyText")]})]},i))})]},i))||[]}),s.jsx("div",{className:"mt-6 p-4 bg-blue-100 rounded-lg",children:(0,s.jsxs)("p",{className:"text-sm text-blue-800",children:[s.jsx("strong",{children:t("communication.usageTips")}),t("communication.usageAdvice")]})})]}),s.jsx("div",{className:"text-center",children:s.jsx("button",{onClick:J,className:"btn-secondary",children:t("navigation.retakeTest")})})]})}return(0,s.jsxs)("div",{className:"max-w-4xl mx-auto",children:[s.jsx(T.Z,{notifications:X,onRemove:ee}),W&&s.jsx(q.Z,{}),(0,s.jsxs)("div",{className:"mb-8 bg-white p-6 rounded-xl shadow-lg border border-purple-100",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[s.jsx("span",{className:"text-sm font-medium text-gray-700 bg-purple-50 px-3 py-1 rounded-full",children:"zh"===e?`第 ${I+1} 题，共 ${R} 题`:`Question ${I+1} of ${R}`}),(0,s.jsxs)("span",{className:"text-sm font-medium text-purple-600 bg-purple-50 px-3 py-1 rounded-full",children:[Math.round(G),"% ","zh"===e?"完成":"Complete"]})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-3 overflow-hidden",children:s.jsx("div",{className:"bg-gradient-to-r from-purple-500 to-purple-600 h-3 rounded-full transition-all duration-500 ease-out shadow-sm",style:{width:`${G}%`}})})]}),F&&(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg border border-purple-100 p-8 animate-fade-in",children:[(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-800 mb-3 leading-tight",children:F.title}),F.description&&s.jsx("p",{className:"text-gray-600 text-lg leading-relaxed",children:F.description})]}),s.jsx("div",{className:"mb-8",children:"scale"===F.type?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"px-4 pain-scale-container",children:[s.jsx("input",{type:"range",min:F.validation?.min||0,max:F.validation?.max||10,value:i[F.id]||0,onChange:e=>es(F.id,e.target.value),className:"w-full pain-scale cursor-pointer"}),(0,s.jsxs)("div",{className:"flex justify-between text-sm text-neutral-600 mt-2",children:[s.jsx("span",{className:"text-xs sm:text-sm",children:t("painScale.levels.none")}),s.jsx("span",{className:"text-xs sm:text-sm",children:t("painScale.levels.mild")}),s.jsx("span",{className:"text-xs sm:text-sm",children:t("painScale.levels.moderate")}),s.jsx("span",{className:"text-xs sm:text-sm",children:t("painScale.levels.severe")}),s.jsx("span",{className:"text-xs sm:text-sm",children:t("painScale.levels.extreme")})]})]}),s.jsx("div",{className:"text-center",children:(0,s.jsxs)("div",{className:"inline-flex items-center bg-gradient-to-r from-purple-100 via-purple-50 to-pink-100 px-8 py-4 rounded-2xl shadow-lg border border-purple-200",children:[s.jsx(c.Z,{className:"w-6 h-6 text-purple-600 mr-3"}),(0,s.jsxs)("span",{className:"text-xl font-bold text-purple-800",children:[t("painScale.title"),s.jsx("span",{className:"text-3xl font-extrabold text-purple-600 mx-2",children:i[F.id]||0}),(0,s.jsxs)("span",{className:"text-base font-medium text-purple-700 ml-2",children:["(",F.options.find(e=>e.value==(i[F.id]||0))?.label,")"]})]})]})}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-purple-50 to-purple-100 p-6 rounded-xl overflow-hidden border border-purple-200 shadow-sm",children:[(0,s.jsxs)("h4",{className:"font-semibold text-purple-800 mb-4 flex items-center",children:[s.jsx(f,{className:"w-5 h-5 mr-2"}),t("painScale.reference")]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm text-purple-700",children:[(0,s.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[s.jsx("span",{className:"w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,s.jsxs)("span",{children:[s.jsx("strong",{children:"0-2:"})," ",t("painScale.descriptions.0-2")]})]}),(0,s.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[s.jsx("span",{className:"w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,s.jsxs)("span",{children:[s.jsx("strong",{children:"3-4:"})," ",t("painScale.descriptions.3-4")]})]}),(0,s.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[s.jsx("span",{className:"w-2 h-2 bg-orange-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,s.jsxs)("span",{children:[s.jsx("strong",{children:"5-7:"})," ",t("painScale.descriptions.5-7")]})]}),(0,s.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[s.jsx("span",{className:"w-2 h-2 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,s.jsxs)("span",{children:[s.jsx("strong",{children:"8-10:"})," ",t("painScale.descriptions.8-10")]})]})]})]})]}):"multiple"===F.type?s.jsx("div",{className:"space-y-4",children:F.options.map(e=>{let t=(Array.isArray(i[F.id])?i[F.id]:i[F.id]?[i[F.id]]:[]).includes(String(e.value));return(0,s.jsxs)("label",{className:`block p-5 border-2 rounded-xl cursor-pointer transition-all duration-300 transform hover:-translate-y-1 ${t?"border-purple-500 bg-gradient-to-r from-purple-50 to-purple-100 shadow-lg":"border-gray-200 hover:border-purple-300 hover:shadow-md bg-white"}`,children:[s.jsx("input",{type:"checkbox",checked:t,onChange:()=>ea(F.id,String(e.value)),className:"sr-only"}),(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:`w-5 h-5 rounded border-2 mr-4 flex items-center justify-center ${t?"border-purple-500 bg-purple-500":"border-gray-300"}`,children:t&&s.jsx(h.Z,{className:"w-3 h-3 text-white"})}),s.jsx("span",{className:"text-gray-800 font-medium",children:e.label})]})]},e.value)})}):s.jsx("div",{className:"space-y-4",children:F.options.map(e=>(0,s.jsxs)("label",{className:`block p-5 border-2 rounded-xl cursor-pointer transition-all duration-300 transform hover:-translate-y-1 ${i[F.id]===e.value?"border-purple-500 bg-gradient-to-r from-purple-50 to-purple-100 shadow-lg":"border-gray-200 hover:border-purple-300 hover:shadow-md bg-white"}`,children:[s.jsx("input",{type:"radio",name:F.id,value:e.value,checked:i[F.id]===e.value,onChange:()=>es(F.id,e.value),className:"sr-only"}),(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx("div",{className:`w-5 h-5 rounded-full border-2 mr-4 flex items-center justify-center ${i[F.id]===e.value?"border-purple-500 bg-purple-500":"border-gray-300"}`,children:i[F.id]===e.value&&s.jsx("div",{className:"w-2 h-2 bg-white rounded-full"})}),s.jsx("span",{className:"text-gray-800 font-medium",children:e.label})]})]},e.value))})}),(0,s.jsxs)("div",{className:"flex justify-between items-center pt-6",children:[(0,s.jsxs)("button",{onClick:()=>{V()},disabled:0===I,className:"flex items-center px-6 py-3 bg-gray-100 text-gray-700 rounded-xl font-medium hover:bg-gray-200 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:bg-gray-100",children:[s.jsx(k.Z,{className:"w-5 h-5 mr-2"}),t("navigation.previous")]}),(0,s.jsxs)("button",{onClick:()=>{I>=R-1?Q()?et(t("messages.testComplete"),t("messages.testCompleteDesc")):ei(t("messages.testFailed"),t("messages.testFailedDesc")):Y()},disabled:!(()=>{if(!F)return!1;let e=en();return"multiple"===F.type||null!=e&&""!==e})(),className:"flex items-center px-8 py-3 bg-gradient-to-r from-purple-600 to-purple-700 text-white rounded-xl font-semibold hover:from-purple-700 hover:to-purple-800 transition-all duration-300 transform hover:-translate-y-1 hover:shadow-lg disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:transform-none disabled:hover:shadow-none",children:[I>=R-1?t("navigation.completeTest"):t("navigation.next"),s.jsx(w.Z,{className:"w-5 h-5 ml-2"})]})]})]}),$&&s.jsx("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:s.jsx("p",{className:"text-red-700",children:$})})]})}},5308:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>k});var s=i(10326),a=i(17577),n=i(23844),o=i(76464),r=i(76557);let l=(0,r.Z)("Plus",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"M12 5v14",key:"s699le"}]]);var c=i(29389),d=i(31540),m=i(91653);let u=e=>{let[t,i]=(0,a.useState)([]),[s,n]=(0,a.useState)({totalEntries:0,averagePain:0,maxPain:0,minPain:0,mostCommonSymptoms:[],mostEffectiveRemedies:[],painFrequency:{},trendDirection:"stable"}),[o,r]=(0,a.useState)(!0),[l,c]=(0,a.useState)(null),d=(0,m.I6)(e||"anonymous","pain_records");(0,a.useEffect)(()=>{(async()=>{try{r(!0),c(null);let e=(0,m.mO)(d);e&&Array.isArray(e)&&(i(e),n((0,m.i5)(e)))}catch(e){c("Failed to load data. Please refresh the page.")}finally{r(!1)}})()},[d]),(0,a.useEffect)(()=>{o||!(t.length>=0)||(0,m.RY)(d,t)||c("Failed to save data. Please try again.")},[t,d,o]),(0,a.useEffect)(()=>{n((0,m.i5)(t))},[t]);let u=(0,a.useCallback)(async e=>{try{c(null);let s=(0,m.Yp)(e);if(s.length>0)return{success:!1,errors:s};if(t.find(t=>t.date===e.date)){if(!0!==e.overwrite)return{success:!1,errors:[{field:"date",message:"An entry for this date already exists. Do you want to overwrite it?",code:"DUPLICATE_DATE"}]};i(t=>t.filter(t=>t.date!==e.date))}let a=new Date().toISOString(),n={id:`pain_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,...e,location:e.location||[],symptoms:e.symptoms||[],remedies:e.remedies||[],createdAt:a,updatedAt:a};return i(e=>[...e,n].sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())),{success:!0}}catch(e){return c("Failed to add entry. Please try again."),{success:!1,errors:[{field:"general",message:"Failed to add entry",code:"ADD_ERROR"}]}}},[t]),p=(0,a.useCallback)(async(e,s)=>{try{c(null);let a=t.find(t=>t.id===e);if(!a)return{success:!1,errors:[{field:"id",message:"Entry not found",code:"NOT_FOUND"}]};let n={...a,...s},o=(0,m.Yp)(n);if(o.length>0)return{success:!1,errors:o};if(s.date&&s.date!==a.date&&t.find(t=>t.id!==e&&t.date===s.date))return{success:!1,errors:[{field:"date",message:"An entry for this date already exists",code:"DUPLICATE_DATE"}]};let r={...a,...s,updatedAt:new Date().toISOString()};return i(t=>t.map(t=>t.id===e?r:t).sort((e,t)=>new Date(t.date).getTime()-new Date(e.date).getTime())),{success:!0}}catch(e){return c("Failed to update entry. Please try again."),{success:!1,errors:[{field:"general",message:"Failed to update entry",code:"UPDATE_ERROR"}]}}},[t]),h=(0,a.useCallback)(async e=>{try{if(c(null),!t.some(t=>t.id===e))return c("Entry not found"),!1;return i(t=>t.filter(t=>t.id!==e)),!0}catch(e){return c("Failed to delete entry. Please try again."),!1}},[t]),g=(0,a.useCallback)(async()=>{try{if(c(null),i([]),!(0,m.B$)(d))return c("Failed to clear data. Please try again."),!1;return!0}catch(e){return c("Failed to clear data. Please try again."),!1}},[d]),y=(0,a.useCallback)(e=>t.find(t=>t.id===e),[t]),x=(0,a.useCallback)((e,i)=>t.filter(t=>{let s=new Date(t.date),a=new Date(e),n=new Date(i);return s>=a&&s<=n}),[t]),b=(0,a.useCallback)(e=>{try{if("json"===e){let e=JSON.stringify(t,null,2),i=new Blob([e],{type:"application/json"}),s=URL.createObjectURL(i),a=document.createElement("a");a.href=s,a.download=`pain-tracker-data-${new Date().toISOString().split("T")[0]}.json`,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s)}else"csv"===e&&b("json")}catch(e){c("Failed to export data. Please try again.")}},[t]);return{entries:t,statistics:s,isLoading:o,error:l,addEntry:u,updateEntry:p,deleteEntry:h,clearAllEntries:g,getEntry:y,getEntriesInRange:x,exportData:b,refreshData:(0,a.useCallback)(()=>{let e=(0,m.mO)(d);e&&Array.isArray(e)&&i(e)},[d]),setError:c}};var p=i(44702);let h=(0,r.Z)("Calendar",[["rect",{width:"18",height:"18",x:"3",y:"4",rx:"2",ry:"2",key:"eu3xkr"}],["line",{x1:"16",x2:"16",y1:"2",y2:"6",key:"m3sa8f"}],["line",{x1:"8",x2:"8",y1:"2",y2:"6",key:"18kwsl"}],["line",{x1:"3",x2:"21",y1:"10",y2:"10",key:"xt86sb"}]]);var g=i(66697),y=i(48998),x=i(77636),b=i(67427);let f=(0,r.Z)("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);var v=i(36283),w=i(62438),j=i(7326);let N=({initialData:e,onSubmit:t,onCancel:i,isLoading:o=!1,locale:r})=>{let l=(0,n.useTranslations)("painTracker"),[c,d]=(0,a.useState)({date:e?.date||(0,m.mn)(new Date),painLevel:e?.painLevel||1,duration:e?.duration||void 0,location:e?.location||[],menstrualStatus:e?.menstrualStatus||"other",symptoms:e?.symptoms||[],remedies:e?.remedies||[],effectiveness:e?.effectiveness||void 0,notes:e?.notes||""}),[u,p]=(0,a.useState)({}),[N,D]=(0,a.useState)(!1),[k,C]=(0,a.useState)({}),S=w.DI[r]||w.DI.en,_=w.C7[r]||w.C7.en,T=w.Ju[r]||w.Ju.en,q=w.Yw[r]||w.Yw.en,P=w.E2[r]||w.E2.en,A=w.bp[r]||w.bp.en,E=w.RZ[r]||w.RZ.en,M=(e,t)=>{d(i=>({...i,[e]:t})),C(t=>({...t,[e]:!0})),u[e]&&p(t=>({...t,[e]:""}))},z=(e,t)=>{let i=c[e];M(e,i.includes(t)?i.filter(e=>e!==t):[...i,t])},Z=()=>{let e={};if(c.date){let t=new Date(c.date),i=new Date;i.setHours(23,59,59,999),t>i&&(e.date=E.futureDate)}else e.date=E.required;return(c.painLevel<1||c.painLevel>10)&&(e.painLevel=E.painLevelRange),void 0!==c.duration&&(c.duration<0||c.duration>1440)&&(e.duration=E.durationRange),void 0!==c.effectiveness&&(c.effectiveness<1||c.effectiveness>5)&&(e.effectiveness=E.effectivenessRange),c.notes&&c.notes.length>500&&(e.notes=E.notesLength),p(e),0===Object.keys(e).length},B=async(e,i=!1)=>{if(e.preventDefault(),Z())try{let e=i?{...c,overwrite:!0}:c,s=await t(e);if(s.success)d({date:(0,m.mn)(new Date),painLevel:1,duration:void 0,location:[],menstrualStatus:"other",symptoms:[],remedies:[],effectiveness:void 0,notes:""}),p({}),D(!1);else if(s.errors){let e={};s.errors.forEach(t=>{e[t.field]=t.message}),p(e),s.errors[0]?.code==="DUPLICATE_DATE"&&D(!0)}}catch(e){p({general:E.storageError})}},L=P.find(e=>e.value===c.painLevel);return(0,s.jsxs)("form",{onSubmit:B,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[s.jsx(h,{className:"w-4 h-4 mr-2"}),l("form.date")]}),s.jsx("input",{type:"date",value:c.date,onChange:e=>M("date",e.target.value),className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 ${u.date?"border-red-500":"border-gray-300"}`,max:(0,m.mn)(new Date)}),u.date&&s.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.date}),N&&s.jsx("div",{className:"mt-3 p-4 bg-yellow-50 border border-yellow-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx("div",{className:"flex-shrink-0",children:s.jsx("svg",{className:"h-5 w-5 text-yellow-400",viewBox:"0 0 20 20",fill:"currentColor",children:s.jsx("path",{fillRule:"evenodd",d:"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",clipRule:"evenodd"})})}),(0,s.jsxs)("div",{className:"ml-3 flex-1",children:[s.jsx("h3",{className:"text-sm font-medium text-yellow-800",children:"zh"===r?"该日期已有记录":"Entry exists for this date"}),s.jsx("p",{className:"mt-1 text-sm text-yellow-700",children:"zh"===r?"该日期已经有疼痛记录了。您想要覆盖现有记录吗？":"There is already a pain entry for this date. Do you want to overwrite the existing entry?"}),(0,s.jsxs)("div",{className:"mt-3 flex space-x-3",children:[s.jsx("button",{type:"button",onClick:e=>{B(e,!0)},className:"bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700 transition-colors",children:"zh"===r?"覆盖":"Overwrite"}),s.jsx("button",{type:"button",onClick:()=>{D(!1),p({})},className:"bg-gray-300 text-gray-700 px-3 py-1 rounded text-sm hover:bg-gray-400 transition-colors",children:"zh"===r?"取消":"Cancel"})]})]})]})})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[s.jsx(g.Z,{className:"w-4 h-4 mr-2"}),l("form.painLevel")," (",c.painLevel,"/10)"]}),(0,s.jsxs)("div",{className:"space-y-4 pain-scale-container",children:[s.jsx("input",{type:"range",min:"1",max:"10",value:c.painLevel,onChange:e=>M("painLevel",parseInt(e.target.value)),className:"w-full pain-scale cursor-pointer"}),(0,s.jsxs)("div",{className:"flex justify-between text-sm text-neutral-600 mt-2",children:[s.jsx("span",{className:"text-xs sm:text-sm",children:"1"}),s.jsx("span",{className:"text-xs sm:text-sm",children:"3"}),s.jsx("span",{className:"text-xs sm:text-sm",children:"5"}),s.jsx("span",{className:"text-xs sm:text-sm",children:"7"}),s.jsx("span",{className:"text-xs sm:text-sm",children:"10"})]}),L&&(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"inline-flex items-center bg-gradient-to-r from-pink-100 via-pink-50 to-purple-100 px-6 py-3 rounded-xl shadow-lg border border-pink-200",children:(0,s.jsxs)("span",{className:"text-lg font-bold text-pink-800",children:["疼痛程度：",s.jsx("span",{className:"text-2xl font-extrabold text-pink-600 mx-2",children:c.painLevel}),(0,s.jsxs)("span",{className:"text-base font-medium text-pink-700",children:["(",L.label,")"]})]})}),s.jsx("p",{className:"text-sm text-gray-600 mt-2",children:L.description})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[s.jsx(y.Z,{className:"w-4 h-4 mr-2"}),l("form.duration")," (",l("form.optional"),")"]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[s.jsx("input",{type:"number",min:"0",max:"1440",value:c.duration||"",onChange:e=>M("duration",e.target.value?parseInt(e.target.value):void 0),className:`flex-1 px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 ${u.duration?"border-red-500":"border-gray-300"}`,placeholder:"0"}),s.jsx("span",{className:"text-sm text-gray-500",children:l("form.minutes")})]}),u.duration&&s.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.duration})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-3",children:[s.jsx(x.Z,{className:"w-4 h-4 mr-2"}),l("form.location")]}),s.jsx("div",{className:"grid grid-cols-2 gap-2",children:S.map(e=>(0,s.jsxs)("button",{type:"button",onClick:()=>z("location",e.value),className:`p-3 text-left border rounded-lg transition-colors ${c.location.includes(e.value)?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 hover:border-gray-400"}`,children:[s.jsx("span",{className:"text-lg mr-2",children:e.icon}),s.jsx("span",{className:"text-sm",children:e.label})]},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-3",children:[s.jsx(b.Z,{className:"w-4 h-4 mr-2"}),l("form.menstrualStatus")]}),s.jsx("div",{className:"grid grid-cols-1 gap-2",children:q.map(e=>(0,s.jsxs)("button",{type:"button",onClick:()=>M("menstrualStatus",e.value),className:`p-3 text-left border rounded-lg transition-colors ${c.menstrualStatus===e.value?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 hover:border-gray-400"}`,children:[s.jsx("span",{className:"text-lg mr-2",children:e.icon}),s.jsx("span",{className:"text-sm",children:e.label})]},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-3",children:[s.jsx(g.Z,{className:"w-4 h-4 mr-2"}),l("form.symptoms")," (",l("form.optional"),")"]}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-48 overflow-y-auto",children:_.map(e=>(0,s.jsxs)("button",{type:"button",onClick:()=>z("symptoms",e.value),className:`p-3 sm:p-2 text-left border rounded-lg transition-colors text-sm mobile-touch-target ${c.symptoms.includes(e.value)?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 hover:border-gray-400 active:bg-gray-50"}`,children:[s.jsx("span",{className:"text-base mr-2",children:e.icon}),s.jsx("span",{children:e.label})]},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-3",children:[s.jsx(b.Z,{className:"w-4 h-4 mr-2"}),l("form.remedies")," (",l("form.optional"),")"]}),s.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-2 max-h-48 overflow-y-auto",children:T.map(e=>(0,s.jsxs)("button",{type:"button",onClick:()=>z("remedies",e.value),className:`p-3 sm:p-2 text-left border rounded-lg transition-colors text-sm mobile-touch-target ${c.remedies.includes(e.value)?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 hover:border-gray-400 active:bg-gray-50"}`,children:[s.jsx("span",{className:"text-base mr-2",children:e.icon}),s.jsx("span",{children:e.label})]},e.value))})]}),c.remedies.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-3",children:[s.jsx(f,{className:"w-4 h-4 mr-2"}),l("form.effectiveness")," (",l("form.optional"),")"]}),s.jsx("div",{className:"grid grid-cols-5 gap-2",children:A.map(e=>(0,s.jsxs)("button",{type:"button",onClick:()=>M("effectiveness",e.value),className:`p-3 text-center border rounded-lg transition-colors ${c.effectiveness===e.value?"border-pink-500 bg-pink-50 text-pink-700":"border-gray-300 hover:border-gray-400"}`,children:[s.jsx("div",{className:"text-lg mb-1",children:e.icon}),s.jsx("div",{className:"text-xs",children:e.label})]},e.value))}),u.effectiveness&&s.jsx("p",{className:"mt-1 text-sm text-red-600",children:u.effectiveness})]}),(0,s.jsxs)("div",{children:[(0,s.jsxs)("label",{className:"flex items-center text-sm font-medium text-gray-700 mb-2",children:[s.jsx(v.Z,{className:"w-4 h-4 mr-2"}),l("form.notes")," (",l("form.optional"),")"]}),s.jsx("textarea",{value:c.notes,onChange:e=>M("notes",e.target.value),rows:3,maxLength:500,className:`w-full px-3 py-2 border rounded-lg focus:ring-2 focus:ring-pink-500 focus:border-pink-500 resize-none ${u.notes?"border-red-500":"border-gray-300"}`,placeholder:l("form.notesPlaceholder")}),(0,s.jsxs)("div",{className:"flex justify-between mt-1",children:[u.notes&&s.jsx("p",{className:"text-sm text-red-600",children:u.notes}),(0,s.jsxs)("p",{className:"text-xs text-gray-500 ml-auto",children:[c.notes?.length||0,"/500"]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-3 sm:space-x-3 pt-4",children:[s.jsx("button",{type:"submit",disabled:o,className:"flex-1 bg-gradient-to-r from-pink-600 to-purple-600 text-white py-3 px-4 rounded-lg font-medium hover:from-pink-700 hover:to-purple-700 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed transition-colors mobile-touch-target order-1",children:o?(0,s.jsxs)("div",{className:"flex items-center justify-center",children:[s.jsx(j.Z,{size:"sm",color:"white",className:"mr-2"}),l("form.saving")]}):l("form.save")}),i&&s.jsx("button",{type:"button",onClick:i,className:"w-full sm:w-auto px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-gray-500 focus:ring-offset-2 transition-colors mobile-touch-target order-2",children:l("form.cancel")})]}),u.general&&s.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-lg",children:s.jsx("p",{className:"text-sm text-red-600",children:u.general})})]})};var D=i(29152);function k({locale:e}){let t=(0,n.useTranslations)("painTracker"),[i,r]=(0,a.useState)("overview"),[m,h]=(0,a.useState)(!1),[g,y]=(0,a.useState)(null),{entries:x,statistics:b,isLoading:f,error:v,addEntry:w,updateEntry:k,deleteEntry:C,setError:S}=u(),{notifications:_,removeNotification:T,addSuccessNotification:q,addErrorNotification:P}=(0,p.z)(),A=async e=>{h(!0);try{let i=await w(e);if(i.success)return q(t("messages.saveSuccess"),t("form.save")),r("entries"),{success:!0};return i}catch(e){return P(t("messages.saveError"),t("messages.validationError")),{success:!1}}finally{h(!1)}},E=async e=>{if(!g)return{success:!1};h(!0);try{let i=await k(g.id,e);if(i.success)return q(t("messages.updateSuccess"),t("form.save")),y(null),r("entries"),{success:!0};return i}catch(e){return P(t("messages.updateError"),t("messages.validationError")),{success:!1}}finally{h(!1)}},M=async e=>{if(window.confirm(t("messages.confirmDelete")))try{await C(e)?q(t("messages.deleteSuccess"),t("entries.delete")):P(t("messages.deleteError"),t("messages.validationError"))}catch(e){P(t("messages.deleteError"),t("messages.validationError"))}},z=e=>{y({id:e.id,data:e}),r("add")},Z=[{id:"overview",label:t("navigation.overview"),icon:o.Z},{id:"add",label:t("navigation.addEntry"),icon:l},{id:"entries",label:t("navigation.viewEntries"),icon:c.Z},{id:"statistics",label:t("navigation.statistics"),icon:o.Z},{id:"export",label:t("navigation.export"),icon:d.Z}];return(0,s.jsxs)("div",{className:"bg-gradient-to-br from-pink-50 via-purple-50 to-blue-50 rounded-xl p-8",children:[(0,s.jsxs)(j.f,{isLoading:f,message:"Loading...",children:[s.jsx("div",{className:"mb-8",children:s.jsx("div",{className:"bg-white rounded-lg shadow-sm p-1",children:s.jsx("nav",{className:"flex space-x-1",children:Z.map(e=>{let t=e.icon;return(0,s.jsxs)("button",{onClick:()=>r(e.id),className:`flex items-center px-4 py-2 rounded-md text-sm font-medium transition-colors ${i===e.id?"bg-gradient-to-r from-pink-600 to-purple-600 text-white":"text-gray-600 hover:text-gray-900 hover:bg-gray-100"}`,children:[s.jsx(t,{className:"w-4 h-4 mr-2"}),e.label]},e.id)})})})}),(0,s.jsxs)("div",{children:["overview"===i&&(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:t("statistics.overview")}),0===x.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx("div",{className:"text-gray-400 mb-4",children:s.jsx(o.Z,{className:"w-16 h-16 mx-auto"})}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:t("entries.noEntries")}),s.jsx("p",{className:"text-gray-600 mb-6",children:t("entries.noEntriesDescription")}),s.jsx("button",{onClick:()=>r("add"),className:"bg-gradient-to-r from-pink-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-pink-700 hover:to-purple-700 transition-colors",children:t("entries.addFirst")})]}):(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-pink-100 to-purple-100 p-6 rounded-lg",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:t("statistics.totalEntries")}),s.jsx("p",{className:"text-3xl font-bold text-gray-900",children:b.totalEntries})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-blue-100 to-indigo-100 p-6 rounded-lg",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:t("statistics.averagePain")}),(0,s.jsxs)("p",{className:"text-3xl font-bold text-gray-900",children:[b.averagePain,"/10"]})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-green-100 to-teal-100 p-6 rounded-lg",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:t("statistics.trendDirection")}),s.jsx("p",{className:"text-lg font-semibold text-gray-900",children:t(`statistics.${b.trendDirection}`)})]})]})]}),"add"===i&&(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:g?t("form.editTitle"):t("form.title")}),s.jsx(N,{onSubmit:g?E:A,onCancel:g?()=>{y(null),r("entries")}:()=>r("overview"),isLoading:m,locale:e,initialData:g?.data})]}),"entries"===i&&(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:t("entries.title")}),0===x.length?(0,s.jsxs)("div",{className:"text-center py-12",children:[s.jsx("div",{className:"text-gray-400 mb-4",children:s.jsx(c.Z,{className:"w-16 h-16 mx-auto"})}),s.jsx("h3",{className:"text-lg font-medium text-gray-900 mb-2",children:t("entries.noEntries")}),s.jsx("p",{className:"text-gray-600 mb-6",children:t("entries.noEntriesDescription")}),s.jsx("button",{onClick:()=>r("add"),className:"bg-gradient-to-r from-pink-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-pink-700 hover:to-purple-700 transition-colors",children:t("entries.addFirst")})]}):s.jsx("div",{className:"space-y-4",children:x.map(i=>s.jsx("div",{className:"border border-gray-200 rounded-lg p-4",children:(0,s.jsxs)("div",{className:"flex justify-between items-start",children:[(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"font-medium text-gray-900",children:new Date(i.date).toLocaleDateString(e)}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[t("entries.painIntensity"),": ",i.painLevel,"/10"]}),i.duration&&(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[t("entries.duration"),": ",i.duration," ",t("entries.minutes")]})]}),(0,s.jsxs)("div",{className:"flex space-x-2",children:[s.jsx("button",{onClick:()=>z(i),className:"text-blue-600 hover:text-blue-800 text-sm font-medium transition-colors",children:t("entries.edit")}),s.jsx("button",{onClick:()=>M(i.id),className:"text-red-600 hover:text-red-800 text-sm font-medium transition-colors",children:t("entries.delete")})]})]})},i.id))})]}),"statistics"===i&&(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:t("statistics.title")}),s.jsx("p",{className:"text-gray-600",children:t("statistics.inDevelopment")})]}),"export"===i&&(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8",children:[s.jsx("h2",{className:"text-2xl font-semibold text-gray-800 mb-6",children:t("export.title")}),s.jsx("p",{className:"text-gray-600",children:t("export.inDevelopment")})]})]}),v&&s.jsx("div",{className:"mt-4",children:(0,s.jsxs)("div",{className:"bg-red-50 border border-red-200 rounded-lg p-4",children:[s.jsx("p",{className:"text-red-600",children:v}),s.jsx("button",{onClick:()=>S(null),className:"mt-2 text-sm text-red-600 hover:text-red-800",children:t("messages.close")})]})})]}),s.jsx(D.Z,{notifications:_,onRemove:T})]})}},89124:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>o});var s=i(10326),a=i(17577),n=i(98415);let o=({locale:e="zh"})=>{let[t,i]=(0,a.useState)(""),[o,r]=(0,a.useState)(""),[l,c]=(0,a.useState)([]),[d,m]=(0,a.useState)(null),[u,p]=(0,a.useState)(!1),{t:h}=(0,n.A0)("periodPainAssessment"),g=(e,t)=>{t?c([...l,e]):c(l.filter(t=>t!==e))};return(0,s.jsxs)("div",{className:"max-w-4xl mx-auto p-4 sm:p-6 bg-white rounded-lg shadow-lg mobile-safe-area",children:[(0,s.jsxs)("div",{className:"text-center mb-6 sm:mb-8",children:[s.jsx("h2",{className:"text-2xl sm:text-3xl font-bold text-primary mb-3 sm:mb-4",children:h("title")}),s.jsx("p",{className:"text-gray-600 text-sm sm:text-base px-2",children:h("subtitle")})]}),u?(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:`p-6 rounded-lg border-l-4 ${d?.severity==="high"?"border-red-500 bg-red-50":d?.severity==="medium"?"border-yellow-500 bg-yellow-50":"border-green-500 bg-green-50"}`,children:[s.jsx("h3",{className:`text-xl font-semibold mb-3 ${d?.severity==="high"?"text-red-700":d?.severity==="medium"?"text-yellow-700":"text-green-700"}`,children:h("results.title")}),s.jsx("p",{className:"text-gray-700 leading-relaxed",children:d?.advice})]}),d?.needConsult&&s.jsx("div",{className:"bg-blue-50 p-4 rounded-lg border border-blue-200",children:s.jsx("p",{className:"text-blue-700 font-medium text-sm",children:h("results.consultAdvice")})}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-center gap-3 sm:gap-4",children:[s.jsx("button",{onClick:()=>{i(""),r(""),c([]),m(null),p(!1)},className:"btn-outline px-4 sm:px-6 py-3 font-semibold mobile-touch-target order-2 sm:order-1",children:h("actions.reset")}),s.jsx("a",{href:`/${e}/teen-health`,className:"btn-secondary px-4 sm:px-6 py-3 font-semibold text-center mobile-touch-target order-1 sm:order-2",children:h("actions.moreInfo")})]})]}):(0,s.jsxs)("div",{className:"space-y-6 sm:space-y-8",children:[(0,s.jsxs)("div",{className:"form-group",children:[s.jsx("h3",{className:"text-lg sm:text-xl font-semibold mb-3 sm:mb-4",children:h("questions.intensity.title")}),s.jsx("div",{className:"space-y-2 sm:space-y-3",children:[{value:"mild",label:h("questions.intensity.options.mild")},{value:"moderate",label:h("questions.intensity.options.moderate")},{value:"severe",label:h("questions.intensity.options.severe")}].map(e=>(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer mobile-touch-target p-2 sm:p-1 rounded-lg hover:bg-gray-50 transition-colors",children:[s.jsx("input",{type:"radio",name:"intensity",value:e.value,checked:t===e.value,onChange:e=>i(e.target.value),className:"w-5 h-5 sm:w-4 sm:h-4 text-primary"}),s.jsx("span",{className:"text-gray-700 text-sm sm:text-base",children:e.label})]},e.value))})]}),(0,s.jsxs)("div",{className:"form-group",children:[s.jsx("h3",{className:"text-lg sm:text-xl font-semibold mb-3 sm:mb-4",children:h("questions.onset.title")}),s.jsx("div",{className:"space-y-2 sm:space-y-3",children:[{value:"early",label:h("questions.onset.options.recent")},{value:"late",label:h("questions.onset.options.later")}].map(e=>(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer mobile-touch-target p-2 sm:p-1 rounded-lg hover:bg-gray-50 transition-colors",children:[s.jsx("input",{type:"radio",name:"onset",value:e.value,checked:o===e.value,onChange:e=>r(e.target.value),className:"w-5 h-5 sm:w-4 sm:h-4 text-primary"}),s.jsx("span",{className:"text-gray-700 text-sm sm:text-base",children:e.label})]},e.value))})]}),(0,s.jsxs)("div",{children:[s.jsx("h3",{className:"text-xl font-semibold mb-4",children:h("questions.symptoms.title")}),s.jsx("div",{className:"space-y-3",children:[{value:"fever",label:h("questions.symptoms.options.fever")},{value:"severe_vomiting",label:h("questions.symptoms.options.vomiting")},{value:"fainting",label:h("questions.symptoms.options.dizziness")},{value:"abnormal_bleeding",label:h("questions.symptoms.options.bleeding")},{value:"non_period_pain",label:h("questions.symptoms.options.nonMenstrual")}].map(e=>(0,s.jsxs)("label",{className:"flex items-center space-x-3 cursor-pointer",children:[s.jsx("input",{type:"checkbox",value:e.value,checked:l.includes(e.value),onChange:t=>g(e.value,t.target.checked),className:"w-4 h-4 text-primary"}),s.jsx("span",{className:"text-gray-700",children:e.label})]},e.value))})]}),s.jsx("div",{className:"text-center pt-4",children:s.jsx("button",{onClick:()=>{if(!t||!o){alert(h("results.validationMessage"));return}let e="",i=!1,s="low";l.length>0?(e=h("results.assessments.severe_symptoms"),i=!0,s="high"):"severe"===t?(e="late"===o?h("results.assessments.severe_late"):h("results.assessments.severe_early"),i=!0,s="high"):"moderate"===t&&"late"===o?(e=h("results.assessments.moderate_late"),i=!0,s="medium"):(e=h("results.assessments.normal"),i=!1,s="low"),m({advice:e,needConsult:i,severity:s}),p(!0)},className:"btn-primary text-base sm:text-lg font-bold w-full sm:w-auto sm:min-w-[200px] mobile-touch-target",children:h("actions.assess")})})]}),s.jsx("div",{className:"mt-8 text-center text-sm text-gray-500",children:s.jsx("p",{children:"⚠️ 本工具仅供参考，不能替代专业医疗诊断。如有疑虑，请咨询医生。"})})]})}},86520:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>b});var s=i(10326),a=i(17577);let n=(0,i(76557).Z)("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]);var o=i(67427),r=i(54659),l=i(66697),c=i(94893),d=i(86333),m=i(24230),u=i(37202),p=i(85353),h=i(44702),g=i(29152),y=i(23844);function x(e,t){let i={zh:{title:"标题",description:"描述",submit:"提交",cancel:"取消",save:"保存",delete:"删除",edit:"编辑",add:"添加",loading:"加载中...",error:"错误",success:"成功",warning:"警告",info:"信息",close:"关闭",open:"打开",start:"开始",stop:"停止",next:"下一步",previous:"上一步",finish:"完成",retry:"重试"},en:{title:"Title",description:"Description",submit:"Submit",cancel:"Cancel",save:"Save",delete:"Delete",edit:"Edit",add:"Add",loading:"Loading...",error:"Error",success:"Success",warning:"Warning",info:"Information",close:"Close",open:"Open",start:"Start",stop:"Stop",next:"Next",previous:"Previous",finish:"Finish",retry:"Retry"}},s=i[t]||i.en,a=e.split(".").pop()?.toLowerCase()||"";return s[a]?s[a]:a.replace(/([A-Z])/g," $1").replace(/[_-]/g," ").replace(/\b\w/g,e=>e.toUpperCase()).trim()||("zh"===t?"未知":"Unknown")}function b({locale:e}){let{t}=function(e){let t=(0,y.useTranslations)(e),i=(0,y.useLocale)();return{t:(s,a,n)=>{try{let o=t(s,a),r=e?`${e}.${s}`:s;if(o===r||o===s||o.includes(r)){if(n)return n;return x(s,i)}return o}catch(e){return n||x(s,i)}},locale:i,isZh:"zh"===i,isEn:"en"===i}}("painTracker.assessment"),[i,b]=(0,a.useState)({}),{currentSession:f,currentQuestion:v,currentQuestionIndex:w,totalQuestions:j,progress:N,isComplete:D,result:k,isLoading:C,error:S,startAssessment:_,answerQuestion:T,goToPreviousQuestion:q,goToNextQuestion:P,completeAssessment:A,resetAssessment:E}=(0,p.o)(),{notifications:M,removeNotification:z,addSuccessNotification:Z,addErrorNotification:B}=(0,h.z)(),L=e=>{v&&(b(t=>({...t,[v.id]:e})),T({questionId:v.id,value:e,timestamp:new Date().toISOString()}))},F=()=>{w>=j-1?A(t)?(Z(t("messages.assessmentComplete"),t("messages.assessmentCompleteDesc")),setTimeout(()=>{},100)):B(t("messages.assessmentFailed"),t("messages.assessmentFailedDesc")):P()};return f?k?(0,s.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8 max-w-4xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-green-600 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(r.Z,{className:"w-8 h-8 text-white"})}),s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:t("result.title",{},"en"===e?"Assessment Results":"评估结果")})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"bg-gradient-to-r from-blue-100 to-purple-100 p-6 rounded-lg text-center",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:t("result.yourScore",{},"en"===e?"Your Score":"您的得分")}),(0,s.jsxs)("p",{className:"text-3xl font-bold text-gray-900",children:[k.score,"/",k.maxScore]}),(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:[Math.round(k.percentage),"%"]})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-green-100 to-teal-100 p-6 rounded-lg text-center",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:t("result.severity")}),s.jsx("p",{className:"text-xl font-bold text-gray-900",children:t(`severity.${k.severity}`)})]}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-yellow-100 to-orange-100 p-6 rounded-lg text-center",children:[s.jsx("h3",{className:"text-sm font-medium text-gray-600 mb-2",children:t("result.type")}),s.jsx("p",{className:"text-xl font-bold text-gray-900",children:t(`severity.${k.type}`)})]})]}),(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:t("result.summary")}),s.jsx("div",{className:"bg-gray-50 rounded-lg p-6",children:s.jsx("p",{className:"text-gray-700",children:k.message})})]}),(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("h3",{className:"text-xl font-semibold text-gray-900 mb-4",children:t("result.recommendations")}),s.jsx("div",{className:"space-y-4",children:k.recommendations.map(e=>(0,s.jsxs)("div",{className:"border border-gray-200 rounded-lg p-6",children:[(0,s.jsxs)("div",{className:"flex items-start justify-between mb-3",children:[s.jsx("h4",{className:"text-lg font-semibold text-gray-900",children:e.title}),s.jsx("span",{className:`px-3 py-1 rounded-full text-xs font-medium ${"high"===e.priority?"bg-red-100 text-red-800":"medium"===e.priority?"bg-yellow-100 text-yellow-800":"bg-green-100 text-green-800"}`,children:t(`priority.${e.priority}`)})]}),s.jsx("p",{className:"text-gray-600 mb-3",children:e.description}),(0,s.jsxs)("p",{className:"text-sm text-gray-500 mb-3",children:[s.jsx("strong",{children:t("result.timeframe")})," ",e.timeframe]}),e.actionSteps&&(0,s.jsxs)("div",{children:[s.jsx("h5",{className:"font-medium text-gray-900 mb-2",children:t("result.actionSteps")}),s.jsx("ul",{className:"list-disc list-inside text-sm text-gray-600 space-y-1",children:(Array.isArray(e.actionSteps)?e.actionSteps:"string"==typeof e.actionSteps?[e.actionSteps]:[]).map((e,t)=>s.jsx("li",{children:e},t))})]})]},e.id))})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[s.jsx("button",{onClick:E,className:"px-6 py-3 border border-gray-300 text-gray-700 rounded-lg font-medium hover:bg-gray-50 transition-colors",children:t("result.retakeAssessment")}),s.jsx("button",{onClick:()=>Z(t("messages.resultsSaved"),t("messages.resultsSavedDesc")),className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 transition-colors",children:t("result.saveResults")})]})]}),s.jsx(g.Z,{notifications:M,onRemove:z})]}):(0,s.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-4 sm:p-6 lg:p-8 mobile-safe-area",children:[(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-4 sm:p-6 lg:p-8 max-w-3xl mx-auto",children:[(0,s.jsxs)("div",{className:"mb-6 sm:mb-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("span",{className:"text-xs sm:text-sm font-medium text-gray-600",children:t("progress.questionOf",{current:Math.min(w+1,j),total:j})}),(0,s.jsxs)("span",{className:"text-xs sm:text-sm font-medium text-gray-600",children:[Math.round(Math.min(N,100)),"%"]})]}),s.jsx("div",{className:"w-full bg-gray-200 rounded-full h-2 sm:h-3",children:s.jsx("div",{className:"bg-gradient-to-r from-blue-600 to-purple-600 h-2 sm:h-3 rounded-full transition-all duration-300",style:{width:`${N}%`}})})]}),v&&(0,s.jsxs)("div",{className:"mb-6 sm:mb-8",children:[s.jsx("h2",{className:"text-lg sm:text-xl lg:text-2xl font-semibold text-gray-900 mb-3 sm:mb-4 leading-tight",children:v.title}),v.description&&s.jsx("p",{className:"text-sm sm:text-base text-gray-600 mb-4 sm:mb-6 leading-relaxed",children:v.description}),(0,s.jsxs)("div",{className:"space-y-2 sm:space-y-3",children:["single"===v.type&&v.options&&s.jsx("div",{className:"space-y-2 sm:space-y-3",children:v.options.map(e=>(0,s.jsxs)("label",{className:`flex items-center p-3 sm:p-4 border rounded-lg cursor-pointer transition-colors mobile-touch-target ${i[v.id]===e.value?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-gray-400 active:bg-gray-50"}`,children:[s.jsx("input",{type:"radio",name:v.id,value:e.value,checked:i[v.id]===e.value,onChange:e=>L(e.target.value),className:"sr-only"}),s.jsx("div",{className:`w-5 h-5 sm:w-4 sm:h-4 rounded-full border-2 mr-3 flex-shrink-0 ${i[v.id]===e.value?"border-blue-500 bg-blue-500":"border-gray-300"}`,children:i[v.id]===e.value&&s.jsx("div",{className:"w-2.5 h-2.5 sm:w-2 sm:h-2 bg-white rounded-full mx-auto mt-0.5"})}),e.icon&&s.jsx("span",{className:"text-lg sm:text-base mr-2 sm:mr-3 flex-shrink-0",children:e.icon}),s.jsx("span",{className:"text-sm sm:text-base text-gray-900 leading-relaxed",children:e.label})]},e.value))}),"multiple"===v.type&&v.options&&s.jsx("div",{className:"space-y-2 sm:space-y-3",children:v.options.map(e=>{let t=Array.isArray(i[v.id])&&i[v.id].includes(e.value);return(0,s.jsxs)("label",{className:`flex items-center p-3 sm:p-4 border rounded-lg cursor-pointer transition-colors mobile-touch-target ${t?"border-blue-500 bg-blue-50":"border-gray-300 hover:border-gray-400 active:bg-gray-50"}`,children:[s.jsx("input",{type:"checkbox",checked:t,onChange:t=>{let s;let a=i[v.id]||[],n="none"===e.value||"no_treatment"===e.value;if(a.includes("none")||a.includes("no_treatment"),n)s=t.target.checked?[String(e.value)]:[];else{let i=a.filter(e=>"none"!==e&&"no_treatment"!==e);s=t.target.checked?[...i,e.value]:i.filter(t=>t!==e.value)}L(s)},className:"sr-only"}),s.jsx("div",{className:`w-5 h-5 sm:w-4 sm:h-4 rounded border-2 mr-3 flex items-center justify-center flex-shrink-0 ${t?"border-blue-500 bg-blue-500":"border-gray-300"}`,children:t&&s.jsx(r.Z,{className:"w-3.5 h-3.5 sm:w-3 sm:h-3 text-white"})}),e.icon&&s.jsx("span",{className:"text-lg sm:text-base mr-2 sm:mr-3 flex-shrink-0",children:e.icon}),s.jsx("span",{className:"text-sm sm:text-base text-gray-900 leading-relaxed",children:e.label})]},e.value)})}),"scale"===v.type&&(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"px-4 pain-scale-container",children:[s.jsx("input",{type:"range",min:v.validation?.min||1,max:v.validation?.max||10,value:i[v.id]||v.validation?.min||1,onChange:e=>L(parseInt(e.target.value)),className:"w-full pain-scale cursor-pointer"}),(0,s.jsxs)("div",{className:"flex justify-between text-sm text-neutral-600 mt-2",children:[s.jsx("span",{className:"text-xs sm:text-sm",children:t("painScale.levels.none",{},"en"===e?"None":"无痛")}),s.jsx("span",{className:"text-xs sm:text-sm",children:t("painScale.levels.mild",{},"en"===e?"Mild":"轻微")}),s.jsx("span",{className:"text-xs sm:text-sm",children:t("painScale.levels.moderate",{},"en"===e?"Moderate":"中等")}),s.jsx("span",{className:"text-xs sm:text-sm",children:t("painScale.levels.severe",{},"en"===e?"Severe":"严重")}),s.jsx("span",{className:"text-xs sm:text-sm",children:t("painScale.levels.extreme",{},"en"===e?"Extreme":"极重")})]})]}),s.jsx("div",{className:"text-center",children:s.jsx("div",{className:"inline-flex items-center bg-gradient-to-r from-blue-100 via-blue-50 to-purple-100 px-8 py-4 rounded-2xl shadow-lg border border-blue-200",children:(0,s.jsxs)("span",{className:"text-xl font-bold text-blue-800",children:[t("painScale.title",{},"en"===e?"Pain Level: ":"疼痛程度："),s.jsx("span",{className:"text-3xl font-extrabold text-blue-600 mx-2",children:i[v.id]||v.validation?.min||1}),(0,s.jsxs)("span",{className:"text-base font-medium text-blue-700 ml-2",children:["(",(()=>{let s=i[v.id]||v.validation?.min||1;return s<=2?t("painScale.levels.none",{},"en"===e?"None":"无痛"):s<=4?t("painScale.levels.mild",{},"en"===e?"Mild":"轻微"):s<=6?t("painScale.levels.moderate",{},"en"===e?"Moderate":"中等"):s<=8?t("painScale.levels.severe",{},"en"===e?"Severe":"严重"):t("painScale.levels.extreme",{},"en"===e?"Extreme":"极重")})(),")"]})]})})}),(0,s.jsxs)("div",{className:"bg-gradient-to-r from-blue-50 to-blue-100 p-6 rounded-xl overflow-hidden border border-blue-200 shadow-sm",children:[(0,s.jsxs)("h4",{className:"font-semibold text-blue-800 mb-4 flex items-center",children:[s.jsx("span",{children:"\uD83D\uDCD6"}),s.jsx("span",{className:"ml-2",children:t("painScale.reference",{},"en"===e?"Pain Level Reference":"疼痛程度参考")})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-3 text-sm text-blue-700",children:[(0,s.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[s.jsx("span",{className:"w-2 h-2 bg-green-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,s.jsxs)("span",{children:[s.jsx("strong",{children:"0-2:"})," ",t("painScale.descriptions.0-2",{},"en"===e?"No pain or very mild discomfort":"无痛或极轻微不适")]})]}),(0,s.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[s.jsx("span",{className:"w-2 h-2 bg-yellow-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,s.jsxs)("span",{children:[s.jsx("strong",{children:"3-4:"})," ",t("painScale.descriptions.3-4",{},"en"===e?"Mild pain, does not affect daily activities":"轻微疼痛，不影响日常活动")]})]}),(0,s.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[s.jsx("span",{className:"w-2 h-2 bg-orange-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,s.jsxs)("span",{children:[s.jsx("strong",{children:"5-7:"})," ",t("painScale.descriptions.5-7",{},"en"===e?"Moderate pain, affects some activities":"中等疼痛，影响部分活动")]})]}),(0,s.jsxs)("div",{className:"flex items-start break-words bg-white p-3 rounded-lg shadow-sm",children:[s.jsx("span",{className:"w-2 h-2 bg-red-400 rounded-full mt-2 mr-3 flex-shrink-0"}),(0,s.jsxs)("span",{children:[s.jsx("strong",{children:"8-10:"})," ",t("painScale.descriptions.8-10",{},"en"===e?"Severe pain, seriously affects life":"严重疼痛，严重影响生活")]})]})]})]})]})]})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row justify-between items-stretch sm:items-center gap-3 sm:gap-0 mt-6 sm:mt-8",children:[(0,s.jsxs)("button",{onClick:()=>{q()},disabled:0===w,className:"flex items-center justify-center sm:justify-start px-4 py-3 sm:py-2 text-gray-600 hover:text-gray-900 disabled:opacity-50 disabled:cursor-not-allowed mobile-touch-target order-2 sm:order-1",children:[s.jsx(d.Z,{className:"w-4 h-4 mr-2"}),s.jsx("span",{className:"text-sm sm:text-base",children:t("navigation.previous")})]}),(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-2 sm:gap-3 order-1 sm:order-2",children:[!v?.validation?.required&&s.jsx("button",{onClick:F,className:"px-4 sm:px-6 py-3 sm:py-2 text-gray-600 hover:text-gray-900 mobile-touch-target text-sm sm:text-base",children:t("navigation.skip")}),s.jsx("button",{onClick:F,disabled:!(()=>{if(!v)return!1;let e=i[v.id];return!v.validation?.required||("multiple"===v.type?Array.isArray(e)&&e.length>0:null!=e&&""!==e)})(),className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-6 py-3 sm:py-2 rounded-lg font-medium hover:from-blue-700 hover:to-purple-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center mobile-touch-target text-sm sm:text-base",children:w>=j-1?(0,s.jsxs)(s.Fragment,{children:[s.jsx(r.Z,{className:"w-4 h-4 mr-2"}),t("navigation.finish")]}):(0,s.jsxs)(s.Fragment,{children:[t("navigation.next"),s.jsx(m.Z,{className:"w-4 h-4 ml-2"})]})})]})]}),S&&s.jsx("div",{className:"mt-4 p-4 bg-red-50 border border-red-200 rounded-lg",children:(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(u.Z,{className:"w-5 h-5 text-red-500 mr-2"}),s.jsx("p",{className:"text-red-600",children:S})]})})]}),s.jsx(g.Z,{notifications:M,onRemove:z})]}):(0,s.jsxs)("div",{className:"bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 rounded-xl p-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-8 max-w-2xl mx-auto",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx("div",{className:"w-16 h-16 bg-gradient-to-r from-blue-600 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4",children:s.jsx(n,{className:"w-8 h-8 text-white"})}),s.jsx("h2",{className:"text-3xl font-bold text-gray-900 mb-4",children:t("title",{},"en"===e?"Symptom Assessment Tool":"症状评估工具")}),s.jsx("p",{className:"text-lg text-gray-600 mb-6",children:t("subtitle",{},"en"===e?"Professional symptom analysis tool to help you understand your health condition":"专业的症状分析工具，帮助您了解自己的健康状况")})]}),(0,s.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 mb-8",children:[s.jsx("h3",{className:"text-lg font-semibold text-blue-900 mb-4",children:t("start.title",{},"en"===e?"Start Assessment":"开始评估")}),s.jsx("p",{className:"text-blue-800 mb-4",children:t("start.description",{},"en"===e?"This assessment tool will help you understand the severity of your symptoms and provide personalized recommendations":"这个评估工具将帮助您了解症状的严重程度并提供个性化建议")}),s.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4 mb-6",children:(()=>{try{return[t("start.feature1",{},"en"===e?"12 Professional Questions":"12个专业问题"),t("start.feature2",{},"en"===e?"Personalized Recommendations":"个性化建议"),t("start.feature3",{},"en"===e?"Scientific Assessment":"科学评估"),t("start.feature4",{},"en"===e?"Instant Results":"即时结果")].map((e,t)=>{let i=[o.Z,n,r.Z,l.Z][t]||o.Z;return(0,s.jsxs)("div",{className:"flex items-center space-x-3",children:[s.jsx(i,{className:"w-5 h-5 text-blue-600"}),s.jsx("span",{className:"text-blue-800",children:e})]},t)})}catch(e){return null}})()})]}),(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsxs)("button",{onClick:()=>{_(e)},className:"bg-gradient-to-r from-blue-600 to-purple-600 text-white px-8 py-4 rounded-lg font-semibold text-lg hover:from-blue-700 hover:to-purple-700 transition-colors inline-flex items-center space-x-2",children:[s.jsx(c.Z,{className:"w-5 h-5"}),s.jsx("span",{children:t("start.startButton",{},"en"===e?"Start Assessment":"开始评估")})]}),s.jsx("p",{className:"text-sm text-gray-500 mt-4",children:t("start.disclaimer",{},"en"===e?"This tool is for reference only and cannot replace professional medical advice":"此工具仅供参考，不能替代专业医疗建议")})]})]}),s.jsx(g.Z,{notifications:M,onRemove:z})]})}},7326:(e,t,i)=>{"use strict";i.d(t,{Z:()=>o,f:()=>n});var s=i(10326);i(17577);let a=({size:e="md",color:t="primary",className:i=""})=>s.jsx("div",{className:`inline-flex items-center justify-center ${i}`,children:(0,s.jsxs)("svg",{className:`animate-spin ${(()=>{switch(e){case"sm":return"w-4 h-4";case"md":default:return"w-6 h-6";case"lg":return"w-8 h-8"}})()} ${(()=>{switch(t){case"primary":default:return"text-pink-600";case"secondary":return"text-purple-600";case"white":return"text-white"}})()}`,xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})}),n=({isLoading:e,message:t="Loading...",children:i})=>(0,s.jsxs)("div",{className:"relative",children:[i,e&&s.jsx("div",{className:"absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center z-10",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx(a,{size:"lg"}),s.jsx("p",{className:"mt-2 text-sm text-gray-600",children:t})]})})]}),o=a},29152:(e,t,i)=>{"use strict";i.d(t,{Z:()=>m});var s=i(10326);i(17577);var a=i(54659),n=i(87888),o=i(37202),r=i(18019),l=i(94019),c=i(62438);let d=({notification:e,onRemove:t})=>s.jsx("div",{className:`
        ${(()=>{switch(e.type){case"success":return"bg-green-50 border-green-200";case"error":return"bg-red-50 border-red-200";case"warning":return"bg-yellow-50 border-yellow-200";case"info":return"bg-blue-50 border-blue-200";default:return"bg-gray-50 border-gray-200"}})()}
        border rounded-lg p-4 shadow-lg
        transform transition-all duration-${c.zn.normal}
        animate-slide-in-right
      `,role:"alert",children:(0,s.jsxs)("div",{className:"flex items-start",children:[s.jsx("div",{className:"flex-shrink-0",children:(()=>{switch(e.type){case"success":return s.jsx(a.Z,{className:"w-5 h-5 text-green-500"});case"error":return s.jsx(n.Z,{className:"w-5 h-5 text-red-500"});case"warning":return s.jsx(o.Z,{className:"w-5 h-5 text-yellow-500"});case"info":return s.jsx(r.Z,{className:"w-5 h-5 text-blue-500"});default:return s.jsx(r.Z,{className:"w-5 h-5 text-gray-500"})}})()}),(0,s.jsxs)("div",{className:"ml-3 flex-1",children:[s.jsx("h3",{className:`text-sm font-medium ${(()=>{switch(e.type){case"success":return"text-green-800";case"error":return"text-red-800";case"warning":return"text-yellow-800";case"info":return"text-blue-800";default:return"text-gray-800"}})()}`,children:e.title}),s.jsx("p",{className:`mt-1 text-sm ${(()=>{switch(e.type){case"success":return"text-green-700";case"error":return"text-red-700";case"warning":return"text-yellow-700";case"info":return"text-blue-700";default:return"text-gray-700"}})()}`,children:e.message}),e.actions&&e.actions.length>0&&s.jsx("div",{className:"mt-3 flex space-x-2",children:e.actions.map((e,t)=>s.jsx("button",{onClick:e.action,className:`
                    text-xs font-medium px-3 py-1 rounded-md
                    ${"primary"===e.style?"bg-pink-600 text-white hover:bg-pink-700":"bg-gray-200 text-gray-800 hover:bg-gray-300"}
                    transition-colors duration-${c.zn.fast}
                  `,children:e.label},t))})]}),s.jsx("div",{className:"ml-4 flex-shrink-0",children:s.jsx("button",{onClick:()=>t(e.id),className:"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-pink-500 focus:ring-offset-2 rounded-md","aria-label":"Close notification",children:s.jsx(l.Z,{className:"w-4 h-4"})})})]})}),m=({notifications:e,onRemove:t})=>0===e.length?null:s.jsx("div",{className:"fixed top-4 right-4 z-50 space-y-2 max-w-sm",children:e.map(e=>s.jsx(d,{notification:e,onRemove:t},e.id))})},62438:(e,t,i)=>{"use strict";i.d(t,{C7:()=>a,DI:()=>s,E2:()=>r,Ju:()=>n,RZ:()=>d,Yw:()=>o,bp:()=>l,zn:()=>c});let s={en:[{value:"lower-abdomen",label:"Lower Abdomen",icon:"\uD83E\uDD30"},{value:"lower-back",label:"Lower Back",icon:"\uD83D\uDD19"},{value:"upper-back",label:"Upper Back",icon:"⬆️"},{value:"thighs",label:"Inner Thighs",icon:"\uD83E\uDDB5"},{value:"head",label:"Head",icon:"\uD83E\uDDE0"},{value:"chest",label:"Chest/Breasts",icon:"\uD83D\uDC97"},{value:"pelvis",label:"Pelvic Area",icon:"\uD83D\uDD3B"},{value:"joints",label:"Joints",icon:"\uD83E\uDDB4"}],zh:[{value:"lower-abdomen",label:"下腹部",icon:"\uD83E\uDD30"},{value:"lower-back",label:"下背部",icon:"\uD83D\uDD19"},{value:"upper-back",label:"上背部",icon:"⬆️"},{value:"thighs",label:"大腿内侧",icon:"\uD83E\uDDB5"},{value:"head",label:"头部",icon:"\uD83E\uDDE0"},{value:"chest",label:"胸部/乳房",icon:"\uD83D\uDC97"},{value:"pelvis",label:"盆腔区域",icon:"\uD83D\uDD3B"},{value:"joints",label:"关节",icon:"\uD83E\uDDB4"}]},a={en:[{value:"cramps",label:"Abdominal Cramps",icon:"\uD83D\uDE23"},{value:"headache",label:"Headache",icon:"\uD83E\uDD15"},{value:"bloating",label:"Bloating",icon:"\uD83C\uDF88"},{value:"backache",label:"Back Pain",icon:"\uD83D\uDD19"},{value:"fatigue",label:"Fatigue",icon:"\uD83D\uDE34"},{value:"nausea",label:"Nausea",icon:"\uD83E\uDD22"},{value:"mood-swings",label:"Mood Swings",icon:"\uD83D\uDE24"},{value:"breast-tenderness",label:"Breast Tenderness",icon:"\uD83D\uDC97"},{value:"diarrhea",label:"Diarrhea",icon:"\uD83D\uDCA9"},{value:"constipation",label:"Constipation",icon:"\uD83D\uDEAB"},{value:"dizziness",label:"Dizziness",icon:"\uD83D\uDCAB"},{value:"hot-flashes",label:"Hot Flashes",icon:"\uD83D\uDD25"},{value:"cold-sweats",label:"Cold Sweats",icon:"\uD83E\uDD76"},{value:"insomnia",label:"Sleep Problems",icon:"\uD83C\uDF19"},{value:"anxiety",label:"Anxiety",icon:"\uD83D\uDE30"},{value:"depression",label:"Low Mood",icon:"\uD83D\uDE22"}],zh:[{value:"cramps",label:"腹部痉挛",icon:"\uD83D\uDE23"},{value:"headache",label:"头痛",icon:"\uD83E\uDD15"},{value:"bloating",label:"腹胀",icon:"\uD83C\uDF88"},{value:"backache",label:"背痛",icon:"\uD83D\uDD19"},{value:"fatigue",label:"疲劳",icon:"\uD83D\uDE34"},{value:"nausea",label:"恶心",icon:"\uD83E\uDD22"},{value:"mood-swings",label:"情绪波动",icon:"\uD83D\uDE24"},{value:"breast-tenderness",label:"乳房胀痛",icon:"\uD83D\uDC97"},{value:"diarrhea",label:"腹泻",icon:"\uD83D\uDCA9"},{value:"constipation",label:"便秘",icon:"\uD83D\uDEAB"},{value:"dizziness",label:"头晕",icon:"\uD83D\uDCAB"},{value:"hot-flashes",label:"潮热",icon:"\uD83D\uDD25"},{value:"cold-sweats",label:"冷汗",icon:"\uD83E\uDD76"},{value:"insomnia",label:"睡眠问题",icon:"\uD83C\uDF19"},{value:"anxiety",label:"焦虑",icon:"\uD83D\uDE30"},{value:"depression",label:"情绪低落",icon:"\uD83D\uDE22"}]},n={en:[{value:"heat-therapy",label:"Heat Therapy",icon:"\uD83D\uDD25"},{value:"cold-therapy",label:"Cold Therapy",icon:"\uD83E\uDDCA"},{value:"massage",label:"Massage",icon:"\uD83D\uDC86"},{value:"exercise",label:"Light Exercise",icon:"\uD83D\uDEB6"},{value:"yoga",label:"Yoga/Stretching",icon:"\uD83E\uDDD8"},{value:"meditation",label:"Meditation",icon:"\uD83D\uDD6F️"},{value:"breathing",label:"Breathing Exercises",icon:"\uD83D\uDCA8"},{value:"bath",label:"Warm Bath",icon:"\uD83D\uDEC1"},{value:"rest",label:"Rest/Sleep",icon:"\uD83D\uDE34"},{value:"hydration",label:"Increased Hydration",icon:"\uD83D\uDCA7"},{value:"diet-change",label:"Dietary Changes",icon:"\uD83E\uDD57"},{value:"herbal-tea",label:"Herbal Tea",icon:"\uD83C\uDF75"},{value:"supplements",label:"Supplements",icon:"\uD83D\uDC8A"},{value:"medication",label:"Pain Medication",icon:"\uD83D\uDC89"},{value:"acupuncture",label:"Acupuncture",icon:"\uD83D\uDCCD"},{value:"aromatherapy",label:"Aromatherapy",icon:"\uD83C\uDF38"}],zh:[{value:"heat-therapy",label:"热敷疗法",icon:"\uD83D\uDD25"},{value:"cold-therapy",label:"冷敷疗法",icon:"\uD83E\uDDCA"},{value:"massage",label:"按摩",icon:"\uD83D\uDC86"},{value:"exercise",label:"轻度运动",icon:"\uD83D\uDEB6"},{value:"yoga",label:"瑜伽/拉伸",icon:"\uD83E\uDDD8"},{value:"meditation",label:"冥想",icon:"\uD83D\uDD6F️"},{value:"breathing",label:"呼吸练习",icon:"\uD83D\uDCA8"},{value:"bath",label:"温水浴",icon:"\uD83D\uDEC1"},{value:"rest",label:"休息/睡眠",icon:"\uD83D\uDE34"},{value:"hydration",label:"增加水分摄入",icon:"\uD83D\uDCA7"},{value:"diet-change",label:"饮食调整",icon:"\uD83E\uDD57"},{value:"herbal-tea",label:"草药茶",icon:"\uD83C\uDF75"},{value:"supplements",label:"营养补充剂",icon:"\uD83D\uDC8A"},{value:"medication",label:"止痛药物",icon:"\uD83D\uDC89"},{value:"acupuncture",label:"针灸",icon:"\uD83D\uDCCD"},{value:"aromatherapy",label:"芳香疗法",icon:"\uD83C\uDF38"}]},o={en:[{value:"period",label:"During Period",icon:"\uD83D\uDD34"},{value:"pre",label:"Pre-menstrual (1-7 days before)",icon:"\uD83D\uDFE1"},{value:"post",label:"Post-menstrual (1-7 days after)",icon:"\uD83D\uDFE2"},{value:"ovulation",label:"Around Ovulation",icon:"\uD83E\uDD5A"},{value:"other",label:"Other Time",icon:"⚪"}],zh:[{value:"period",label:"月经期",icon:"\uD83D\uDD34"},{value:"pre",label:"经前期（前1-7天）",icon:"\uD83D\uDFE1"},{value:"post",label:"经后期（后1-7天）",icon:"\uD83D\uDFE2"},{value:"ovulation",label:"排卵期",icon:"\uD83E\uDD5A"},{value:"other",label:"其他时期",icon:"⚪"}]},r={en:[{value:1,label:"Very Mild",description:"Barely noticeable"},{value:2,label:"Mild",description:"Noticeable but not bothersome"},{value:3,label:"Mild+",description:"Slightly bothersome"},{value:4,label:"Moderate",description:"Bothersome but manageable"},{value:5,label:"Moderate+",description:"Quite bothersome"},{value:6,label:"Strong",description:"Interferes with activities"},{value:7,label:"Strong+",description:"Difficult to ignore"},{value:8,label:"Severe",description:"Dominates thoughts"},{value:9,label:"Very Severe",description:"Unable to function"},{value:10,label:"Unbearable",description:"Worst pain imaginable"}],zh:[{value:1,label:"非常轻微",description:"几乎感觉不到"},{value:2,label:"轻微",description:"能感觉到但不困扰"},{value:3,label:"轻微+",description:"稍有困扰"},{value:4,label:"中等",description:"困扰但可管理"},{value:5,label:"中等+",description:"相当困扰"},{value:6,label:"强烈",description:"影响日常活动"},{value:7,label:"强烈+",description:"难以忽视"},{value:8,label:"严重",description:"占据思维"},{value:9,label:"非常严重",description:"无法正常功能"},{value:10,label:"无法忍受",description:"能想象的最严重疼痛"}]},l={en:[{value:1,label:"Not Helpful",icon:"❌"},{value:2,label:"Slightly Helpful",icon:"\uD83D\uDFE1"},{value:3,label:"Moderately Helpful",icon:"\uD83D\uDFE0"},{value:4,label:"Very Helpful",icon:"\uD83D\uDFE2"},{value:5,label:"Extremely Helpful",icon:"✅"}],zh:[{value:1,label:"无效",icon:"❌"},{value:2,label:"稍有帮助",icon:"\uD83D\uDFE1"},{value:3,label:"中等帮助",icon:"\uD83D\uDFE0"},{value:4,label:"很有帮助",icon:"\uD83D\uDFE2"},{value:5,label:"极其有效",icon:"✅"}]},c={fast:150,normal:300,slow:500};new Date().toISOString().split("T")[0];let d={en:{required:"This field is required",invalidDate:"Please enter a valid date",futureDate:"Date cannot be in the future",painLevelRange:"Pain level must be between 1 and 10",durationRange:"Duration must be between 0 and 1440 minutes",effectivenessRange:"Effectiveness must be between 1 and 5",notesLength:"Notes cannot exceed 500 characters",storageError:"Failed to save data. Please try again.",loadError:"Failed to load data. Please refresh the page.",exportError:"Failed to export data. Please try again.",networkError:"Network error. Please check your connection."},zh:{required:"此字段为必填项",invalidDate:"请输入有效日期",futureDate:"日期不能是未来时间",painLevelRange:"疼痛等级必须在1-10之间",durationRange:"持续时间必须在0-1440分钟之间",effectivenessRange:"有效性必须在1-5之间",notesLength:"备注不能超过500个字符",storageError:"保存数据失败，请重试",loadError:"加载数据失败，请刷新页面",exportError:"导出数据失败，请重试",networkError:"网络错误，请检查连接"}}},98415:(e,t,i)=>{"use strict";i.d(t,{A0:()=>a});var s=i(23844);let a=e=>{let t=(0,s.useTranslations)("interactiveTools"),i=(0,s.useLocale)();return{t:e?i=>t(`${e}.${i}`):t,locale:i,isZh:"zh"===i,isEn:"en"===i}}},44702:(e,t,i)=>{"use strict";i.d(t,{z:()=>a});var s=i(17577);let a=()=>{let[e,t]=(0,s.useState)([]),i=(0,s.useCallback)(e=>{let i=`notification_${Date.now()}_${Math.random().toString(36).substr(2,9)}`,s={...e,id:i,duration:e.duration??5e3};return t(e=>[...e,s]),s.duration&&s.duration>0&&setTimeout(()=>{a(i)},s.duration),i},[]),a=(0,s.useCallback)(e=>{t(t=>t.filter(t=>t.id!==e))},[]),n=(0,s.useCallback)(()=>{t([])},[]),o=(0,s.useCallback)((e,t,s)=>i({type:"success",title:e,message:t,duration:s}),[i]),r=(0,s.useCallback)((e,t,s)=>i({type:"error",title:e,message:t,duration:s??8e3}),[i]),l=(0,s.useCallback)((e,t,s)=>i({type:"warning",title:e,message:t,duration:s}),[i]),c=(0,s.useCallback)((e,t,s)=>i({type:"info",title:e,message:t,duration:s}),[i]);return{notifications:e,addNotification:i,removeNotification:a,clearAllNotifications:n,addSuccessNotification:o,addErrorNotification:r,addWarningNotification:l,addInfoNotification:c}}},66697:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("Activity",[["path",{d:"M22 12h-4l-3 9L9 3l-3 9H2",key:"d5dnw9"}]])},86333:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("ArrowLeft",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]])},24230:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("ArrowRight",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]])},76464:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},48998:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("Clock",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["polyline",{points:"12 6 12 12 16 14",key:"68esgv"}]])},43810:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("Copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]])},31540:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]])},36283:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("FileText",[["path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z",key:"1nnpy2"}],["polyline",{points:"14 2 14 8 20 8",key:"1ew0cm"}],["line",{x1:"16",x2:"8",y1:"13",y2:"13",key:"14keom"}],["line",{x1:"16",x2:"8",y1:"17",y2:"17",key:"17nazh"}],["line",{x1:"10",x2:"8",y1:"9",y2:"9",key:"1a5vjj"}]])},67427:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},29389:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("List",[["line",{x1:"8",x2:"21",y1:"6",y2:"6",key:"7ey8pc"}],["line",{x1:"8",x2:"21",y1:"12",y2:"12",key:"rjfblc"}],["line",{x1:"8",x2:"21",y1:"18",y2:"18",key:"c3b1m8"}],["line",{x1:"3",x2:"3.01",y1:"6",y2:"6",key:"1g7gq3"}],["line",{x1:"3",x2:"3.01",y1:"12",y2:"12",key:"1pjlvk"}],["line",{x1:"3",x2:"3.01",y1:"18",y2:"18",key:"28t2mc"}]])},77636:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("MapPin",[["path",{d:"M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z",key:"2oe9fu"}],["circle",{cx:"12",cy:"10",r:"3",key:"ilqhr7"}]])},94893:(e,t,i)=>{"use strict";i.d(t,{Z:()=>s});let s=(0,i(76557).Z)("Play",[["polygon",{points:"5 3 19 12 5 21 5 3",key:"191637"}]])},933:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"BailoutToCSR",{enumerable:!0,get:function(){return a}});let s=i(94129);function a(e){let{reason:t,children:i}=e;throw new s.BailoutToCSRError(t)}},46618:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"PreloadCss",{enumerable:!0,get:function(){return n}});let s=i(10326),a=i(54580);function n(e){let{moduleIds:t}=e,i=(0,a.getExpectedRequestStore)("next/dynamic css"),n=[];if(i.reactLoadableManifest&&t){let e=i.reactLoadableManifest;for(let i of t){if(!e[i])continue;let t=e[i].files.filter(e=>e.endsWith(".css"));n.push(...t)}}return 0===n.length?null:(0,s.jsx)(s.Fragment,{children:n.map(e=>(0,s.jsx)("link",{precedence:"dynamic",rel:"stylesheet",href:i.assetPrefix+"/_next/"+encodeURI(e),as:"style"},e))})}},73077:(e,t,i)=>{"use strict";i.r(t),i.d(t,{default:()=>y,generateMetadata:()=>g,generateStaticParams:()=>h});var s=i(19510),a=i(58585),n=i(40055),o=i(75013),r=i(57371),l=i(55782);let c=(0,l.default)(()=>i.e(105).then(i.bind(i,30105)),{loadableGenerated:{modules:["app/[locale]/interactive-tools/[tool]/page.tsx -> ../components/PainTrackerTool"]},ssr:!1,loading:()=>s.jsx("div",{className:"flex items-center justify-center py-12",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"})})}),d=(0,l.default)(()=>i.e(3074).then(i.bind(i,13074)),{loadableGenerated:{modules:["app/[locale]/interactive-tools/[tool]/page.tsx -> ../components/SymptomAssessmentTool"]},ssr:!1,loading:()=>s.jsx("div",{className:"flex items-center justify-center py-12",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"})})}),m=(0,l.default)(()=>i.e(6141).then(i.bind(i,46141)),{loadableGenerated:{modules:["app/[locale]/interactive-tools/[tool]/page.tsx -> ../components/ConstitutionTestTool"]},ssr:!1,loading:()=>s.jsx("div",{className:"flex items-center justify-center py-12",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-green-600"})})}),u=(0,l.default)(()=>i.e(6914).then(i.bind(i,56914)),{loadableGenerated:{modules:["app/[locale]/interactive-tools/[tool]/page.tsx -> ../components/PeriodPainAssessmentTool"]},ssr:!1,loading:()=>s.jsx("div",{className:"flex items-center justify-center py-12",children:s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-pink-600"})})}),p=async(e,t)=>[{slug:"symptom-assessment",frontmatter:{title:"zh"===t?"症状评估工具":"Symptom Assessment Tool",description:"zh"===t?"通过专业问卷快速识别疼痛类型，为您提供精准的个性化建议。":"Quickly identify pain types through professional questionnaires and receive precise personalized recommendations.",category:"zh"===t?"评估工具":"Assessment",difficulty:"zh"===t?"简单":"Easy",estimatedTime:"zh"===t?"5-10分钟":"5-10 minutes"},content:"zh"===t?`
        <div class="bg-blue-50 border-l-4 border-blue-500 p-6 mb-8">
          <h3 class="text-lg font-semibold text-blue-800 mb-2">开始评估前</h3>
          <p class="text-blue-700">请确保您在一个安静、私密的环境中，可以专心回答问题。这个评估将帮助您更好地了解自己的症状模式。</p>
        </div>
        
        <h2>症状评估问卷</h2>
        <p>请根据您最近3个月的经期体验回答以下问题：</p>
        
        <div class="space-y-6 mt-6">
          <div class="bg-white p-6 rounded-lg shadow-sm border">
            <h3 class="font-semibold mb-4">1. 疼痛强度</h3>
            <p class="text-gray-600 mb-3">请评估您经期疼痛的平均强度（1-10分，10分为最痛）：</p>
            <div class="grid grid-cols-5 gap-2">
              <button class="p-3 border rounded hover:bg-primary-50 transition-colors">1-2分<br><span class="text-xs text-gray-500">轻微</span></button>
              <button class="p-3 border rounded hover:bg-primary-50 transition-colors">3-4分<br><span class="text-xs text-gray-500">轻度</span></button>
              <button class="p-3 border rounded hover:bg-primary-50 transition-colors">5-6分<br><span class="text-xs text-gray-500">中度</span></button>
              <button class="p-3 border rounded hover:bg-primary-50 transition-colors">7-8分<br><span class="text-xs text-gray-500">重度</span></button>
              <button class="p-3 border rounded hover:bg-primary-50 transition-colors">9-10分<br><span class="text-xs text-gray-500">极重</span></button>
            </div>
          </div>
          
          <div class="bg-white p-6 rounded-lg shadow-sm border">
            <h3 class="font-semibold mb-4">2. 疼痛类型</h3>
            <p class="text-gray-600 mb-3">您的疼痛主要表现为：</p>
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 痉挛性疼痛（抽筋样）
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 胀痛（腹部胀满）
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 刺痛（针扎样）
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 钝痛（持续性隐痛）
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 放射痛（向腰背部扩散）
              </label>
            </div>
          </div>
          
          <div class="bg-white p-6 rounded-lg shadow-sm border">
            <h3 class="font-semibold mb-4">3. 伴随症状</h3>
            <p class="text-gray-600 mb-3">除了腹痛，您还有以下症状吗？</p>
            <div class="grid grid-cols-2 gap-2">
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 头痛
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 恶心呕吐
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 腰痛
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 乳房胀痛
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 情绪波动
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 疲劳乏力
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 失眠
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> 食欲改变
              </label>
            </div>
          </div>
          
          <div class="bg-white p-6 rounded-lg shadow-sm border">
            <h3 class="font-semibold mb-4">4. 疼痛时间</h3>
            <p class="text-gray-600 mb-3">疼痛通常在什么时候开始？</p>
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="radio" name="pain-timing" class="mr-3"> 月经前1-2天
              </label>
              <label class="flex items-center">
                <input type="radio" name="pain-timing" class="mr-3"> 月经第一天
              </label>
              <label class="flex items-center">
                <input type="radio" name="pain-timing" class="mr-3"> 月经期间持续
              </label>
              <label class="flex items-center">
                <input type="radio" name="pain-timing" class="mr-3"> 排卵期也有疼痛
              </label>
            </div>
          </div>
          
          <div class="text-center">
            <button class="btn-primary px-8 py-3">
              提交评估
            </button>
          </div>
        </div>
        
        <div class="bg-yellow-50 border-l-4 border-yellow-500 p-6 mt-8">
          <h3 class="text-lg font-semibold text-yellow-800 mb-2">重要提醒</h3>
          <p class="text-yellow-700">此评估仅供参考，不能替代专业医疗诊断。如果您的症状严重或持续恶化，请及时就医。</p>
        </div>
      `:`
        <div class="bg-blue-50 border-l-4 border-blue-500 p-6 mb-8">
          <h3 class="text-lg font-semibold text-blue-800 mb-2">Before Starting</h3>
          <p class="text-blue-700">Please ensure you're in a quiet, private environment where you can focus on answering the questions. This assessment will help you better understand your symptom patterns.</p>
        </div>
        
        <h2>Symptom Assessment Questionnaire</h2>
        <p>Please answer the following questions based on your menstrual experience over the past 3 months:</p>
        
        <div class="space-y-6 mt-6">
          <div class="bg-white p-6 rounded-lg shadow-sm border">
            <h3 class="font-semibold mb-4">1. Pain Intensity</h3>
            <p class="text-gray-600 mb-3">Please rate the average intensity of your menstrual pain (1-10 scale, 10 being the worst):</p>
            <div class="grid grid-cols-5 gap-2">
              <button class="p-3 border rounded hover:bg-primary-50 transition-colors">1-2<br><span class="text-xs text-gray-500">Minimal</span></button>
              <button class="p-3 border rounded hover:bg-primary-50 transition-colors">3-4<br><span class="text-xs text-gray-500">Mild</span></button>
              <button class="p-3 border rounded hover:bg-primary-50 transition-colors">5-6<br><span class="text-xs text-gray-500">Moderate</span></button>
              <button class="p-3 border rounded hover:bg-primary-50 transition-colors">7-8<br><span class="text-xs text-gray-500">Severe</span></button>
              <button class="p-3 border rounded hover:bg-primary-50 transition-colors">9-10<br><span class="text-xs text-gray-500">Extreme</span></button>
            </div>
          </div>
          
          <div class="bg-white p-6 rounded-lg shadow-sm border">
            <h3 class="font-semibold mb-4">2. Pain Type</h3>
            <p class="text-gray-600 mb-3">Your pain is mainly characterized as:</p>
            <div class="space-y-2">
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> Cramping (spasm-like)
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> Bloating (abdominal fullness)
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> Sharp pain (stabbing)
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> Dull ache (continuous)
              </label>
              <label class="flex items-center">
                <input type="checkbox" class="mr-3"> Radiating pain (to back/legs)
              </label>
            </div>
          </div>
          
          <div class="text-center">
            <button class="btn-primary px-8 py-3">
              Submit Assessment
            </button>
          </div>
        </div>
        
        <div class="bg-yellow-50 border-l-4 border-yellow-500 p-6 mt-8">
          <h3 class="text-lg font-semibold text-yellow-800 mb-2">Important Notice</h3>
          <p class="text-yellow-700">This assessment is for reference only and cannot replace professional medical diagnosis. If your symptoms are severe or worsening, please seek medical attention promptly.</p>
        </div>
      `,locale:t},{slug:"pain-tracker",frontmatter:{title:"zh"===t?"疼痛追踪器":"Pain Tracker",description:"zh"===t?"记录疼痛模式，分析趋势变化，优化治疗效果。":"Track pain patterns, analyze trends, and optimize treatment effectiveness.",category:"zh"===t?"追踪工具":"Tracking",difficulty:"zh"===t?"简单":"Easy",estimatedTime:"zh"===t?"每日2-3分钟":"2-3 minutes daily"},content:"zh"===t?`
        <h2>疼痛追踪工具</h2>
        <p>通过每日记录，帮助您识别疼痛模式，为医生提供准确的症状信息。</p>
        
        <div class="bg-green-50 border-l-4 border-green-500 p-6 mb-8">
          <h3 class="text-lg font-semibold text-green-800 mb-2">使用建议</h3>
          <p class="text-green-700">建议每天在固定时间记录，持续至少3个月经周期，以获得更准确的模式分析。</p>
        </div>
        
        <div class="grid md:grid-cols-2 gap-8">
          <div class="bg-white p-6 rounded-lg shadow-sm border">
            <h3 class="font-semibold mb-4">今日记录</h3>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium mb-2">疼痛强度 (0-10)</label>
                <input type="range" min="0" max="10" class="w-full">
                <div class="flex justify-between text-xs text-gray-500 mt-1">
                  <span>无痛</span>
                  <span>极痛</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium mb-2">月经状态</label>
                <select class="w-full p-2 border rounded">
                  <option>请选择</option>
                  <option>月经期</option>
                  <option>月经前期</option>
                  <option>月经后期</option>
                  <option>排卵期</option>
                  <option>其他时期</option>
                </select>
              </div>
              
              <div>
                <label class="block text-sm font-medium mb-2">使用的缓解方法</label>
                <div class="space-y-1">
                  <label class="flex items-center text-sm">
                    <input type="checkbox" class="mr-2"> 热敷
                  </label>
                  <label class="flex items-center text-sm">
                    <input type="checkbox" class="mr-2"> 药物
                  </label>
                  <label class="flex items-center text-sm">
                    <input type="checkbox" class="mr-2"> 运动
                  </label>
                  <label class="flex items-center text-sm">
                    <input type="checkbox" class="mr-2"> 休息
                  </label>
                </div>
              </div>
              
              <button class="w-full btn-primary">保存今日记录</button>
            </div>
          </div>
          
          <div class="bg-white p-6 rounded-lg shadow-sm border">
            <h3 class="font-semibold mb-4">趋势分析</h3>
            <div class="space-y-4">
              <div class="bg-gray-100 p-4 rounded">
                <h4 class="font-medium mb-2">本月平均疼痛强度</h4>
                <div class="text-2xl font-bold text-primary-600">6.2/10</div>
                <p class="text-sm text-gray-600">比上月下降 0.8 分</p>
              </div>
              
              <div class="bg-gray-100 p-4 rounded">
                <h4 class="font-medium mb-2">疼痛天数</h4>
                <div class="text-2xl font-bold text-secondary-600">4天</div>
                <p class="text-sm text-gray-600">本周期疼痛持续时间</p>
              </div>
              
              <div class="bg-gray-100 p-4 rounded">
                <h4 class="font-medium mb-2">最有效缓解方法</h4>
                <div class="text-lg font-semibold text-accent-600">热敷 + 休息</div>
                <p class="text-sm text-gray-600">基于您的记录分析</p>
              </div>
            </div>
          </div>
        </div>
        
        <div class="mt-8 bg-purple-50 border-l-4 border-purple-500 p-6">
          <h3 class="text-lg font-semibold text-purple-800 mb-2">数据导出</h3>
          <p class="text-purple-700 mb-4">您可以导出疼痛记录，在就医时提供给医生参考。</p>
          <button class="btn-outline">导出PDF报告</button>
        </div>
      `:`
        <h2>Pain Tracking Tool</h2>
        <p>Track your daily pain to identify patterns and provide accurate symptom information to your healthcare provider.</p>
        
        <div class="bg-green-50 border-l-4 border-green-500 p-6 mb-8">
          <h3 class="text-lg font-semibold text-green-800 mb-2">Usage Tips</h3>
          <p class="text-green-700">We recommend recording at the same time each day for at least 3 menstrual cycles to get more accurate pattern analysis.</p>
        </div>
        
        <div class="grid md:grid-cols-2 gap-8">
          <div class="bg-white p-6 rounded-lg shadow-sm border">
            <h3 class="font-semibold mb-4">Today's Record</h3>
            <div class="space-y-4">
              <div>
                <label class="block text-sm font-medium mb-2">Pain Intensity (0-10)</label>
                <input type="range" min="0" max="10" class="w-full">
                <div class="flex justify-between text-xs text-gray-500 mt-1">
                  <span>No Pain</span>
                  <span>Extreme</span>
                </div>
              </div>
              
              <div>
                <label class="block text-sm font-medium mb-2">Menstrual Status</label>
                <select class="w-full p-2 border rounded">
                  <option>Please select</option>
                  <option>Menstrual period</option>
                  <option>Pre-menstrual</option>
                  <option>Post-menstrual</option>
                  <option>Ovulation</option>
                  <option>Other</option>
                </select>
              </div>
              
              <button class="w-full btn-primary">Save Today's Record</button>
            </div>
          </div>
          
          <div class="bg-white p-6 rounded-lg shadow-sm border">
            <h3 class="font-semibold mb-4">Trend Analysis</h3>
            <div class="space-y-4">
              <div class="bg-gray-100 p-4 rounded">
                <h4 class="font-medium mb-2">Average Pain This Month</h4>
                <div class="text-2xl font-bold text-primary-600">6.2/10</div>
                <p class="text-sm text-gray-600">Decreased by 0.8 from last month</p>
              </div>
              
              <div class="bg-gray-100 p-4 rounded">
                <h4 class="font-medium mb-2">Pain Days</h4>
                <div class="text-2xl font-bold text-secondary-600">4 days</div>
                <p class="text-sm text-gray-600">Duration this cycle</p>
              </div>
            </div>
          </div>
        </div>
      `,locale:t},{slug:"constitution-test",frontmatter:{title:"zh"===t?"中医体质测试":"TCM Constitution Test",description:"zh"===t?"通过8个问题了解您的中医体质类型，获得个性化的穴位、饮食和生活方式建议。":"Discover your TCM constitution type through 8 questions and get personalized acupoint, diet, and lifestyle recommendations.",category:"zh"===t?"体质评估":"Constitution Assessment",difficulty:"zh"===t?"简单":"Easy",estimatedTime:"zh"===t?"5-8分钟":"5-8 minutes"},content:"",locale:t},{slug:"period-pain-assessment",frontmatter:{title:"zh"===t?"痛经速测小工具":"Period Pain Assessment Tool",description:"zh"===t?"回答几个简单问题，初步了解你的痛经类型和严重程度，获得个性化的健康建议。":"Answer a few simple questions to understand your period pain type and severity, and get personalized health recommendations.",category:"zh"===t?"健康评估":"Health Assessment",difficulty:"zh"===t?"简单":"Easy",estimatedTime:"zh"===t?"3-5分钟":"3-5 minutes"},content:"",locale:t},{slug:"period-pain-assessment",frontmatter:{title:"zh"===t?"痛经速测小工具":"Period Pain Assessment Tool",description:"zh"===t?"回答几个简单问题，初步了解你的痛经类型和严重程度，获得个性化的健康建议。":"Answer a few simple questions to understand your period pain type and severity, and get personalized health recommendations.",category:"zh"===t?"健康评估":"Health Assessment",difficulty:"zh"===t?"简单":"Easy",estimatedTime:"zh"===t?"3-5分钟":"3-5 minutes"},content:"",locale:t}].find(i=>i.slug===e&&i.locale===t)||null;async function h(){let e=["symptom-assessment","pain-tracker","constitution-test","period-pain-assessment"],t=[];for(let i of["en","zh"])for(let s of e)t.push({locale:i,tool:s});return t}async function g({params:{locale:e,tool:t}}){let i=await p(t,e);return i?{title:`${i.frontmatter.title} | periodhub.health`,description:i.frontmatter.description,openGraph:{title:i.frontmatter.title,description:i.frontmatter.description,type:"website"}}:{title:"zh"===e?"工具未找到":"Tool Not Found",description:"zh"===e?"抱歉，我们找不到您要查找的工具。":"Sorry, we could not find the tool you are looking for."}}async function y({params:{locale:e,tool:t}}){(0,n.t)(e);let i=await p(t,e);await (0,o.Z)({locale:e,namespace:"common"});let l=await (0,o.Z)({locale:e,namespace:"toolPage"});return i||(0,a.notFound)(),(0,s.jsxs)("div",{className:"space-y-8",children:[s.jsx("div",{className:"container-custom",children:(0,s.jsxs)(r.default,{href:`/${e}/interactive-tools`,className:"inline-flex items-center text-primary-600 hover:text-primary-700 font-medium",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 mr-2",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 19l-7-7 7-7"})}),l("backToTools")]})}),s.jsx("header",{className:"container-custom",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto text-center",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-4 text-sm text-neutral-600 mb-4",children:[s.jsx("span",{className:"bg-secondary-100 text-secondary-700 px-3 py-1 rounded-full",children:i.frontmatter.category}),(0,s.jsxs)("span",{children:["• ",i.frontmatter.difficulty]}),(0,s.jsxs)("span",{children:["• ",i.frontmatter.estimatedTime]})]}),s.jsx("h1",{className:"text-3xl md:text-4xl lg:text-5xl font-bold text-neutral-800 mb-6",children:i.frontmatter.title}),s.jsx("p",{className:"text-lg md:text-xl text-neutral-600 mb-8 leading-relaxed max-w-3xl mx-auto",children:i.frontmatter.description})]})}),s.jsx("main",{className:"container-custom",children:s.jsx("div",{className:"max-w-6xl mx-auto",children:"pain-tracker"===t?s.jsx(c,{locale:e}):"symptom-assessment"===t?s.jsx(d,{locale:e}):"constitution-test"===t?s.jsx(m,{locale:e}):"period-pain-assessment"===t?s.jsx(u,{locale:e}):s.jsx("div",{className:"prose prose-lg max-w-none prose-primary prose-headings:text-neutral-800 prose-p:text-neutral-700",dangerouslySetInnerHTML:{__html:i.content}})})}),s.jsx("section",{className:"container-custom",children:s.jsx("div",{className:"max-w-4xl mx-auto",children:(0,s.jsxs)("div",{className:"bg-red-50 border-l-4 border-red-500 text-red-700 p-6 rounded-r-lg",children:[s.jsx("p",{className:"font-bold mb-2",children:l("medicalDisclaimer")}),s.jsx("p",{className:"text-sm",children:l("medicalDisclaimerText")})]})})})]})}},55782:(e,t,i)=>{"use strict";i.d(t,{default:()=>a.a});var s=i(34567),a=i.n(s)},57371:(e,t,i)=>{"use strict";i.d(t,{default:()=>a.a});var s=i(670),a=i.n(s)},670:(e,t,i)=>{"use strict";let{createProxy:s}=i(68570);e.exports=s("/Users/<USER>/Downloads/periodhub-health_副本01版/node_modules/next/dist/client/link.js")},34567:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return n}});let s=i(53370);i(19510),i(71159);let a=s._(i(26155));function n(e,t){var i;let s={loading:e=>{let{error:t,isLoading:i,pastDelay:s}=e;return null}};"function"==typeof e&&(s.loader=e);let n={...s,...t};return(0,a.default)({...n,modules:null==(i=n.loadableGenerated)?void 0:i.modules})}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},13689:(e,t,i)=>{"use strict";let{createProxy:s}=i(68570);e.exports=s("/Users/<USER>/Downloads/periodhub-health_副本01版/node_modules/next/dist/shared/lib/lazy-dynamic/dynamic-bailout-to-csr.js")},26155:(e,t,i)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return c}});let s=i(19510),a=i(71159),n=i(13689),o=i(44459);function r(e){return{default:e&&"default"in e?e.default:e}}let l={loader:()=>Promise.resolve(r(()=>null)),loading:null,ssr:!0},c=function(e){let t={...l,...e},i=(0,a.lazy)(()=>t.loader().then(r)),c=t.loading;function d(e){let r=c?(0,s.jsx)(c,{isLoading:!0,pastDelay:!0,error:null}):null,l=t.ssr?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(o.PreloadCss,{moduleIds:t.modules}),(0,s.jsx)(i,{...e})]}):(0,s.jsx)(n.BailoutToCSR,{reason:"next/dynamic",children:(0,s.jsx)(i,{...e})});return(0,s.jsx)(a.Suspense,{fallback:r,children:l})}return d.displayName="LoadableComponent",d}},44459:(e,t,i)=>{"use strict";let{createProxy:s}=i(68570);e.exports=s("/Users/<USER>/Downloads/periodhub-health_副本01版/node_modules/next/dist/shared/lib/lazy-dynamic/preload-css.js")}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),s=t.X(0,[8948,1386,6621,6929,2810,6544,4033,5353],()=>i(15129));module.exports=s})();