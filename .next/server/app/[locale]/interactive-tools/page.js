(()=>{var e={};e.id=8999,e.ids=[8999],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},50512:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>r.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>o,routeModule:()=>h,tree:()=>d}),s(48088),s(75110),s(21149),s(35866);var a=s(23191),i=s(88716),l=s(37922),r=s.n(l),n=s(95231),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);s.d(t,c);let d=["",{children:["[locale]",{children:["interactive-tools",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,48088)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,75110)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,21149)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],o=["/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/page.tsx"],m="/[locale]/interactive-tools/page",x={require:s,loadChunk:()=>Promise.resolve()},h=new a.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/[locale]/interactive-tools/page",pathname:"/[locale]/interactive-tools",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},31006:(e,t,s)=>{Promise.resolve().then(s.bind(s,1671)),Promise.resolve().then(s.t.bind(s,79404,23))},1671:(e,t,s)=>{"use strict";s.d(t,{default:()=>l});var a=s(10326),i=s(17577);function l({locale:e}){let[t,s]=(0,i.useState)(!1),[l,r]=(0,i.useState)(0),[n,c]=(0,i.useState)(0),[d,o]=(0,i.useState)(!1),m=[{name:"吸气",nameEn:"Inhale",duration:4,color:"bg-blue-600"},{name:"屏息",nameEn:"Hold",duration:7,color:"bg-purple-600"},{name:"呼气",nameEn:"Exhale",duration:8,color:"bg-pink-600"}],x=()=>{s(!0),o(!1),r(0),c(m[0].duration)},h=()=>m[l];return(0,a.jsxs)("div",{className:"bg-white rounded-xl shadow-lg p-6 border border-blue-100",children:[(0,a.jsxs)("div",{className:"text-center mb-6",children:[a.jsx("div",{className:"w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4",children:a.jsx("span",{className:"text-2xl",children:"\uD83E\uDEC1"})}),a.jsx("h3",{className:"text-2xl font-bold text-blue-800 mb-2",children:"en"===e?"4-7-8 Breathing Exercise":"4-7-8 深呼吸练习"}),a.jsx("p",{className:"text-blue-600 text-sm",children:"en"===e?"Natural pain relief through nervous system regulation":"通过调节神经系统自然缓解疼痛"})]}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-4 mb-6",children:[a.jsx("h4",{className:"font-semibold text-blue-800 mb-2",children:"en"===e?"How to practice:":"练习方法："}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-3 text-center text-sm",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2",children:a.jsx("span",{className:"text-lg font-bold text-blue-600",children:"4"})}),(0,a.jsxs)("p",{className:"text-blue-700",children:["en"===e?"Inhale":"吸气"," 4","en"===e?"s":"秒"]})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2",children:a.jsx("span",{className:"text-lg font-bold text-purple-600",children:"7"})}),(0,a.jsxs)("p",{className:"text-purple-700",children:["en"===e?"Hold":"屏息"," 7","en"===e?"s":"秒"]})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"w-12 h-12 bg-pink-100 rounded-full flex items-center justify-center mx-auto mb-2",children:a.jsx("span",{className:"text-lg font-bold text-pink-600",children:"8"})}),(0,a.jsxs)("p",{className:"text-pink-700",children:["en"===e?"Exhale":"呼气"," 8","en"===e?"s":"秒"]})]})]})]}),a.jsx("div",{className:"text-center mb-6",children:t?(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("div",{className:`w-32 h-32 rounded-full flex items-center justify-center mx-auto transition-all duration-1000 ${h().color}`,children:(0,a.jsxs)("div",{className:"text-white text-center",children:[a.jsx("div",{className:"text-3xl font-bold",children:n}),a.jsx("div",{className:"text-sm",children:"zh"===e?h().name:h().nameEn})]})}),(0,a.jsxs)("p",{className:"text-gray-600",children:["en"===e?"Current:":"正在进行："," ","zh"===e?h().name:h().nameEn]})]}):a.jsx("div",{className:"w-32 h-32 bg-gray-100 rounded-full flex items-center justify-center mx-auto",children:a.jsx("span",{className:"text-4xl text-gray-400",children:"\uD83E\uDEC1"})})}),(0,a.jsxs)("div",{className:"text-center space-y-3",children:[!t&&!d&&a.jsx("button",{onClick:x,className:"bg-blue-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-blue-700 transition-colors w-full",children:"en"===e?"\uD83E\uDEC1 Start Guided Practice":"\uD83E\uDEC1 开始引导练习"}),d&&(0,a.jsxs)("div",{className:"space-y-3",children:[a.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-3",children:a.jsx("p",{className:"text-green-700 font-medium",children:"en"===e?"✅ One cycle completed!":"✅ 一轮练习完成！"})}),a.jsx("button",{onClick:x,className:"bg-purple-600 text-white px-8 py-3 rounded-lg font-semibold hover:bg-purple-700 transition-colors w-full",children:"en"===e?"Practice Again":"再次练习"})]}),t&&a.jsx("button",{onClick:()=>{s(!1),o(!1),r(0),c(0)},className:"bg-gray-500 text-white px-6 py-2 rounded-lg font-medium hover:bg-gray-600 transition-colors",children:"en"===e?"Stop Practice":"停止练习"})]}),(0,a.jsxs)("div",{className:"mt-6 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-4",children:[a.jsx("h5",{className:"font-semibold text-gray-800 mb-2",children:"en"===e?"Scientific Benefits:":"科学效果："}),(0,a.jsxs)("div",{className:"grid grid-cols-3 gap-3 text-center text-xs",children:[(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-lg font-bold text-blue-600",children:"-40%"}),a.jsx("div",{className:"text-gray-600",children:"en"===e?"Pain Perception":"疼痛感知"})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-lg font-bold text-purple-600",children:"-35%"}),a.jsx("div",{className:"text-gray-600",children:"en"===e?"Muscle Tension":"肌肉紧张"})]}),(0,a.jsxs)("div",{children:[a.jsx("div",{className:"text-lg font-bold text-pink-600",children:"+60%"}),a.jsx("div",{className:"text-gray-600",children:"en"===e?"Relaxation":"放松感受"})]})]})]}),a.jsx("div",{className:"mt-4 text-xs text-gray-600",children:a.jsx("p",{children:"en"===e?"\uD83D\uDCA1 Tip: Find a comfortable sitting or lying position, relax all muscles. Beginners should do 3-4 cycles.":"\uD83D\uDCA1 建议：找一个舒适的坐位或躺位，放松全身肌肉。初学者建议进行3-4个循环。"})})]})}},48088:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j,generateMetadata:()=>g,generateStaticParams:()=>b});var a=s(19510),i=s(57371),l=s(75013),r=s(40055),n=s(47725);let c=(0,s(68570).createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/components/BreathingExercise.tsx#default`);var d=s(36478);let o=(0,d.Z)("ClipboardCheck",[["rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1",key:"tgr4d6"}],["path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2",key:"116196"}],["path",{d:"m9 14 2 2 4-4",key:"df797q"}]]),m=(0,d.Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]);var x=s(55716);let h=(0,d.Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),u=(0,d.Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 ******* 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]]);var p=s(69526);async function g({params:{locale:e}}){let t=await (0,l.Z)({locale:e,namespace:"interactiveToolsPage"});return{title:t("title"),description:t("description")}}async function b(){return p.k.map(e=>({locale:e}))}async function j({params:{locale:e}}){(0,r.t)(e);let t=await (0,l.Z)({locale:e,namespace:"interactiveToolsPage"}),s=await (0,l.Z)({locale:e,namespace:"common"}),d=[{title:t("symptomAssessment.title"),description:t("symptomAssessment.description"),href:`/${e}/interactive-tools/symptom-assessment`,icon:a.jsx(o,{className:"w-8 h-8 text-primary-600"}),cta:t("symptomAssessment.startButton")},{title:t("periodPainAssessment.title"),description:t("periodPainAssessment.description"),href:`/${e}/interactive-tools/period-pain-assessment`,icon:a.jsx(m,{className:"w-8 h-8 text-pink-600"}),cta:t("periodPainAssessment.cta")},{title:t("painTracker.title"),description:t("painTracker.description"),href:`/${e}/interactive-tools/pain-tracker`,icon:a.jsx(x.Z,{className:"w-8 h-8 text-secondary-600"}),cta:t("painTracker.startButton")},{title:t("constitutionTest.title"),description:t("constitutionTest.description"),href:`/${e}/interactive-tools/constitution-test`,icon:a.jsx(h,{className:"w-8 h-8 text-green-600"}),cta:t("constitutionTest.cta")},{title:t("personalizedInsights.title"),description:t("personalizedInsights.description"),href:"#",icon:a.jsx(u,{className:"w-8 h-8 text-accent-600"}),cta:s("comingSoon")}];return(0,a.jsxs)("div",{className:"space-y-8 sm:space-y-12 mobile-safe-area",children:[(0,a.jsxs)("header",{className:"text-center px-4 sm:px-0",children:[a.jsx("h1",{className:"text-2xl sm:text-3xl md:text-4xl font-bold text-primary-700 mb-3 sm:mb-4 leading-tight",children:t("title")}),a.jsx("p",{className:"text-base sm:text-lg text-neutral-600 max-w-3xl mx-auto leading-relaxed",children:t("description")})]}),a.jsx("section",{className:"bg-gradient-to-br from-primary-50 to-neutral-50 p-4 sm:p-6 md:p-8 rounded-xl mx-4 sm:mx-0",children:a.jsx("div",{className:"max-w-4xl mx-auto",children:(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-6 sm:gap-8 items-center",children:[a.jsx("div",{children:a.jsx("p",{className:"text-sm sm:text-base text-neutral-700 leading-relaxed",children:t("toolsIntroduction")})}),a.jsx("div",{className:"flex justify-center order-first md:order-last",children:a.jsx(n.Z,{filename:"assessment-illustration.jpg",alt:"Woman using digital health assessment tool on tablet in comfortable home setting",width:300,height:225,description:"Woman using digital health assessment tool, modern tablet interface, comfortable home setting, soft lighting",className:"w-full max-w-sm sm:max-w-md"})})]})})}),a.jsx("section",{className:"container-custom",children:a.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8 max-w-6xl mx-auto",children:d.map(e=>(0,a.jsxs)("div",{className:"card flex flex-col items-center text-center h-full p-4 sm:p-6",children:[a.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 flex items-center justify-center rounded-full bg-neutral-100 mb-4 sm:mb-6",children:e.icon}),a.jsx("h2",{className:"text-base sm:text-lg lg:text-xl font-semibold text-neutral-800 mb-2 sm:mb-3 leading-tight",children:e.title}),a.jsx("p",{className:"text-sm sm:text-base text-neutral-600 mb-4 sm:mb-6 flex-grow leading-relaxed",children:e.description}),"#"===e.href?a.jsx("span",{className:"btn-disabled w-full mobile-touch-target text-sm sm:text-base px-4 py-3",children:e.cta}):a.jsx(i.default,{href:e.href,className:`w-full mobile-touch-target text-sm sm:text-base px-4 py-3 text-center ${e.title.includes("Symptom")||e.title.includes("症状")?"btn-primary":"btn-secondary"}`,children:e.cta})]},e.title))})}),a.jsx("section",{id:"breathing-exercise",className:"container-custom",children:(0,a.jsxs)("div",{className:"space-y-4 sm:space-y-6 bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-4 sm:p-6 lg:p-8",children:[(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("div",{className:"inline-flex items-center justify-center w-16 h-16 sm:w-20 sm:h-20 bg-blue-100 rounded-full mb-4 sm:mb-6",children:a.jsx("span",{className:"text-2xl sm:text-3xl",children:"\uD83E\uDEC1"})}),a.jsx("h2",{className:"text-xl sm:text-2xl md:text-3xl font-bold text-blue-700 mb-3 sm:mb-4 leading-tight",children:t("breathingExercise.title")}),a.jsx("p",{className:"text-sm sm:text-base lg:text-lg text-neutral-600 max-w-3xl mx-auto leading-relaxed",children:t("breathingExercise.description")})]}),a.jsx("div",{className:"max-w-2xl mx-auto",children:a.jsx(c,{locale:e})}),(0,a.jsxs)("div",{className:"bg-blue-50 rounded-lg p-6 max-w-4xl mx-auto",children:[a.jsx("h3",{className:"text-lg font-semibold text-blue-800 mb-3",children:t("breathingExercise.usageTips.title")}),(0,a.jsxs)("div",{className:"grid md:grid-cols-2 gap-4 text-sm text-blue-700",children:[(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium mb-2",children:t("breathingExercise.usageTips.bestTiming.title")}),a.jsx("ul",{className:"space-y-1",children:t.raw("breathingExercise.usageTips.bestTiming.items").map((e,t)=>(0,a.jsxs)("li",{children:["• ",e]},t))})]}),(0,a.jsxs)("div",{children:[a.jsx("h4",{className:"font-medium mb-2",children:t("breathingExercise.usageTips.precautions.title")}),a.jsx("ul",{className:"space-y-1",children:t.raw("breathingExercise.usageTips.precautions.items").map((e,t)=>(0,a.jsxs)("li",{children:["• ",e]},t))})]})]})]})]})}),a.jsx("section",{className:"text-center py-8",children:a.jsx("p",{className:"text-neutral-700",children:t("developmentNote")})}),(0,a.jsxs)("div",{className:"bg-red-50 border-l-4 border-red-500 text-red-700 p-4 my-8 rounded-r-lg",role:"alert",children:[a.jsx("p",{className:"font-bold",children:s("importantNote")}),a.jsx("p",{className:"text-sm",children:s("medicalDisclaimer")})]})]})}},47725:(e,t,s)=>{"use strict";s.d(t,{Z:()=>i});var a=s(19510);function i({filename:e,alt:t,width:s=400,height:i=300,className:l="",description:r}){return(0,a.jsxs)("div",{className:`bg-gradient-to-br from-gray-100 to-gray-200 border-2 border-dashed border-gray-300 rounded-lg flex flex-col items-center justify-center p-4 ${l}`,style:{width:s,height:i,minHeight:i},children:[a.jsx("div",{className:"text-gray-400 text-2xl mb-2",children:"\uD83D\uDCF7"}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("p",{className:"text-sm font-medium text-gray-600 mb-1",children:e}),(0,a.jsxs)("p",{className:"text-xs text-gray-500 mb-2",children:[s,"x",i,"px"]}),r&&a.jsx("p",{className:"text-xs text-gray-400 max-w-xs",children:r}),(0,a.jsxs)("p",{className:"text-xs text-gray-400 mt-2 italic",children:["Alt: ",t]})]})]})}s(71159)},36478:(e,t,s)=>{"use strict";s.d(t,{Z:()=>r});var a=s(71159),i={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),r=(e,t)=>{let s=(0,a.forwardRef)(({color:s="currentColor",size:r=24,strokeWidth:n=2,absoluteStrokeWidth:c,className:d="",children:o,...m},x)=>(0,a.createElement)("svg",{ref:x,...i,width:r,height:r,stroke:s,strokeWidth:c?24*Number(n)/Number(r):n,className:["lucide",`lucide-${l(e)}`,d].join(" "),...m},[...t.map(([e,t])=>(0,a.createElement)(e,t)),...Array.isArray(o)?o:[o]]));return s.displayName=`${e}`,s}},55716:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});let a=(0,s(36478).Z)("BarChart3",[["path",{d:"M3 3v18h18",key:"1s2lah"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},57371:(e,t,s)=>{"use strict";s.d(t,{default:()=>i.a});var a=s(670),i=s.n(a)},670:(e,t,s)=>{"use strict";let{createProxy:a}=s(68570);e.exports=a("/Users/<USER>/Downloads/periodhub-health_副本01版/node_modules/next/dist/client/link.js")}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,1386,6621,6929,2810,6544],()=>s(50512));module.exports=a})();