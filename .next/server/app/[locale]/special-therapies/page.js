(()=>{var e={};e.id=9601,e.ids=[9601],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},44442:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>c,pages:()=>u,routeModule:()=>f,tree:()=>d}),r(29994),r(75110),r(21149),r(35866);var i=r(23191),n=r(88716),s=r(37922),o=r.n(s),a=r(95231),l={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>a[e]);r.d(t,l);let d=["",{children:["[locale]",{children:["special-therapies",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,29994)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/special-therapies/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,75110)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,21149)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/special-therapies/page.tsx"],c="/[locale]/special-therapies/page",p={require:r,loadChunk:()=>Promise.resolve()},f=new i.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/[locale]/special-therapies/page",pathname:"/[locale]/special-therapies",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},5919:(e,t,r)=>{Promise.resolve().then(r.bind(r,13170)),Promise.resolve().then(r.t.bind(r,92481,23)),Promise.resolve().then(r.t.bind(r,79404,23))},92481:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"Image",{enumerable:!0,get:function(){return x}});let i=r(91174),n=r(58374),s=r(10326),o=n._(r(17577)),a=i._(r(60962)),l=i._(r(60815)),d=r(23078),u=r(35248),c=r(31206);r(576);let p=r(50131),f=i._(r(86820)),m={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0};function h(e,t,r,i,n,s,o){let a=null==e?void 0:e.src;e&&e["data-loaded-src"]!==a&&(e["data-loaded-src"]=a,("decode"in e?e.decode():Promise.resolve()).catch(()=>{}).then(()=>{if(e.parentElement&&e.isConnected){if("empty"!==t&&n(!0),null==r?void 0:r.current){let t=new Event("load");Object.defineProperty(t,"target",{writable:!1,value:e});let i=!1,n=!1;r.current({...t,nativeEvent:t,currentTarget:e,target:e,isDefaultPrevented:()=>i,isPropagationStopped:()=>n,persist:()=>{},preventDefault:()=>{i=!0,t.preventDefault()},stopPropagation:()=>{n=!0,t.stopPropagation()}})}(null==i?void 0:i.current)&&i.current(e)}}))}function g(e){return o.use?{fetchPriority:e}:{fetchpriority:e}}globalThis.__NEXT_IMAGE_IMPORTED=!0;let v=(0,o.forwardRef)((e,t)=>{let{src:r,srcSet:i,sizes:n,height:a,width:l,decoding:d,className:u,style:c,fetchPriority:p,placeholder:f,loading:m,unoptimized:v,fill:b,onLoadRef:x,onLoadingCompleteRef:y,setBlurComplete:w,setShowAltText:j,sizesInput:_,onLoad:S,onError:P,...C}=e;return(0,s.jsx)("img",{...C,...g(p),loading:m,width:l,height:a,decoding:d,"data-nimg":b?"fill":"1",className:u,style:c,sizes:n,srcSet:i,src:r,ref:(0,o.useCallback)(e=>{t&&("function"==typeof t?t(e):"object"==typeof t&&(t.current=e)),e&&(P&&(e.src=e.src),e.complete&&h(e,f,x,y,w,v,_))},[r,f,x,y,w,P,v,_,t]),onLoad:e=>{h(e.currentTarget,f,x,y,w,v,_)},onError:e=>{j(!0),"empty"!==f&&w(!0),P&&P(e)}})});function b(e){let{isAppRouter:t,imgAttributes:r}=e,i={as:"image",imageSrcSet:r.srcSet,imageSizes:r.sizes,crossOrigin:r.crossOrigin,referrerPolicy:r.referrerPolicy,...g(r.fetchPriority)};return t&&a.default.preload?(a.default.preload(r.src,i),null):(0,s.jsx)(l.default,{children:(0,s.jsx)("link",{rel:"preload",href:r.srcSet?void 0:r.src,...i},"__nimg-"+r.src+r.srcSet+r.sizes)})}let x=(0,o.forwardRef)((e,t)=>{let r=(0,o.useContext)(p.RouterContext),i=(0,o.useContext)(c.ImageConfigContext),n=(0,o.useMemo)(()=>{var e;let t=m||i||u.imageConfigDefault,r=[...t.deviceSizes,...t.imageSizes].sort((e,t)=>e-t),n=t.deviceSizes.sort((e,t)=>e-t),s=null==(e=t.qualities)?void 0:e.sort((e,t)=>e-t);return{...t,allSizes:r,deviceSizes:n,qualities:s}},[i]),{onLoad:a,onLoadingComplete:l}=e,h=(0,o.useRef)(a);(0,o.useEffect)(()=>{h.current=a},[a]);let g=(0,o.useRef)(l);(0,o.useEffect)(()=>{g.current=l},[l]);let[x,y]=(0,o.useState)(!1),[w,j]=(0,o.useState)(!1),{props:_,meta:S}=(0,d.getImgProps)(e,{defaultLoader:f.default,imgConf:n,blurComplete:x,showAltText:w});return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(v,{..._,unoptimized:S.unoptimized,placeholder:S.placeholder,fill:S.fill,onLoadRef:h,onLoadingCompleteRef:g,setBlurComplete:y,setShowAltText:j,sizesInput:e.sizes,ref:t}),S.priority?(0,s.jsx)(b,{isAppRouter:!r,imgAttributes:_}):null]})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},23484:(e,t,r)=>{"use strict";e.exports=r(81616).vendored.contexts.AmpContext},81157:(e,t,r)=>{"use strict";e.exports=r(81616).vendored.contexts.HeadManagerContext},31206:(e,t,r)=>{"use strict";e.exports=r(81616).vendored.contexts.ImageConfigContext},98710:(e,t)=>{"use strict";function r(e){let{ampFirst:t=!1,hybrid:r=!1,hasQuery:i=!1}=void 0===e?{}:e;return t||r&&i}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isInAmpMode",{enumerable:!0,get:function(){return r}})},23078:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),r(576);let i=r(20380),n=r(35248);function s(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var r,a;let l,d,u,{src:c,sizes:p,unoptimized:f=!1,priority:m=!1,loading:h,className:g,quality:v,width:b,height:x,fill:y=!1,style:w,overrideSrc:j,onLoad:_,onLoadingComplete:S,placeholder:P="empty",blurDataURL:C,fetchPriority:M,decoding:E="async",layout:z,objectFit:O,objectPosition:N,lazyBoundary:I,lazyRoot:k,...A}=e,{imgConf:R,showAltText:D,blurComplete:q,defaultLoader:T}=t,G=R||n.imageConfigDefault;if("allSizes"in G)l=G;else{let e=[...G.deviceSizes,...G.imageSizes].sort((e,t)=>e-t),t=G.deviceSizes.sort((e,t)=>e-t),i=null==(r=G.qualities)?void 0:r.sort((e,t)=>e-t);l={...G,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===T)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let U=A.loader||T;delete A.loader,delete A.srcSet;let B="__next_img_default"in U;if(B){if("custom"===l.loader)throw Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=U;U=t=>{let{config:r,...i}=t;return e(i)}}if(z){"fill"===z&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[z];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[z];t&&!p&&(p=t)}let L="",F=o(b),W=o(x);if("object"==typeof(a=c)&&(s(a)||void 0!==a.src)){let e=s(c)?c.default:c;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(d=e.blurWidth,u=e.blurHeight,C=C||e.blurDataURL,L=e.src,!y){if(F||W){if(F&&!W){let t=F/e.width;W=Math.round(e.height*t)}else if(!F&&W){let t=W/e.height;F=Math.round(e.width*t)}}else F=e.width,W=e.height}}let V=!m&&("lazy"===h||void 0===h);(!(c="string"==typeof c?c:L)||c.startsWith("data:")||c.startsWith("blob:"))&&(f=!0,V=!1),l.unoptimized&&(f=!0),B&&c.endsWith(".svg")&&!l.dangerouslyAllowSVG&&(f=!0),m&&(M="high");let H=o(v),Z=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:O,objectPosition:N}:{},D?{}:{color:"transparent"},w),$=q||"empty"===P?null:"blur"===P?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:F,heightInt:W,blurWidth:d,blurHeight:u,blurDataURL:C||"",objectFit:Z.objectFit})+'")':'url("'+P+'")',J=$?{backgroundSize:Z.objectFit||"cover",backgroundPosition:Z.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:$}:{},Y=function(e){let{config:t,src:r,unoptimized:i,width:n,quality:s,sizes:o,loader:a}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:i,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);i)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,o),u=l.length-1;return{sizes:o||"w"!==d?o:"100vw",srcSet:l.map((e,i)=>a({config:t,src:r,quality:s,width:e})+" "+("w"===d?e:i+1)+d).join(", "),src:a({config:t,src:r,quality:s,width:l[u]})}}({config:l,src:c,unoptimized:f,width:F,quality:H,sizes:p,loader:U});return{props:{...A,loading:V?"lazy":h,fetchPriority:M,width:F,height:W,decoding:E,className:g,style:{...Z,...J},sizes:Y.sizes,srcSet:Y.srcSet,src:j||Y.src},meta:{unoptimized:f,priority:m,placeholder:P,fill:y}}}},60815:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return h},defaultHead:function(){return c}});let i=r(91174),n=r(58374),s=r(10326),o=n._(r(17577)),a=i._(r(78003)),l=r(23484),d=r(81157),u=r(98710);function c(e){void 0===e&&(e=!1);let t=[(0,s.jsx)("meta",{charSet:"utf-8"})];return e||t.push((0,s.jsx)("meta",{name:"viewport",content:"width=device-width"})),t}function p(e,t){return"string"==typeof t||"number"==typeof t?e:t.type===o.default.Fragment?e.concat(o.default.Children.toArray(t.props.children).reduce((e,t)=>"string"==typeof t||"number"==typeof t?e:e.concat(t),[])):e.concat(t)}r(576);let f=["name","httpEquiv","charSet","itemProp"];function m(e,t){let{inAmpMode:r}=t;return e.reduce(p,[]).reverse().concat(c(r).reverse()).filter(function(){let e=new Set,t=new Set,r=new Set,i={};return n=>{let s=!0,o=!1;if(n.key&&"number"!=typeof n.key&&n.key.indexOf("$")>0){o=!0;let t=n.key.slice(n.key.indexOf("$")+1);e.has(t)?s=!1:e.add(t)}switch(n.type){case"title":case"base":t.has(n.type)?s=!1:t.add(n.type);break;case"meta":for(let e=0,t=f.length;e<t;e++){let t=f[e];if(n.props.hasOwnProperty(t)){if("charSet"===t)r.has(t)?s=!1:r.add(t);else{let e=n.props[t],r=i[t]||new Set;("name"!==t||!o)&&r.has(e)?s=!1:(r.add(e),i[t]=r)}}}}return s}}()).reverse().map((e,t)=>{let i=e.key||t;if(!r&&"link"===e.type&&e.props.href&&["https://fonts.googleapis.com/css","https://use.typekit.net/"].some(t=>e.props.href.startsWith(t))){let t={...e.props||{}};return t["data-href"]=t.href,t.href=void 0,t["data-optimized-fonts"]=!0,o.default.cloneElement(e,t)}return o.default.cloneElement(e,{key:i})})}let h=function(e){let{children:t}=e,r=(0,o.useContext)(l.AmpStateContext),i=(0,o.useContext)(d.HeadManagerContext);return(0,s.jsx)(a.default,{reduceComponentsToState:m,headManager:i,inAmpMode:(0,u.isInAmpMode)(r),children:t})};("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},20380:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:n,blurDataURL:s,objectFit:o}=e,a=i?40*i:t,l=n?40*n:r,d=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},35248:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},86820:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:n,quality:s}=e,o=s||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+n+"&q="+o}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},78003:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return o}});let i=r(17577),n=()=>{},s=()=>{};function o(e){var t;let{headManager:r,reduceComponentsToState:o}=e;function a(){if(r&&r.mountedInstances){let t=i.Children.toArray(Array.from(r.mountedInstances).filter(Boolean));r.updateHead(o(t,e))}}return null==r||null==(t=r.mountedInstances)||t.add(e.children),a(),n(()=>{var t;return null==r||null==(t=r.mountedInstances)||t.add(e.children),()=>{var t;null==r||null==(t=r.mountedInstances)||t.delete(e.children)}}),n(()=>(r&&(r._pendingUpdate=a),()=>{r&&(r._pendingUpdate=a)})),s(()=>(r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null),()=>{r&&r._pendingUpdate&&(r._pendingUpdate(),r._pendingUpdate=null)})),null}},29994:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u,generateMetadata:()=>d});var i=r(19510),n=r(66794),s=r.n(n),o=r(57371),a=r(36442),l=r(75013);async function d({params:{locale:e}}){let t=await (0,l.Z)({locale:e,namespace:"SpecialTherapiesPage"});return{title:t("title"),description:t("description")}}function u({params:{locale:e}}){let t=(0,a.Z)("SpecialTherapiesPage"),r=(0,a.Z)("common");return(0,i.jsxs)("div",{className:"space-y-12",children:[(0,i.jsxs)("header",{className:"text-center",children:[i.jsx("h1",{className:"text-3xl md:text-4xl font-bold text-primary-700 mb-4",children:t("title")}),i.jsx("p",{className:"text-lg text-neutral-600 max-w-3xl mx-auto",children:t("description")})]}),i.jsx("section",{className:"bg-gradient-to-br from-primary-50 to-neutral-50 p-6 md:p-8 rounded-xl",children:i.jsx("div",{className:"max-w-4xl mx-auto",children:i.jsx("p",{className:"text-neutral-700 leading-relaxed",children:t("introduction")})})}),(0,i.jsxs)("section",{children:[(0,i.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[i.jsx("h2",{className:"text-2xl font-semibold text-neutral-800",children:t("therapyCards.title")}),(0,i.jsxs)(o.default,{href:`/${e}/natural-therapies`,className:"text-primary-600 hover:text-primary-700 font-medium inline-flex items-center",children:[t("therapyCards.viewAll"),i.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 ml-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})]}),i.jsx("div",{className:"grid md:grid-cols-2 lg:grid-cols-3 gap-6",children:[{id:1,title:"Acupressure Techniques",description:"Traditional pressure point techniques to relieve menstrual discomfort.",image:"https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"},{id:2,title:"Herbal Compresses",description:"Warm herbal compresses using traditional medicinal plants.",image:"https://images.unsplash.com/photo-1471864190281-a93a3070b6de?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"},{id:3,title:"Meridian Massage",description:"Specialized massage techniques focusing on energy meridians.",image:"https://images.unsplash.com/photo-1519823551278-64ac92734fb1?ixlib=rb-4.0.3&auto=format&fit=crop&w=600&q=80"}].map(t=>(0,i.jsxs)("div",{className:"card group overflow-hidden",children:[i.jsx("div",{className:"relative h-48 mb-4 overflow-hidden rounded-lg",children:i.jsx(s(),{src:t.image,alt:t.title,fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover transition-transform duration-300 group-hover:scale-105",onError:e=>{e.target.src="https://placehold.co/600x400?text=Therapy+Image"}})}),i.jsx("h3",{className:"text-xl font-semibold text-neutral-800 mb-2",children:t.title}),i.jsx("p",{className:"text-neutral-600 mb-4",children:t.description}),(0,i.jsxs)(o.default,{href:`/${e}/natural-therapies`,className:"text-primary-600 hover:text-primary-700 font-medium",children:[r("learnMore")," →"]})]},t.id))})]}),(0,i.jsxs)("section",{className:"bg-neutral-100 p-6 md:p-8 rounded-xl",children:[i.jsx("h2",{className:"text-2xl font-semibold text-neutral-800 mb-4",children:t("videoSection.title")}),i.jsx("p",{className:"text-neutral-600 mb-6",children:t("videoSection.description")}),i.jsx("div",{className:"aspect-w-16 aspect-h-9 rounded-lg overflow-hidden shadow-lg",children:i.jsx("iframe",{src:"https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4",allow:"accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture",allowFullScreen:!0,title:"Therapy demonstration video",className:"w-full h-full",onError:e=>{let t=e.target.parentElement;t&&(t.innerHTML='<div class="flex items-center justify-center h-full bg-neutral-200 text-neutral-600">Video unavailable</div>')}})})]}),i.jsx("section",{className:"bg-primary-50 border-l-4 border-primary-500 p-4 rounded-r-lg",children:(0,i.jsxs)("p",{className:"text-neutral-700",children:[i.jsx("strong",{className:"text-primary-700",children:"Disclaimer:"})," ",t("disclaimer")]})})]})}},61506:(e,t,r)=>{"use strict";r.d(t,{Z:()=>s});var i=r(71159),n=r(35073);function s(e){return function(e,t){try{return(0,i.use)(t)}catch(t){throw t instanceof TypeError&&t.message.includes("Cannot read properties of null (reading 'use')")?Error("`".concat(e,"` is not callable within an async component. Please refer to https://next-intl.dev/docs/environments/server-client-components#async-components"),{cause:t}):t}}(e,(0,n.Z)())}},36442:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var i=r(71159),n=r(77872),s=(0,i.cache)(function(e,t){return(0,n.eX)({...e,namespace:t})}),o=r(61506);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[i]=t;return s((0,o.Z)("useTranslations"),i)}},57371:(e,t,r)=>{"use strict";r.d(t,{default:()=>n.a});var i=r(670),n=r.n(i)},10221:(e,t,r)=>{"use strict";let{createProxy:i}=r(68570);e.exports=i("/Users/<USER>/Downloads/periodhub-health_副本01版/node_modules/next/dist/client/image-component.js")},670:(e,t,r)=>{"use strict";let{createProxy:i}=r(68570);e.exports=i("/Users/<USER>/Downloads/periodhub-health_副本01版/node_modules/next/dist/client/link.js")},79241:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImgProps",{enumerable:!0,get:function(){return a}}),r(96501);let i=r(95728),n=r(29472);function s(e){return void 0!==e.default}function o(e){return void 0===e?e:"number"==typeof e?Number.isFinite(e)?e:NaN:"string"==typeof e&&/^[0-9]+$/.test(e)?parseInt(e,10):NaN}function a(e,t){var r,a;let l,d,u,{src:c,sizes:p,unoptimized:f=!1,priority:m=!1,loading:h,className:g,quality:v,width:b,height:x,fill:y=!1,style:w,overrideSrc:j,onLoad:_,onLoadingComplete:S,placeholder:P="empty",blurDataURL:C,fetchPriority:M,decoding:E="async",layout:z,objectFit:O,objectPosition:N,lazyBoundary:I,lazyRoot:k,...A}=e,{imgConf:R,showAltText:D,blurComplete:q,defaultLoader:T}=t,G=R||n.imageConfigDefault;if("allSizes"in G)l=G;else{let e=[...G.deviceSizes,...G.imageSizes].sort((e,t)=>e-t),t=G.deviceSizes.sort((e,t)=>e-t),i=null==(r=G.qualities)?void 0:r.sort((e,t)=>e-t);l={...G,allSizes:e,deviceSizes:t,qualities:i}}if(void 0===T)throw Error("images.loaderFile detected but the file is missing default export.\nRead more: https://nextjs.org/docs/messages/invalid-images-config");let U=A.loader||T;delete A.loader,delete A.srcSet;let B="__next_img_default"in U;if(B){if("custom"===l.loader)throw Error('Image with src "'+c+'" is missing "loader" prop.\nRead more: https://nextjs.org/docs/messages/next-image-missing-loader')}else{let e=U;U=t=>{let{config:r,...i}=t;return e(i)}}if(z){"fill"===z&&(y=!0);let e={intrinsic:{maxWidth:"100%",height:"auto"},responsive:{width:"100%",height:"auto"}}[z];e&&(w={...w,...e});let t={responsive:"100vw",fill:"100vw"}[z];t&&!p&&(p=t)}let L="",F=o(b),W=o(x);if("object"==typeof(a=c)&&(s(a)||void 0!==a.src)){let e=s(c)?c.default:c;if(!e.src)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include src. Received "+JSON.stringify(e));if(!e.height||!e.width)throw Error("An object should only be passed to the image component src parameter if it comes from a static image import. It must include height and width. Received "+JSON.stringify(e));if(d=e.blurWidth,u=e.blurHeight,C=C||e.blurDataURL,L=e.src,!y){if(F||W){if(F&&!W){let t=F/e.width;W=Math.round(e.height*t)}else if(!F&&W){let t=W/e.height;F=Math.round(e.width*t)}}else F=e.width,W=e.height}}let V=!m&&("lazy"===h||void 0===h);(!(c="string"==typeof c?c:L)||c.startsWith("data:")||c.startsWith("blob:"))&&(f=!0,V=!1),l.unoptimized&&(f=!0),B&&c.endsWith(".svg")&&!l.dangerouslyAllowSVG&&(f=!0),m&&(M="high");let H=o(v),Z=Object.assign(y?{position:"absolute",height:"100%",width:"100%",left:0,top:0,right:0,bottom:0,objectFit:O,objectPosition:N}:{},D?{}:{color:"transparent"},w),$=q||"empty"===P?null:"blur"===P?'url("data:image/svg+xml;charset=utf-8,'+(0,i.getImageBlurSvg)({widthInt:F,heightInt:W,blurWidth:d,blurHeight:u,blurDataURL:C||"",objectFit:Z.objectFit})+'")':'url("'+P+'")',J=$?{backgroundSize:Z.objectFit||"cover",backgroundPosition:Z.objectPosition||"50% 50%",backgroundRepeat:"no-repeat",backgroundImage:$}:{},Y=function(e){let{config:t,src:r,unoptimized:i,width:n,quality:s,sizes:o,loader:a}=e;if(i)return{src:r,srcSet:void 0,sizes:void 0};let{widths:l,kind:d}=function(e,t,r){let{deviceSizes:i,allSizes:n}=e;if(r){let e=/(^|\s)(1?\d?\d)vw/g,t=[];for(let i;i=e.exec(r);i)t.push(parseInt(i[2]));if(t.length){let e=.01*Math.min(...t);return{widths:n.filter(t=>t>=i[0]*e),kind:"w"}}return{widths:n,kind:"w"}}return"number"!=typeof t?{widths:i,kind:"w"}:{widths:[...new Set([t,2*t].map(e=>n.find(t=>t>=e)||n[n.length-1]))],kind:"x"}}(t,n,o),u=l.length-1;return{sizes:o||"w"!==d?o:"100vw",srcSet:l.map((e,i)=>a({config:t,src:r,quality:s,width:e})+" "+("w"===d?e:i+1)+d).join(", "),src:a({config:t,src:r,quality:s,width:l[u]})}}({config:l,src:c,unoptimized:f,width:F,quality:H,sizes:p,loader:U});return{props:{...A,loading:V?"lazy":h,fetchPriority:M,width:F,height:W,decoding:E,className:g,style:{...Z,...J},sizes:Y.sizes,srcSet:Y.srcSet,src:j||Y.src},meta:{unoptimized:f,priority:m,placeholder:P,fill:y}}}},95728:(e,t)=>{"use strict";function r(e){let{widthInt:t,heightInt:r,blurWidth:i,blurHeight:n,blurDataURL:s,objectFit:o}=e,a=i?40*i:t,l=n?40*n:r,d=a&&l?"viewBox='0 0 "+a+" "+l+"'":"";return"%3Csvg xmlns='http://www.w3.org/2000/svg' "+d+"%3E%3Cfilter id='b' color-interpolation-filters='sRGB'%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3CfeColorMatrix values='1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 100 -1' result='s'/%3E%3CfeFlood x='0' y='0' width='100%25' height='100%25'/%3E%3CfeComposite operator='out' in='s'/%3E%3CfeComposite in2='SourceGraphic'/%3E%3CfeGaussianBlur stdDeviation='20'/%3E%3C/filter%3E%3Cimage width='100%25' height='100%25' x='0' y='0' preserveAspectRatio='"+(d?"none":"contain"===o?"xMidYMid":"cover"===o?"xMidYMid slice":"none")+"' style='filter: url(%23b);' href='"+s+"'/%3E%3C/svg%3E"}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getImageBlurSvg",{enumerable:!0,get:function(){return r}})},29472:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{VALID_LOADERS:function(){return r},imageConfigDefault:function(){return i}});let r=["default","imgix","cloudinary","akamai","custom"],i={deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",loaderFile:"",domains:[],disableStaticImages:!1,minimumCacheTTL:60,formats:["image/webp"],dangerouslyAllowSVG:!1,contentSecurityPolicy:"script-src 'none'; frame-src 'none'; sandbox;",contentDispositionType:"inline",localPatterns:void 0,remotePatterns:[],qualities:void 0,unoptimized:!1}},66794:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{default:function(){return l},getImageProps:function(){return a}});let i=r(53370),n=r(79241),s=r(10221),o=i._(r(52049));function a(e){let{props:t}=(0,n.getImgProps)(e,{defaultLoader:o.default,imgConf:{deviceSizes:[640,750,828,1080,1200,1920,2048,3840],imageSizes:[16,32,48,64,96,128,256,384],path:"/_next/image",loader:"default",dangerouslyAllowSVG:!1,unoptimized:!0}});for(let[e,r]of Object.entries(t))void 0===r&&delete t[e];return{props:t}}let l=s.Image},52049:(e,t)=>{"use strict";function r(e){var t;let{config:r,src:i,width:n,quality:s}=e,o=s||(null==(t=r.qualities)?void 0:t.reduce((e,t)=>Math.abs(t-75)<Math.abs(e-75)?t:e))||75;return r.path+"?url="+encodeURIComponent(i)+"&w="+n+"&q="+o}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return i}}),r.__next_img_default=!0;let i=r},96501:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[8948,1386,6621,6929,2810,6544],()=>r(44442));module.exports=i})();