(()=>{var e={};e.id=9448,e.ids=[9448],e.modules={72934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},54580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},45869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},20399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},55315:e=>{"use strict";e.exports=require("path")},17360:e=>{"use strict";e.exports=require("url")},23524:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>r.a,__next_app__:()=>u,originalPathname:()=>p,pages:()=>c,routeModule:()=>m,tree:()=>d}),s(52946),s(21149),s(35866);var a=s(23191),n=s(88716),i=s(37922),r=s.n(i),l=s(95231),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let d=["",{children:["test-assessment",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,52946)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/test-assessment/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,21149)),"/Users/<USER>/Downloads/periodhub-health_副本01版/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,35866,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,87421))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/Users/<USER>/Downloads/periodhub-health_副本01版/app/test-assessment/page.tsx"],p="/test-assessment/page",u={require:s,loadChunk:()=>Promise.resolve()},m=new a.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/test-assessment/page",pathname:"/test-assessment",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},47714:(e,t,s)=>{Promise.resolve().then(s.bind(s,43773))},43773:(e,t,s)=>{"use strict";s.d(t,{default:()=>i});var a=s(10326);s(17577);var n=s(94033);function i(){let e=n.n.zh||[],t=n.n.en||[];return(0,a.jsxs)("div",{className:"p-8",children:[a.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Assessment Questions Test"}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold mb-2",children:["中文问题 (",e.length," 题)"]}),a.jsx("ol",{className:"list-decimal list-inside space-y-2",children:e.map((e,t)=>(0,a.jsxs)("li",{className:"text-sm",children:[t+1,". ",e.title," (ID: ",e.id,")"]},e.id))})]}),(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h2",{className:"text-xl font-semibold mb-2",children:["English Questions (",t.length," 题)"]}),a.jsx("ol",{className:"list-decimal list-inside space-y-2",children:t.map((e,t)=>(0,a.jsxs)("li",{className:"text-sm",children:[t+1,". ",e.title," (ID: ",e.id,")"]},e.id))})]})]})}},52946:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var a=s(19510);let n=(0,s(68570).createProxy)(String.raw`/Users/<USER>/Downloads/periodhub-health_副本01版/app/[locale]/interactive-tools/test-assessment.tsx#default`);function i(){return a.jsx(n,{})}},87421:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i}),s(19510);var a=s(66621);let n={runtime:"edge",size:{width:32,height:32},contentType:"image/png"};async function i(e){let{__metadata_id__:t,...s}=e.params,i=(0,a.fillMetadataSegment)(".",s,"icon"),{generateImageMetadata:r}=n;function l(e,t){let s={alt:e.alt,type:e.contentType||"image/png",url:i+(t?"/"+t:"")+"?040dbbaeb70c8654"},{size:a}=e;return a&&(s.sizes=a.width+"x"+a.height),s}return r?(await r({params:s})).map((e,t)=>{let s=(e.id||t)+"";return l(e,s)}):[l(n,"")]}}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),a=t.X(0,[8948,1386,6621,2810,4033],()=>s(23524));module.exports=a})();