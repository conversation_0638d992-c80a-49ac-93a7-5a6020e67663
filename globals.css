@tailwind base;
@tailwind components;
@tailwind utilities;

/* 📱 移动端优先的全局样式 */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #374151;
    background-color: #ffffff;
    /* 移动端优化 */
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-rendering: optimizeLegibility;
  }

  /* 📱 移动端优化标题层级 */
  h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    color: #1f2937;
    margin-bottom: 0.5rem;
  }

  h1 {
    font-size: 1.875rem; /* 30px - 移动端 */
  }

  h2 {
    font-size: 1.5rem; /* 24px - 移动端 */
  }

  h3 {
    font-size: 1.25rem; /* 20px - 移动端 */
  }

  /* 平板及以上设备的标题大小 */
  @media (min-width: 768px) {
    h1 {
      font-size: 2.25rem; /* 36px */
    }

    h2 {
      font-size: 1.875rem; /* 30px */
    }

    h3 {
      font-size: 1.5rem; /* 24px */
    }
  }

  /* 桌面设备的标题大小 */
  @media (min-width: 1024px) {
    h1 {
      font-size: 2.5rem; /* 40px */
    }

    h2 {
      font-size: 2rem; /* 32px */
    }

    h3 {
      font-size: 1.75rem; /* 28px */
    }
  }

  /* 📱 移动端优化段落和链接 */
  p {
    margin-bottom: 1rem;
    line-height: 1.7; /* 移动端更舒适的行高 */
  }

  a {
    color: #9333ea;
    text-decoration: none;
    transition: color 0.2s ease;
  }

  a:hover {
    color: #7c3aed;
    text-decoration: underline;
  }

  /* 📱 移动端优化列表 */
  ul, ol {
    margin-bottom: 1rem;
    padding-left: 1.5rem;
  }

  li {
    margin-bottom: 0.25rem;
    line-height: 1.6;
  }

  /* 📱 移动端优化表格 */
  table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
    font-size: 0.875rem; /* 移动端较小字体 */
  }

  th, td {
    padding: 0.5rem; /* 移动端较小内边距 */
    text-align: left;
    border-bottom: 1px solid #e5e7eb;
  }

  th {
    font-weight: 600;
    background-color: #f9fafb;
  }

  /* 平板及以上设备的表格样式 */
  @media (min-width: 768px) {
    table {
      font-size: 1rem;
    }

    th, td {
      padding: 0.75rem;
    }
  }
}

@layer components {
  /* 📱 移动端优先容器 */
  .container-custom {
    width: 100%;
    margin-left: auto;
    margin-right: auto;
    padding-left: 1rem; /* 16px - 移动端 */
    padding-right: 1rem;
    max-width: 1200px;
  }

  /* 平板及以上的容器内边距 */
  @media (min-width: 640px) {
    .container-custom {
      padding-left: 1.5rem; /* 24px */
      padding-right: 1.5rem;
    }
  }

  @media (min-width: 1024px) {
    .container-custom {
      padding-left: 2rem; /* 32px */
      padding-right: 2rem;
    }
  }

  /* 📱 移动端优化卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-md p-4 transition-all duration-300 hover:shadow-lg;
    /* 移动端较小的内边距和圆角 */
    border-radius: 0.5rem; /* 8px */
    padding: 1rem; /* 16px */
  }

  /* 平板及以上的卡片样式 */
  @media (min-width: 768px) {
    .card {
      border-radius: 0.75rem; /* 12px */
      padding: 1.5rem; /* 24px */
    }
  }

  /* 📱 移动端优化按钮基础样式 */
  .btn-base {
    @apply inline-flex items-center justify-center font-medium rounded-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-offset-2;
    /* 移动端触摸友好的最小尺寸 */
    min-height: 44px; /* iOS推荐的最小触摸目标 */
    min-width: 44px;
    padding: 0.75rem 1rem; /* 12px 16px */
    font-size: 0.875rem; /* 14px */
    line-height: 1.25;
    text-align: center;
    cursor: pointer;
    user-select: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* 平板及以上的按钮样式 */
  @media (min-width: 768px) {
    .btn-base {
      padding: 0.875rem 1.5rem; /* 14px 24px */
      font-size: 1rem; /* 16px */
    }
  }

  /* 主要按钮 - 移动端优化 */
  .btn-primary {
    @apply inline-flex items-center justify-center border border-transparent rounded-lg shadow-lg text-white bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-all duration-300;
    /* 移动端按钮尺寸 */
    padding: 0.75rem 1.25rem; /* 12px 20px */
    font-size: 0.875rem; /* 14px */
    min-height: 44px; /* iOS推荐的最小触摸目标 */
  }

  /* 平板及以上的按钮尺寸 */
  @media (min-width: 768px) {
    .btn-primary {
      padding: 0.875rem 1.5rem; /* 14px 24px */
      font-size: 1rem; /* 16px */
      @apply transform hover:scale-105;
    }
  }

  /* 次要按钮 - 移动端优化 */
  .btn-outline {
    @apply inline-flex items-center justify-center border-2 border-white rounded-md text-white bg-white/10 backdrop-blur-sm hover:bg-white hover:text-primary-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-white transition-all duration-300;
    padding: 0.75rem 1rem; /* 12px 16px */
    font-size: 0.875rem; /* 14px */
    min-height: 44px;
    font-weight: 600;
  }

  @media (min-width: 768px) {
    .btn-outline {
      padding: 0.75rem 1.25rem; /* 12px 20px */
      font-size: 1rem; /* 16px */
      @apply transform hover:scale-105;
    }
  }

  /* 辅助按钮 - 移动端优化 */
  .btn-secondary {
    @apply inline-flex items-center justify-center border border-transparent rounded-lg shadow-lg text-white bg-gradient-to-r from-secondary-600 to-primary-600 hover:from-secondary-700 hover:to-primary-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-secondary-500 transition-all duration-300;
    padding: 0.75rem 1.25rem; /* 12px 20px */
    font-size: 0.875rem; /* 14px */
    min-height: 44px;
  }

  @media (min-width: 768px) {
    .btn-secondary {
      padding: 0.875rem 1.5rem; /* 14px 24px */
      font-size: 1rem; /* 16px */
      @apply transform hover:scale-105;
    }
  }

  /* 禁用按钮 */
  .btn-disabled {
    @apply inline-flex items-center justify-center border border-gray-300 rounded-lg text-gray-500 bg-gray-100 cursor-not-allowed;
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
    min-height: 44px;
  }
  
  /* 📱 移动端优先标题样式 */
  .section-title {
    @apply font-bold text-neutral-800 mb-4;
    font-size: 1.5rem; /* 24px - 移动端 */
    line-height: 1.3;
  }

  @media (min-width: 640px) {
    .section-title {
      font-size: 1.75rem; /* 28px - 小平板 */
      margin-bottom: 1.25rem; /* 20px */
    }
  }

  @media (min-width: 768px) {
    .section-title {
      font-size: 2rem; /* 32px - 平板 */
      margin-bottom: 1.5rem; /* 24px */
    }
  }

  @media (min-width: 1024px) {
    .section-title {
      font-size: 2.25rem; /* 36px - 桌面 */
      margin-bottom: 2rem; /* 32px */
    }
  }

  .section-subtitle {
    @apply font-semibold text-neutral-700 mb-3;
    font-size: 1.125rem; /* 18px - 移动端 */
    line-height: 1.4;
  }

  @media (min-width: 768px) {
    .section-subtitle {
      font-size: 1.25rem; /* 20px - 平板+ */
      margin-bottom: 1rem; /* 16px */
    }
  }

  /* Enhanced card styles */
  .card-hover {
    @apply transition-all duration-300 hover:shadow-lg hover:scale-105;
  }

  /* Gradient backgrounds */
  .gradient-purple-pink {
    @apply bg-gradient-to-br from-primary-700 via-primary-600 to-secondary-600;
  }

  .gradient-red-orange {
    @apply bg-gradient-to-br from-red-500 to-orange-500;
  }

  .gradient-green-teal {
    @apply bg-gradient-to-br from-green-500 to-teal-500;
  }

  .gradient-blue-indigo {
    @apply bg-gradient-to-br from-blue-500 to-indigo-500;
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.5s ease-out;
  }

  /* NSAID Article specific styles */
  .mechanism-diagram {
    @apply bg-blue-50 border border-blue-200 rounded-lg p-6 my-6 text-center;
  }

  .mechanism-diagram p {
    @apply text-blue-800 font-semibold text-lg mb-2;
  }

  .treatment-steps {
    @apply bg-purple-50 border border-purple-200 rounded-lg p-6 my-6;
  }

  .treatment-steps p {
    @apply text-purple-800 mb-4 last:mb-0;
  }

  .treatment-steps strong {
    @apply text-purple-900 font-bold;
  }

  .treatment-recommendation {
    @apply bg-green-50 border border-green-200 rounded-lg p-4 my-4;
  }

  .treatment-recommendation p {
    @apply text-green-800 mb-2 last:mb-0;
  }

  /* 📱 文章页面移动端优化 */
  .article-content {
    /* 移动端文字优化 */
    line-height: 1.7;
    word-break: break-word;
  }

  .article-content h1,
  .article-content h2,
  .article-content h3,
  .article-content h4,
  .article-content h5,
  .article-content h6 {
    scroll-margin-top: 80px; /* 为固定头部留出空间 */
  }

  /* 移动端表格优化 */
  @media (max-width: 768px) {
    .article-content table {
      font-size: 0.875rem;
    }

    .article-content th,
    .article-content td {
      padding: 0.5rem !important;
    }
  }

  /* 代码块移动端优化 */
  .article-content pre {
    @apply text-sm overflow-x-auto;
    max-width: 100%;
  }

  @media (max-width: 640px) {
    .article-content pre {
      font-size: 0.75rem;
      padding: 0.75rem;
    }
  }

  /* 引用块移动端优化 */
  .article-content blockquote {
    @apply border-l-4 border-primary-400 pl-4 py-2 bg-primary-50 italic my-4 rounded-r-lg;
  }

  @media (max-width: 640px) {
    .article-content blockquote {
      @apply pl-3 py-3 text-sm;
    }
  }

  /* 列表移动端优化 */
  .article-content ul,
  .article-content ol {
    @apply space-y-2;
  }

  @media (max-width: 640px) {
    .article-content ul,
    .article-content ol {
      @apply space-y-1 text-sm;
    }
  }

  /* 图片移动端优化 */
  .article-content img {
    @apply rounded-lg shadow-sm max-w-full h-auto;
  }

  /* 文本截断工具类 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .treatment-recommendation strong {
    @apply text-green-900 font-bold;
  }
}

/* Custom slider styles */
input[type="range"] {
  -webkit-appearance: none;
  appearance: none;
  background: transparent;
  cursor: pointer;
  height: 12px;
  border-radius: 6px;
}

/* Pain scale slider with gradient background */
input[type="range"].pain-scale {
  background: linear-gradient(to right,
    #10b981 0%,
    #22c55e 20%,
    #eab308 40%,
    #f97316 60%,
    #ef4444 80%,
    #dc2626 100%);
  height: 12px;
  border-radius: 6px;
}

/* Prevent text overflow in pain scale cards */
.pain-scale-container {
  overflow: hidden;
}

.pain-scale-container .text-sm {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.75rem;
}

/* Mobile responsive pain scale labels */
@media (max-width: 640px) {
  .pain-scale-container .text-sm {
    font-size: 0.625rem;
  }
}

input[type="range"]::-webkit-slider-track {
  background: #e5e7eb;
  height: 12px;
  border-radius: 6px;
}

input[type="range"].pain-scale::-webkit-slider-track {
  background: linear-gradient(to right,
    #10b981 0%,
    #22c55e 20%,
    #eab308 40%,
    #f97316 60%,
    #ef4444 80%,
    #dc2626 100%);
  height: 12px;
  border-radius: 6px;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: #ffffff;
  cursor: pointer;
  border: 3px solid #dc2626;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

input[type="range"]::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

input[type="range"]::-moz-range-track {
  background: #e5e7eb;
  height: 12px;
  border-radius: 6px;
  border: none;
}

input[type="range"].pain-scale::-moz-range-track {
  background: linear-gradient(to right,
    #10b981 0%,
    #22c55e 20%,
    #eab308 40%,
    #f97316 60%,
    #ef4444 80%,
    #dc2626 100%);
  height: 12px;
  border-radius: 6px;
  border: none;
}

input[type="range"]::-moz-range-thumb {
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: #ffffff;
  cursor: pointer;
  border: 3px solid #dc2626;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

input[type="range"]::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

/* Keyframe animations */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 📱 移动端特定优化 */
@media (max-width: 767px) {
  /* 移动端文本选择优化 */
  * {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
  }

  /* 允许文本内容被选择 */
  p, span, div[class*="text"], h1, h2, h3, h4, h5, h6 {
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }

  /* 移动端滚动优化 */
  body {
    -webkit-overflow-scrolling: touch;
    overscroll-behavior-y: contain;
  }

  /* 移动端点击反馈优化 */
  button, a, [role="button"] {
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
    touch-action: manipulation;
  }

  /* 移动端输入框优化 */
  input, textarea, select {
    font-size: 16px; /* 防止iOS缩放 */
    -webkit-appearance: none;
    border-radius: 0.5rem; /* 移动端友好的圆角 */
    min-height: 44px; /* iOS推荐最小触摸目标 */
    padding: 0.75rem 1rem; /* 更舒适的内边距 */
  }

  /* 移动端表单元素间距优化 */
  .form-group {
    margin-bottom: 1.5rem; /* 24px - 移动端更大间距 */
  }

  /* 移动端单选框和复选框优化 */
  input[type="radio"], input[type="checkbox"] {
    min-width: 20px;
    min-height: 20px;
    margin-right: 0.75rem;
  }

  /* 移动端标签优化 */
  label {
    display: flex;
    align-items: center;
    min-height: 44px; /* 确保足够的触摸目标 */
    padding: 0.5rem 0;
    cursor: pointer;
    -webkit-tap-highlight-color: rgba(59, 130, 246, 0.1);
  }
}

/* 📱 移动端横屏优化 */
@media (max-width: 767px) and (orientation: landscape) {
  .container-custom {
    padding-left: 1rem;
    padding-right: 1rem;
  }

  /* 横屏时减少垂直间距 */
  .space-y-8 > * + * {
    margin-top: 1.5rem;
  }
}

/* 📱 超小屏设备优化 (320px-374px) */
@media (max-width: 374px) {
  .container-custom {
    padding-left: 0.5rem;
    padding-right: 0.5rem;
  }

  .btn-primary, .btn-secondary, .btn-outline {
    padding: 0.625rem 1rem;
    font-size: 0.8125rem;
    min-height: 40px;
  }

  /* 超小屏网格优化 */
  .grid-cols-2 {
    grid-template-columns: 1fr; /* 强制单列 */
  }

  /* 超小屏文字大小调整 */
  .text-3xl {
    font-size: 1.5rem; /* 24px */
  }

  .text-2xl {
    font-size: 1.25rem; /* 20px */
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    transition-duration: 0.01ms !important;
  }
}

/* Performance optimizations */
.mobile-touch-target {
  min-height: 44px;
  min-width: 44px;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Loading states */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Focus improvements */
.focus-visible:focus-visible {
  outline: 2px solid #9333ea;
  outline-offset: 2px;
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }

  body {
    font-size: 12pt;
    line-height: 1.4;
  }

  h1, h2, h3 {
    page-break-after: avoid;
  }
}
