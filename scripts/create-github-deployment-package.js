#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

console.log('🚀 Creating GitHub Deployment Package...\n');

// Define the output file
const outputPath = path.join(process.cwd(), 'periodhub-github-deployment.zip');

// Create a file to stream archive data to
const output = fs.createWriteStream(outputPath);
const archive = archiver('zip', {
  zlib: { level: 9 } // Maximum compression
});

// Listen for all archive data to be written
output.on('close', function() {
  const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
  console.log(`\n✅ GitHub deployment package created successfully!`);
  console.log(`📦 File: periodhub-github-deployment.zip`);
  console.log(`📊 Size: ${sizeInMB} MB (${archive.pointer()} bytes)`);
  console.log(`\n🎯 This package contains all necessary files for GitHub upload.`);
  console.log(`📋 Next steps:`);
  console.log(`   1. Upload this ZIP to GitHub repository`);
  console.log(`   2. Extract and commit the files`);
  console.log(`   3. Vercel will automatically redeploy`);
});

// Handle warnings
archive.on('warning', function(err) {
  if (err.code === 'ENOENT') {
    console.warn('⚠️  Warning:', err.message);
  } else {
    throw err;
  }
});

// Handle errors
archive.on('error', function(err) {
  console.error('❌ Error creating archive:', err);
  throw err;
});

// Pipe archive data to the file
archive.pipe(output);

// Files and directories to exclude
const excludePatterns = [
  '.next',
  'node_modules',
  '.git',
  '.DS_Store',
  '*.log',
  '.env*',
  '.vercel',
  'coverage',
  'dist',
  'build',
  '*.zip',
  'scripts/create-github-deployment-package.js',
  'scripts/create-corrected-deployment.js',
  'scripts/verify-deployment-readiness.js',
  'periodhub-deployment-CORRECTED.zip',
  'VERCEL-DEPLOYMENT-TROUBLESHOOTING.md',
  'IMMEDIATE-ACTION-PLAN.md',
  'SYMPTOM-ASSESSMENT-TOOL-FIXES.md'
];

// Function to check if a file should be excluded
function shouldExclude(filePath) {
  const relativePath = path.relative(process.cwd(), filePath);
  
  return excludePatterns.some(pattern => {
    if (pattern.includes('*')) {
      // Handle wildcard patterns
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(relativePath);
    } else {
      // Handle exact matches and directory patterns
      return relativePath.startsWith(pattern) || relativePath.includes(`/${pattern}/`) || relativePath.endsWith(`/${pattern}`);
    }
  });
}

// Function to recursively add files
function addFilesToArchive(dirPath, archivePath = '') {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const archiveItemPath = archivePath ? path.join(archivePath, item) : item;
    
    if (shouldExclude(fullPath)) {
      console.log(`⏭️  Skipping: ${archiveItemPath}`);
      continue;
    }
    
    const stats = fs.statSync(fullPath);
    
    if (stats.isDirectory()) {
      console.log(`📁 Adding directory: ${archiveItemPath}/`);
      addFilesToArchive(fullPath, archiveItemPath);
    } else {
      console.log(`📄 Adding file: ${archiveItemPath}`);
      archive.file(fullPath, { name: archiveItemPath });
    }
  }
}

console.log('📦 Packaging files for GitHub deployment...\n');

// Add all files except excluded ones
addFilesToArchive(process.cwd());

// Finalize the archive
archive.finalize();
