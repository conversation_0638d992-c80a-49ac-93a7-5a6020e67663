#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

const outputPath = path.join(process.cwd(), 'periodhub-deployment-CORRECTED.zip');

// 删除现有的部署包
if (fs.existsSync(outputPath)) {
  fs.unlinkSync(outputPath);
  console.log('🗑️  Removed existing deployment package');
}

// 创建输出流
const output = fs.createWriteStream(outputPath);
const archive = archiver('zip', {
  zlib: { level: 9 } // 最高压缩级别
});

// 监听事件
output.on('close', function() {
  const sizeInMB = (archive.pointer() / 1024 / 1024).toFixed(2);
  console.log(`✅ Deployment package created successfully!`);
  console.log(`📦 File: ${outputPath}`);
  console.log(`📊 Size: ${sizeInMB} MB`);
  console.log(`🚀 Ready for Vercel deployment`);
});

archive.on('warning', function(err) {
  if (err.code === 'ENOENT') {
    console.warn('⚠️  Warning:', err.message);
  } else {
    throw err;
  }
});

archive.on('error', function(err) {
  throw err;
});

// 连接输出流
archive.pipe(output);

console.log('📦 Creating corrected deployment package...');

// 需要包含的文件和目录
const includePatterns = [
  'app/**/*',
  'components/**/*',
  'hooks/**/*',
  'lib/**/*',
  'messages/**/*',
  'public/**/*',
  'styles/**/*',
  'types/**/*',
  'middleware.ts',
  'next.config.js',
  'package.json',
  'package-lock.json',
  'tailwind.config.ts',
  'tsconfig.json',
  'postcss.config.js',
  '.env.example'
];

// 需要排除的文件和目录
const excludePatterns = [
  'node_modules',
  '.next',
  '.git',
  '.env.local',
  '.env',
  'dist',
  'build',
  '*.log',
  '.DS_Store',
  'Thumbs.db',
  '*.zip',
  'scripts/create-deployment-package.js',
  'scripts/comprehensive-translation-audit.js',
  'scripts/real-time-translation-validator.js',
  'scripts/translation-quality-check.js',
  'scripts/final-translation-validation.js',
  'real-time-validation-report.json',
  'translation-audit-report.json',
  'translation-quality-report.json',
  'final-validation-report.json'
];

// 检查文件是否应该被排除
function shouldExclude(filePath) {
  return excludePatterns.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(filePath);
    }
    return filePath.includes(pattern);
  });
}

// 递归添加文件
function addDirectory(dirPath, archivePath = '') {
  const items = fs.readdirSync(dirPath);
  
  items.forEach(item => {
    const fullPath = path.join(dirPath, item);
    const relativePath = path.relative(process.cwd(), fullPath);
    const archiveItemPath = archivePath ? path.join(archivePath, item) : item;
    
    if (shouldExclude(relativePath)) {
      return;
    }
    
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory()) {
      addDirectory(fullPath, archiveItemPath);
    } else {
      archive.file(fullPath, { name: archiveItemPath });
    }
  });
}

// 添加根目录文件
const rootFiles = [
  'middleware.ts',
  'next.config.js',
  'package.json',
  'package-lock.json',
  'tailwind.config.ts',
  'tsconfig.json',
  'postcss.config.js'
];

rootFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath) && !shouldExclude(file)) {
    archive.file(filePath, { name: file });
  }
});

// 添加目录
const directories = ['app', 'components', 'hooks', 'lib', 'messages', 'public', 'styles', 'types'];

directories.forEach(dir => {
  const dirPath = path.join(process.cwd(), dir);
  if (fs.existsSync(dirPath)) {
    addDirectory(dirPath, dir);
  }
});

// 完成打包
archive.finalize();
