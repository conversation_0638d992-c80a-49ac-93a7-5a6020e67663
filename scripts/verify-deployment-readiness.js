#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying deployment readiness...\n');

// 检查关键文件
const criticalFiles = [
  'app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx',
  'app/[locale]/interactive-tools/components/PainTrackerTool.tsx',
  'app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx',
  'app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx',
  'messages/zh.json',
  'messages/en.json',
  'package.json',
  'next.config.js'
];

let allFilesExist = true;

console.log('📁 Checking critical files:');
criticalFiles.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`${exists ? '✅' : '❌'} ${file}`);
  if (!exists) allFilesExist = false;
});

// 检查翻译文件
console.log('\n🌐 Checking translation files:');
const translationFiles = ['messages/zh.json', 'messages/en.json'];
let translationsValid = true;

translationFiles.forEach(file => {
  try {
    const content = fs.readFileSync(file, 'utf8');
    const translations = JSON.parse(content);
    const keyCount = countKeys(translations);
    console.log(`✅ ${file}: ${keyCount} translation keys`);
  } catch (error) {
    console.log(`❌ ${file}: Invalid JSON or missing`);
    translationsValid = false;
  }
});

// 检查是否有 returnObjects 模式
console.log('\n🔍 Checking for problematic patterns:');
const componentFiles = [
  'app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx',
  'app/[locale]/interactive-tools/components/PainTrackerTool.tsx',
  'app/[locale]/interactive-tools/components/ConstitutionTestTool.tsx',
  'app/[locale]/interactive-tools/components/PeriodPainAssessmentTool.tsx'
];

let hasProblematicPatterns = false;

componentFiles.forEach(file => {
  if (fs.existsSync(file)) {
    const content = fs.readFileSync(file, 'utf8');
    const hasReturnObjects = content.includes('returnObjects: true');
    console.log(`${hasReturnObjects ? '❌' : '✅'} ${file}: ${hasReturnObjects ? 'Contains returnObjects pattern' : 'Clean'}`);
    if (hasReturnObjects) hasProblematicPatterns = true;
  }
});

// 检查部署包
console.log('\n📦 Checking deployment package:');
const deploymentPackage = 'periodhub-deployment-CORRECTED.zip';
const packageExists = fs.existsSync(deploymentPackage);
console.log(`${packageExists ? '✅' : '❌'} ${deploymentPackage}: ${packageExists ? 'Ready' : 'Missing'}`);

if (packageExists) {
  const stats = fs.statSync(deploymentPackage);
  const sizeInMB = (stats.size / 1024 / 1024).toFixed(2);
  console.log(`📊 Package size: ${sizeInMB} MB`);
}

// 检查 PDF 文件
console.log('\n📄 Checking PDF resources:');
const pdfFiles = [
  'public/downloads/pain-tracking-form.pdf',
  'public/downloads/pain-tracking-form-en.pdf',
  'public/downloads/menstrual-cycle-nutrition-plan.pdf',
  'public/downloads/menstrual-cycle-nutrition-plan-en.pdf',
  'public/downloads/natural-therapy-assessment.pdf',
  'public/downloads/natural-therapy-assessment-en.pdf'
];

let pdfCount = 0;
pdfFiles.forEach(file => {
  const exists = fs.existsSync(file);
  if (exists) pdfCount++;
  console.log(`${exists ? '✅' : '⚠️'} ${file}`);
});

// 最终评估
console.log('\n🎯 Deployment Readiness Assessment:');
console.log('=====================================');

const checks = [
  { name: 'Critical files exist', passed: allFilesExist },
  { name: 'Translation files valid', passed: translationsValid },
  { name: 'No problematic patterns', passed: !hasProblematicPatterns },
  { name: 'Deployment package ready', passed: packageExists },
  { name: 'PDF resources available', passed: pdfCount >= 3 }
];

let passedChecks = 0;
checks.forEach(check => {
  console.log(`${check.passed ? '✅' : '❌'} ${check.name}`);
  if (check.passed) passedChecks++;
});

console.log(`\n📊 Overall Score: ${passedChecks}/${checks.length} checks passed`);

if (passedChecks === checks.length) {
  console.log('\n🚀 READY FOR DEPLOYMENT!');
  console.log('✅ All checks passed');
  console.log('📦 Upload periodhub-deployment-CORRECTED.zip to Vercel');
  console.log('📖 Follow DEPLOYMENT-GUIDE.md for configuration steps');
} else {
  console.log('\n⚠️  DEPLOYMENT NOT READY');
  console.log('❌ Some checks failed - please review and fix issues');
}

// 辅助函数
function countKeys(obj, prefix = '') {
  let count = 0;
  for (const key in obj) {
    if (typeof obj[key] === 'object' && obj[key] !== null) {
      count += countKeys(obj[key], prefix + key + '.');
    } else {
      count++;
    }
  }
  return count;
}
