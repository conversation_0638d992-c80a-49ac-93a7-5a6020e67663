#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const archiver = require('archiver');

console.log('🚀 创建最终部署包...');

// 要排除的文件和目录
const excludePatterns = [
  '.git',
  '.next',
  'node_modules',
  '.DS_Store',
  '*.log',
  '*.zip',
  'scripts',
  '*.md',
  '*.txt',
  '参考文档排除',
  'period-hub-*.zip',
  'periodhub-*.zip',
  '.idea',
  '.vscode',
  'coverage',
  'dist',
  'build',
  'tmp',
  'temp'
];

// 检查文件是否应该被排除
function shouldExclude(filePath) {
  const relativePath = path.relative(process.cwd(), filePath);
  
  return excludePatterns.some(pattern => {
    if (pattern.includes('*')) {
      const regex = new RegExp(pattern.replace(/\*/g, '.*'));
      return regex.test(relativePath);
    }
    return relativePath.includes(pattern);
  });
}

// 递归添加文件到压缩包
function addFilesToArchive(archive, dirPath, basePath = '') {
  const items = fs.readdirSync(dirPath);
  
  for (const item of items) {
    const fullPath = path.join(dirPath, item);
    const relativePath = path.join(basePath, item);
    
    if (shouldExclude(fullPath)) {
      console.log(`⏭️  跳过: ${relativePath}`);
      continue;
    }
    
    const stats = fs.statSync(fullPath);
    
    if (stats.isDirectory()) {
      addFilesToArchive(archive, fullPath, relativePath);
    } else {
      console.log(`📁 添加: ${relativePath}`);
      archive.file(fullPath, { name: relativePath });
    }
  }
}

// 创建部署包
async function createDeploymentPackage() {
  const outputPath = 'periodhub-final-deployment.zip';
  
  // 删除现有的包
  if (fs.existsSync(outputPath)) {
    fs.unlinkSync(outputPath);
  }
  
  const output = fs.createWriteStream(outputPath);
  const archive = archiver('zip', {
    zlib: { level: 9 }
  });
  
  return new Promise((resolve, reject) => {
    output.on('close', () => {
      console.log(`✅ 部署包创建成功: ${outputPath}`);
      console.log(`📦 总大小: ${(archive.pointer() / 1024 / 1024).toFixed(2)} MB`);
      resolve();
    });
    
    archive.on('error', reject);
    archive.pipe(output);
    
    // 添加所有文件
    addFilesToArchive(archive, process.cwd());
    
    archive.finalize();
  });
}

// 验证关键文件
function validateDeployment() {
  console.log('\n🔍 验证关键文件...');
  
  const keyFiles = [
    'package.json',
    'next.config.js',
    'app/[locale]/interactive-tools/components/SymptomAssessmentTool.tsx'
  ];
  
  for (const file of keyFiles) {
    if (fs.existsSync(file)) {
      console.log(`✅ ${file} - 存在`);
      
      if (file.includes('SymptomAssessmentTool.tsx')) {
        const content = fs.readFileSync(file, 'utf8');
        if (content.includes('returnObjects: true')) {
          console.log(`❌ ${file} - 仍包含 returnObjects: true`);
          return false;
        } else {
          console.log(`✅ ${file} - returnObjects 问题已修复`);
        }
      }
    } else {
      console.log(`❌ ${file} - 缺失`);
      return false;
    }
  }
  
  return true;
}

// 主函数
async function main() {
  try {
    if (!validateDeployment()) {
      console.log('❌ 验证失败，请检查文件');
      process.exit(1);
    }
    
    await createDeploymentPackage();
    
    console.log('\n🎉 最终部署包创建完成！');
    console.log('📋 下一步操作：');
    console.log('1. 删除 GitHub 仓库中的所有文件');
    console.log('2. 上传 periodhub-final-deployment.zip');
    console.log('3. 解压并提交到 GitHub');
    console.log('4. 等待 Vercel 自动部署');
    
  } catch (error) {
    console.error('❌ 创建部署包失败:', error);
    process.exit(1);
  }
}

main();
